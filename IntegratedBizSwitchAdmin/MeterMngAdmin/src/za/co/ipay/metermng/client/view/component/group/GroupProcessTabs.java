package za.co.ipay.metermng.client.view.component.group;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.widgets.ScrollableTabLayoutPanel;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.workspace.dashboard.DashboardWorkspaceView;
import za.co.ipay.metermng.client.view.workspace.group.type.GroupTypeWorkspaceView;

public abstract class GroupProcessTabs {

    public GroupProcessTabs() {
        
    }

    
    public void processOpenTabs(final ClientFactory clientFactory, int aleft, int atop, final GroupTypeWorkspaceView parentWorkspace) {
        final String thisPlaceString = parentWorkspace.getPlaceString();
        final int tabCount = clientFactory.getWorkspaceContainer().getWorkspacesCount();
        final ScrollableTabLayoutPanel tabLayoutPanel = ((TabLayoutWorkspaceContainer)clientFactory.getWorkspaceContainer()).getTabLayoutPanel();
        boolean askToClose = false;
        for (int i = 0; i < tabCount; i++) {
             Workspace w = (Workspace) tabLayoutPanel.getWidget(i);
             if(!(w instanceof DashboardWorkspaceView) && !w.getPlaceString().equalsIgnoreCase(thisPlaceString)) {
                 askToClose = true;
                 break;
             }
         }
        if (askToClose) {
             Dialogs.confirm(
                     new String[] {
                             ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.close.tabs.1"),
                             ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.close.tabs.2"),
                             ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.close.tabs.3"),},
                     ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                     ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                     ResourcesFactoryUtil.getInstance().getQuestionIcon(), 
                     new ConfirmHandler() {
                         @Override
                         public void confirmed(boolean confirm) {
                             if (confirm) {
                                 //close tabs
                                 int i = 0;
                                 boolean isMoreTabs = true;
                                 while (isMoreTabs) {
                                     Workspace w = (Workspace) tabLayoutPanel.getWidget(i);
                                     if(!(w instanceof DashboardWorkspaceView) && !w.getPlaceString().equalsIgnoreCase(thisPlaceString)) {
                                         clientFactory.getWorkspaceContainer().closeWorkspaceNow(w);
                                     } else {
                                         i++;
                                     }
                                     int tabsRemaining = clientFactory.getWorkspaceContainer().getWorkspacesCount();
                                     if (i >= tabsRemaining) {
                                          isMoreTabs = false;    
                                     }
                                 }
                                 //When finished closing refocus - focus on tab 0 then back on parentWorkspace tab
                                 tabLayoutPanel.selectTab(0);
                                 int index = tabLayoutPanel.getWidgetIndex(parentWorkspace);
                                 if (index != 0) {
                                     tabLayoutPanel.selectTab(index);
                                 }
                             
                                 continueProcess();
                             } else {
                                 //do nothing - user will close other tabs themselves
                             }
                         }
                     }, aleft, atop
              ); 
         } else {
             //no other tabs open (except this one & perhaps dashboard)
             continueProcess();
         }
 }
    
    protected abstract void continueProcess();
}
