package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouDayProfileTimeData;
import za.co.ipay.metermng.shared.TouSeasonDateData;
import za.co.ipay.metermng.shared.TouSpecialDayData;
import za.co.ipay.metermng.shared.dto.pricing.TouCalendarSeasonsPeriodsSpecialDaysData;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.SuggestOracle.Request;
import com.google.gwt.user.client.ui.SuggestOracle.Response;

public interface CalendarRpcAsync {

    void addSeason(TouSeason add, AsyncCallback<TouSeason> callback);

    void getSeasons(AsyncCallback<ArrayList<TouSeason>> callback);

    void updateSeason(TouSeason updated, AsyncCallback<TouSeason> callback);
    
    void deleteSeason(TouSeason del, AsyncCallback<TouSeason> callback);

    void getPeriods(AsyncCallback<ArrayList<TouPeriod>> callback);

    void addPeriod(TouPeriod add, AsyncCallback<TouPeriod> callback);

    void updatePeriod(TouPeriod updated, AsyncCallback<TouPeriod> callback);

    void deletePeriod(TouPeriod del, AsyncCallback<TouPeriod> callback);

    void getCalendars(Long theCalendarId, AsyncCallback<ArrayList<TouCalendar>> callback);

    void getSeasonSuggestions(Request req, AsyncCallback<Response> callback);

    void getCalendar(Long theCalendarId, AsyncCallback<TouCalendarData> callback);

    void getPeriodSuggestions(Request req, AsyncCallback<Response> callback);

    void getDayProfilesByCalendar(Long calendarId, AsyncCallback<ArrayList<TouDayProfileData>> callback);

    void getDayProfileSuggestions(Long calendarId, Long seasonId, Request request,  AsyncCallback<Response> callback);

    void getCalendarSeasons(Long calendarId, AsyncCallback<ArrayList<TouCalendarSeason>> callback);

    void updateCalendarSeasons(ArrayList<TouCalendarSeason> arrayList, AsyncCallback<Boolean> clientCallback);
    
    void updateCalendar(TouCalendarData calendar, AsyncCallback<TouCalendarData> callback);

    void updateDayProfiles(TouDayProfileData dayProfile, AsyncCallback<TouDayProfileData> callback);

    void deleteDayProfile(TouDayProfileData dayProfile, AsyncCallback<Boolean> clientCallback);
    
    void getSpecialDays(Long calendarId, AsyncCallback<ArrayList<TouSpecialDayData>> clientCallback);

    void getSeasonDates(Long calendarId, AsyncCallback<ArrayList<TouSeasonDateData>> clientCallback);

    void addSeasonDate(TouSeasonDateData seasonDateData, AsyncCallback<TouSeasonDateData> clientCallback);

    void updateSeasonDate(TouSeasonDateData seasonDateData, AsyncCallback<TouSeasonDateData> clientCallback);

    void deleteSeasonDate(TouSeasonDateData seasonDateData, boolean deleteCalendarSeason, AsyncCallback<Boolean> clientCallback);

    void getTimesByTouDayProfile(Long dayProfileId, AsyncCallback<ArrayList<TouDayProfileTimeData>> clientCallback);

    void updateDayProfileTimes(TouDayProfileTimeData dayProfileTime, Long calendarId, AsyncCallback<TouDayProfileTimeData> clientCallback);

    void updateSpecialDay(TouSpecialDayData specialDay, AsyncCallback<TouSpecialDayData> clientCallback);

    void deleteSpecialDay(Long specialDayPK, Long calendarId, AsyncCallback<Boolean> callback);

    void deleteDayProfileTime(Long id, Long calendarId, AsyncCallback<Boolean> callback);

    void getTimeInMilliseconds(TouDayProfileTimeData timeData, AsyncCallback<TouDayProfileTimeData> callback);

    void getTouCalendarData(Long calendarId, AsyncCallback<TouCalendarSeasonsPeriodsSpecialDaysData> callback);

    void getActiveCalendars(AsyncCallback<ArrayList<TouCalendar>> callback);

    void getCalendars(AsyncCallback<ArrayList<TouCalendar>> callback);
    
}
