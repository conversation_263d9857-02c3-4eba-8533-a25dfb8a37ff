package za.co.ipay.metermng.client.view.component.selection;

import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.group.entity.EntityContactPanel;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class GroupDetailsDialogueBox extends DialogBox {

    @UiField ScrollPanel scrollPanel;
    @UiField(provided=true) EntityContactPanel entityPanel;
    
    private HasDirtyData hasDirtyDataLocal = new LocalOnlyHasDirtyData();
    
    private ClientFactory clientFactory;
    
    private static Logger logger = Logger.getLogger(GroupDetailsDialogueBox.class.getName());
    private static GroupDetailsDialogueBoxUiBinder uiBinder = GWT.create(GroupDetailsDialogueBoxUiBinder.class);
    
    interface GroupDetailsDialogueBoxUiBinder extends UiBinder<Widget, GroupDetailsDialogueBox> {
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    
    public GroupDetailsDialogueBox(final ClientFactory clientFactory, final Long genGroupId) {
        this.clientFactory = clientFactory;
        entityPanel = new EntityContactPanel(new Runnable() {
            @Override
            public void run() {
                clientFactory.getGroupRpc().getEntityFromGenGroupId(genGroupId, new ClientCallback<GroupEntity>() {
                    @Override
                    public void onFailure(Throwable caught) {
                        logger.log(Level.SEVERE, "ERROR: GroupDetailsPopup: Failed to fetch group entity info for genGroupId=" + genGroupId, caught);
                        GroupDetailsDialogueBox.this.hide();
                    }
                    @Override
                    public void onSuccess(GroupEntity result) {
                        populate(genGroupId, result);
                    }
                });
            }
        }, null, clientFactory, hasDirtyDataLocal);
        setWidget(uiBinder.createAndBindUi(this));
    }

    private void populate(Long genGroupId, GroupEntity groupEntity) {
        this.setTitle(MessagesUtil.getInstance().getMessage("groupentity.title"));
        this.setGlassEnabled(true);
        this.setModal(true);
        this.setAutoHideEnabled(true);
        
        // Check for permission to edit before enabling button visibility
        boolean permissionToEdit = clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_GROUP_ENTITY);

        if (groupEntity == null && !permissionToEdit) {
            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("usagepointgroup.hierarchy.no.additionalinfo"), 
                                       MediaResourceUtil.getInstance().getInformationIcon(), 
                                       null);
            
        } else {
            customiseEntityPanelButtons();
            entityPanel.changeButtonsPanelVisibility(permissionToEdit);

            GenGroupData genGroupData = new GenGroupData();
            genGroupData.setId(genGroupId);
            if (groupEntity != null) {
                genGroupData.setGroupEntityId(groupEntity.getId());
                entityPanel.display(groupEntity);
            }  
            entityPanel.setGenGroupData(genGroupData);

            //for initial layout must checkHeight as well - but only makes sense after Dom created, so defer 
            Scheduler.get().scheduleDeferred(new ScheduledCommand() {    
                @Override
                public void execute() {
                    int someHeight = entityPanel.getOffsetHeight();
                    int popupHeight = Window.getClientHeight() / 3;
                    int height;
                    if (someHeight > 0 && someHeight < popupHeight * 2) {
                        height = someHeight;
                        logger.info("The Criteria match passed: someHeight = " + someHeight);
                    } else {
                        height = popupHeight * 2;
                        logger.info("The Criteria match didn't pass: someHeight = " + someHeight);
                    }
                    scrollPanel.setHeight(height + "px");
                    GroupDetailsDialogueBox.this.center();
                }
              });
            
            this.setWidget(scrollPanel);
            this.show();
        }
    }
    
    private void customiseEntityPanelButtons() {
        entityPanel.removeSomeBtns();  //remove back & cancel
        Button cancelButton = new Button(MessagesUtil.getInstance().getMessage("button.cancel"));
        cancelButton.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                if (hasDirtyDataLocal.isDirtyData()) {
                    Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                hasDirtyDataLocal.setDirtyData(false);
                                GroupDetailsDialogueBox.this.hide();
                            }
                        }
                    });
                } else {
                    GroupDetailsDialogueBox.this.hide();
                }
            }
        });
        entityPanel.getButtonPanel().add(cancelButton);
    }
}