<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:igwtw="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:ipay="urn:import:za.co.ipay.metermng.client.view.component.meter">
    <ui:style>
        .marginAbove5px {
            margin-top: 5px;
        }
        
        .paddingLeft50px {
            padding-left: 50px;
        }
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:VerticalPanel ui:field="bulkKeyChangeParamsPanel" spacing="10">
            <p1:FormRowPanel>
                <g:Label ui:field="bulkKeyChangeParamHeading" styleName="gwt-Label-header"/>
            </p1:FormRowPanel>

            <p1:FormGroupPanel ui:field="toParams" labelText="{msg.getBulkKeyToHeader}">              
                  <p1:FormRowPanel>
                    <p1:FormElement ui:field="chckbxUseTargetDataElement" labelText="{msg.getBulkKeyChangeUseTarget}"
                                    helpMsg="{msg.getBulkKeyChangeUseTargetHelp}" >
                        <g:CheckBox ui:field="chckbxUseTargetData" checked="false" />
                    </p1:FormElement>    
                  </p1:FormRowPanel>
                  
                  <p1:FormRowPanel>
                      <p1:FormElement ui:field="supGrCdeElement" helpMsg="{msg.getBulkKeyChangeSupplyGroupCodeHelp}" labelText="{msg.getBulkKeyChangeSupplyGroupCode}">
                          <p3:IpayListBox visibleItemCount="1" ui:field="lstbxSupGrCde" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                      </p1:FormElement>

                      <p1:FormElement ui:field="tariffIndxElement" helpMsg="{msg.getBulkKeyChangeTariffIndexHelp}"
                                      labelText="{msg.getBulkKeyChangeTariffIndex}" required="true">
                          <g:TextBox ui:field="txtbxTariffIndx" styleName="gwt-TextBox" visibleLength="3" maxLength="2"/>
                      </p1:FormElement>
                  </p1:FormRowPanel>
            </p1:FormGroupPanel>    
                  
            <ipay:EngineeringTokenUserRefPanel ui:field="engineeringTokenUserRefPanel"/>
                  
            <p1:FormRowPanel>
                <p1:FormElement ui:field="bulkInstrElement" required="true" helpMsg="{msg.getBulkKeyChangeInstructionHelp}" labelText="{msg.getBulkKeyChangeInstructionLabel}">
                    <p3:IpayListBox ui:field="lstbxBulkInstr" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                </p1:FormElement>
                
                <p1:FormElement ui:field="keyChangeAfterDateElement" visible="false" labelText="{msg.getBulkKeyChangeAfterDateLabel}" helpMsg="{msg.getBulkKeyChangeAfterDateHelp}">
                    <igwtw:IpayDateBox ui:field="dtbxKeyChangeAfterDate" styleName="gwt-TextBox"/>
                </p1:FormElement>                  
            </p1:FormRowPanel>
            
            <p1:FormRowPanel>
                <p1:FormElement ui:field="chckbxOverWriteExistingElement" helpMsg="{msg.getBulkKeyChangeOverwriteExistingHelp}"
                                labelText="{msg.getBulkKeyChangeOverWriteExisting}" required="true" >
                    <g:FlowPanel>
                        <g:RadioButton ui:field="yesOverWriteExisting" name="OverWriteExisting" value="false" enabled="true" text="{msg.getOptionPositive}" />
                        <g:RadioButton ui:field="noOverWriteExisting" name="OverWriteExisting" value="false" enabled="true" text="{msg.getOptionNegative}" />
                        <p3:Message ui:field="overWriteExistingFeedBack" visible="false"/>
                 </g:FlowPanel>
                </p1:FormElement>    
            </p1:FormRowPanel>
                  
    </g:VerticalPanel>

</ui:UiBinder> 