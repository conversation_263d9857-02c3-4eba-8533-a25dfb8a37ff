package za.co.ipay.metermng.client.history;

import java.util.Arrays;
import java.util.logging.Logger;

import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * SearchPlace represents a search type place such as the advanced search screen.
 * <AUTHOR>
 */
public class SearchPlace extends Place {

    public static final String ADVANCED_SEARCH_TYPE = "advanced";
    public static final String VIEWED_SEARCH_TYPE = "viewed";
    public static final String MODIFIED_SEARCH_TYPE = "modified";
    
    public static final SearchPlace ADVANCED_SEARCH_PLACE = new SearchPlace(ADVANCED_SEARCH_TYPE);
    
    /** The type of search that is required, eg: advanced, last modified, last viewed, etc. */
    private String searchType;
    /** The type of data that the user wants to view, eg: last modified meters, last viewed customers, etc. */
    private SearchResultType dataType;
    /** The search text that was previously entered. */
    private String searchText;
    
    private static Logger logger = Logger.getLogger(SearchPlace.class.getName());
    
    public SearchPlace(String searchType) {
        this.searchType = searchType;
    }
    
    public SearchPlace(String searchType, SearchResultType dataType, String searchText) {
        this.searchType = searchType;
        this.dataType = dataType;
        this.searchText = searchText;
    }
    
    public String getSearchType() {
        return searchType;
    }
    
    public SearchResultType getDataType() {
        return dataType;
    }

    public String getSearchText() {
        return searchText;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("SearchPlace:");
        sb.append(" searchType:").append(getSearchType());
        sb.append(" dataType:").append(dataType);
        sb.append(" searchText:").append(searchText);
        return sb.toString();
    }
    
    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((dataType == null) ? 0 : dataType.hashCode());
		result = prime * result
				+ ((searchText == null) ? 0 : searchText.hashCode());
		result = prime * result
				+ ((searchType == null) ? 0 : searchType.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SearchPlace other = (SearchPlace) obj;
		if (dataType != other.dataType)
			return false;
		if (searchText == null) {
			if (other.searchText != null)
				return false;
		} else if (!searchText.equals(other.searchText))
			return false;
		if (searchType == null) {
			if (other.searchType != null)
				return false;
		} else if (!searchType.equals(other.searchType))
			return false;
		return true;
	}

	public static String getPlaceAsString(SearchPlace place) {
        return getPlaceAsString(place, true);
    }

    public static String getPlaceAsString(SearchPlace place, boolean includePrefix) {
        if (place != null) {
            StringBuilder sb = new StringBuilder();
            if (includePrefix) {
                sb.append("search:");
            }
            sb.append(place.getSearchType());
            sb.append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
            if (place.getDataType() != null) {
                sb.append(place.getDataType().name());
            }
            sb.append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
            if (place.getSearchText() != null) {
                sb.append(place.getSearchText());
            }
            return sb.toString();
        } else {
            return "";
        }
    }
    
    @Prefix(value="search")
    public static class Tokenizer implements PlaceTokenizer<SearchPlace> {
        @Override
        public String getToken(SearchPlace place) {
            return SearchPlace.getPlaceAsString(place, false);
        }

        @Override
        public SearchPlace getPlace(String token) {
            if (token != null) {
                String[] tokens = token.split("\\"+MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                if (tokens != null) {
                    logger.info("SearchPlace tokens: "+Arrays.toString(tokens));
                    String type = (tokens.length > 0 ? tokens[0] : "");
                    SearchResultType resultType = SearchResultType.getType((tokens.length > 1 ? tokens[1] : ""));
                    String text = (tokens.length >= 3 ? tokens[2] : "");
                    SearchPlace searchPlace = new SearchPlace(type, resultType, text);
                    logger.info("Tokenized searchPlace: "+searchPlace);
                    return searchPlace;
                }
            } 
            return new SearchPlace(token);
        }
    }
}
