package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;

import com.google.gwt.event.shared.GwtEvent;

public class AuxChargeScheduleUpdatedEvent extends GwtEvent<AuxChargeScheduleUpdatedEventHandler> {

    public static Type<AuxChargeScheduleUpdatedEventHandler> TYPE = new Type<AuxChargeScheduleUpdatedEventHandler>();
    
    private AuxChargeSchedule auxChargeSchedule; 
    
    public AuxChargeScheduleUpdatedEvent(AuxChargeSchedule auxChargeSchedule) {
        this.auxChargeSchedule = auxChargeSchedule;
    }
    
	public AuxChargeSchedule getAuxChargeSchedule() {
        return auxChargeSchedule;
    }

    public void setAuxChargeScheduleId(AuxChargeSchedule auxChargeSchedule) {
        this.auxChargeSchedule = auxChargeSchedule;
    }



    @Override
    public Type<AuxChargeScheduleUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(AuxChargeScheduleUpdatedEventHandler handler) {
        handler.processAuxChargeScheduleUpdatedEvent(this);
    }
}
