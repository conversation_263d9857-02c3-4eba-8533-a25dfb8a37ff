package za.co.ipay.metermng.client.view.component.importfile;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkblocking.BulkBlockingParamsPanel;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkfreeissue.BulkFreeIssueParamsPanel;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkkeychange.BulkKeyChangeParamsPanel;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkpricingstructurechange.BulkPricingStructureChangeParamsPanel;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkstoremovement.BulkStoreMovementParamsPanel;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileActionParamsDto;
import za.co.ipay.metermng.shared.integration.bulkblocking.BulkBlockingParamRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;
import za.co.ipay.metermng.shared.integration.bulkkeychange.BulkKeyChangeParamRecord;
import za.co.ipay.metermng.shared.integration.bulkpricingstructurechange.BulkPricingStructureChangeParamRecord;
import za.co.ipay.metermng.shared.integration.bulkstoremovement.BulkStoreMovementParamRecord;

public class ActionParamsPanel extends BaseComponent implements ProvidesResize, RequiresResize {
    //Static Map is to avoid generating a filename and an entry in import_File when there is no action_param panel for a typeclass
    //or when incorrectly specified has_input_file in import_file_type table
    //key is the typeclass and the boolean is what has been implemented in code for the typeclass and has_input_file setting
    //eg code for bulk Keychange generator only sofar implements has_input_file = 'y'. But at some point will implement for 'b'oth
    static Map<String, String> actionPanelsMap = new HashMap<String, String>();
    static {
        actionPanelsMap.put(MeterMngStatics.FILETYPE_BULK_KEY_CHANGE_GENERATOR, "y");
        actionPanelsMap.put(MeterMngStatics.FILETYPE_BULK_PRICING_STRUCTURE_CHANGE_GENERATOR, "y");
        actionPanelsMap.put(MeterMngStatics.FILETYPE_BULK_BLOCKING_GENERATOR, "y");
        actionPanelsMap.put(MeterMngStatics.FILETYPE_BULK_STORE_MOVEMENT, "y");
        actionPanelsMap.put(MeterMngStatics.FILETYPE_BULK_FREE_ISSUE, "y");
    }

    @UiField FlowPanel actionParamsView;

    @UiField DisclosurePanel actionParamsDisclosurePanel;
    private Boolean disclosureOpen = null;

    @UiField VerticalPanel actionParamsVerticalPanel;
    @UiField HTMLPanel buttons;
    @UiField Button submitBtn;
    @UiField Button closeBtn;

    private ImportFileView parent;
    protected ImportFileActionParamsDto importFileActionParamsDto;
    private ActionParamsComponent typeClassPanel;

    private static Logger logger = Logger.getLogger(ActionParamsPanel.class.getName());

    private static ActionParamsPanelUiBinder uiBinder = GWT.create(ActionParamsPanelUiBinder.class);

    interface ActionParamsPanelUiBinder extends UiBinder<Widget, ActionParamsPanel> {
    }

    public ActionParamsPanel(ClientFactory clientFactory, ImportFileView parent, String typeClass, ImportFileActionParamsDto result, boolean canSubmit, boolean inputFileUploaded) {
        super();
        this.clientFactory = clientFactory;
        this.parent = parent;
        this.importFileActionParamsDto = result;

        if (typeClass.equals(MeterMngStatics.FILETYPE_BULK_KEY_CHANGE_GENERATOR)) {
            if (!inputFileUploaded) {
                //Bulk Keychange only implemented for has_input_file='y' sofar
                typeClassPanel = new ActionParamsComponent(MessagesUtil.getInstance().getMessage("bulk.keychange.without.input.file.not.implemented.yet"));
                return;
            }
            typeClassPanel = new BulkKeyChangeParamsPanel(clientFactory, result.getImportFile().getImportFilename(), (BulkKeyChangeParamRecord)importFileActionParamsDto.getBulkParamRecord(), inputFileUploaded);
        } else if (typeClass.equals(MeterMngStatics.FILETYPE_BULK_PRICING_STRUCTURE_CHANGE_GENERATOR)) {
            if (!inputFileUploaded) {
                typeClassPanel = new ActionParamsComponent(MessagesUtil.getInstance()
                        .getMessage("bulk.pricing.structure.change.without.input.file.not.implemented.yet"));
                return;
            }
            typeClassPanel = new BulkPricingStructureChangeParamsPanel(clientFactory,
                    result.getImportFile().getImportFilename(),
                    (BulkPricingStructureChangeParamRecord) importFileActionParamsDto.getBulkParamRecord());
        } else if (typeClass.equals(MeterMngStatics.FILETYPE_BULK_BLOCKING_GENERATOR)) {
            if (!inputFileUploaded) {
                typeClassPanel = new ActionParamsComponent(
                        MessagesUtil.getInstance().getMessage("bulk.blocking.without.input.file.not.implemented.yet"));
                return;
            }
            typeClassPanel = new BulkBlockingParamsPanel(clientFactory, result.getImportFile().getImportFilename(),
                    (BulkBlockingParamRecord) importFileActionParamsDto.getBulkParamRecord());
        } else if (typeClass.equals(MeterMngStatics.FILETYPE_BULK_STORE_MOVEMENT)) {
            if (!inputFileUploaded) {
                typeClassPanel = new ActionParamsComponent(
                        MessagesUtil.getInstance().getMessage("bulk.device.store.movement.without.input.file.not.implemented.yet"));
                return;
            }
            typeClassPanel = new BulkStoreMovementParamsPanel(clientFactory, result.getImportFile().getImportFilename(), (BulkStoreMovementParamRecord) importFileActionParamsDto.getBulkParamRecord());
        } else if (typeClass.equals(MeterMngStatics.FILETYPE_BULK_FREE_ISSUE)) {
            if (!inputFileUploaded) {
                typeClassPanel = new ActionParamsComponent(
                        MessagesUtil.getInstance().getMessage("bulk.free.issue.without.input.file.not.implemented.yet"));
                return;
            }
            typeClassPanel = new BulkFreeIssueParamsPanel(clientFactory, result.getImportFile().getImportFilename(), (BulkFreeIssueParamRecord) importFileActionParamsDto.getBulkParamRecord());
        } else {
            //other parameter types OR  } else {
           typeClassPanel = null;
        }

        initWidget(uiBinder.createAndBindUi(this));

        if (typeClassPanel != null) {
            actionParamsVerticalPanel.add(typeClassPanel);
        }

        if (!canSubmit) {
            submitBtn.setEnabled(false);
            submitBtn.setVisible(false);
        } else {
            submitBtn.setEnabled(true);
            submitBtn.setVisible(true);
        }
    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            actionParamsDisclosurePanel.setOpen(isOpen);
        } else {
            actionParamsDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    public void setSubmitBtn(boolean canSubmit) {
        if (!canSubmit) {
            submitBtn.removeFromParent();
        }
    }

    protected ActionParamsComponent getTypeClassPanel() {
        return typeClassPanel;
    }

    public boolean validateForm() {
        return typeClassPanel.validateForm();
    }

    public BulkParamRecord mapFormToData() {
        return typeClassPanel.mapFormToData();
    }

    @UiHandler("submitBtn")
    protected void handleSubmitButton(ClickEvent clickEvent) {
        if (validateForm()) {
            enableButtons(false);
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    importFileActionParamsDto.setBulkParamRecord(mapFormToData());
                    clientFactory.getImportFileDataRpc().updateImportFileParams(importFileActionParamsDto,  new ClientCallback<Void>() {
                        @Override
                        public void onSuccess(Void result) {
                            logger.info("Parameters saved for class");
                            enableButtons(true);
                            parent.reset();
                            Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("import.file.parameters.updated", new String[] {importFileActionParamsDto.getImportFile().getImportFilename()}),
                                    new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                                    StyleNames.POPUP_MESSAGE,
                                    MessagesUtil.getInstance().getMessage("button.close"), null,
                                    true, false);
                        }
                        @Override
                        public void onFailureClient() {
                            enableButtons(true);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void enableButtons(boolean enable) {
        submitBtn.setEnabled(enable);
        closeBtn.setEnabled(enable);
    }

    @UiHandler("closeBtn")
    protected void handleCloseButton(ClickEvent clickEvent) {
        if (checkDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        close();
                    }
                }
            });
        } else {
            close();
        }
    }

    public boolean checkDirtyData() {
        if (submitBtn.isVisible() && submitBtn.isEnabled()) {
            return typeClassPanel.checkDirtyData();
        } else {
            return false;
        }
    }

    private void close() {
        typeClassPanel.removeFromParent();
        parent.reset();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                actionParamsDisclosurePanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
