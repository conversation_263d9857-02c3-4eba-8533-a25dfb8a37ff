package za.co.ipay.metermng.client.view.component;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;

public class TranslateDbTerms {
    
        
    public TranslateDbTerms() {
        // TODO Auto-generated constructor stub
    }
    
    public static String translateDbAction(String incoming) {
        String action = ""; 
        if (incoming.toLowerCase().equals("update")) {
            action = MessagesUtil.getInstance().getMessage("db.action.update");
        }    
        if (incoming.toLowerCase().equals("insert")) {
            action = MessagesUtil.getInstance().getMessage("db.action.insert");
        }
        if (incoming.toLowerCase().equals("delete")) {
            action = MessagesUtil.getInstance().getMessage("db.action.delete");
        }   
        return action;
    }

}
