package za.co.ipay.metermng.client.view.workspace.appsetting;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.user.client.ui.PopupPanel;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.appsetting.DataTypeListDialogueBox;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.appsetting.DataTypeListDialogueBox;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;

public class AppSettingPanel extends SimpleFormPanel {

    @UiField TextBox nameTextBox;
    @UiField TextBox valueTextBox;
    @UiField IpayListBox statusListBox;
    @UiField IpayListBox dataTypeListBox;
    @UiField IpayListBox groupTypeListBox;    
    @UiField TextArea descriptionTextArea;
    
    @UiField FormElement nameElement;
    @UiField FormElement valueElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement powerLimitTableElement;
    @UiField FormElement dataTypeElement;
    @UiField FormElement groupTypeElement;
    @UiField FormElement dataTypeViewListBtnElement;
    @UiField Button dataTypeViewListBtn;
    
    private String listItemsString;
    private ClientFactory clientFactory;

    private static AppSettingPanelUiBinder uiBinder = GWT.create(AppSettingPanelUiBinder.class);
    interface AppSettingPanelUiBinder extends UiBinder<Widget, AppSettingPanel> {
    }

    public AppSettingPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));        
        addFieldHandlers();
        populateStatusListBox();
        populatedataTypeListBox();
        populateGroupTypeListBox();
    }
    
    public void setDataTypeList(String listItemsString) {
        this.listItemsString = listItemsString;
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        valueTextBox.setText("");
        descriptionTextArea.setEnabled(true);
        descriptionTextArea.setText("");
        setDataTypeList("");
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        valueElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        valueTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextArea.addChangeHandler(new FormDataChangeHandler(form));
        groupTypeListBox.addChangeHandler(new FormDataChangeHandler(form));
        statusListBox.addChangeHandler(new FormDataChangeHandler(form));
        dataTypeListBox.addChangeHandler(new FormDataChangeHandler(form) {
            @Override
            public void onChange(ChangeEvent event) {
                super.onChange(event);
                int selectedIndex = dataTypeListBox.getSelectedIndex();
                String selected = dataTypeListBox.getItemText(selectedIndex);
                if(selected.equals(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.list"))) {
                    createDataTypeListPopup(null);
                    dataTypeViewListBtnElement.setVisible(true);
                    descriptionTextArea.setEnabled(false);
                } else {
                    dataTypeViewListBtnElement.setVisible(false);
                    descriptionTextArea.setEnabled(true);
                    descriptionTextArea.setText(MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.default.datatype.description"));
                }
            }
        });
    }
    @UiHandler("dataTypeViewListBtn")
    public void createDataTypeListPopup(ClickEvent event){
        descriptionTextArea.setText(listItemsString);
        int left = AppSettingPanel.this.getAbsoluteLeft() + AppSettingPanel.this.getOffsetWidth();
        int top = AppSettingPanel.this.getAbsoluteTop();
        
        //calc pagesize for scrollpanel
        int pageHeight = Window.getClientHeight();
        int heightAvail = pageHeight - top - 120;
        
        DataTypeListDialogueBox listPopup = new DataTypeListDialogueBox(listItemsString, descriptionTextArea, left, top, heightAvail);

        listPopup.setPopupPosition(left, top);
        listPopup.addCloseHandler(new CloseHandler<PopupPanel>() {
            @Override
            public void onClose(CloseEvent<PopupPanel> event) {
                listItemsString = descriptionTextArea.getText();
            }
        });
        listPopup.show();
    }
    
    private void populateStatusListBox() {
        statusListBox.clear();
        statusListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable"), MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable"));
        statusListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.status.optional"), MessagesUtil.getInstance().getMessage("user.custom.field.status.optional"));
        statusListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.status.required"), MessagesUtil.getInstance().getMessage("user.custom.field.status.required"));
    }

    private void populatedataTypeListBox() {
        dataTypeListBox.clear();
        dataTypeListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.text"), MessagesUtil.getInstance().getMessage("user.custom.field.datatype.text"));
        dataTypeListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.numeric"), MessagesUtil.getInstance().getMessage("user.custom.field.datatype.numeric"));
        dataTypeListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.date"), MessagesUtil.getInstance().getMessage("user.custom.field.datatype.date"));
        dataTypeListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.boolean"), MessagesUtil.getInstance().getMessage("user.custom.field.datatype.boolean"));
        dataTypeListBox.addItem(MessagesUtil.getInstance().getMessage("user.custom.field.datatype.list"), MessagesUtil.getInstance().getMessage("user.custom.field.datatype.list"));
    }
    
    private void populateGroupTypeListBox() {
        groupTypeListBox.clear();

        clientFactory.getUsagePointGroupsRpc().getUsagePointGroupsList(new ClientCallback<ArrayList<ItemValuePairDto>>() {
            @Override
            public void onSuccess(ArrayList<ItemValuePairDto> result) {
            	for (ItemValuePairDto itemValuePairDto: result) {
            		groupTypeListBox.addItem(itemValuePairDto.getItem(),itemValuePairDto.getValue());
            	}
            }
        });
    }
}
