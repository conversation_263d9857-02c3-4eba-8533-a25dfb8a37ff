package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenDashboardEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class DashboardActivity extends AbstractActivity {

private ClientFactory clientFactory;
    
    public DashboardActivity(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new OpenDashboardEvent());
    }
}
