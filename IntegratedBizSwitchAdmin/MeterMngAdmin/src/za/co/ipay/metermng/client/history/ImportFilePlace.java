package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class ImportFilePlace extends Place {

    public static ImportFilePlace ALL_PLACE = new ImportFilePlace("all");

    private String name;
    
    public ImportFilePlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(ImportFilePlace p) {
        return "importfile:"+p.getName();
    }

    @Prefix(value = "importfile")
    public static class Tokenizer implements PlaceTokenizer<ImportFilePlace> {
        
        @Override
        public String getToken(ImportFilePlace place) {
            return "all";
        }

        @Override
        public ImportFilePlace getPlace(String token) {
            return new ImportFilePlace(token);
        }
    }
}
