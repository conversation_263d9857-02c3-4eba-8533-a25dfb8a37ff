package za.co.ipay.metermng.client.rpc;

import java.util.List;

import za.co.ipay.metermng.mybatis.generated.model.AuxType;

import com.google.gwt.user.client.rpc.AsyncCallback;

public interface AuxTypeRpcAsync {

    void addAuxType(AuxType addMe, AsyncCallback<AuxType> callback);

    void getAuxTypes(AsyncCallback<List<AuxType>> callback);

    void updateAuxType(AuxType updated, AsyncCallback<AuxType> callback);
    
}
