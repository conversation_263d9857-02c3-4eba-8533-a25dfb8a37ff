<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets">
  <ui:style>
    
  </ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel width="99%" height="100%">
        <g:VerticalPanel spacing="5"> 
	        <g:HTMLPanel ui:field="seasonsPanel">
	            <g:HTML text="{msg.getCalendarSeasonTitle}" styleName="dataTitle" />
	            <g:HTML text="{msg.getCalendarSeasonDescription}" styleName="dataDescription" />     
<!-- 	            <g:HTML text="{msg.getCalendarSeasonCurrentTitle}" styleName="dataTitle" /> -->
	            <g2:CellTable ui:field="seasonsTable" debugId="seasonsTable"/>
                <widget:TablePager ui:field="seasonsPager" styleName="pager" location="CENTER" />
	        </g:HTMLPanel>
	        <g:FlowPanel>
	            <g:Label ui:field="formHeading" styleName="sectionTitle"></g:Label>
	            <form:FormRowPanel>
	                <form:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getCalendarSeasonName}:" helpMsg="{msg.getCalendarSeasonNameHelp}" required="true">
	                    <g:TextBox text="" ui:field="nameTextBox" debugId="nameTextBox" title="{msg.getCalendarSeasonName}" />
	                </form:FormElement>
	            </form:FormRowPanel>
	            <g:FlowPanel>
	               <g:Button text="{msg.getSaveButton}" ui:field="saveBtn" debugId="saveBtn" />
	               <g:Button text="{msg.getCancelButton}" ui:field="cancelBtn" debugId="cancelBtn" />
                   <g:Button text="{msg.getDeleteButton}" ui:field="deleteBtn" debugId="deleteBtn" />
	            </g:FlowPanel>   
	        </g:FlowPanel>
	        
        </g:VerticalPanel>        
    </g:FlowPanel>
</ui:UiBinder> 