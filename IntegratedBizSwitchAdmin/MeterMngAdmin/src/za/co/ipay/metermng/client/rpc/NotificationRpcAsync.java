package za.co.ipay.metermng.client.rpc;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.email.shared.EmailBuilder;


/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 *         Created on 3/24/17.
 */
public interface NotificationRpcAsync {

    void sendSms(String messsage, String recipient, AsyncCallback<Void> callback);

    void sendEmail(EmailBuilder emailBuilder, AsyncCallback<Void> async);
}
