package za.co.ipay.metermng.client.view.workspace.meter.model;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterModelPlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.ModelChannelConfigDto;

public class MeterModelWorkspaceView extends BaseWorkspace {
    
    @UiField DeckLayoutPanel mainLayoutPanel;        
    @UiField SimpleTableView<MeterModelDto> view;
    private MeterModelView meterModelView;
    
    @UiField SimpleTableView<ModelChannelConfigDto> view2;
    private ModelChannelConfigView modelChannelConfigView;
    
    private static Logger logger = Logger.getLogger(MeterModelWorkspaceView.class.getName());

    private static MeterModelWorkspaceViewUiBinder uiBinder = GWT.create(MeterModelWorkspaceViewUiBinder.class);

    interface MeterModelWorkspaceViewUiBinder extends UiBinder<Widget, MeterModelWorkspaceView> {
    }

    public MeterModelWorkspaceView(ClientFactory clientFactory, MeterModelPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(MeterModelPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("meter.models"));
        initUi();
    }

    private void initUi() {        
        meterModelView = new MeterModelView(clientFactory, this, view);
        modelChannelConfigView = new ModelChannelConfigView(clientFactory, this, view2);
        mainLayoutPanel.showWidget(view);
    }
    
    @Override
    public void onArrival(Place place) {
        meterModelView.onArrival(place);
    }

    public void showModelChannelConfigs(MeterModelDto meterModel) {
        modelChannelConfigView.setMeterModel(meterModel);;
        mainLayoutPanel.showWidget(view2);
        mainLayoutPanel.animate(getAnimationTime());
    }

    public void showMeterModel() {
        mainLayoutPanel.showWidget(view);
        mainLayoutPanel.animate(getAnimationTime());
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof MeterModelPlace);
    }
    
    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("Received notification: "+notification);
        if (NotificationType.DATA_UPDATED.equals(notification.getNotificationType())) {
           if (MeterMngStatics.MANUFACTURER_DATA.equals(notification.getDataType()) || 
                        MeterMngStatics.MDC_DATA.equals(notification.getDataType())) {
               meterModelView.loadInitData();
           } else if (MeterMngStatics.MDC_CHANNEL_MODIFIED.equals(notification.getDataType())) {
               modelChannelConfigView.repopulateMdcChannels();
           }
        }
    }

}