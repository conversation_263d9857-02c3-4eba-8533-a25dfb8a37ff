<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets">
  <ui:style>    
  </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    <g:FlowPanel>
                    <p3:FormRowPanel>
                        <p3:FormElement ui:field="meterDisconnectElement" labelText="{msg.getThresholdDisconnectText}:" helpMsg="{msg.getThresholdDisconnectHelp}" required="true" debugId="meterDisconnectElement">
                            <p2:CurrencyTextBox ui:field="meterDisconnectBox" styleName="gwt-TextBox largeNumericInput" debugId="meterDisconnectBox"/>
                        </p3:FormElement>
                        <p3:FormElement ui:field="emergencyCreditElement" labelText="{msg.getThresholdEmergencyCreditText}:" helpMsg="{msg.getThresholdEmergencyCreditHelp}" required="true" debugId="emergencyCreditElement">
                            <p2:CurrencyTextBox ui:field="emergencyCreditBox" styleName="gwt-TextBox largeNumericInput" debugId="emergencyCreditBox"/>
                        </p3:FormElement>
                    </p3:FormRowPanel>

                    <p3:FormRowPanel>
                        <p3:FormElement ui:field="meterReconnectElement" labelText="{msg.getThresholdReconnectText}:" helpMsg="{msg.getThresholdReconnectHelp}" required="true" debugId="meterReconnectElement">
                            <p2:CurrencyTextBox ui:field="meterReconnectBox" styleName="gwt-TextBox largeNumericInput" debugId="meterReconnectBox"/>
                        </p3:FormElement>
                        <p3:FormElement ui:field="lowBalanceElement" labelText="{msg.getThresholdLowBalanceText}:" helpMsg="{msg.getThresholdLowBalanceHelp}" required="true" debugId="lowBalanceElement">
                            <p2:CurrencyTextBox ui:field="lowBalanceBox" styleName="gwt-TextBox largeNumericInput" debugId="lowBalanceBox"/>
                        </p3:FormElement>
                    </p3:FormRowPanel>
    </g:FlowPanel>
</ui:UiBinder> 