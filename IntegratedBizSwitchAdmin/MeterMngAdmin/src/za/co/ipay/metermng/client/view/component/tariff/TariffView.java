package za.co.ipay.metermng.client.view.component.tariff;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.DateCell;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.event.TariffUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.pricing.PricingStructureWorkspaceView;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.tariff.TariffWithData;

public class TariffView extends BaseComponent implements FormManager<TariffWithData> {

    private PricingStructureWorkspaceView parent;
    private ListDataProvider<TariffWithData> dataProvider;
    private SimpleTableView<TariffWithData> view;
    private TariffPanel panel;

    private PricingStructure pricingStructure;
    private List<TariffClass> tariffClassList;

    private static Logger logger = Logger.getLogger(TariffView.class.getName());

    public TariffView(ClientFactory clientFactory, PricingStructureWorkspaceView parent, SimpleTableView<TariffWithData> view) {
        this.clientFactory = clientFactory;
        this.parent = parent;
        this.view = view;
        initView();
        initForm();
        createTable();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);

        Anchor anchor = new Anchor(MessagesUtil.getInstance().getMessage("pricingstructures.header"));
        anchor.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            goBack();
                        }
                    }});
            }
        });
        view.getPageHeader().addPageHeaderLink(anchor);
    }

    private void initForm() {
        panel = new TariffPanel(view.getForm(), clientFactory);
        view.getForm().setHasDirtyDataManager(parent);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("tariff.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onSave();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("tariffSaveBtn");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            clear();
                        }
                    }});
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("tariffCancelBtn");

        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.back"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                            }
                            goBack();
                        }
                    }
                });
            }
        });
        view.getForm().getBackBtn().ensureDebugId("backBtn");
    }

    private void clear() {
        panel.clearFields();
        panel.clearErrors();
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("tariff.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().setDirtyData(false);
        view.clearTableSelection();
        TariffWithData tariffWithData = new TariffWithData();
        tariffWithData.setTariff(new Tariff());
        panel.setTariffWithData(tariffWithData);
    }

    private void createTable() {
        TextColumn<TariffWithData> nameCol = new TextColumn<TariffWithData>() {
            @Override
            public String getValue(TariffWithData tariffWithData) {
                return tariffWithData.getTariff().getName();
            }
        };

        DateCell startDateCell = new DateCell(DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss"));
        Column<TariffWithData, Date> startDateCol = new Column<TariffWithData, Date>(startDateCell) {

            @Override
            public Date getValue(TariffWithData tariffWithData) {
                Format format = FormatUtil.getInstance();
                return format.parseDateTime(format.formatDateTime(tariffWithData.getTariff().getStartDate()));
            }
        };

        TextColumn<TariffWithData> typeCol = new TextColumn<TariffWithData>() {
            @Override
            public String getValue(TariffWithData tariffWithData) {
                if (tariffClassList != null) {
                    return getTariffClassName(tariffWithData.getTariff().getTariffClassId());
                }
                return "";
            }
        };

        view.getTable().addColumn(nameCol, MessagesUtil.getInstance().getMessage("pricingstructure.field.name"));
        view.getTable().addColumn(startDateCol, MessagesUtil.getInstance().getMessage("pricingstructure.field.startdate"));
        view.getTable().addColumn(typeCol, MessagesUtil.getInstance().getMessage("pricingstructure.field.type"));

        view.getTable().ensureDebugId("tariffTable");

        dataProvider = new ListDataProvider<TariffWithData>();
        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
    }

    private String getTariffClassName(long id) {
        if (tariffClassList == null)
            return "";
        for (TariffClass tariffClass : tariffClassList) {
            if (tariffClass.getId().longValue() == id) {
                return tariffClass.getCalcClassName();
            }
        }
        return "";
    }

    private void loadTariffClasses(final PricingStructure pricingStructure) {
        logger.info("Loading tariff classes: "+ pricingStructure.getId()+" "+pricingStructure.getServiceResourceId()+" "+pricingStructure.getMeterTypeId()+" "+pricingStructure.getPaymentModeId());
        clientFactory.getPricingStructureRpc().getTariffClasses(pricingStructure.getServiceResourceId(), pricingStructure.getMeterTypeId(), pricingStructure.getPaymentModeId(),
                new ClientCallback<ArrayList<TariffClass>>() {
                    @Override
                    public void onSuccess(ArrayList<TariffClass> result) {
                        tariffClassList = result;
                        panel.initData(pricingStructure, result);
                        if (result.size() == 0) {
                            Dialogs.displayErrorMessage(
                                    MessagesUtil.getInstance().getMessage("pricingstructure.tariffs.prt.none"),
                                    MediaResourceUtil.getInstance().getErrorIcon());
                        }
                    }
                });
    }

    public void goBack() {
        parent.goToPricingStructures(pricingStructure.getId(), dataProvider.getList().size());
    }

    private void onSave() {
        final TariffWithData model = panel.getTariffWithData(dataProvider.getList());
        if (model == null) {
            return;
        } else {
            model.getTariff().setRecordStatus(RecordStatus.ACT);
        }
        model.getTariff().setPricingStructureId(pricingStructure.getId());

        String calcClass = tariffClassList.get(panel.typeListBox.getSelectedIndex()).getCalcClass();
        if (pricingStructure.getRecordStatus().equals(RecordStatus.ACT) && calcClass != null && calcClass.contains("RegisterReadingThinTariff")) {
            clientFactory.getPricingStructureRpc().checkRegReadTariffActiveUpBillingDetAlignment(model, clientFactory.getUser().getUserName(), new ClientCallback<ChannelCompatibilityE>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(ChannelCompatibilityE match) {
                    final Messages messages = MessagesUtil.getInstance();
                    if (ChannelCompatibilityE.NO_DATA.equals(match)) {
                        onSaveContinue(model);
                    } else if (ChannelCompatibilityE.NONE_MATCH.equals(match)) {
                        logger.info("TariffView: checkRegReadTariffActiveUpBillingDetAlignment = NONE_MATCH. ERROR displayed to user: " + clientFactory.getUser().getUserName());
                        Dialogs.centreErrorMessage(messages.getMessage("error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        return;
                    } else if (ChannelCompatibilityE.PARTIAL_MATCH.equals(match)) {
                        logger.info("TariffView: checkRegReadTariffActiveUpBillingDetAlignment = PARTIAL_MATCH. Warning displayed to user: " + clientFactory.getUser().getUserName());
                        Dialogs.confirm(messages.getMessage("warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps"),
                                messages.getMessage("button.yes"),
                                messages.getMessage("button.no"),
                                MediaResourceUtil.getInstance().getQuestionIcon(),
                                new ConfirmHandler() {
                                    @Override
                                    public void confirmed(boolean confirm) {
                                        if(confirm) {
                                            onSaveContinue(model);
                                        } else {
                                            clear();
                                            return;
                                        }
                                    }
                                },
                                null, null);  //if make position null, will centre confirm
                    } else {
                        onSaveContinue(model);
                    }
                }
            });
        } else {
            onSaveContinue(model);
        }

    }

    @SuppressWarnings("deprecation")
    private void onSaveContinue(final TariffWithData model) {
        //if not STS tariff and startdate of Tariff is not the 1st of a month at midnight, issue warning
        if (pricingStructure.getPaymentModeId() != PaymentModeE.UNITS.getId()) {
            //for java.util.date can't extract milliseconds, so to cater for that, construct a new date with default 0 milliseconds and compare that to the startDate
            Date startDate = model.getTariff().getStartDate();
            Date firstOfMonth = new Date();
            firstOfMonth.setYear(startDate.getYear());
            firstOfMonth.setMonth(startDate.getMonth());
            firstOfMonth.setDate(01);               //set day = 1st
            firstOfMonth.setHours(00);
            firstOfMonth.setMinutes(00);
            firstOfMonth.setSeconds(00);
            long time = firstOfMonth.getTime();    // getting timestamp
            time -= time % 1000L;                  // getting rid of milliseconds by substracting remainder
            firstOfMonth.setTime(time);            // setting the timestamp without milliseconds

            if (startDate.compareTo(firstOfMonth) != 0) {
                logger.info("startDate=" + startDate + " firstOfMonth=" + firstOfMonth);
                Messages messages = MessagesUtil.getInstance();
                Dialogs.confirm(messages.getMessage("warning.tariff.start.date.not.on.month.boundary"),
                        messages.getMessage("button.yes"),
                        messages.getMessage("button.no"),
                        MediaResourceUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if(confirm) {
                            onSaveContinueAfterDateWarning(model);
                        } else {
                            return;
                        }
                    }
                },
                        null, null);  //if make position null, will centre confirm
            } else {
                onSaveContinueAfterDateWarning(model);
            }
        } else {
            onSaveContinueAfterDateWarning(model);
        }
    }

    private void onSaveContinueAfterDateWarning(final TariffWithData model) {
        logger.info("Saving tariff: "+model);
        clientFactory.getPricingStructureRpc().saveTariff(model, new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(Void result) {
                clientFactory.getEventBus().fireEvent(new TariffUpdatedEvent());
                Messages messages = MessagesUtil.getInstance();
                Dialogs.displayInformationMessage(
                        messages.getSavedMessage(new String[] { messages.getMessage("tariff.title") }),
                        MediaResourceUtil.getInstance().getInformationIcon(), messages.getMessage("button.close"),
                        null);
                updateTariffData();
                clear();
            }
        });
    }

    public void initTariffData(PricingStructure pricingStructure) {
        this.pricingStructure = pricingStructure;
        if (pricingStructure != null) {
            view.setDataDetails(pricingStructure.getName(), pricingStructure.getDescription());
            loadTariffClasses(pricingStructure);
            updateTariffData();
            hideCreateButton();
        } else {
            dataProvider.getList().clear();
            dataProvider.refresh();
        }
    }

    public void clearForm() {
        clear();
    }

    private void updateTariffData() {
        clientFactory.getPricingStructureRpc().getTariffsForPricingStructure(pricingStructure,
                new ClientCallback<List<TariffWithData>>() {
                    @Override
                    public void onSuccess(List<TariffWithData> result) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    dataProvider.refresh();
                    view.getTable().setPageStart(0);
                }
        });
    }

    @Override
    public void displaySelected(TariffWithData selected) {
        logger.info("Displayed selected: "+selected);
        panel.clearFields();
        panel.clearErrors();
        if (selected == null) {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("tariff.title.add"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("tariff.title.edit"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        }
        panel.setTariffWithData(selected);
    }

    private void hideCreateButton() {
        view.getForm().getSaveBtn().setVisible(true);
        if (!clientFactory.hasAccessGroupsEditPermissions(pricingStructure.getAccessGroupId())) {
            view.getForm().getSaveBtn().setVisible(false);
        }
    }

    public TariffPanel getPanel() {
        return panel;
    }

    public void handleBillingDetNotification() {
        if (panel.getTariffUIClass() != null
                && panel.getTariffUIClass().getClass().getSimpleName().contains("RegisterReadingBlockTariffView")) {
            panel.clearFields();
            view.clearTableSelection();
        }
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_PRICING_STRUCT)) {
            view.getForm().getOtherBtn().removeFromParent();
            view.getForm().getSaveBtn().removeFromParent();
            //do not remove the back button
        }
    }
}
