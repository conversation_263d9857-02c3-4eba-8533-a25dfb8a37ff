package za.co.ipay.metermng.client.rpc;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.metermng.mybatis.generated.model.BlockingType;

public interface BlockingTypeRpcAsync {

	void getAllBlockingTypes(AsyncCallback<List<BlockingType>> callback);

	void getBlockingType(Long blockingTypeId, AsyncCallback<BlockingType> callback);

	void addBlockingType(BlockingType blockingType, AsyncCallback<BlockingType> callback);

	void updateBlockingType(BlockingType blockingType, AsyncCallback<BlockingType> callback);
}
