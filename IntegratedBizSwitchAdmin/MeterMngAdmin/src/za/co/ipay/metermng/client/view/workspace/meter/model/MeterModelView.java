package za.co.ipay.metermng.client.view.workspace.meter.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.ModelSupportsPaymentModeData;
import za.co.ipay.metermng.mybatis.generated.model.MeterDataDecoder;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterPhase;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.ModelChannelConfigDto;

public class MeterModelView implements FormManager<MeterModelDto> {

    private final MeterModelWorkspaceView parentWorkspace;
    private ClientFactory clientFactory;

    @UiField SimpleTableView<MeterModelDto> view;
    private MeterModelPanel panel;
    private Button viewBtn;

    private AsyncDataProvider<MeterModelDto> dataProvider;
    private MeterModelDto meterModelDto;

    private static final Logger logger = Logger.getLogger(MeterModelView.class.getName());

    public MeterModelView(ClientFactory clientFactory, MeterModelWorkspaceView parentWorkspace, SimpleTableView<MeterModelDto> view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
        this.clientFactory = clientFactory;
        initView();
        initForm();
        createTable();
        loadInitData();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new MeterModelPanel(view.getForm());
        panel.checkEnableSTS(clientFactory.isEnableSTS());
        panel.clearFields();
        panel.mridComponent.initMrid(clientFactory);

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        viewBtn = new Button(MessagesUtil.getInstance().getMessage("channel.config.overrides.button"));
        viewBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {

                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                                displayMeterModel(meterModelDto);
                            }
                            onViewClick();
                        }
                    }
                });
            }
        });
        panel.mdcBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                updateMdcDependentFields();
            }
        });
        panel.paymentModesBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                validateMdcForThinUnits();
            }
        });
        viewBtn.setVisible(false);
        viewBtn.ensureDebugId("viewBtn");
        view.getForm().getSecondaryButtons().add(viewBtn);
        view.getForm().getSecondaryButtons().setVisible(true);
        view.getForm().getSecondaryButtons().ensureDebugId("secondaryButtonsPanel");

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.models.title.add"));
    }

    private void updateMdcDependentFields() {
        boolean mdcSelected = getMdc() != null;
        if (mdcSelected) {
            panel.mdcElement.clearErrorMsg();
            panel.mdcGroupPanel.setVisible(true);
            showViewChannelButton(true);
        } else {
            panel.clearMdcFeatures();
            showViewChannelButton(false);
        }
    }

    private void validateMdcForThinUnits() {
        boolean hasThinUnit = false;
        for (int i = 0; i < panel.paymentModesBox.getItemCount(); i++) {
            if (panel.paymentModesBox.isItemSelected(i)) {
                long id = Long.valueOf(panel.paymentModesBox.getValue(i)).longValue();
                if (PaymentModeE.THIN_UNITS.getId() == id || PaymentModeE.THIN.getId() == id) {
                    hasThinUnit = true;
                    break;
                }
            }
        }
        panel.mdcElement.setRequired(hasThinUnit);
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<MeterModelDto, ?> nameColumn = createNameColumn();
            Column<MeterModelDto, ?> manuColumn = createManufacturerColumn();
            StatusTableColumn<MeterModelDto> statusColumn = new StatusTableColumn<MeterModelDto>();
            statusColumn.setSortable(true);
            Column<MeterModelDto, ?> resourceColumn = createServiceResourceColumn();
            Column<MeterModelDto, ?> typeColumn = createMeterTypeColumn();
            Column<MeterModelDto, ?> mdcColumn = createMdcColumn();

            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("meter.models.field.name"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("meter.models.field.status"));
            view.getTable().addColumn(manuColumn, MessagesUtil.getInstance().getMessage("meter.models.field.manufacturer"));
            view.getTable().addColumn(resourceColumn, MessagesUtil.getInstance().getMessage("meter.models.field.resource"));
            view.getTable().addColumn(typeColumn, MessagesUtil.getInstance().getMessage("meter.models.field.metertype"));
            view.getTable().addColumn(mdcColumn, MessagesUtil.getInstance().getMessage("meter.models.field.mdc"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<MeterModelDto>() {
                @Override
                protected void onRangeChanged(HasData<MeterModelDto> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<MeterModelDto, ?> sColumn = (Column<MeterModelDto, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                    }
                    clientFactory.getMeterModelRpc().getMeterModels(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<MeterModelDto>>() {
                                @Override
                                public void onSuccess(ArrayList<MeterModelDto> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<MeterModelDto, ?>) event.getColumn());
                    String sortColumn = getColumnName(sortIndex);
                    boolean isAscending = event.isSortAscending();
                    clientFactory.getMeterModelRpc().getMeterModels(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<MeterModelDto>>() {
                                public void onSuccess(ArrayList<MeterModelDto> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(typeColumn);
            view.getTable().getColumnSortList().push(resourceColumn);
            view.getTable().getColumnSortList().push(statusColumn);
            view.getTable().getColumnSortList().push(manuColumn);
            view.getTable().getColumnSortList().push(mdcColumn);
            view.getTable().getColumnSortList().push(nameColumn);
            view.getTable().ensureDebugId("meterModelTable");
        }
    }

    private String getColumnName(int index) {
        if (index == 0) {
            return "name";
        } else if (index == 1) {
            return "status";
        } else if (index == 2) {
            return "manufacturer";
        } else if (index == 3) {
            return "serviceResource";
        } else if (index == 4) {
            return "meterType";
        } else {
            return "mdc";
        }
    }

    private Column<MeterModelDto, ?> createNameColumn() {
        TextColumn<MeterModelDto> nameColumn = new TextColumn<MeterModelDto>() {
            @Override
            public String getValue(MeterModelDto object) {
                return object.getMeterModel().getName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<MeterModelDto, ?> createManufacturerColumn() {
        TextColumn<MeterModelDto> column = new TextColumn<MeterModelDto>() {
            @Override
            public String getValue(MeterModelDto m) {
                return (m.getManufacturer() != null ? m.getManufacturer().getName() : "");
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<MeterModelDto, ?> createServiceResourceColumn() {
        TextColumn<MeterModelDto> column = new TextColumn<MeterModelDto>() {
            @Override
            public String getValue(MeterModelDto m) {
                return (m.getServiceResource() != null ? m.getServiceResource().getName() : "");
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<MeterModelDto, ?> createMeterTypeColumn() {
        TextColumn<MeterModelDto> column = new TextColumn<MeterModelDto>() {
            @Override
            public String getValue(MeterModelDto m) {
                return (m.getMeterType() != null ? m.getMeterType().getName() : "");
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<MeterModelDto, ?> createMdcColumn() {
        TextColumn<MeterModelDto> column = new TextColumn<MeterModelDto>() {
            @Override
            public String getValue(MeterModelDto m) {
                return (m.getMdc() != null ? m.getMdc().getName() : "");
            }
        };
        column.setSortable(true);
        return column;
    }

    private int getPageSize() {
        return parentWorkspace.getPageSize();
    }

    protected void loadInitData() {
        clientFactory.getMeterModelRpc().getMeterModelsScreenData(new ClientCallback<MeterModelScreenDataDto>() {
            @Override
            public void onSuccess(MeterModelScreenDataDto screenData) {
                setInitData(screenData);
            }
        });
    }

    private void setInitData(MeterModelScreenDataDto screenData) {
        panel.manufacturerBox.clear();
        panel.manufacturerBox.addItem("", "");

        panel.serviceResourceBox.clear();
        panel.serviceResourceBox.addItem("", "");

        panel.meterTypeBox.clear();
        panel.meterTypeBox.addItem("", "");

        panel.meterPhaseBox.clear();
        panel.meterPhaseBox.addItem("", "");

        panel.mdcBox.clear();
        panel.mdcBox.addItem("", "");

        panel.paymentModesBox.clear();

        panel.dataDecoderBox.clear();
        panel.dataDecoderBox.addItem("", "");

        ArrayList<IdNameDto> manus = screenData.getManufacturers();
        if (manus != null) {
            for (IdNameDto dto : manus) {
                panel.manufacturerBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> resources = screenData.getServiceResources();
        if (resources != null) {
            for (IdNameDto dto : resources) {
                panel.serviceResourceBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> types = screenData.getMeterTypes();
        if (types != null) {
            for (IdNameDto dto : types) {
                panel.meterTypeBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> phases = screenData.getMeterPhases();
        if (types != null) {
            for (IdNameDto dto : phases) {
                panel.meterPhaseBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> mdcs = screenData.getMdcs();
        if (mdcs != null) {
            for (IdNameDto dto : mdcs) {
                panel.mdcBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> modes = screenData.getPaymentModes();
        if (modes != null) {
            for (IdNameDto dto : modes) {
                panel.paymentModesBox.addItem(dto.getName(), dto.getId().toString());
            }
        }

        ArrayList<IdNameDto> dataDecoders = screenData.getDecoders();
        if (dataDecoders != null) {
            for (IdNameDto dto : dataDecoders) {
                panel.dataDecoderBox.addItem(dto.getName(), dto.getId().toString());
            }
        }
    }

    public void onArrival(Place place) {
        getModelsCount(false);
    }

    private void getModelsCount(final boolean refreshTable) {
        clientFactory.getMeterModelRpc().getMeterModelsCount(new ClientCallback<Integer>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }

            @Override
            public void onSuccess(Integer result) {
                view.getTable().setRowCount(result, true);
                dataProvider.updateRowCount(result, true);
                // Force a table update - reloads data from the back end
                if (refreshTable) {
                    refreshTable(false);
                }
            }
        });
    }

    public void onViewClick() {
        if (meterModelDto != null && meterModelDto.getMeterModel().getId() != null) {
            parentWorkspace.showModelChannelConfigs(meterModelDto);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("mdc.error.noneselected"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    @Override
    public void displaySelected(MeterModelDto selected) {
        displayMeterModel(selected);
    }

    private void displayMeterModel(MeterModelDto selected) {
        clear();
        this.meterModelDto = selected;
        if (meterModelDto == null) {
            meterModelDto = new MeterModelDto();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.models.title.add"));
            view.clearTableSelection();
            panel.mridComponent.initMrid(clientFactory);
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.models.title.update"));
            panel.mridComponent.setMrid(meterModelDto.getMeterModel().getMrid());
            panel.mridComponent.setIsExternal(meterModelDto.getMeterModel().isMridExternal());
        }

        setManufacturer();
        panel.nameTextBox.setText(meterModelDto.getMeterModel().getName());
        panel.descriptionTextBox.setText(meterModelDto.getMeterModel().getDescription());
        panel.activeBox.setValue(RecordStatus.ACT.equals(meterModelDto.getRecordStatus()));
        panel.toaBox.setValue(meterModelDto.getMeterModel().isTokenOverAir());
        panel.balSyncBox.setValue(meterModelDto.getMeterModel().isBalanceSync());
        panel.needsBreakerIdBox.setValue(meterModelDto.getMeterModel().isNeedsBreakerId());
        panel.messageDisplayBox.setValue(meterModelDto.getMeterModel().isDisplayMessage());
        panel.needsEncryptionKeyBox.setValue(meterModelDto.getMeterModel().isNeedsEncKey());
        panel.batteryCapacityTextBox.setValue(meterModelDto.getMeterModel().getBatteryCapacity());
        panel.lowThresholdPercentTextBox.setAmount(meterModelDto.getMeterModel().getBatteryLowThreshold());
        panel.uriPresentBox.setValue(meterModelDto.getMeterModel().isUriPresent());

        setServiceResource();
        setMeterType();
        setMeterPhase();
        setMdc();
        updateMdcDependentFields();
        setPaymentModes();
        validateMdcForThinUnits();
        setMeterDataDecoders();
        panel.checkServiceResource();
        panel.hasAttachedMeters(selected.isMeterAttachedToMeterModel());
    }

    protected void showViewChannelButton(boolean visible) {
        viewBtn.setVisible(visible);
        enableViewChannelButton(visible);
    }

    protected void enableViewChannelButton(boolean enable) {
        viewBtn.setEnabled(enable);
    }

    private void setManufacturer() {
        if (meterModelDto.getManufacturer() != null
            && meterModelDto.getManufacturer().getId() != null
            && panel.manufacturerBox.getItemCount() > 0) {
            for (int i = 0; i < panel.manufacturerBox.getItemCount(); i++) {
                if (panel.manufacturerBox.getValue(i).equals(meterModelDto.getManufacturer().getId().toString())) {
                    panel.manufacturerBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }

    private void setServiceResource() {
        if (meterModelDto.getServiceResource() != null
            && meterModelDto.getServiceResource().getId() != null
            && panel.serviceResourceBox.getItemCount() > 0) {
            for (int i = 0; i < panel.serviceResourceBox.getItemCount(); i++) {
                if (panel.serviceResourceBox.getValue(i).equals(meterModelDto.getServiceResource().getId().toString())) {
                    panel.serviceResourceBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }

    private void setMeterType() {
        if (meterModelDto.getMeterType() != null
            && meterModelDto.getMeterType().getId() != null
            && panel.meterTypeBox.getItemCount() > 0) {
            for (int i = 0; i < panel.meterTypeBox.getItemCount(); i++) {
                if (panel.meterTypeBox.getValue(i).equals(meterModelDto.getMeterType().getId().toString())) {
                    panel.meterTypeBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }

    private void setMdc() {
        if (meterModelDto.getMdc() != null
            && meterModelDto.getMdc().getId() != null
            && panel.mdcBox.getItemCount() > 0) {
            for (int i = 0; i < panel.mdcBox.getItemCount(); i++) {
                if (panel.mdcBox.getValue(i).equals(meterModelDto.getMdc().getId().toString())) {
                    panel.mdcBox.setSelectedIndex(i);
                    panel.mdcGroupPanel.setVisible(true);
                    showViewChannelButton(true);
                    break;
                }
            }
        }
    }

    private void setMeterDataDecoders() {
        MeterDataDecoder meterDataDecoder = meterModelDto.getMeterDataDecoder();
        if (meterDataDecoder != null) {
            Long id = meterDataDecoder.getId();
            if (id != null) {
                int itemCount = panel.dataDecoderBox.getItemCount();
                if (itemCount > 0) {
                    for (int i = 0; i < itemCount; i++) {
                        if (panel.dataDecoderBox.getValue(i).equals(id.toString())) {
                            panel.dataDecoderBox.setSelectedIndex(i);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void setMeterPhase() {
        MeterPhase meterPhase = meterModelDto.getMeterPhase();
        if (meterPhase != null) {
            Long id = meterPhase.getId();
            if (id != null) {
                int itemCount = panel.meterPhaseBox.getItemCount();
                if (itemCount > 0) {
                    for (int i = 0; i < itemCount; i++) {
                        if (panel.meterPhaseBox.getValue(i).equals(id.toString())) {
                            panel.meterPhaseBox.setSelectedIndex(i);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void setPaymentModes() {
        if (meterModelDto.getSupportedPaymentModes() != null
            && meterModelDto.getSupportedPaymentModes().size() > 0
            && panel.paymentModesBox.getItemCount() > 0) {

            for (int i = 0; i < panel.paymentModesBox.getItemCount(); i++) {
                panel.paymentModesBox.setItemSelected(i, isPaymentMode(panel.paymentModesBox.getValue(i)));
            }
        }
    }

    private boolean isPaymentMode(String id) {
        for (int i = 0; i < meterModelDto.getSupportedPaymentModes().size(); i++) {
            if (meterModelDto.getSupportedPaymentModes().get(i).getPaymentMode().getId().toString().equals(id)) {
                return true;
            }
        }
        return false;
    }

    private void clear() {
        meterModelDto = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.models.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
        showViewChannelButton(false);
        updateMdcDependentFields();
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        MeterModelDto dto = updateMeterModel();

        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "manufacturerId", panel.manufacturerElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "description", panel.descriptionElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "recordStatus", panel.activeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "serviceResourceId", panel.serviceResourceElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "meterTypeId", panel.meterTypeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "mdcId", panel.mdcElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "needsBreakerId", panel.needsBreakerIdElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "needsEncKey", panel.needsEncryptionKeyElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto.getMeterModel(), "mrid", panel.mridComponent.getTxtbxMridElement())) {
            valid = false;
        }
        if (!panel.mridComponent.validate()) {
            valid = false;
        }

        ArrayList<IdNameDto> paymentModes = getPaymentModes();
        boolean hasThinPaymentMode = false;
        for (IdNameDto mode : paymentModes) {
            if (mode.getName().toLowerCase().contains("thin")) {
                hasThinPaymentMode = true;
                break;
            }
        }
        if (hasThinPaymentMode && getMdc() == null) {
            valid = false;
            panel.mdcElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.models.mdc.required.for.thin.payment"));
        }
        if (paymentModes.size() == 0) {
            valid = false;
            panel.paymentModesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.models.paymentmodes.required"));
        } else if (meterModelDto != null && meterModelDto.isMeterAttachedToMeterModel()) {
            for (ModelSupportsPaymentModeData payment : meterModelDto.getSupportedPaymentModes()) {
                boolean isSelected = false;
                for (IdNameDto mode : paymentModes) {
                    if (mode.getId().equals(payment.getPaymentMode().getId())) {
                        isSelected = true;
                        break;
                    }
                }
                if (!isSelected) {
                    valid = false;
                    panel.paymentModesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.models.paymentmodes.preselected"));
                    break;
                }
            }
        }

        // Battery Event
        BigDecimal lowThresholdPercent = panel.lowThresholdPercentTextBox.getAmount();
        BigDecimal capacity = panel.batteryCapacityTextBox.getValue();
        if ((lowThresholdPercent != null && lowThresholdPercent.compareTo(BigDecimal.ZERO) < 0)
            || (lowThresholdPercent == null && capacity != null)) {
            valid = false;
            panel.lowThresholdPercentElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.models.battery.threshold.error"));
        }
        if ((capacity != null && capacity.compareTo(BigDecimal.ZERO) < 0)
            || (lowThresholdPercent != null && capacity == null)) {
            valid = false;
            panel.batteryCapacityElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.models.battery.capacity.error"));
        }
        return valid;
    }

    private MeterModelDto updateMeterModel() {
        MeterModelDto m = new MeterModelDto();
        updateMeterModel(m);
        return m;
    }

    private void updateMeterModel(MeterModelDto dto) {
        if (meterModelDto != null && meterModelDto.getMeterModel() != null) {
            dto.getMeterModel().setId(meterModelDto.getMeterModel().getId());
        }
        dto.getMeterModel().setManufacturerId(getManufacturer());
        dto.getMeterModel().setName(panel.nameTextBox.getText());
        dto.getMeterModel().setDescription(panel.descriptionTextBox.getText());
        dto.getMeterModel().setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        dto.getMeterModel().setServiceResourceId(getServiceResource());
        dto.getMeterModel().setMeterTypeId(getMeterType());
        if (panel.meterPhaseElement.isVisible()) {
            dto.getMeterModel().setMeterPhaseId(getMeterPhase());
        }
        dto.getMeterModel().setMdcId(getMdc());
        dto.getMeterModel().setTokenOverAir(panel.toaBox.getValue());
        dto.getMeterModel().setBalanceSync(panel.balSyncBox.getValue());
        dto.getMeterModel().setNeedsBreakerId(panel.needsBreakerIdBox.getValue());
        dto.getMeterModel().setDisplayMessage(panel.messageDisplayBox.getValue());
        dto.getMeterModel().setNeedsEncKey(panel.needsEncryptionKeyBox.getValue());
        dto.getMeterModel().setMeterDataDecoderId(getDataDecoder());
        dto.getMeterModel().setBatteryCapacity(panel.batteryCapacityTextBox.getValue());
        dto.getMeterModel().setBatteryLowThreshold(panel.lowThresholdPercentTextBox.getAmount());
        dto.getMeterModel().setMrid(panel.mridComponent.getMrid());
        dto.getMeterModel().setMridExternal(panel.mridComponent.isExternal());
        dto.getMeterModel().setUriPresent(panel.uriPresentBox.getValue());
    }

    private Long getManufacturer() {
        int index = panel.manufacturerBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.manufacturerBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getServiceResource() {
        int index = panel.serviceResourceBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.serviceResourceBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getMeterType() {
        int index = panel.meterTypeBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.meterTypeBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getMeterPhase() {
        int index = panel.meterPhaseBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.meterPhaseBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getMdc() {
        int index = panel.mdcBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.mdcBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getDataDecoder() {
        int index = panel.dataDecoderBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(panel.dataDecoderBox.getValue(index));
        } else {
            return null;
        }
    }

    private ArrayList<IdNameDto> getPaymentModes() {
        ArrayList<IdNameDto> selected = new ArrayList<IdNameDto>();
        for (int i = 0; i < panel.paymentModesBox.getItemCount(); i++) {
            if (panel.paymentModesBox.isItemSelected(i)) {
                selected.add(new IdNameDto(Long.valueOf(panel.paymentModesBox.getValue(i)), panel.paymentModesBox.getItemText(i)));
            }
        }
        return selected;
    }

    private void onSave() {
        disableButtons();
        if (isValidInput()) {
            final MeterModelDto m = updateMeterModel();
            boolean isMdcChanged = false;     //for new  meter models
            if (meterModelDto != null && meterModelDto.getMeterModel().getId() != null) {
                isMdcChanged = (panel.mdcBox.getValue(panel.mdcBox.getSelectedIndex()).isEmpty() && meterModelDto.getMeterModel().getMdcId() != null)
                               || (!panel.mdcBox.getValue(panel.mdcBox.getSelectedIndex()).isEmpty() && meterModelDto.getMeterModel().getMdcId() == null)
                               || (!panel.mdcBox.getValue(panel.mdcBox.getSelectedIndex()).isEmpty() && !Long.valueOf(panel.mdcBox.getValue(panel.mdcBox.getSelectedIndex())).equals(meterModelDto.getMeterModel().getMdcId()));
            }
            final boolean finalIsMdcChanged = isMdcChanged;
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (finalIsMdcChanged) {
                        //TODO RC match again the whole group
                        Long newMdcId = getMdc();
                        clientFactory.getUsagePointRpc().getMeterModelChannelCompatibility(m.getMeterModel().getId(), newMdcId, clientFactory.getUser().getUserName(), new ClientCallback<MdcChannelMatchDto>() {
                            @Override
                            public void onSuccess(MdcChannelMatchDto result) {
                                Integer act = result.getActiveUpCount();
                                Integer dac = result.getInactiveUpCount();
                                boolean isOnlyThinUnits = (act + dac) == result.getUpWithThinUnitsPsCount();
                                final Messages messages = MessagesUtil.getInstance();
                                logger.info("MeterModelView: on change MDC on a MeterModel: match = " + result.getMatch().toString() + " countActiveUpWithMdc = " + act + " inactive=" + dac + " Potential WARNING or ERROR displayed to user: " + clientFactory.getUser().getUserName());
                                if (ChannelCompatibilityE.NO_DATA.equals(result.getMatch())) {
                                    //could be TOU PS with an MDC;  ChannelCompatibilityE only compares to regRead, else returns NO_DATA
                                    if (!isOnlyThinUnits && act + dac > 0) {
                                        displayWarningMessage(m, messages, "warning.change.mdc.on.meter.NO.DATA", act, dac);
                                    } else {
                                        saveContinueCheckMdcChannelOverrides(m);
                                    }
                                } else if (ChannelCompatibilityE.NONE_MATCH.equals(result.getMatch()) && act > 0) {
                                    Dialogs.centreErrorMessage(messages.getMessage("error.change.mdc.on.meter.NONE.MATCH.active.up", new String[]{act.toString()}),
                                            MediaResourceUtil.getInstance().getErrorIcon(),
                                            messages.getMessage("button.close"),
                                            new ClickHandler() {
                                                @Override
                                                public void onClick(ClickEvent arg0) {
                                                    setMdc();
                                                    enableButtons();
                                                }
                                            });
                                } else if (act + dac > 0) {
                                    displayWarningMessage(m, messages, "warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up", act, dac);
                                } else {
                                    saveContinueCheckMdcChannelOverrides(m);
                                }
                            }
                        });
                    } else {
                        saveNow(m);
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            enableButtons();
        }
    }

    private void displayWarningMessage(final MeterModelDto m, Messages messages, String messageKey, Integer act, Integer dac) {
        Dialogs.confirm(messages.getMessage(messageKey, new String[]{act.toString(), dac.toString()}),
                messages.getMessage("button.yes"),
                messages.getMessage("button.no"),
                MediaResourceUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            saveContinueCheckMdcChannelOverrides(m);
                        } else {
                            //redisplay selected
                            setMdc();
                            enableButtons();
                        }
                    }
                },
                null, null);  //if make position null, will centre confirm
    }

    private void saveContinueCheckMdcChannelOverrides(final MeterModelDto m) {
        //if changing the mdc on the model, check to see if any configs on the old mdc, and if so, whether should go ahead with change which deletes them
        clientFactory.getMdcChannelRpc().getModelChannelConfigs(m.getMeterModel().getId(), new ClientCallback<List<ModelChannelConfigDto>>() {
            @Override
            public void onSuccess(List<ModelChannelConfigDto> result) {
                if (result == null || result.isEmpty()) {
                    saveNow(m);
                } else {
                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.model.change.mdc.confirm.delete.configs"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        deletePreviousChannelOverrides(m);
                                    } else {
                                        setMdc();
                                        enableButtons();
                                    }
                                }
                            });
                }
            }
        });
    }

    private void saveNow(final MeterModelDto m) {
        ArrayList<IdNameDto> paymentModes = getPaymentModes();
        final Long id = m.getMeterModel().getId();
        // keep track whether the table's total row count is increasing or not
        clientFactory.getMeterModelRpc().saveMeterModel(m.getMeterModel(), paymentModes, new ClientCallback<MeterModel>(
                view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(MeterModel meterModel) {
                view.getForm().setDirtyData(false);
                boolean refresh = id == null;
                refreshTable(refresh);
                Dialogs.displayInformationMessage(
                        MessagesUtil.getInstance().getSavedMessage(
                                new String[]{MessagesUtil.getInstance().getMessage("meter.models.name")}),
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                if (meterModel.getMdcId() == null) {
                    clear();
                    panel.mridComponent.initMrid(clientFactory);
                } else {
                    // else keep panel populated so can View Channels
                    refreshMeterModelDto(meterModel.getId());
                }
                sendNotification();
                enableButtons();
            }

            @Override
            public void onFailure(Throwable caught) {
                super.onFailure(caught);
                refreshTable(true); //Refresh table for any changes
                refreshMeterModelDto(m.getMeterModel().getId());
                enableButtons();
            }
        });
    }

    private void refreshMeterModelDto(Long meterModelId) {
        clientFactory.getMeterModelRpc().getMeterModelDtoForMeterModelId(meterModelId, new ClientCallback<MeterModelDto>() {
            @Override
            public void onSuccess(MeterModelDto result) {
                meterModelDto = result;
                displayMeterModel(meterModelDto);
            }
        });
    }

    private void deletePreviousChannelOverrides(final MeterModelDto m) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getMdcChannelRpc().deleteAllModelChannelConfigForMeter(m.getMeterModel().getId(), new ClientCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        saveNow(m);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void disableButtons() {
        view.getForm().getSaveBtn().setEnabled(false);
        view.getForm().getOtherBtn().setEnabled(false);
        viewBtn.setEnabled(false);
    }

    private void enableButtons() {
        view.getForm().getSaveBtn().setEnabled(true);
        view.getForm().getOtherBtn().setEnabled(true);
        viewBtn.setEnabled(true);
    }

    private void sendNotification() {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.METER_MODEL_MODIFIED));
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be
    //reloaded due to other changes like disabled user.
    private void refreshTable(boolean insertedNew) {
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_MODEL)) {
            view.getForm().getButtons().removeFromParent();
            viewBtn.removeFromParent();
        }
    }
}
