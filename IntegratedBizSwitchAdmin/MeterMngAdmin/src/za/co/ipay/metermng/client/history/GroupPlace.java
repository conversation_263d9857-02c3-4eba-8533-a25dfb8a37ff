package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class GroupPlace extends Place {
    
    public static GroupPlace GROUP_TYPE_PLACE = new GroupPlace("type");
    public static GroupPlace USAGE_POINT_GROUPS_PLACE = new GroupPlace(MeterMngStatics.USAGE_POINT_GROUP_TYPE);
    public static GroupPlace ACCESS_GROUPS_PLACE = new GroupPlace(MeterMngStatics.ACCESS_GROUP_TYPE);
    public static GroupPlace LOCATION_GROUPS_PLACE = new GroupPlace(MeterMngStatics.LOCATION_GROUP_TYPE);
    
    private String groupType;

    public GroupPlace(String grouptype) {
        this.groupType = grouptype;
    }

    public String getGroupType() {
        return groupType;
    }
    
    @Override
    public String toString() {
        return "GroupPlace: groupType: " + getGroupType();
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((groupType == null) ? 0 : groupType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else if (!(obj instanceof GroupPlace)) {
            return false;
        } else {
            GroupPlace other = (GroupPlace) obj;
            if (groupType == null) {
                if (other.groupType != null) {
                    return false;
                }
            } else if (!groupType.equals(other.groupType)) {
                return false;
            }
            return true;
        }
    }

    public static String getPlaceAsString(GroupPlace place) {
        if (place != null) {
            return "group:"+place.getGroupType();
        } else {
            return "";
        }
    }
    
    @Prefix(value="group")
    public static class Tokenizer implements PlaceTokenizer<GroupPlace> {
        @Override
        public String getToken(GroupPlace place) {
            return place.getGroupType();
        }

        @Override
        public GroupPlace getPlace(String token) {
            return new GroupPlace(token);
        }
    }
}
