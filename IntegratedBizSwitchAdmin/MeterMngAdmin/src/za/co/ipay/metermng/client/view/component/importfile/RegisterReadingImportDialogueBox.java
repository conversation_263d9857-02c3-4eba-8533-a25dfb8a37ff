package za.co.ipay.metermng.client.view.component.importfile;

import java.math.BigDecimal;
import java.util.List;

import com.google.gwt.i18n.client.DateTimeFormat;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.registerreading.RegisterReadingImportRecord;
import za.co.ipay.metermng.shared.integration.registerreading.RegisterReadingParseResult;

public class RegisterReadingImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private RegisterReadingImportRecord recordIn;
    private DateTimeFormat dfFormat = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss Z");

    public RegisterReadingImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        RegisterReadingImportRecord record = recordIn = itemDto.getRegReadImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Meter Number", record.getMeterNum()));
        if (record.getReadingTimestamp() != null) {
            dataList.add(new ImportRecordField("Reading Timestamp", dfFormat.format(record.getReadingTimestamp())));
        } else {
            dataList.add(new ImportRecordField("Reading Timestamp, yyyy-MM-dd HH:mm:ss Z", ""));
        }
        dataList.add(new ImportRecordField("Channel Value", record.getChannelValue()));
        if (record.getReadingValue() != null) {
            dataList.add(new ImportRecordField("Reading Value", record.getReadingValue().toPlainString()));
        } else {
            dataList.add(new ImportRecordField("Reading Value", ""));
        }

        dataList.add(new ImportRecordField("Estimate", booleanToString(record.isEstimate())));
        dataList.add(new ImportRecordField("Manual", booleanToString(record.isManual())));
        return dataList;
    }

    private String booleanToString(boolean bool) {
        if (bool) {
            return "true";
        } else {
            return "false";
        }
    }

    private Boolean getBoolean(String boolStr) throws Exception {
        if (boolStr == null) {
            return Boolean.FALSE;
        }
        String str = boolStr.toLowerCase();
        if (str.startsWith("t")) {
            return Boolean.TRUE;
        } else if (str.startsWith("f")) {
            return Boolean.FALSE;
        } else {
            throw new Exception("Must start with 't' or 'f'");
        }
    }

    @Override
    protected void checkDirtyData() {
        isDirtyData = false;
        RegisterReadingParseResult result = createRecordFromList();
        if (result.getErrorMsg() != null && !result.getErrorMsg().isEmpty()) {
            isDirtyData = true;
            return;
        }

        RegisterReadingImportRecord chgRec = result.getRegReadImportRecord();

        if (!recordIn.getMeterNum().equals(chgRec.getMeterNum())) {
            isDirtyData = true;
            return;
        }
        if ((recordIn.getReadingTimestamp() == null && chgRec.getReadingTimestamp() != null)
                || (recordIn.getReadingTimestamp() != null && chgRec.getReadingTimestamp() == null)
                || (recordIn.getReadingTimestamp() != null && chgRec.getReadingTimestamp() != null
                    && !recordIn.getReadingTimestamp().equals(chgRec.getReadingTimestamp()))) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getChannelValue().equals(chgRec.getChannelValue())) {
            isDirtyData = true;
            return;
        }
        if ((recordIn.getReadingValue() == null && chgRec.getReadingValue() != null)
                || (recordIn.getReadingValue() != null && chgRec.getReadingValue() == null)
                || (recordIn.getReadingValue() != null && chgRec.getReadingValue() != null
                    && !recordIn.getReadingValue().equals(chgRec.getReadingValue()))) {
            isDirtyData = true;
            return;
        }
        if (!booleanToString(recordIn.isEstimate()).equals(booleanToString(chgRec.isEstimate()))) {
            isDirtyData = true;
            return;
        }
        if (!booleanToString(recordIn.isManual()).equals(booleanToString(chgRec.isManual()))) {
            isDirtyData = true;
            return;
        }
    }

    private RegisterReadingParseResult createRecordFromList() {
        StringBuilder errorBuilder = new StringBuilder();

        RegisterReadingImportRecord chgRec = new RegisterReadingImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Meter Number")) {
                chgRec.setMeterNum(field.getFieldValue());
            }

            if (field.getFieldname().contains("Reading Timestamp")) {
                try {
                    chgRec.setReadingTimestamp(dfFormat.parse(field.getFieldValue()));
                } catch (Exception e) {
                    errorBuilder.append("Date " + field.getFieldValue() + " cannot be parsed to required format: yyyy-MM-dd HH:mm:ss Z; ");
                }
            }

            if (field.getFieldname().equals("Channel Value")) {
                chgRec.setChannelValue(field.getFieldValue());
            }

            if (field.getFieldname().equals("Reading Value")) {
                try {
                    chgRec.setReadingValue(new BigDecimal(field.getFieldValue()));
                } catch (Exception e) {
                    errorBuilder.append("Reading Value must be numeric; ");
                }
            }

            if (field.getFieldname().equals("Estimate")) {
                try {
                    chgRec.setEstimate(getBoolean(field.getFieldValue()));
                } catch (Exception e) {
                    errorBuilder.append("isEstimate: must start with 't' or 'f'; ");
                }
            }
            if (field.getFieldname().equals("Manual")) {
                try {
                    chgRec.setManual(getBoolean(field.getFieldValue()));
                } catch (Exception e) {
                    errorBuilder.append("isManual: must start with 't' or 'f'; ");
                }
            }
        }

        String errors = null;
        if(errorBuilder.length() != 0) {
            errorBuilder.insert(0, "Errors: ");
            errors = errorBuilder.toString();
            errors = errors.substring(0, errors.length()- 2);
        }
        return (new RegisterReadingParseResult(chgRec, errors));
    }

    @Override
    protected void updateParentRow() {
        RegisterReadingParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getRegReadImportRecord());
        importFileItemDto.getImportFileItem().setComment("");                 //eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }

    @Override
    protected void displayUpdateMessage() {
        RegisterReadingParseResult result = createRecordFromList();
        RegisterReadingImportRecord record = result.getRegReadImportRecord();
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.reg.Read.item.update.success",
                        new String[] { record.getMeterNum(), dfFormat.format(record.getReadingTimestamp()) }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        RegisterReadingParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getRegReadImportRecord());
        importFileItemDto.getImportFileItem().setComment("");                 //eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }
}
