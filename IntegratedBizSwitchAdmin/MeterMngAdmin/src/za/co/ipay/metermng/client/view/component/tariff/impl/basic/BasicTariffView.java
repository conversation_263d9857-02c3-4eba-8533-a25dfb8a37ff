package za.co.ipay.metermng.client.view.component.tariff.impl.basic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.view.component.tariff.ContainsPayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.component.tariff.PayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.impl.BaseTariffView;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.shared.bsst.BsstAllowance;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.basic.BasicPrivateUtilityTariffCalcContents;

public class BasicTariffView extends BaseTariffView implements ITariffUIClass, ContainsPayTypeDiscountPanel {

    private static BasicTariffUiBinder uiBinder = GWT.create(BasicTariffUiBinder.class);
    
    @UiField FormElement unitPriceElement;
    @UiField Label unitPriceCurrencyLabel;
    @UiField BigDecimalValueBox unitPriceTextBox;
    
    @UiField FormElement minVendAmountElement;
    @UiField Label minVendAmountCurrencyLabel;
    @UiField BigDecimalValueBox minVendAmountBox;
    
    @UiField FormElement taxPercentElement;
    @UiField PercentageTextBox taxPercentTextBox;

    @UiField FormElement freeUnitsNameElement;
    @UiField TextBox freeUnitsNameTextBox;

    @UiField FormElement freeUnitsElement;
    @UiField BigDecimalValueBox freeUnitsTextBox;
    
    @UiField FormElement cycleListBoxElement;
    @UiField ListBox cycleListBox;

    @UiField FormElement cyclicChargeNameElement;
    @UiField TextBox cyclicChargeNameTextBox;

    @UiField FormElement cyclicChargeElement;
    @UiField Label cyclicChargeCurrencyLabel;
    @UiField BigDecimalValueBox cyclicChargeTextBox;

    @UiField FormElement percentChargeNameElement;
    @UiField TextBox percentChargeNameTextBox;

    @UiField FormElement percentChargeElement;
    @UiField(provided=true) PercentageTextBox percentChargeTextBox;
    
    @UiField FormRowPanel paytypeDiscountFormRowPanel;
    
    private PayTypeDiscountPanel payTypeDiscountPanel;
    
    private static final Logger logger = Logger.getLogger("BasicTariffView");

    interface BasicTariffUiBinder extends UiBinder<Widget, BasicTariffView> {
    }

    public BasicTariffView() {
        createPercentChargeBox();
        initWidget(uiBinder.createAndBindUi(this));
    	payTypeDiscountPanel = new PayTypeDiscountPanel(this);
    	paytypeDiscountFormRowPanel.add(payTypeDiscountPanel); 
        createCycleList();
        createCurrencyLabels();
    }
    
    public void createPercentChargeBox() {
        percentChargeTextBox = new PercentageTextBox();
        percentChargeTextBox.setAmount(null);
    }
    
    public void createCycleList() {
        cycleListBox.addItem("", "");
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.daily"), "3");    //addItem(text, CycleE.id)
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.monthly"), "4");
    }
    
    private void createCurrencyLabels() {
        unitPriceCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        cyclicChargeCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        minVendAmountCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        if (FormatUtil.getInstance().isRightToLeft()) {
            unitPriceCurrencyLabel.setStyleName("btCurrency-right");
            cyclicChargeCurrencyLabel.setStyleName("btCurrency-right");
            minVendAmountCurrencyLabel.setStyleName("btCurrency-right");
        } else {
            unitPriceCurrencyLabel.setStyleName("btCurrency-left");
            cyclicChargeCurrencyLabel.setStyleName("btCurrency-left");
            minVendAmountCurrencyLabel.setStyleName("btCurrency-left");
        }
    }
    
    public void clearForm() {
        unitPriceTextBox.setValue(null);
        minVendAmountBox.setValue(null);
        freeUnitsNameTextBox.setText(null);
        freeUnitsTextBox.setValue(null);
        cycleListBox.setSelectedIndex(0);
        cyclicChargeNameTextBox.setText(null);
        cyclicChargeTextBox.setValue(null);
        taxPercentTextBox.getBigDecimalValueBox().setText("");
        percentChargeNameTextBox.setText(null);
        percentChargeTextBox.setAmount(null);
        clearErrors();
    }

    public void clearErrors() {
        minVendAmountElement.clearErrorMsg();
        unitPriceElement.clearErrorMsg();
        freeUnitsNameElement.clearErrorMsg();
        freeUnitsElement.clearErrorMsg();
        cycleListBoxElement.clearErrorMsg();
        cyclicChargeNameElement.clearErrorMsg();
        cyclicChargeElement.clearErrorMsg();
        taxPercentElement.clearErrorMsg();
        percentChargeNameElement.clearErrorMsg();
        percentChargeElement.clearErrorMsg();
        payTypeDiscountPanel.clearErrors();
    }
    
    @Override
    public void setTariffData(ITariffData tariffData) {
        logger.info("Setting tariffData:" + tariffData);
        BasicPrivateUtilityTariffCalcContents calcContents = (BasicPrivateUtilityTariffCalcContents) tariffData;
        unitPriceTextBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getUnitPrice()));
        minVendAmountBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getMinVendAmount()));
        BsstAllowance bsstAllowance = calcContents.getBsstAllowance();
        if (bsstAllowance != null) {
            freeUnitsNameTextBox.setText(bsstAllowance.getDescription());
            freeUnitsTextBox.setText(FormatUtil.getInstance().formatDecimal(bsstAllowance.getUnits()));
        }
        
        cyclicChargeNameTextBox.setText(calcContents.getCyclicChargeName());
        if (calcContents.getCyclicCharge() != null)
            cyclicChargeTextBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getCyclicCharge()));
        
        long cycleId = calcContents.getCycle().getId();
        if (cycleId == 4 && calcContents.getCyclicCharge() == null) {
            cycleListBox.setSelectedIndex(0);
        } else {
            for (int i = 1; i < cycleListBox.getItemCount(); i++) {
                cycleListBox.setSelectedIndex(i);
                if (Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())) == cycleId) {
                    break;
                }
            }
        }
        
        taxPercentTextBox.setAmount(calcContents.getTaxPercent());
        percentChargeNameTextBox.setText(calcContents.getPercentChargeName());
        if(calcContents.getPercentChargeMultiplier() != null)
            percentChargeTextBox.setAmount(calcContents.getPercentChargeMultiplier().movePointRight(2));
        
        //discounts
        payTypeDiscountPanel.setCalcContentsPayTypeDiscount(calcContents.getPayTypeDiscounts());

    }

    @Override
    public boolean tariffDataRequired() {
        return true;
    }
    
    @Override
    public ITariffData getTariffData() {       
        boolean error = false;
        
        BigDecimal unitPrice = null;
        try {
            unitPrice = unitPriceTextBox.getValue();
            if (unitPrice != null && unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                error = true;
                unitPriceElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.numeric.positive_not_zero"));
            }
        } catch (Exception e) {
            error = true;
            unitPriceElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }

        // minVendAmount
        BigDecimal minVendAmt = minVendAmountBox.getValue();
        if (minVendAmt != null && minVendAmt.compareTo(BigDecimal.ZERO) < 0) {
            error = true;
            minVendAmountElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        
        BigDecimal taxPercent = taxPercentTextBox.getAmount();
        if (taxPercent != null && taxPercent.compareTo(BigDecimal.ZERO) < 0) {
            error = true;
            taxPercentElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }

        BigDecimal freeUnits = null;
        try {
            if(freeUnitsTextBox.getValue() != null) {
                if(freeUnitsTextBox.getValue().compareTo(BigDecimal.ZERO) < 0) {
                    error = true; 
                    freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.freeunits.positive"));
                }
                freeUnits = freeUnitsTextBox.getValue();
                if (ValidateUtil.getNumberOfDecimalPlaces(freeUnits) > 1) {
                    error = true; 
                    freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.freeunits.decimal.limit"));
                }
            }
        } catch (Exception e) {
            logger.info("Free units exception= " + e);
            error = true;
            freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }
        
        String freeUnitsName = freeUnitsNameTextBox.getText();
        boolean hasFreeUnitsName = false;
        if (freeUnitsName != null && !freeUnitsName.trim().isEmpty()) {
            hasFreeUnitsName = true;
        }
        if (hasFreeUnitsName && freeUnits == null) {
            error = true;
            freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
        } else if (!hasFreeUnitsName && freeUnits != null) {
            error = true;
            freeUnitsNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
        }

        BsstAllowance bsstAllowance = null;
        if (freeUnits != null && hasFreeUnitsName) {
            bsstAllowance = new BsstAllowance();
            bsstAllowance.setDescription(freeUnitsName.trim());
            bsstAllowance.setUnits(freeUnits);
        }
        
        BigDecimal cyclicCharge = null;
        try {
            if(cyclicChargeTextBox.getValue() != null) {
                cyclicCharge = cyclicChargeTextBox.getValue();
                if(cyclicCharge.compareTo(BigDecimal.ZERO) <= 0) {
                    error = true; 
                    cyclicChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.cyclic.charge.positive"));
                }
            }
        } catch (Exception e) {
            error = true;
            cyclicChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }

        String cyclicChargeName = cyclicChargeNameTextBox.getText();
        if (cyclicCharge != null && (cyclicChargeName == null || cyclicChargeName.trim().length() < 1)) {
            error = true;
            cyclicChargeNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
        }else if (cyclicCharge == null && !(cyclicChargeName == null || cyclicChargeName.trim().length() < 1)) {
            error = true; 
            cyclicChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.cyclic.charge"));
        }
        
        CycleE cycle = CycleE.fromId(4);     // default to monthly
        try {
            if (cyclicCharge != null) {
                int index = cycleListBox.getSelectedIndex();
                if (index < 1) {
                    error = true;
                    cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.error"));
                } else {
                    cycle = CycleE.fromId(Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())));
                }
            }
        } catch (Exception e) {
            error = true;
            cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("cycle.error"));
        } 
        
        BigDecimal taxMultiplier = null;
        try {
            // taxMultiplier is 1 + (percent / 100) eg. 14% = 1.14
            // more efficient to do this calculation here than to store 14% and have the tariff calculator do this calculation on every vend
            taxMultiplier = BigDecimal.ONE.add(taxPercentTextBox.getAmount().divide(BigDecimal.valueOf(100L)));
        } catch (Exception e) {
            error = true;
            taxPercentElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }
        
        BigDecimal percentChargeMultiplier = null;
        try {
            if(percentChargeTextBox.getAmount() != null) {
                if(percentChargeTextBox.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    error = true; 
                    percentChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.numeric.positive_not_zero"));
                } else {
                    // more efficient to do this calculation here than have the tariff calculator do this calculation on every vend
                    percentChargeMultiplier = percentChargeTextBox.getAmount().movePointLeft(2);
                }
            } 
        } catch (Exception e) {
            error = true;
            percentChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }

        String percentChargeName = percentChargeNameTextBox.getText();
        if (percentChargeMultiplier != null && (percentChargeName == null || percentChargeName.length() < 1)) {
            error = true;
            percentChargeNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.percent_charge.name"));
        }
        
        if(percentChargeMultiplier == null && (percentChargeName != null && percentChargeName.length() > 0)) {
            error = true;
            percentChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.percent_charge"));
        }

        //Discounts
        if (!payTypeDiscountPanel.isValid()) {
        	error = true;
        }

        if (error) {
            return null;
        }

        BasicPrivateUtilityTariffCalcContents cc = new BasicPrivateUtilityTariffCalcContents(unitPrice, cycle, cyclicChargeName, cyclicCharge, 
                taxMultiplier, RoundingMode.UP, percentChargeName, percentChargeMultiplier, bsstAllowance, minVendAmt, payTypeDiscountPanel.getThisPayTypeDiscountsList());

        if (!ClientValidatorUtil.getInstance().validateField(cc, "unitPrice", unitPriceElement)) {
         	error = true;
        } 

        if (error) {
            return null;
        }
        
        return cc;
    }

    
    @Override
    public void setCalcContents(String contents) {   // because already set calcContents from server dataService for JSON!
    }

    @Override
    public String getCalcContents() {
        // gets past validator will be replaced on server
        return "placeholder";
    }

    @Override
    protected void addFieldHandlers() {
        unitPriceTextBox.addChangeHandler(new FormDataChangeHandler(form));
        minVendAmountBox.addChangeHandler(new FormDataChangeHandler(form));
        freeUnitsNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        freeUnitsTextBox.addChangeHandler(new FormDataChangeHandler(form));
        cycleListBox.addChangeHandler(new FormDataChangeHandler(form));
        cyclicChargeNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        cyclicChargeTextBox.addChangeHandler(new FormDataChangeHandler(form));
        taxPercentTextBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        percentChargeNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        percentChargeTextBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
    }

    @Override
    public void setFormReadOnly(boolean readOnly) {
    }
}