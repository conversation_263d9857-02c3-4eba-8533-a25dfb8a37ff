package za.co.ipay.metermng.client.view.component.search.customer;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.search.Search;
import za.co.ipay.metermng.client.view.component.search.SearchCriteriaForm;
import za.co.ipay.metermng.client.view.workspace.search.AdvancedSearchWorkspaceView;
import za.co.ipay.metermng.shared.dto.search.SearchData;

public class CustomerSearchForm extends BaseComponent implements Search {
    
    @UiField SearchCriteriaForm searchForm;
    private CustomerSearchPanel panel;
    
    private AdvancedSearchWorkspaceView advancedSearchWorkspaceView;

    private static CustomerSearchFormUiBinder uiBinder = GWT.create(CustomerSearchFormUiBinder.class);

    interface CustomerSearchFormUiBinder extends UiBinder<Widget, CustomerSearchForm> {
    }

    public CustomerSearchForm(AdvancedSearchWorkspaceView advancedSearchWorkspaceView) {
        this.advancedSearchWorkspaceView = advancedSearchWorkspaceView;
        initWidget(uiBinder.createAndBindUi(this));
        initForm();
    }
    
    private void initForm() {
        panel = new CustomerSearchPanel(advancedSearchWorkspaceView);
        searchForm.setSearchPanel(panel);
    }

    @Override
    public void clear() {
        enable();
        panel.clear();
    }

    @Override
    public boolean displayCriteria(SearchPlace searchPlace) {
        return panel.displayCriteria(searchPlace);        
    }

    @Override
    public boolean isValidInput() {
        return panel.isValidInput();
    }

    @Override
    public void populateSearchCriteria(SearchData searchData) {
        panel.populateSearchCriteria(searchData);
    }
    
    @Override
    public void addDefaultKeyHandler(KeyDownHandler handler) {
        panel.addDefaultKeyHandler(handler);
    }
    
    public void disable() {
        this.setVisible(false);
    }
    
    public void enable() {
        this.setVisible(true);
    }
    
    public void showCheckbox() {
        panel.showCheckbox();
    }
    public void hideCheckbox() {
        panel.hideCheckbox();
    }
    public CustomerSearchPanel getPanel() {
        return panel;
    }
}