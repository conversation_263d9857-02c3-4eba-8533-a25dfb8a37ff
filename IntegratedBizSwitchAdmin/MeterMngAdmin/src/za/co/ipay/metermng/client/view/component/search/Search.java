package za.co.ipay.metermng.client.view.component.search;

import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.shared.dto.search.SearchData;

import com.google.gwt.event.dom.client.KeyDownHandler;

/**
 * SearchForm is a simple interface defining the functionality a search form provides.
 * <AUTHOR>
 */
public interface Search {

    public void clear();
    
    public void addDefaultKeyHandler(KeyDownHandler handler);
    
    public boolean displayCriteria(SearchPlace searchPlace);
    
    public boolean isValidInput();
    
    public void populateSearchCriteria(SearchData searchData);
    
}
