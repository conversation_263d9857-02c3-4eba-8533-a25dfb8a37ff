package za.co.ipay.metermng.client.rpc;

import java.util.List;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

@RemoteServiceRelativePath("secure/auxChargeSchedule.do")
public interface AuxChargeScheduleRpc extends RemoteService {
    
    public List<AuxChargeSchedule> getAllAuxChargeSchedules() throws ServiceException, AccessControlException;

    public List<AuxAccount> getAllAuxAccounts() throws AccessControlException;
    
    public AuxChargeSchedule updateAuxChargeSchedule(AuxChargeSchedule auxChargeSchedule) throws ValidationException, ServiceException, AccessControlException;
}
