package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;
import za.co.ipay.metermng.client.event.MetadataUploadEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class MetadataUploadActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private MetadataUploadPlace metadataUploadPlace;

    public MetadataUploadActivity(MetadataUploadPlace metadataUploadPlace, ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.metadataUploadPlace = metadataUploadPlace;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new MetadataUploadEvent(metadataUploadPlace.getName()));
    }

}
