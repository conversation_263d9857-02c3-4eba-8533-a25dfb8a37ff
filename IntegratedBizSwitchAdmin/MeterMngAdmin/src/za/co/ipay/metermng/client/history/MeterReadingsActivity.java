package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenMeterReadingsEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class MeterReadingsActivity extends AbstractActivity {

    private MeterReadingsPlace meterReadingsPlace;
    private ClientFactory clientFactory;
    
    public MeterReadingsActivity(MeterReadingsPlace meterReadingsPlace, ClientFactory clientFactory) {
        super();
        this.meterReadingsPlace = meterReadingsPlace;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(
                new OpenMeterReadingsEvent(meterReadingsPlace.getSuperMeter(),
                                           meterReadingsPlace.getGraphType(),
                                           meterReadingsPlace.getReadingType(),
                                           meterReadingsPlace.getStartDate(),
                                           meterReadingsPlace.getEndDate()));
    }
}
