package za.co.ipay.metermng.client.view.component.group.entity.ndp;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.ImageResourceCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FocusWidget;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarModel;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.NdpDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDay;
import za.co.ipay.metermng.shared.NdpDayProfileData;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class SpecialDayDetailPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField FormElement specialDayElement;
    @UiField ListBox dayListBox;
    @UiField ListBox monthListBox;

    @UiField FormElement startTimeElement;
    @UiField TextBox txtbxStartHour;
    @UiField TextBox txtbxStartMinute;
    @UiField FormElement endTimeElement;
    @UiField TextBox txtbxEndHour;
    @UiField TextBox txtbxEndMinute;
    @UiField Button btnAddSpecialDayTimes;

    @UiField Label errorMsgTimes;

    @UiField CellTable<NdpDayProfileData> ndpDaysTable;
    @UiField TablePager ndpDaysPager;

    @UiField FormRowPanel enterTimesPanel;
    @UiField FormRowPanel btnPanel;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField HTML specialDayDescrip;

    private ListDataProvider<NdpDayProfileData> dataProvider;

    private CalendarModel calmod;
    private ListHandler<NdpDayProfileData> columnSortHandler;
    private ColumnSortList columnSortList;

    private TextColumn<NdpDayProfileData> startColumn;
    private TextColumn<NdpDayProfileData> endColumn;
    private Column<NdpDayProfileData, ImageResource> deleteColumn;

    private SchedulePanel parentSchedulePanel;
    private NdpScheduleData ndpScheduleData;
    private Boolean isInherited = false;
    private Boolean isViewOnly = false;
    private NdpSpecialDay ndpSpecialDay;
    private ArrayList<NdpDayProfileData> deleteTheseDayProfiles = new ArrayList<NdpDayProfileData>();
    private Boolean isDeactivateSchedule = false;

    DateTimeFormat daysOfWeekFormat = DateTimeFormat.getFormat("EEE");
    private StringBuilder sb;

    private HasDirtyData hasDirtyData;

    private static Logger logger = Logger.getLogger(SpecialDayDetailPanel.class.getName());

    private static NdpSpecialDayDaysPanelUiBinder uiBinder = GWT.create(NdpSpecialDayDaysPanelUiBinder.class);

    interface NdpSpecialDayDaysPanelUiBinder extends UiBinder<Widget, SpecialDayDetailPanel> {
    }

    public SpecialDayDetailPanel(ClientFactory clientFactory, SchedulePanel parentSchedulePanel, NdpScheduleData ndpScheduleData, Boolean isInherited, Boolean isViewOnly) {
        this.clientFactory = clientFactory;
        this.parentSchedulePanel = parentSchedulePanel;
        this.ndpScheduleData = ndpScheduleData;
        this.isInherited = isInherited;
        this.isViewOnly = isViewOnly;

        calmod = new CalendarModel();
        createTable();

        initWidget(uiBinder.createAndBindUi(this));

        // manage dirty
        hasDirtyData = parentSchedulePanel.getHasDirtyDataManager().createAndRegisterHasDirtyData();

        initTable();
        populateListBoxes();
        addFieldHandlers();

        txtbxStartHour.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxStartHour.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSpecialDayTimes.setEnabled(true);
            }
        });
        txtbxStartMinute.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxStartMinute.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSpecialDayTimes.setEnabled(true);
            }
        });
        txtbxEndHour.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxEndHour.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSpecialDayTimes.setEnabled(true);
            }
        });
        txtbxEndMinute.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxEndMinute.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSpecialDayTimes.setEnabled(true);
            }
        });

        txtbxStartHour.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartHour.getText().trim().length()==1) {
                    txtbxStartHour.setText("0"+txtbxStartHour.getText().trim());
                }
            }
        });
        txtbxStartHour.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxStartHour, txtbxStartMinute);
            }

        });

        txtbxStartMinute.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartMinute.getText().trim().length()==1) {
                    txtbxStartMinute.setText("0"+txtbxStartMinute.getText().trim());
                }
            }
        });
        txtbxStartMinute.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxStartMinute, txtbxEndHour);
            }

        });

        txtbxEndHour.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndHour.getText().trim().length()==1) {
                    txtbxEndHour.setText("0"+txtbxEndHour.getText().trim());
                }
            }
        });
        txtbxEndHour.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxEndHour, txtbxEndMinute);
            }

        });

        txtbxEndMinute.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndMinute.getText().trim().length()==1) {
                    txtbxEndMinute.setText("0"+txtbxEndMinute.getText().trim());
                }
            }
        });
        txtbxEndMinute.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxEndMinute, btnAddSpecialDayTimes);
            }

        });

    }

    public void addFieldHandlers() {
        dayListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        monthListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));

        txtbxStartMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxStartHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }


    private void handleHourBox(TextBox hour, FocusWidget nextFocus) {
        boolean accept = true;
        String txt = hour.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 9) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 23) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            hour.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    private void handleMinuteBox(TextBox minuteBox, Focusable nextFocus) {
        boolean accept = true;
        String txt = minuteBox.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 5) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 59) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            minuteBox.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    private void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            ndpDaysTable = new CellTable<NdpDayProfileData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            ndpDaysTable = new CellTable<NdpDayProfileData>(DEFAULT_PAGE_SIZE);
        }
    }

   private void initTable() {
        if (dataProvider == null) {

            startColumn = new TextColumn<NdpDayProfileData>() {
                @Override
                public String getValue(NdpDayProfileData data) {
                    if (data.getNdpDayProfile().getStartHour() == null || data.getNdpDayProfile().getStartMinute() == null) {
                        return null;
                    }
                    return prefixWithZero(data.getNdpDayProfile().getStartHour()) + ":" + prefixWithZero(data.getNdpDayProfile().getStartMinute());
                }


            };
            startColumn.setSortable(true);

            endColumn = new TextColumn<NdpDayProfileData>() {
                @Override
                public String getValue(NdpDayProfileData data) {
                    if (data.getNdpDayProfile().getEndHour() == null || data.getNdpDayProfile().getEndMinute() == null) {
                        return null;
                    }
                    return prefixWithZero(data.getNdpDayProfile().getEndHour()) + ":" + prefixWithZero(data.getNdpDayProfile().getEndMinute());
                }
            };
            endColumn.setSortable(true);

            ImageResourceCell deleteCell = new ImageResourceCell() {
                public Set<String> getConsumedEvents() {
                    HashSet<String> events = new HashSet<String>();
                    events.add("click");
                    return events;
                }
            };

            deleteColumn = new Column<NdpDayProfileData, ImageResource>(deleteCell) {
                @Override
                public ImageResource getValue(NdpDayProfileData dataObj) {
                    return MediaResourceUtil.getInstance().getDeleteImage();
                }

                @Override
                public void onBrowserEvent(Context context, Element elem,
                        final NdpDayProfileData object, NativeEvent event) {
                    super.onBrowserEvent(context, elem, object, event);
                    if ("click".equals(event.getType())) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                deleteDayProfile(object);
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            };

            // Add the columns.
            ndpDaysTable.addColumn(startColumn, MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.start"));
            ndpDaysTable.addColumn(endColumn, MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.end"));
            ndpDaysTable.addColumn(deleteColumn, "");

            dataProvider = new ListDataProvider<NdpDayProfileData>();
            dataProvider.addDataDisplay(ndpDaysTable);
            ndpDaysPager.setDisplay(ndpDaysTable);
            ndpDaysTable.setPageSize(getPageSize());

            logger.info("Created Ndp Special Day Table");
        }
    }

   public void sortDataProviderList() {
       dataProvider.refresh();
       if (columnSortHandler == null || columnSortHandler.getList() == null) {
           columnSortHandler = new ListHandler<NdpDayProfileData>(dataProvider.getList());

           columnSortHandler.setComparator(startColumn, new Comparator<NdpDayProfileData>() {
               public int compare(NdpDayProfileData o1, NdpDayProfileData o2) {
                   if (o1 == o2) {
                       return 0;
                   }
                   if (o1 == null) return -1;
                   if (o2 == null) return 1;

                   int result = o1.getNdpDayProfile().getStartHour().compareTo(o2.getNdpDayProfile().getStartHour());
                   if (result != 0) return result;

                   return o1.getNdpDayProfile().getEndMinute().compareTo(o2.getNdpDayProfile().getEndMinute());
               }
           });

           ndpDaysTable.addColumnSortHandler(columnSortHandler);
           columnSortList = ndpDaysTable.getColumnSortList();
           columnSortList.push(startColumn);
           ColumnSortEvent.fire(ndpDaysTable, columnSortList);
       } else {
           columnSortHandler.setList(dataProvider.getList());
           ColumnSortEvent.fire(ndpDaysTable, columnSortList);
       }
   }


   private String prefixWithZero (Integer i) {
       if (i < 10) {
           sb = new StringBuilder("0");
           sb.append(String.valueOf(i));
           return sb.toString();
       } else {
           return String.valueOf(i);
       }
   }

   protected void populateListBoxes() {
       monthListBox.clear();
       dayListBox.clear();

       for (int i=0; i<12; i++) {
               monthListBox.addItem(calmod.formatMonth(i), String.valueOf(i+1));
       }
       for (int i=1; i<32; i++) {
           dayListBox.addItem(String.valueOf(i), String.valueOf(i));
       }

       monthListBox.setSelectedIndex(0);
       dayListBox.setSelectedIndex(0);

       txtbxStartHour.setText("");
       txtbxStartMinute.setText("");
       txtbxEndHour.setText("");
       txtbxEndMinute.setText("");
   }

    public void display(NdpSpecialDay ndpSpecialDay) {
        this.ndpSpecialDay = ndpSpecialDay;

        txtbxStartHour.setText("");
        txtbxStartMinute.setText("");
        txtbxEndHour.setText("");
        txtbxEndMinute.setText("");

        if (ndpSpecialDay != null) {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
            monthListBox.setSelectedIndex(ndpSpecialDay.getNdpMonth() - 1);
            dayListBox.setSelectedIndex(ndpSpecialDay.getNdpDay() - 1);

            if (isInherited || isViewOnly) {
                setupInherited();
            }

            clientFactory.getNdpRpc().getNdpSpecialDayProfilesData(ndpSpecialDay.getId(), new ClientCallback<ArrayList<NdpDayProfileData>>() {
                @Override
                public void onSuccess(ArrayList<NdpDayProfileData> result) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    sortDataProviderList();
                }
            });
        } else {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
            monthListBox.setSelectedIndex(0);
            dayListBox.setSelectedIndex(0);

            if (isInherited) {             //if nothing to inherit!!  .i.e. no Global!!
                setupInherited();
            }

            dataProvider.getList().clear();
            dataProvider.refresh();
        }
    }

    private void disableDates() {
        monthListBox.setEnabled(false);
        dayListBox.setEnabled(false);
    }

    protected void setupInherited() {
        disableDates();
        specialDayDescrip.removeFromParent();
        enterTimesPanel.removeFromParent();
        btnSave.removeFromParent();
        btnCancel.removeFromParent();
        errorMsgTimes.removeFromParent();

        disableButtons();
        ndpDaysTable.removeColumn(deleteColumn);

        if (isInherited && !isViewOnly) {
            parentSchedulePanel.setInheritedPopupHeightForScrollBar(this.getOffsetHeight());
        }
    }

    @UiHandler("monthListBox")
    public void onStartMonthChange(ChangeEvent event) {
        onStartMonthChange();
    }

    //sets up the startDAYbox
    private void onStartMonthChange() {
        int s = getLastDayOfMonth(Integer.valueOf(monthListBox.getValue(monthListBox.getSelectedIndex())));
        dayListBox.clear();
        for (int i=1; i<=s; i++) {
            dayListBox.addItem(String.valueOf(i), String.valueOf(i));
        }
    }

    private int getLastDayOfMonth(int month) {
        int e = 31;
        if (month == 4
                || month == 6
                || month == 9
                || month == 11) {
            e=30;
        } else if (month == 2) {
            e=29;
        }
        return e;
    }

    @UiHandler("btnAddSpecialDayTimes")
    void addSpecialDayTimes(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                addNewDay();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnSave")
    void save(ClickEvent e) {
        //disable buttons to avoid clicking
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                disableButtons();
                saveNow();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    void cancel(ClickEvent e) {
        cancelPanel();
    }


    //-----------------------------------add Day to table -----------------------------------------------
    private void addNewDay() {
        //validate & add to dayTable
         btnAddSpecialDayTimes.setEnabled(false);

        if (!isValidTimes()) {
            btnAddSpecialDayTimes.setEnabled(true);
            return;
        }
        final NdpDayProfileData ndpDayProfileData = dayTimesMapFormToData();

        //check overlap
        clientFactory.getNdpRpc().setTimeInMillis(ndpDayProfileData, new ClientCallback<NdpDayProfileData>() {
            @Override
            public void onSuccess(NdpDayProfileData result) {
                if (checkTimeOverlap(result)) {
                    setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.time.already.assigned"));
                    return;
                } else {
                    refreshDayTable(result);
                }
            }
        });
    }

    private boolean isValidTimes() {
        boolean isValid = true;
        startTimeElement.clearErrorMsg();
        endTimeElement.clearErrorMsg();
        errorMsgTimes.setText("");
        errorMsgTimes.setVisible(false);

        if (txtbxStartHour.getText() == null || txtbxStartHour.getText().isEmpty()
                || txtbxStartMinute.getText() == null || txtbxStartMinute.getText().isEmpty()
                || txtbxEndHour.getText() == null || txtbxEndHour.getText().isEmpty()
                || txtbxEndMinute.getText() == null || txtbxEndMinute.getText().isEmpty()) {
            setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.nulls"));
            return false;
        }

        Integer startHour = Integer.valueOf(txtbxStartHour.getText());
        Integer startMinute = Integer.valueOf(txtbxStartMinute.getText());
        Integer endHour = Integer.valueOf(txtbxEndHour.getText());
        Integer endMinute = Integer.valueOf(txtbxEndMinute.getText());

        if (startHour.equals(endHour)) {
            if (startMinute > endMinute) {
                logger.info("NDP day profile time end time before start time");
                setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
                isValid = false;
            }
        } else if (startHour > endHour) {
            logger.info("NDP day profile time end before start ");
            setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
            isValid =  false;
        }
        return isValid;
    }

    private void setErrorMessage(String msg) {
        if (msg == null) {
            msg = "";
        }
        errorMsgTimes.setText(msg);
        errorMsgTimes.setVisible(msg != null || !(msg.trim().equals("")));
    }

    private NdpDayProfileData dayTimesMapFormToData() {
        NdpDayProfile ndp = new NdpDayProfile();
        //Comment - don't setup the ndpSpecialDayId here - it is used in the backend to decide whether to save this day (if it is null)
        ndp.setStartHour(Integer.valueOf(txtbxStartHour.getText()));
        ndp.setStartMinute(Integer.valueOf(txtbxStartMinute.getText()));
        ndp.setEndHour(Integer.valueOf(txtbxEndHour.getText()));
        ndp.setEndMinute(Integer.valueOf(txtbxEndMinute.getText()));

        return new NdpDayProfileData(ndp);
    }

    private boolean checkTimeOverlap(NdpDayProfileData newNdpDayProfileData) {
        boolean overlap = false;
        for (NdpDayProfileData data : dataProvider.getList()) {
            if (newNdpDayProfileData.getStartMs() >= data.getStartMs() && newNdpDayProfileData.getStartMs() <= data.getEndMs()
                    || newNdpDayProfileData.getEndMs() >= data.getStartMs() && newNdpDayProfileData.getEndMs() <= data.getEndMs()) {
                    overlap = true;
                    break;
            }
        }
        return overlap;
    }

    private void refreshDayTable(NdpDayProfileData ndpDayProfileData) {
        dataProvider.getList().add(ndpDayProfileData);
        sortDataProviderList();

        clearDayTimes();
        btnAddSpecialDayTimes.setEnabled(true);
        hasDirtyData.setDirtyData(true);
    }

    public void clearDayTimes() {
        clearErrors();
        txtbxStartHour.setText("");
        txtbxStartMinute.setText("");
        txtbxEndHour.setText("");
        txtbxEndMinute.setText("");
    }

    public void clearErrors() {
        specialDayElement.clearErrorMsg();
        startTimeElement.clearErrorMsg();
        endTimeElement.clearErrorMsg();
        errorMsgTimes.setText("");
        errorMsgTimes.setVisible(false);
    }

    //-----------------------save Panel -------------------------------------------------------------------
       private void saveNow() {

        if (!isValidSpecialDay()) {
            enableButtons();
            return;
        }

        if (ndpSpecialDay == null) {
            ndpSpecialDay = new NdpSpecialDay();
            ndpSpecialDay.setNdpScheduleId(ndpScheduleData.getNdpSchedule().getId());
        }
        ndpSpecialDay = specialDayMapFormToData(ndpSpecialDay);

        ArrayList<NdpDayProfileData> dayProfileList = new ArrayList<NdpDayProfileData>();
        for (NdpDayProfileData ndpd : dataProvider.getList()) {
            dayProfileList.add(ndpd);
        }
        final boolean isDayTimeEntered = dayProfileList.size() > 0;

        clientFactory.getNdpRpc().saveNdpSpecialDayAndTimes(ndpSpecialDay, dayProfileList, deleteTheseDayProfiles, new ClientCallback<NdpSpecialDay>() {
            @Override
            public void onSuccess(NdpSpecialDay result) {
                hasDirtyData.setDirtyData(false);
                parentSchedulePanel.addNewSpecialDay(result, isDayTimeEntered);
                if (isDeactivateSchedule) {
                    if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentSchedulePanel.getNdpParentPanel().isActiveBoxTicked()) {
                        parentSchedulePanel.getNdpParentPanel().deactivateSchedule();
                    } else {
                        parentSchedulePanel.getNdpParentPanel().disableActivate();
                    }
                }
            }

            @Override
            public void onFailureClient() {
                enableButtons();
            }
        });

    }

    private boolean isValidSpecialDay() {
        specialDayElement.clearErrorMsg();
        boolean valid = true;

        NdpSpecialDay tempDay = specialDayMapFormToData(new NdpSpecialDay());
        if (ndpSpecialDay != null) {
            tempDay.setId(ndpSpecialDay.getId());
        }

        if (parentSchedulePanel.checkDuplicate(tempDay)) {
            logger.info("Special day already assigned");
            specialDayElement.setErrorMsg(MessagesUtil.getInstance().getMessage("ndp.assign.special.day.duplicate"));
            valid = false;
        }
        return valid;
    }

    private NdpSpecialDay specialDayMapFormToData(NdpSpecialDay mapSpecialDay) {
        mapSpecialDay.setNdpDay(dayListBox.getSelectedIndex()+1);
        mapSpecialDay.setNdpMonth(monthListBox.getSelectedIndex()+1);
        return mapSpecialDay;
    }

    private void disableButtons() {
        btnSave.setEnabled(false);
        btnCancel.setEnabled(false);
        btnAddSpecialDayTimes.setEnabled(false);
    }

    private void enableButtons() {
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        btnAddSpecialDayTimes.setEnabled(true);
    }

    private void cancelPanel() {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    parentSchedulePanel.returnSpecialDaySetup();
                }
                else {
                    enableButtons();
                    return;
                }
            }
        });
    }

    //--------------DELETE TIMES --------------------------------------------------------------------------------------------------------

    private void deleteDayProfile(final NdpDayProfileData dayProfileData) {
        logger.info("delete this Special Day dayProfile, startHour= "+ dayProfileData.getNdpDayProfile().getStartHour());
        if (ndpSpecialDay == null) {
            confirmDeleteTimes(dayProfileData);      //because adding this extra special day adding a time & then deleting it now before have saved it, will have had no effect yet on current activatable status
        } else {
            clientFactory.getNdpRpc().isDayProfilePresentForSchedule(ndpScheduleData.getNdpSeasonList(), ndpScheduleData.getNdpSpecialDayList(), dayProfileData, deleteTheseDayProfiles, new ClientCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean result) {
                    if (result) {
                        confirmDeleteTimes(dayProfileData);
                    } else {
                        confirmDeleteTimesAndDeactivateSchedule(dayProfileData);
                    }
                }
            });
        }
    }

    private void confirmDeleteTimes(final NdpDayProfileData dayProfileData) {
        String timesToDelete = constructTimesString(dayProfileData);
        //Confirm Delete
        Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.day.profile.confirm.delete", new String[] {timesToDelete})),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            hasDirtyData.setDirtyData(true);
                            deleteTimes(dayProfileData);
                        }
                        else {
                            return;
                        }
                    }
                }
         );
    }

    private void deleteTimes(NdpDayProfileData dayProfileData) {
        //Take out of dataProvider List
        List<NdpDayProfileData> dayProfileList = dataProvider.getList();
        dayProfileList.remove(dayProfileData);
        sortDataProviderList();

        hasDirtyData.setDirtyData(true);
        deleteTheseDayProfiles.add(dayProfileData);
    }

    private void confirmDeleteTimesAndDeactivateSchedule(final NdpDayProfileData dayProfileData) {
        String timesToDelete = constructTimesString(dayProfileData);
        if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentSchedulePanel.getNdpParentPanel().isActiveBoxTicked()) {
            Dialogs.confirm (
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.special.day.times.confirm.delete.and.deactivate", new String[] {timesToDelete})),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                deleteTimes(dayProfileData);
                                isDeactivateSchedule = true;
                            }
                            else {
                                return;
                            }
                        }
                    }
                    );
        } else {
            confirmDeleteTimes(dayProfileData);
        }
    }

    private String constructTimesString(NdpDayProfileData dayProfileData) {
        return prefixWithZero(dayProfileData.getNdpDayProfile().getStartHour()) + ":" + prefixWithZero(dayProfileData.getNdpDayProfile().getStartMinute())
                + " - " + prefixWithZero(dayProfileData.getNdpDayProfile().getEndHour()) + ":" + prefixWithZero(dayProfileData.getNdpDayProfile().getEndMinute());
    }

    public HasDirtyData getHasDirtyData() {
        return hasDirtyData;
    }
}
