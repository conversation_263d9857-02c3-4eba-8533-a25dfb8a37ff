package za.co.ipay.metermng.client.view.component.meter;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.FreeIssueTokenIssuedEvent;
import za.co.ipay.metermng.client.event.FreeIssueTokenIssuedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.mdc.MdcTransactionView;
import za.co.ipay.metermng.client.view.component.usagepoint.UpMeterInstallHistoryView;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.view.workspace.meter.readings.view.MeterReadingsViews;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.shared.MeterHistData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.STSMeterHistData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class MeterInformation extends BaseComponent {

    private static MeterInformationUiBinder uiBinder = GWT.create(MeterInformationUiBinder.class);

    interface MeterInformationUiBinder extends UiBinder<Widget, MeterInformation> {
    }

    @UiField DisclosurePanel dsclsrEngineeringPanel;
    @UiField VerticalPanel engineeringPanelVP;

    @UiField DisclosurePanel dsclsrMeterTransPanel;
    @UiField VerticalPanel meterTransPanelVP;

    @UiField DisclosurePanel dsclsrMeterHistory;
    @UiField VerticalPanel meterHistoryVP;

    @UiField DisclosurePanel dsclsrMeterReportsSpecific;
    @UiField VerticalPanel meterReportsSpecificVP;

    @UiField DisclosurePanel dsclsrMeterReadingsPanel;
    @UiField VerticalPanel meterReadingsVP;

    @UiField DisclosurePanel dsclsrRegisterReadingPanel;
    @UiField VerticalPanel registerReadingVP;

    @UiField DisclosurePanel dsclsrMdcTransPanel;
    @UiField VerticalPanel mdcTransVP;

    private MeterInfoEngTokens meterInfoEngTokens;
    private MeterTransactionViews meterTransactions;
    private MeterHistoryView meterHistory;
    private StsMeterHistoryView stsMeterHistory;
    private MeterInfoRecharge meterInfoRecharge;
    private MeterReadingsViews usagePointMeterReadings;
    private RegisterReadingsView registerReadingsView;
    private MdcTransactionView mdcTransactionView;
    private UpMeterInstallHistoryView upMeterInstallHistoryView;

    private ClientFactory clientFactory;
    private MeterData meterData;
    private Long usagepointid = null;
    private ArrayList<STSMeterHistData> stsMeterHistoryList;
    private ArrayList<MeterHistData> meterHistoryList;
    private ArrayList<CustomerTransAlphaDataWithTotals> meterTransactionList;
    private ArrayList<CustomerTransAlphaData> meterVendTransactionList;

    private boolean usagePointActive;
    private Long freeIssueAuxAccountId;
    private Long custAgreementId;

    private UsagePointWorkspaceView usagePointWorkspaceView;

    public MeterInformation(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
    	initWidget(uiBinder.createAndBindUi(this));
    	init();
        checkPermissions();
    }

    protected void init() {
        clientFactory.getEventBus().addHandler(FreeIssueTokenIssuedEvent.TYPE, new FreeIssueTokenIssuedEventHandler() {
            @Override
            public void handleFreeIssueTokenIssuedEvent(FreeIssueTokenIssuedEvent event) {
                if (event.getMeter() != null &&
                        event.getMeter().getId() != null &&
                        meterData != null &&
                        meterData.getId() != null &&
                        event.getMeter().getId().equals(meterData.getId())) {

                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            if (meterTransactions != null && meterTransactions.isViewConstructed()) {
                                populateMeterTransactions();
                            }
                            if (meterInfoRecharge != null && meterInfoRecharge.isViewConstructed()) {
                                populateChrtMeterRechargeTransactions(meterInfoRecharge);
                            }
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }

            }
        });

        this.dsclsrEngineeringPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (meterInfoEngTokens == null || !meterInfoEngTokens.isViewConstructed()) {
                    meterInfoEngTokens = new MeterInfoEngTokens(clientFactory, usagePointWorkspaceView);
                    engineeringPanelVP.add(meterInfoEngTokens);
                }
                meterInfoEngTokens.setMeterInfo(meterData, usagepointid);
                meterInfoEngTokens.setFreeIssueAuxAccountId(freeIssueAuxAccountId);
                meterInfoEngTokens.setCustomerAgreementId(custAgreementId);
            }
        });

        this.dsclsrMeterTransPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (meterTransactions == null || !meterTransactions.isViewConstructed()) {
                    meterTransactions = new MeterTransactionViews(clientFactory, usagePointWorkspaceView);
                    meterTransPanelVP.add(meterTransactions);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateMeterTransactions();
                    }
                };
               clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        this.dsclsrMeterHistory.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (meterHistory == null || !meterHistory.isViewConstructed() || stsMeterHistory == null
                        || !stsMeterHistory.isViewConstructed() || upMeterInstallHistoryView == null
                        || !upMeterInstallHistoryView.isViewConstructed()) {
                    //for now construct & add them both - when populated with data will remove one
                    meterHistory = new MeterHistoryView(clientFactory);
                    stsMeterHistory = new StsMeterHistoryView(clientFactory);

                    upMeterInstallHistoryView = new UpMeterInstallHistoryView(clientFactory, true);
                    upMeterInstallHistoryView.addStyleName("container");

                    meterHistoryVP.add(meterHistory);
                    meterHistoryVP.add(stsMeterHistory);
                    meterHistoryVP.add(upMeterInstallHistoryView);
                }

                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        if (clientFactory.isEnableSTS() && meterData.getStsMeter() != null) {
                            populateStsMeterHistory();
                            meterHistory.removeFromParent();
                        } else {
                            populateMeterHistory();
                            stsMeterHistory.removeFromParent();
                        }
                        populateUpMeterInstallHistData();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        dsclsrMeterReportsSpecific.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (meterInfoRecharge == null || !meterInfoRecharge.isViewConstructed()) {
                    meterInfoRecharge = new MeterInfoRecharge(clientFactory);
                    meterReportsSpecificVP.add(meterInfoRecharge);
                }

                meterInfoRecharge.setMeterInfo(meterData);
                populateChrtMeterRechargeTransactions(meterInfoRecharge);
                if (usagePointWorkspaceView != null && usagePointWorkspaceView.getUsagePointData() != null) {
                    LocationData locationData = usagePointWorkspaceView.getUsagePointData().getServiceLocation();
                    locationData = locationData != null ? locationData : usagePointWorkspaceView.getUsagePointData().getUsagepointLocation();
                    meterInfoRecharge.setMapCoordinates(locationData);
                    meterInfoRecharge.setGisMetadata(usagePointWorkspaceView.loadGisMetadata(usagePointWorkspaceView.getUsagePointData()));
                }
                meterInfoRecharge.loadMapPanel();
                meterInfoRecharge.setWidth("100%");      //must set width here not in constructor call above. SetWidth after have added map
            }
        });

        dsclsrMeterReadingsPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (usagePointMeterReadings == null || !usagePointMeterReadings.isViewConstructed()) {
                    usagePointMeterReadings = new MeterReadingsViews(clientFactory, new MeterReadingsPlace(MeterReadingsPlace.USAGE_POINT_METER, MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single")), usagePointWorkspaceView);
                    usagePointMeterReadings.setSize("100%", "600px");
                    meterReadingsVP.add(usagePointMeterReadings);
                }
                usagePointMeterReadings.setSelectedMeter(new MeterDto(meterData.getId(), meterData.getMeterNum()), meterData.getMeterModelData().getServiceResourceId());
            }
        });

        dsclsrRegisterReadingPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (registerReadingsView == null || !registerReadingsView.isViewConstructed()) {
                    registerReadingsView = new RegisterReadingsView(clientFactory, usagePointWorkspaceView);
                    registerReadingsView.setWidth("100%");
                    registerReadingVP.add(registerReadingsView);
                }

                populateRegisterReadingsView();
            }
        });

        dsclsrMdcTransPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (mdcTransactionView == null || !mdcTransactionView.isViewConstructed()) {
                    mdcTransactionView = new MdcTransactionView(clientFactory);
                    mdcTransactionView.setWidth("100%");
                    mdcTransVP.add(mdcTransactionView);
                }

                populateMdcTrans();
            }
        });
    }

    private void checkPermissions() {
        MeterMngUser user = clientFactory.getUser();
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_READINGS)
                && !user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_READINGS_VIEW)) {
            dsclsrMeterReadingsPanel.removeFromParent();
        }

        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_TRANS_VIEW)) {
            dsclsrMdcTransPanel.removeFromParent();
        }
    }

    public void populateMeterHistory() {
        if (meterData != null) {
            meterHistory.setVisible(true);
            clientFactory.getMeterRpc().getMeterHistory(meterData.getId(),
                    clientFactory.isEnableAccessGroups(),
                    new ClientCallback<ArrayList<MeterHistData>>() {
                @Override
                public void onSuccess(ArrayList<MeterHistData> result) {
                    if (result != null) {
                        meterHistoryList = result;
                        meterHistory.setMeterHistoryList(meterHistoryList);
                    }
                }
            });
        }
    }

    public void populateStsMeterHistory() {
        if (meterData != null) {
            stsMeterHistory.setVisible(true);
            clientFactory.getMeterRpc().getStsMeterHistory(meterData.getId(),
                    clientFactory.isEnableAccessGroups(),
                    new ClientCallback<ArrayList<STSMeterHistData>>() {
                @Override
                public void onSuccess(ArrayList<STSMeterHistData> result) {
                    if (result != null) {
                        stsMeterHistoryList = result;
                        stsMeterHistory.setMeterHistoryList(stsMeterHistoryList);
                    }
                }
            });
        }
    }

    private void populateUpMeterInstallHistData() {
        if (meterData != null) {
            upMeterInstallHistoryView.setVisible(true);
            clientFactory.getUsagePointRpc().fetchUpMeterInstallHistory(null, meterData.getId(), clientFactory.isEnableAccessGroups(),
                    new ClientCallback<ArrayList<UpMeterInstallHistData>>() {
                        @Override
                        public void onSuccess(ArrayList<UpMeterInstallHistData> result) {
                            if (result != null) {
                                upMeterInstallHistoryView.setUpMeterInstallHistDataList(result);
                            }
                        }
                    });
        } else {
            upMeterInstallHistoryView.setVisible(false);
        }
    }

    public void populateMeterTransactions() {
        if (meterTransactions != null) {
            if (meterData != null) {
                meterTransactions.setVisible(true);
                clientFactory.getMeterRpc().getTransactionHistoryWithTotals(meterData.getId(), new ClientCallback<ArrayList<CustomerTransAlphaDataWithTotals>>() {
                    @Override
                    public void onSuccess(ArrayList<CustomerTransAlphaDataWithTotals> result) {
                        if (result != null) {
                            meterTransactionList = result;
                            MeterTransactionView meterTransactionView = meterTransactions.getTablePanel();
                            meterTransactionView.setMeterTransactionList(meterTransactionList);
                            meterTransactionView.setMeterData(meterData);
                            meterTransactions.getGraphPanel().populateTransactionData(meterTransactionList);
                        }
                    }
                });
            } else {
                meterTransactions.setVisible(false);
            }
        }
    }

    protected void populateChrtMeterRechargeTransactions(final MeterInfoRecharge meterInfoRecharge) {
        if (meterData != null) {
            clientFactory.getMeterRpc().getVendTransactionHistory(meterData.getId(), new ClientCallback<ArrayList<CustomerTransAlphaData>>() {
                @Override
                public void onSuccess(ArrayList<CustomerTransAlphaData> result) {
                    if (result != null) {
                        meterVendTransactionList = result;
                        meterInfoRecharge.chrtMeterRecharge.populateTransactionData(meterVendTransactionList);
                    }
                }
            });
        }
    }

    public void populateMdcTrans() {
        if (meterData != null && meterData.getMeterModelData() != null
                && meterData.getMeterModelData().getMdcId() != null) {
            dsclsrMdcTransPanel.setVisible(true);
            if (mdcTransactionView != null && mdcTransactionView.isViewConstructed()) {
                mdcTransactionView.setMdcTransactions(meterData, usagePointWorkspaceView);
            }
        }
    }

    protected void populateRegisterReadingsView() {
        if (meterData != null) {
            dsclsrRegisterReadingPanel.setVisible(true);
            meterData.setUsagePointId(usagePointWorkspaceView.getUsagePoint().getId());
            registerReadingsView.setMeterData(meterData);
        } else {
            dsclsrRegisterReadingPanel.setVisible(false);
        }
    }

    public MeterData getMeterData() {
        return meterData;
    }

    public void setMeterInfo(MeterData meterData, Long usagePointId) {
        this.usagepointid = usagePointId;
        if (meterData != null) {
            this.meterData = meterData;
            if (clientFactory.isEnableSTS() && meterData.getStsMeter() != null) {
                dsclsrEngineeringPanel.setVisible(true);
            } else {
                dsclsrEngineeringPanel.setVisible(false);
            }
            if (meterData.getMeterNum() != null) {
                dsclsrMeterReportsSpecific.getHeaderTextAccessor().setText(MessagesUtil.getInstance().getMessage("meter.reports.meter") + " " + meterData.getMeterNum());
            }
        }
    }

    public boolean isUsagePointActive() {
        return usagePointActive;
    }

    public void setUsagePointActive(boolean usagePointActive) {
        this.usagePointActive = usagePointActive;
    }

    public void setFreeIssueAuxAccountId(Long freeIssueAuxAccountId) {
        this.freeIssueAuxAccountId = freeIssueAuxAccountId;
    }

    public void setCustomerAgreementId(Long custAgreementId) {
        this.custAgreementId = custAgreementId;
    }

    public void meterInfoRechargeSetMapCoords(LocationData locationData) {
        if (meterInfoRecharge != null && meterInfoRecharge.isViewConstructed()) {
            meterInfoRecharge.setMapCoordinates(locationData);
        }
    }

    public void meterInfoRechargeSetGisMetadata(List<GisMetadata> gisMetadata) {
        if (meterInfoRecharge != null && meterInfoRecharge.isViewConstructed()) {
            meterInfoRecharge.setGisMetadata(gisMetadata);
        }
    }

    public void reloadMap() {
        if (meterInfoRecharge != null && meterInfoRecharge.isViewConstructed()) {
            meterInfoRecharge.loadMapPanel();
        }
    }

    public void openEngineeringTokenHistory() {
        dsclsrEngineeringPanel.setOpen(true);
    }

    public void openTransactionHistory() {
        dsclsrMeterTransPanel.setOpen(true);
    }

    public void updateEngineeringTokenUserRefStatus() {
        if (meterInfoEngTokens != null && meterInfoEngTokens.isViewConstructed()) {
            meterInfoEngTokens.updateEngineeringTokenUserRefStatusExNotify();
        }
    }

    public void refreshMeterHistoryAppSettings() {
        if (meterHistory != null && meterHistory.isViewConstructed()) {
            meterHistory.refreshCustomAppSettings();
        }
    }

    public MeterTransactionViews getMeterTransactions() {
        return meterTransactions;
    }

    public void refreshPowerLimitAppSettings() {
        if (mdcTransactionView != null && mdcTransactionView.isViewConstructed()) {
            mdcTransactionView.updatePowerLimits();
        }
    }
}
