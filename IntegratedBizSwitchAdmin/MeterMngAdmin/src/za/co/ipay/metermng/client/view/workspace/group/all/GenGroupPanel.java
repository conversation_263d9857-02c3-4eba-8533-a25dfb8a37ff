package za.co.ipay.metermng.client.view.workspace.group.all;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.metermng.client.view.component.BaseComponent;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class GenGroupPanel extends BaseComponent {
    
    @UiField FormElement parentElement;
    @UiField TextBox parentBox;
    @UiField FormElement hierarchyElement;
    @UiField TextBox nameBox;

    private static GenGroupPanelUiBinder uiBinder = GWT.create(GenGroupPanelUiBinder.class);

    interface GenGroupPanelUiBinder extends UiBinder<Widget, GenGroupPanel> {
    }

    public GenGroupPanel() {
        initWidget(uiBinder.createAndBindUi(this));
    }
}
