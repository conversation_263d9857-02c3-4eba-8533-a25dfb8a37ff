package za.co.ipay.metermng.client.view.menu;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;

public class PricingStructureMenuView extends BaseComponent {

    private static PricingStructureMenuViewUiBinder uiBinder = GWT.create(PricingStructureMenuViewUiBinder.class);
    @UiField FlowPanel pricingStructureLnk;
    @UiField FlowPanel auxTypeLnk;
    @UiField FlowPanel auxChargeScheduleLnk;
    @UiField FlowPanel calendarsLink;
    @UiField FlowPanel calendarSettingsLink;
    @UiField FlowPanel billingDetLnk;
    
    @UiField FlowPanel pricingMenu;
    
    interface PricingStructureMenuViewUiBinder extends UiBinder<Widget, PricingStructureMenuView> {
    }

    public PricingStructureMenuView(ClientFactory clientFactory) {
        initWidget(uiBinder.createAndBindUi(this));
    }
    
    public void checkPermissions(MeterMngUser user) {
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_PRICING_STRUCT_ADMIN)) {
            pricingStructureLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_AUX_TYPE_ADMIN)) {
            auxTypeLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_AUX_SCHEDULE_ADMIN)) {
            auxChargeScheduleLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_PS_CALENDAR_ADMIN)) {
            calendarsLink.removeFromParent();
            calendarSettingsLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_BILLING_DET_ADMIN)) {
            billingDetLnk.removeFromParent();
        }
    }
    
    public boolean isPricingMenuEmpty() {
        if (pricingMenu.getWidgetCount() < 1) {
            return true;   //IS empty
        } else {
            return false;  //has elements
        }
    }
}
