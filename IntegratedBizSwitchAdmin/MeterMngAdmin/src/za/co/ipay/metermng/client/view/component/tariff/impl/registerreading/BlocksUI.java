package za.co.ipay.metermng.client.view.component.tariff.impl.registerreading;

import java.util.List;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.metermng.shared.dto.BillingDetPrimaryDto;
import za.co.ipay.metermng.shared.dto.tariff.BlockDto;
import za.co.ipay.metermng.shared.tariff.units.UnitChargeData;

public class BlocksUI {

	private FormElement blockElement;
	private CellTable<BlockDto> blocksTable;
	private ListDataProvider<BlockDto> blocksDataProvider;
	
	private BillingDetPrimaryDto billingDetPrimaryDto;
	private List<UnitChargeData> blockUnitChargesBase;

	public BlocksUI() {
	}

	public BlocksUI(BillingDetPrimaryDto billingDetPrimaryDto, CellTable<BlockDto> blocksTable, List<UnitChargeData> blockUnitChargesBase) {
		super();
		this.billingDetPrimaryDto = billingDetPrimaryDto;
		this.blocksTable = blocksTable;
		this.blockUnitChargesBase = blockUnitChargesBase;
		
		this.blockElement = new FormElement();
		this.blockElement.add(new Label(billingDetPrimaryDto.getName()));   //getBillingDetNameLbl());
		this.blockElement.add(blocksTable);
		
		this.blocksDataProvider = new ListDataProvider<BlockDto>();
		this.blocksDataProvider.addDataDisplay(blocksTable);
	}

	public Long getBillingDetId() {
		return billingDetPrimaryDto.getId();
	}
	
    public String getBillingDetName() {
        return billingDetPrimaryDto.getName();
    }

	public FormElement getBlockElement() {
		return blockElement;
	}

	public void setBlockElement(FormElement blockElement) {
		this.blockElement = blockElement;
	}

	public CellTable<BlockDto> getBlocksTable() {
		return blocksTable;
	}

	public void setBlocksTable(CellTable<BlockDto> blocksTable) {
		this.blocksTable = blocksTable;
	}

	public ListDataProvider<BlockDto> getBlocksDataProvider() {
		return blocksDataProvider;
	}

	public void setBlocksDataProvider(ListDataProvider<BlockDto> blocksDataProvider) {
		this.blocksDataProvider = blocksDataProvider;
	}

    public BillingDetPrimaryDto getBillingDetPrimaryDto() {
        return billingDetPrimaryDto;
    }

    public void setBillingDetPrimaryDto(BillingDetPrimaryDto billingDetPrimaryDto) {
        this.billingDetPrimaryDto = billingDetPrimaryDto;
    }

    public List<UnitChargeData> getBlockUnitChargesBase() {
        return blockUnitChargesBase;
    }

    public void setBlockUnitChargesBase(List<UnitChargeData> blockUnitChargesBase) {
        this.blockUnitChargesBase = blockUnitChargesBase;
    }
}
