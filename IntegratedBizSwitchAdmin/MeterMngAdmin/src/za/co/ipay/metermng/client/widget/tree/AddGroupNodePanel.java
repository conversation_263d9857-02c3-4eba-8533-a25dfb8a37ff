package za.co.ipay.metermng.client.widget.tree;

import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.MridComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.shared.GenGroupData;

public class AddGroupNodePanel extends BaseComponent {

    private static AddGroupNodePanelUiBinder uiBinder = GWT.create(AddGroupNodePanelUiBinder.class);

    interface AddGroupNodePanelUiBinder extends UiBinder<Widget, AddGroupNodePanel> {
    }

    @UiField TextBox txtbxNew;
    @UiField FormElement txtbxNewElement;
    @UiField ListBox accessGroupBox;
    @UiField FormElement accessGroupElement;

    @UiField Button btnSave;
    @UiField Button btnCancel;

    private GenGroupData group;
    private GenGroupParentComponent view;
    private PopupPanel popupPanel;

    @UiField(provided = true) MridComponent mridComponent;

    private HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();

    private static Logger logger = Logger.getLogger(AddGroupNodePanel.class.getName());

    public AddGroupNodePanel(ClientFactory clientFactory, GenGroupData currentGroup, String groupNameLabel,
            GenGroupParentComponent parentView, PopupPanel popup, List<Group> accessGroups) {
        this.clientFactory = clientFactory;
        mridComponent = new MridComponent(clientFactory, hasDirtyData, currentGroup.getMrid(), currentGroup.isMridExternal());
        initWidget(uiBinder.createAndBindUi(this));
        if (accessGroups != null) {
            initAccessGroupBox(accessGroups);
            txtbxNewElement.setLabelText(groupNameLabel);
            txtbxNewElement.setHelpMsg(groupNameLabel);
            txtbxNewElement.setVisible(false);
        } else {
            accessGroupBox.setVisible(false);
            accessGroupElement.setVisible(false);
            txtbxNewElement.setLabelText(groupNameLabel);
            txtbxNewElement.setHelpMsg(groupNameLabel);
        }
        group = currentGroup;
        view = parentView;
        popupPanel = popup;

        txtbxNew.addKeyDownHandler(new EnterKeyHandler() {
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                saveNewGroupNode();
            }
        });
        mridComponent.getTxtbxMrid().addKeyDownHandler(new EnterKeyHandler() {
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                saveNewGroupNode();
            }
        });
        addFieldHandlers();
    }

    private void initAccessGroupBox(List<Group> accessGroups) {
        if (accessGroups.isEmpty()) {
            accessGroupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("groupnode.field.access_group.error.no_groups"));
        } else {
            accessGroupBox.addItem("");
            for (Group group : accessGroups) {
                accessGroupBox.addItem(group.getName(), group.getId().toString());
            }
        }
    }

    private void addFieldHandlers() {

        txtbxNew.addValueChangeHandler(new ValueChangeHandler<String>() {
            @Override
            public void onValueChange(ValueChangeEvent<String> event) {
                if ((txtbxNew.getValue() != null && !txtbxNew.getValue().trim().isEmpty())) {
                    hasDirtyData.setDirtyData(true);
                } else {
                    hasDirtyData.setDirtyData(false);
                }
            }
        });

        accessGroupBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    public void setFocusOnTextBox() {
        txtbxNew.setFocus(true);
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                saveNewGroupNode();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    void handleCancelButton(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        popupPanel.hide();
                    }
                }
            });
        } else {
            popupPanel.hide();
        }
        view.reLoadGroups(group);
    }

    @UiHandler("txtbxNew")
    void handleGetFoucus(FocusEvent event) {
        txtbxNewElement.clearErrorMsg();
    }

    protected void saveNewGroupNode() {
        boolean valid = true;
        txtbxNewElement.clearErrorMsg();
        accessGroupElement.clearErrorMsg();
        mridComponent.clearErrorMsg();

        if (accessGroupBox.isVisible()) {
            if (ValidateUtil.isNullOrBlank(accessGroupBox.getSelectedValue())) {
                valid = false;
                accessGroupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
            } else {
                group.setName(accessGroupBox.getSelectedItemText());
                group.setAccessGroupId(Long.parseLong(accessGroupBox.getSelectedValue()));
            }
        } else if (!(txtbxNew.getValue().trim().isEmpty())) {
            group.setName(txtbxNew.getValue());
        } else {
            valid = false;
            txtbxNewElement.showErrorMsg(txtbxNewElement.getLabelText());
        }

        if (mridComponent.validate()) {
            group.setMrid(mridComponent.getMrid());
            group.setMridExternal(mridComponent.isExternal());
        } else {
            valid = false;
        }

        final boolean isValid = valid;
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().isGenGroupNameUnique(group, new ClientCallback<Boolean>() {
                    @Override
                    public void onSuccess(final Boolean nameIsUnique) {
                        clientFactory.getGroupRpc().isGenGroupMridUnique(group, new ClientCallback<Boolean>() {
                            @Override
                            public void onSuccess(final Boolean mridIsUnique) {
                                if (isValid && nameIsUnique && mridIsUnique && mridComponent.getMrid().length() <= 100) {
                                    view.saveGenGroup(group);
                                    popupPanel.hide();
                                } else {
                                    if (!mridIsUnique) {
                                        mridComponent.getTxtbxMridElement().showErrorMsg(MessagesUtil.getInstance().getMessage("gen.group.mrid.external.unique.validation"));
                                    }
                                    if (!(mridComponent.getMrid().length() <= 100)) {
                                        mridComponent.getTxtbxMridElement().showErrorMsg(MessagesUtil.getInstance().getMessage("gen.group.mrid.external.length.validation"));
                                    }
                                    if (!nameIsUnique) {
                                        txtbxNewElement.showErrorMsg(MessagesUtil.getInstance().getMessage("group.error.name.nonunique"));
                                    }
                                }
                            }
                        });
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void populateEditInputs(GenGroupData group) {
        txtbxNew.setValue(group.getName());
    }

    public void populateAddNewMrid() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getMeterRpc().getNewMrid(new ClientCallback<String>() {
                    @Override
                    public void onSuccess(String result) {
                        mridComponent.setMrid(result);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

}
