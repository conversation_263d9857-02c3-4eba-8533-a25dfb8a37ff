<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client" 
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:p3="urn:import:za.co.ipay.metermng.client.view.component"
    xmlns:p4="urn:import:za.co.ipay.metermng.client.view.component.meter"
    xmlns:form="urn:import:za.co.ipay.gwt.common.client.form">
    
	<ui:style>
        .cellTable {
            border-bottom: 1px solid #ccc;
            text-align: left; 
            margin-bottom: 4px;
        }		
    </ui:style> 
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">
        <g:north size="30">
            <form:PageHeader heading="" ui:field="pageHeader" />
        </g:north>
    
        <g:center>
            <g:ScrollPanel>
                <g:FlowPanel width="99%" height="100%">
                    <g:HTMLPanel>
                        <g:HTML ui:field="dataName" text="" styleName="dataTitle"  />
                        <g:HTML ui:field="dataDescription" text="" styleName="dataDescription"  />
			            <g:HorizontalPanel spacing="3">
			                <g:Cell verticalAlignment="ALIGN_MIDDLE">
			                    <g:Label text="{msg.getMeterHistoryFilter}:" styleName="gwt-Label-bold"/>
			                </g:Cell>
			                <g:Cell verticalAlignment="ALIGN_MIDDLE">
			                    <g:ListBox visibleItemCount="1" name="Filter" ui:field="filterDropdown"/>
			                </g:Cell>
			                <g:Cell verticalAlignment="ALIGN_MIDDLE">
			                    <g:TextBox ui:field="txtbxfilter"/>
			                </g:Cell>
			            </g:HorizontalPanel>
                        <g:HTML ui:field="tableTitle" text="" styleName="pageSectionTitle" />            
                        <c:CellTable ui:field="clltblMeters"/>
                        <p2:TablePager ui:field="smplpgrMeters"  styleName="pager" location="CENTER" /> 
                        <p></p>
                    </g:HTMLPanel>
          
                    <g:FlowPanel ui:field="meterpanel" width="100%">
                        <p3:MeterComponent ui:field="metercomponent"></p3:MeterComponent>
                    </g:FlowPanel>
                    <p4:MeterInformation ui:field="meterInfo"></p4:MeterInformation>
                </g:FlowPanel>
            </g:ScrollPanel>
        </g:center>
    </g:DockLayoutPanel>
	
</ui:UiBinder> 
		