package za.co.ipay.metermng.client.view.workspace.schedule;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.ScheduleUi;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

public class TaskSchedulePanel extends SimpleFormPanel {
        
    @UiField CheckBox activeBox;
    @UiField TextBox nameTextBox;
    @UiField ListBox scheduleBox;
    
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement scheduleElement;
    
    @UiField VerticalPanel schedulePanel;

    private static TaskSchedulePanelUiBinder uiBinder = GWT.create(TaskSchedulePanelUiBinder.class);

    interface TaskSchedulePanelUiBinder extends UiBinder<Widget, TaskSchedulePanel> {
    }

    public TaskSchedulePanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }    
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        scheduleBox.setSelectedIndex(0);
        activeBox.setValue(true);
        clearSchedulePanel();
    }
    
    protected void clearSchedulePanel() {
        if (schedulePanel.getWidgetCount() == 1) {
            ((ScheduleUi) schedulePanel.getWidget(0)).removeFieldHandlers();
            schedulePanel.remove(0);
        }
    }

    public void clearErrors() {
        activeElement.setErrorMsg(null);
        nameElement.setErrorMsg(null);
        scheduleElement.setErrorMsg(null);
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        scheduleBox.addChangeHandler(new FormDataChangeHandler(form));
    }
}
