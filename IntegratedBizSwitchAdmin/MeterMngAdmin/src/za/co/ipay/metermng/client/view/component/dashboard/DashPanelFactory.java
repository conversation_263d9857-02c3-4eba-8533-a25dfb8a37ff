package za.co.ipay.metermng.client.view.component.dashboard;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;

public class DashPanelFactory {

    private static Logger logger = Logger.getLogger(DashPanelFactory.class.getName());

    private DashPanelFactory() {}

    public static DashPanel getDashPanel(String panelName, ClientFactory clientFactory) {
        String pan = panelName.replaceAll("\\W", "").toUpperCase();

        switch (pan) {
            case "SALESPERRESOURCE":
                return new SalesPerResourcePanel(clientFactory);

            case "VENDINGACTIVITY":
                return new VendingActivityPanel(clientFactory);

            case "USAGEPOINTGROUPSADDED":
                return new UsagePointGroupsAddedPanel(clientFactory);

            case "KEYINDICATOR":
                return new KeyIndicatorPanel(clientFactory);

            case "METERCOUNT":
                return new MeterCountPanel(clientFactory);

            case "BUYINGINDEX":
                return new BuyingIndexPanel(clientFactory);

            default:
                logger.info("Unrecognised panel name: " + pan);
                return null;
        }
    }

}
