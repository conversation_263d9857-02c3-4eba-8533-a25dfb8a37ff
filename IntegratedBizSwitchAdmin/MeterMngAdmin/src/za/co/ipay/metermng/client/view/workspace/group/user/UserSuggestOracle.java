package za.co.ipay.metermng.client.view.workspace.group.user;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.user.client.ui.SuggestOracle;

public class UserSuggestOracle extends SuggestOracle {
    
    private ClientFactory clientFactory;
    
    private static Logger logger = Logger.getLogger(UserSuggestOracle.class.getName());
    
    public UserSuggestOracle(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
    }

    @Override
    public void requestSuggestions(final Request request, final Callback callback) {
        if (request.getQuery().trim().length() > 1) {
            logger.info("Getting user suggestions..."+request.getQuery());
            clientFactory.getUserRpc().getUserSuggestions(request.getQuery(), 
                                                          request.getLimit(), 
                    new ClientCallback<List<UserData>>() {
                        @Override
                        public void onSuccess(List<UserData> users) {
                            Collection<Suggestion> result = new LinkedList<Suggestion>();
                            for (UserData user : users) {
                                result.add(new UserSuggestion(user));
                            }
                            Response response = new Response(result);
                            callback.onSuggestionsReady(request, response);
                        }                
                    }); 
        } else {
            Response response = new Response(Collections.<Suggestion> emptyList());
            callback.onSuggestionsReady(request, response);
        }
    }
}
