package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class SelectAccessGroupPlace extends Place {
    
    public static final String SELECTACCESSGROUP_PLACE_PREFIX = "selectaccessgroup"; 

    public SelectAccessGroupPlace() {
    }

    @Prefix(value = SELECTACCESSGROUP_PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<SelectAccessGroupPlace> {
        @Override
        public String getToken(SelectAccessGroupPlace place) {
            return "all";
        }

        @Override
        public SelectAccessGroupPlace getPlace(String token) {
            return new SelectAccessGroupPlace();
        }
    }
}

