package za.co.ipay.metermng.client.view.component;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.user.client.ui.Composite;

public class BaseComponent extends Composite {
    
    protected ClientFactory clientFactory;

    /**
     * This is a Ui factory method that allows all the workspace's  UiBinder XML files to use the same instance of the UiMessages.
     * @return The UiMessages used to get i18n strings for the UiBinder XML files.
     */
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }
    
    protected int getPageSize() {
        return MeterMngStatics.DEFAULT_PAGE_SIZE;
    }

    public void setClientFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
    }

}





