package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import za.co.ipay.metermng.client.event.AuxTransUploadEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class AuxTransUploadActivity extends AbstractActivity {

	private ClientFactory clientFactory;
	private AuxTransUploadPlace place;

	public AuxTransUploadActivity(AuxTransUploadPlace place, ClientFactory clientFactory) {
		super();
		this.clientFactory = clientFactory;
		this.place = place;
	}

	@Override
	public void start(AcceptsOneWidget panel, EventBus eventBus) {
		clientFactory.getEventBus().fireEvent(new AuxTransUploadEvent(place.getName()));
	}

}
