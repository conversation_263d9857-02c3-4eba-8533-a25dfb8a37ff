package za.co.ipay.metermng.client.view.workspace.supplygroup;

import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.IntegerValueBox;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class SupplyGroupPanel extends SimpleFormPanel {    
    
    //FormElements used to display validation messages, etc
    @UiField FormElement nameElement;
    @UiField FormElement codeElement;
    @UiField FormElement keyRevisionNumberElement;
    @UiField FormElement activeElement;
    @UiField FormElement kmcExpiryDateElement;
    @UiField FormElement targetSupGrCdeElement;
    @UiField FormElement isDefaultElement;

    //Actual fields wrapped by the FormElements above
    @UiField TextBox nameBox;
    @UiField IntegerValueBox codeBox;
    @UiField ListBox keyRevisionNumberBox;
    @UiField DateBox kmcExpiryDateBox;
    @UiField CheckBox activeBox;
    @UiField Label baseDateLabel;
    @UiField IpayListBox lstbxTargetSupGrCde;
    @UiField CheckBox isDefaultBox;
    @UiField Label isInUseByMeterLbl;

    private static SupplyGroupPanelUiBinder uiBinder = GWT.create(SupplyGroupPanelUiBinder.class);

    interface SupplyGroupPanelUiBinder extends UiBinder<Widget, SupplyGroupPanel> {
    }

    public SupplyGroupPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        kmcExpiryDateBox.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        addFieldHandlers();
    }
    
    public void addFieldHandlers() {
        nameBox.addChangeHandler(new FormDataChangeHandler(form));
        codeBox.addChangeHandler(new FormDataChangeHandler(form));
        keyRevisionNumberBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        kmcExpiryDateBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        lstbxTargetSupGrCde.addChangeHandler(new FormDataChangeHandler(form));
        isDefaultBox.addClickHandler(new FormDataClickHandler(form));
    }

    public void clearFields() {
        isInUseByMeterLbl.setVisible(false);
        form.setDirtyData(false);
        nameBox.setText("");
        codeBox.setText("");
        keyRevisionNumberBox.setSelectedIndex(0);
        kmcExpiryDateBox.setValue(null);
        activeBox.setValue(true);
        lstbxTargetSupGrCde.setSelectedIndex(0);
        baseDateLabel.setText("");
        isDefaultBox.setValue(false);
    }

    public void isInUseByMeter(boolean isInUse) {
        isInUseByMeterLbl.setVisible(isInUse);
        keyRevisionNumberBox.setEnabled(!isInUse);
        codeBox.setEnabled(!isInUse);
        nameBox.setEnabled(!isInUse);
        kmcExpiryDateBox.setEnabled(!isInUse);
        activeBox.setEnabled(!isInUse);
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        codeElement.clearErrorMsg();
        keyRevisionNumberElement.clearErrorMsg();
        kmcExpiryDateElement.clearErrorMsg();
        activeElement.clearErrorMsg();
        isDefaultElement.clearErrorMsg();
    }
    
    public Long getSelectedTargetSgcId() {
        if (lstbxTargetSupGrCde.getSelectedIndex() > 0) {
            return Long.valueOf(lstbxTargetSupGrCde.getValue(lstbxTargetSupGrCde.getSelectedIndex()));
        } else {
            return null;
        }
    }
    
    public Short getSelectedTargetSgcBaseDate() {
        if (lstbxTargetSupGrCde.getSelectedIndex() > 0) {
            Map<String, Object> extraInfoMap = lstbxTargetSupGrCde.getItem(lstbxTargetSupGrCde.getSelectedIndex()).getExtraInfoMap();
            if (extraInfoMap != null && extraInfoMap.get("baseDate") != null) {
                return Short.valueOf(String.valueOf(extraInfoMap.get("baseDate")));
            } 
        } 
        return null;
    }
}
