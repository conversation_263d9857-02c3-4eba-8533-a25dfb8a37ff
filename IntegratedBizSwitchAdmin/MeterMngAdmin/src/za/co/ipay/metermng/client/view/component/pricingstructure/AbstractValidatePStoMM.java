package za.co.ipay.metermng.client.view.component.pricingstructure;

import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.dto.PSDto;

public abstract class AbstractValidatePStoMM {

    public abstract void errorNoTariff();
    public abstract void noneMatchContinue();
    public abstract void partialMatchConfirmContinue();
    public abstract void partialMatchDenyContinue();
    public abstract void exactMatchOrNoDataContinue();

    public AbstractValidatePStoMM() {
    }

    public void isregReadPsSameBillingDetsAsMeterModel(final ClientFactory clientFactory, final List<PSDto> pricingStructureDtos,
            final Long mdcId, final String userName, final Logger logger, final String componentName) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                clientFactory.getPricingStructureRpc().isregReadPsSameBillingDetsAsMeterModel(pricingStructureDtos, mdcId, userName, new ClientCallback<List<ChannelCompatibilityE>>() {
                    @Override
                    public void onSuccess(List<ChannelCompatibilityE> results) {
                        final Messages messages = MessagesUtil.getInstance();
                        if (results.contains(ChannelCompatibilityE.ERROR_NOTARIFF)) {
                            logger.info(componentName + ": isregReadPsSameBillingDetsAsMeterModel = ERROR_NOTARIFF. ERROR displayed");
                            Dialogs.centreErrorMessage(messages.getMessage("error.pricingStructure.future.ps.no.tariff.at.date"),
                                    MediaResourceUtil.getInstance().getErrorIcon(),
                                    MessagesUtil.getInstance().getMessage("button.close"),
                                    new ClickHandler() {
                                        @Override
                                        public void onClick(ClickEvent arg0) {
                                            errorNoTariff();
                                        }
                                    });
                            return;
                        } else if (results.contains(ChannelCompatibilityE.NONE_MATCH)) {
                            logger.info(componentName + ": isregReadPsSameBillingDetsAsMeterModel = NONE_MATCH. ERROR displayed");
                            Dialogs.centreErrorMessage(messages.getMessage("error.pricingStructure.billingDets.notsame.asmetermodel.mdc"),
                                    MediaResourceUtil.getInstance().getErrorIcon(),
                                    MessagesUtil.getInstance().getMessage("button.close"),
                                    new ClickHandler() {
                                        @Override
                                        public void onClick(ClickEvent arg0) {
                                            noneMatchContinue();
                                        }
                                    });
                            return;
                        } else if (results.contains(ChannelCompatibilityE.PARTIAL_MATCH)) {
                            logger.info(componentName + ": isregReadPsSameBillingDetsAsMeterModel = PARTIAL_MATCH. Warning displayed");
                            Dialogs.confirm(messages.getMessage("warning.pricingStructure.billingDets.notsame.asmetermodel.mdc"),
                                    messages.getMessage("button.yes"),
                                    messages.getMessage("button.no"),
                                    MediaResourceUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                                        @Override
                                        public void confirmed(boolean confirm) {
                                            if(confirm) {
                                                partialMatchConfirmContinue();
                                            } else {
                                                partialMatchDenyContinue();
                                            }
                                        }
                                    });
                        } else {
                            //EXACT_MATCH or NO_DATA(ThinUnits payment mode or Appsetting says only check RegRead Tariffs)
                            exactMatchOrNoDataContinue();
                        }
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

}
