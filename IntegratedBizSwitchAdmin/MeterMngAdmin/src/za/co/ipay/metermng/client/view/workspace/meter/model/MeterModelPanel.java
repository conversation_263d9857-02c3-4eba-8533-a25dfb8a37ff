package za.co.ipay.metermng.client.view.workspace.meter.model;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.component.MridComponent;
import za.co.ipay.metermng.datatypes.ServiceResourceE;

public class MeterModelPanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    @UiField ListBox manufacturerBox;
    @UiField ListBox serviceResourceBox;
    @UiField ListBox meterTypeBox;
    @UiField ListBox paymentModesBox;
    @UiField CheckBox toaBox;
    @UiField ListBox mdcBox;
    @UiField CheckBox balSyncBox;
    @UiField CheckBox needsBreakerIdBox;
    @UiField CheckBox messageDisplayBox;
    @UiField CheckBox needsEncryptionKeyBox;
    @UiField CheckBox uriPresentBox;
    @UiField BigDecimalValueBox batteryCapacityTextBox;
    @UiField PercentageTextBox lowThresholdPercentTextBox;
    @UiField FormGroupPanel batteryPanel;
    @UiField FormGroupPanel mdcGroupPanel;


    @UiField FormElement manufacturerElement;
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement serviceResourceElement;
    @UiField FormElement meterTypeElement;
    @UiField FormElement paymentModesElement;
    @UiField FormElement toaElement;
    @UiField FormElement mdcElement;
    @UiField FormElement balSyncElement;
    @UiField FormElement needsBreakerIdElement;
    @UiField FormElement uriPresentElement;
    @UiField FormElement messageDisplayElement;
    @UiField FormElement needsEncryptionKeyElement;
    @UiField FormElement batteryCapacityElement;
    @UiField FormElement lowThresholdPercentElement;
   
    @UiField FormElement meterPhaseElement;
    @UiField FormElement dataDecoderElement;
    @UiField ListBox meterPhaseBox;
    @UiField ListBox dataDecoderBox;
    @UiField Label meterModelInUseLabel;
    @UiField (provided=true)MridComponent mridComponent;

    private static MeterModelPanelUiBinder uiBinder = GWT.create(MeterModelPanelUiBinder.class);

    interface MeterModelPanelUiBinder extends UiBinder<Widget, MeterModelPanel> {
    }

    public MeterModelPanel(SimpleForm form) {
        super(form);
        mridComponent = new MridComponent();
        initWidget(uiBinder.createAndBindUi(this));
        clearFields();
        addFieldHandlers();
    }

    public void checkEnableSTS(boolean enableSTS) {
        if (!enableSTS) {
            toaElement.removeFromParent();
            toaBox.removeFromParent();
        }
    }

    public void clearFields() {
        form.setDirtyData(false);
        mridComponent.setMrid(null);
        mridComponent.setIsExternal(false);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        activeBox.setValue(true);
        manufacturerBox.setSelectedIndex(0);
        serviceResourceBox.setSelectedIndex(0);
        meterTypeBox.setSelectedIndex(0);
        mdcBox.setSelectedIndex(0);
        meterPhaseElement.setVisible(false);
        meterPhaseBox.setSelectedIndex(0);
        if (paymentModesBox.getItemCount() > 0) {
            for(int i=0;i<paymentModesBox.getItemCount();i++) {
                paymentModesBox.setItemSelected(i, false);
            }
        }
        clearMdcFeatures();
        hasAttachedMeters(false);
    }

    public void clearMdcFeatures() {
        mdcGroupPanel.setVisible(false);
        toaBox.setValue(false);
        balSyncBox.setValue(false);
        needsBreakerIdBox.setValue(false);
        messageDisplayBox.setValue(false);
        needsEncryptionKeyBox.setValue(false);
        uriPresentBox.setValue(false);
        batteryCapacityTextBox.setValue(null);
        lowThresholdPercentTextBox.getBigDecimalValueBox().setText("");
        dataDecoderBox.setSelectedIndex(0);
    }

    public void hasAttachedMeters(boolean hasMeters) {
        meterModelInUseLabel.setVisible(hasMeters);
        activeBox.setEnabled(!hasMeters);
        needsEncryptionKeyBox.setEnabled(!hasMeters);
        needsBreakerIdBox.setEnabled(!hasMeters);
        serviceResourceBox.setEnabled(!hasMeters);
        meterTypeBox.setEnabled(!hasMeters);
        uriPresentBox.setEnabled(!hasMeters);
    }

    public void clearErrors() {
        activeElement.clearErrorMsg();
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        manufacturerElement.setErrorMsg(null);
        serviceResourceElement.setErrorMsg(null);
        meterTypeElement.setErrorMsg(null);
        toaElement.clearErrorMsg();
        mdcElement.setErrorMsg(null);
        paymentModesElement.setErrorMsg(null);
        balSyncElement.clearErrorMsg();
        needsBreakerIdElement.clearErrorMsg();
        uriPresentElement.clearErrorMsg();
        messageDisplayElement.clearErrorMsg();
        needsEncryptionKeyElement.clearErrorMsg();
        meterPhaseElement.clearErrorMsg();
        dataDecoderElement.clearErrorMsg();
        batteryCapacityElement.clearErrorMsg();
        lowThresholdPercentElement.clearErrorMsg();
        mridComponent.clearErrorMsg();
        mdcElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        manufacturerBox.addChangeHandler(new FormDataChangeHandler(form));
        serviceResourceBox.addChangeHandler(new FormDataChangeHandler(form));
        meterTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        toaBox.addClickHandler(new FormDataClickHandler(form));
        paymentModesBox.addChangeHandler(new FormDataChangeHandler(form));
        balSyncBox.addClickHandler(new FormDataClickHandler(form));
        needsBreakerIdBox.addClickHandler(new FormDataClickHandler(form));
        messageDisplayBox.addClickHandler(new FormDataClickHandler(form));
        needsEncryptionKeyBox.addClickHandler(new FormDataClickHandler(form));
        uriPresentBox.addClickHandler(new FormDataClickHandler(form));
        mdcBox.addChangeHandler(new FormDataChangeHandler(form));
        meterPhaseBox.addClickHandler(new FormDataClickHandler(form));
        serviceResourceBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent arg0) {
                checkServiceResource();
            }
        });
        dataDecoderBox.addClickHandler(new FormDataClickHandler(form));
        batteryCapacityTextBox.addChangeHandler(new FormDataChangeHandler(form));
        lowThresholdPercentTextBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        mridComponent.addFieldHandlers(form);
    }
    
    void checkServiceResource() {
        meterPhaseElement.setVisible(serviceResourceBox.getSelectedIndex() > 0
                && ServiceResourceE.ELEC.getId() == Long.valueOf(serviceResourceBox.getSelectedValue()));
    }
}
