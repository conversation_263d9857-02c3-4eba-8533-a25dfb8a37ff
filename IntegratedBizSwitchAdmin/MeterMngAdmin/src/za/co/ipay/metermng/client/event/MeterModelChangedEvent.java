package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;
public class MeterModelChangedEvent extends GwtEvent<MeterModelChangedEventHandler> {
    
    public static Type<MeterModelChangedEventHandler> TYPE = new Type<MeterModelChangedEventHandler>();

    @Override
    public Type<MeterModelChangedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MeterModelChangedEventHandler handler) {
        handler.processEvent(this);
    }
}
