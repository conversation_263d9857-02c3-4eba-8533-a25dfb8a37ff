package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SpecialActionReasonsUpdatedEvent extends GwtEvent<SpecialActionReasonsUpdatedEventHandler> {

    public static Type<SpecialActionReasonsUpdatedEventHandler> TYPE = new Type<SpecialActionReasonsUpdatedEventHandler>();
    
    public SpecialActionReasonsUpdatedEvent() {
        
    }
    
	@Override
    public Type<SpecialActionReasonsUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SpecialActionReasonsUpdatedEventHandler handler) {
        handler.processSpecialActionReasonsUpdatedEvent(this);
    }


}
