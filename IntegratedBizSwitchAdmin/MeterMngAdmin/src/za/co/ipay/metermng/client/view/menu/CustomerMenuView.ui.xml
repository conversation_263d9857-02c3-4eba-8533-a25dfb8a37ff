<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui">
	<ui:style>
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel width="100%" height="100%" ui:field="customerMenu">

		<g:FlowPanel styleName="menuLink" ui:field="addCustomerLink"><g:InlineHyperlink debugId="addCustomerLink" text="{msg.getAddCustomer}" targetHistoryToken="customer:new"/></g:FlowPanel>
        <g:FlowPanel styleName="menuLink" ui:field="transactionUploadLink"><g:InlineHyperlink debugId="transactionUploadLink" text="{msg.getTransUploadHeading}" targetHistoryToken="customertransupload:all"/></g:FlowPanel>
        <g:FlowPanel styleName="menuLink" ui:field="auxTransUploadLink"><g:InlineHyperlink debugId="auxTransUploadLink" text="{msg.getAuxTransactionUpload}" targetHistoryToken="auxtransupload:all"/></g:FlowPanel>
        <g:FlowPanel styleName="menuLink" ui:field="auxAccountUploadLink"><g:InlineHyperlink debugId="auxAccountUploadLink" text="{msg.getAuxAccountUpload}" targetHistoryToken="auxaccountupload:all"/></g:FlowPanel>

	</g:FlowPanel>

</ui:UiBinder> 