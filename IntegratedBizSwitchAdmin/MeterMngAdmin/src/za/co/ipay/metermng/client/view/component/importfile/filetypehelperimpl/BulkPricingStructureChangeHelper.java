package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class BulkPricingStructureChangeHelper extends BaseFiletypeHelper {
    public BulkPricingStructureChangeHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getBulkPricingStructureChangeImportRecord().getMeterNum();
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
    }
}
