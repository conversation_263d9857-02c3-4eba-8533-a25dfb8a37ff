package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenEnergyBalancingEvent extends GwtEvent<OpenEnergyBalancingEventHandler> {

    public static Type<OpenEnergyBalancingEventHandler> TYPE = new Type<OpenEnergyBalancingEventHandler>();
    
    private String meter;
    
    public OpenEnergyBalancingEvent(String meter) {
        this.meter = meter;
    }

    public String getMeter() {
        return meter;
    }

    @Override
    public Type<OpenEnergyBalancingEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenEnergyBalancingEventHandler handler) {
        handler.openEnergyBalaning(this);
    }
}
