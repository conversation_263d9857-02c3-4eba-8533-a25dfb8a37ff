package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouDayProfileTimeData;
import za.co.ipay.metermng.shared.TouSeasonDateData;
import za.co.ipay.metermng.shared.TouSpecialDayData;
import za.co.ipay.metermng.shared.dto.pricing.TouCalendarSeasonsPeriodsSpecialDaysData;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;
import com.google.gwt.user.client.ui.SuggestOracle.Request;
import com.google.gwt.user.client.ui.SuggestOracle.Response;

@RemoteServiceRelativePath("secure/calendar.do")
public interface CalendarRpc extends RemoteService {
    
    //Season
    public ArrayList<TouSeason> getSeasons() throws ValidationException, ServiceException, AccessControlException;
    public Response getSeasonSuggestions(Request req) throws ServiceException;
    public TouSeason addSeason(TouSeason add) throws ValidationException, ServiceException, AccessControlException;
    public TouSeason updateSeason(TouSeason updated) throws ValidationException, ServiceException, AccessControlException;
    public TouSeason deleteSeason(TouSeason del) throws ServiceException, AccessControlException;
    
    //Period
    public ArrayList<TouPeriod> getPeriods() throws ValidationException, ServiceException, AccessControlException;
    public TouPeriod addPeriod(TouPeriod add) throws ValidationException, ServiceException, AccessControlException;
    public TouPeriod updatePeriod(TouPeriod updated) throws ValidationException, ServiceException, AccessControlException;
    public TouPeriod deletePeriod(TouPeriod del) throws ServiceException, AccessControlException;
    public Response getPeriodSuggestions(Request req) throws ServiceException;
    
    //Calendar
    public ArrayList<TouCalendar> getCalendars() throws ValidationException, ServiceException, AccessControlException;
    public TouCalendarData getCalendar(Long theCalendarId) throws ValidationException, ServiceException, AccessControlException;
    public ArrayList<TouCalendar> getActiveCalendars() throws ValidationException, ServiceException, AccessControlException;
    public ArrayList<TouCalendar> getCalendars(Long theCalendarId) throws ValidationException, ServiceException, AccessControlException;
    
    public ArrayList<TouDayProfileData> getDayProfilesByCalendar(Long calendarId);
    public Response getDayProfileSuggestions(Long calendarId, Long seasonId, Request request);
    public ArrayList<TouCalendarSeason> getCalendarSeasons(Long calendarId);
    public boolean updateCalendarSeasons(ArrayList<TouCalendarSeason> arrayList) throws ValidationException, ServiceException, AccessControlException;
    public TouCalendarData updateCalendar(TouCalendarData calendar) throws ValidationException, ServiceException, AccessControlException;
    public TouDayProfileData updateDayProfiles(TouDayProfileData dayProfile) throws ValidationException, ServiceException, AccessControlException;
    public ArrayList<TouSeasonDateData> getSeasonDates(Long  calendarId);
    public boolean deleteDayProfile(TouDayProfileData dayProfile) throws ValidationException, ServiceException, AccessControlException;
    
    //Season dates
    public TouSeasonDateData addSeasonDate(TouSeasonDateData seasonDateData) throws ValidationException, ServiceException;
    public TouSeasonDateData updateSeasonDate(TouSeasonDateData seasonDateData) throws ValidationException, ServiceException;
    boolean deleteSeasonDate(TouSeasonDateData seasonDateData, boolean deleteCalendarSeason) throws ValidationException;
    
    //Day profile time
    public ArrayList<TouDayProfileTimeData> getTimesByTouDayProfile(Long dayProfileId);
    public TouDayProfileTimeData updateDayProfileTimes(TouDayProfileTimeData dayProfileTime, Long calendarId) throws ValidationException, ServiceException;
    public boolean deleteDayProfileTime(Long id, Long calendarId) throws ServiceException, ValidationException;
    public TouDayProfileTimeData getTimeInMilliseconds(TouDayProfileTimeData data);
    
    //Special days
    public ArrayList<TouSpecialDayData> getSpecialDays(Long calendarId) throws ValidationException, ServiceException;
    public TouSpecialDayData updateSpecialDay(TouSpecialDayData specialDay) throws ValidationException, ServiceException;
    public Boolean deleteSpecialDay(Long specialDayPK, Long calendarId) throws ServiceException, ValidationException;
    
    public TouCalendarSeasonsPeriodsSpecialDaysData getTouCalendarData(Long calendarId) throws ServiceException;
}
