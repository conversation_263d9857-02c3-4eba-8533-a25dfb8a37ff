package za.co.ipay.metermng.client.view.component.group.upgengrouptree;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTree;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HasWidgets;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.SelectionChangeEvent;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormView;
import za.co.ipay.gwt.common.client.workspace.SimpleTreeView;
import za.co.ipay.gwt.common.client.workspace.TreeFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.client.view.component.group.entity.EntityView;
import za.co.ipay.metermng.client.view.workspace.group.all.GroupTypeSelection;
import za.co.ipay.metermng.client.view.workspace.group.all.GroupTypeSelectionPanel;
import za.co.ipay.metermng.client.widget.tree.AddGroupNodePanel;
import za.co.ipay.metermng.client.widget.tree.GroupDataTreeHandler;
import za.co.ipay.metermng.client.widget.tree.GroupDataTreeModel;
import za.co.ipay.metermng.client.widget.tree.MeterMngTreeFactory;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUserGroup;
import za.co.ipay.metermng.shared.group.GroupTypeData;

/**
 * UpGenGroupTreePanel is a panel used to display groups in a tree, using their corresponding group hierarchies, and allow
 * adding, updating and deleting of groups, with some restricting depending upon which group type or group types are
 * being displayed.
 * Copy of GenGroupWorkspaceView widget with adaptation for popup scenario.
 * Used by MeterOnlineBulkWorkspaceView.
 *
 * PARAM: Pass IN the groupType id
 */
public class UpGenGroupTreePanel extends BaseComponent implements GenGroupParentComponent, GroupDataTreeHandler, HasWidgets {

    /** The current selection group type which could be all, location, access, etc. */
    private String selectionType;

    @UiField HTMLPanel basePanel;
    @UiField DeckLayoutPanel mainLayoutPanel;
    @UiField SimpleTreeView<GenGroupData> view;
    private GroupDataTreeModel model;
    private GroupTypeSelection groupTypeSelection;
    @UiField SimpleFormView view2;
    private EntityView entityView;
    private SuggestBox filterBox;
    private HorizontalPanel searchPanel;

    /** The current group hierarchy for the selected group type. */
    private ArrayList<GroupHierarchyData> groupHierarchies = new ArrayList<GroupHierarchyData>();
    /** This stores the current last or deepest level in the current group hierarchy. It is used to know whether a leaf
     * in the tree needs to have add child menu item or not if they are the deepest in the hierarchy. */
    private int lastLevel = 0;
    private GroupHierarchyData currentHierarchy;
    private ArrayList<Long> lastGenGroupPath;
    /** The left co-ordinate for the add group pop up window. */
    private int mouseLeft;
    /** The top co-ordinate for the add group pop up window. */
    private int mouseTop;

    /** The current user with their access control group and group path. */
    private MeterMngUserGroup currentUser;

    private Long groupTypeId;
    private PopupPanel popup;
    // Since this is used as part of a modal dialog
    private HasDirtyDataManager hasDirtyDataManager;

    private static Logger logger = Logger.getLogger(UpGenGroupTreePanel.class.getName());
    final RegExp pattern = RegExp.compile("^usagepointgroup\\.[a-zA-Z_]+(\\d{1,})\\.(status|label|datatype)$");

    private static UpGenGroupTreePanelUiBinder uiBinder = GWT.create(UpGenGroupTreePanelUiBinder.class);
    private FormElement searchElement;

    interface UpGenGroupTreePanelUiBinder extends UiBinder<Widget, UpGenGroupTreePanel> {
    }

    public UpGenGroupTreePanel(ClientFactory clientFactory, Long groupTypeId, PopupPanel popup, HasDirtyDataManager hasDirtyDataManager) {
        this.clientFactory = clientFactory;
        this.groupTypeId = groupTypeId;
        this.popup = popup;
        this.hasDirtyDataManager = hasDirtyDataManager;
        this.selectionType = MeterMngStatics.USAGE_POINT_GROUP_TYPE;
        this.groupTypeSelection = new GroupTypeSelectionPanel(this, selectionType);
        initWidget(uiBinder.createAndBindUi(this));

        initUi();
        loadGroupTypes();

    }

    @UiFactory
    public TreeFactory getTreeFactory() {
        model = new GroupDataTreeModel(clientFactory, this);
        return new MeterMngTreeFactory(model, clientFactory);
    }

    private void initUi() {
        this.lastGenGroupPath = new ArrayList<Long>();
        initForms();
        buildFilterBox();

        searchPanel = new HorizontalPanel();
        searchPanel.setSpacing(3);
        Label searchLabel = new Label(MessagesUtil.getInstance().getMessage("grouptree.search"));
        searchLabel.setStylePrimaryName("gwt-Label-bold");
        FormRowPanel formRowPanel = new FormRowPanel();
        searchElement = new FormElement();
        searchElement.add(filterBox);
        searchElement.setLabelText(MessagesUtil.getInstance().getMessage("grouptree.search"));
        searchElement.setHelpMsg(MessagesUtil.getInstance().getMessage("grouptree.search.help"));
        formRowPanel.add(searchElement);
        FormElement btnElement = new FormElement();
        Button clearSearchBtn = new Button(MessagesUtil.getInstance().getMessage("button.clear"));
        clearSearchBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                // clear the search box
                filterBox.setValue("");
                lastGenGroupPath.clear();
                recreateTree();
            }
        });
        btnElement.add(clearSearchBtn);
        formRowPanel.add(btnElement);
        searchPanel.add(formRowPanel);
        view.getExtraPanel().add(searchPanel);
        addSelectionChangeHandlerToModel();

        entityView = new EntityView(clientFactory, this, view2);
        basePanel.setSize(Window.getClientWidth()/2 + "px", Window.getClientHeight()/2 + "px");
        mainLayoutPanel.showWidget(view);
    }

    private void buildFilterBox() {
        filterBox = new SuggestBox(model.getGroupSuggestOracle());
        filterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof GenGroupData) {
                    GenGroupData selected = (GenGroupData) event.getSelectedItem();
                    saveLastGenGroupPath(selected);
                    filterGroup(selected);
                }
            }
        });
        filterBox.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                filterBox.showSuggestionList();
            }
        });
    }

    private void addSelectionChangeHandlerToModel() {
        view.getTree().setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
        model.getSingleSelectionModel().addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            @Override
            public void onSelectionChange(SelectionChangeEvent event) {
                GenGroupData selectedGroup = model.getSingleSelectionModel().getSelectedObject();
                logger.info("onSelectionChange: selected: " + selectedGroup + ",filterBox.text: " + filterBox.getText());
                if(selectedGroup == null) {
                    return;
                }
                // This following line breaks addition of nodes for all but top level
                // Normally not good to leave commented code, but leaving this for the
                // sake of warning that small changes can break stuff here, and this can
                // be a basis for finding the root issue potentially.
                //model.nodeFlush(getTree().getRootTreeNode(), selectedGroup.getId());
            }
        });
    }

    private void recreateTree() {
        List<GenGroupData> sourceGroupdata = model.getSourceGroupdata();
        this.recreateTree(sourceGroupdata);
    }

    private void recreateTree(List<GenGroupData> sourceGroupdata) {
        // After adding "show more" searching and selecting an item
        // was no longer straightforward, so now filtering sets the
        // visible range to 1 for the searched element and the same
        // is applied when adding items cos they could be outside the
        // show more range.
        // There were numerous side effects on doing that like duplicate
        // items showing up which was only resolved by completely
        // recreating the tree (where previously only the data was
        // replaced for example when changing the group type
        // ideally this approach was more structurally visible on the
        // SimpleTreeView and filter box and also in the order of events
        // which is a little async in terms of when the factory/model
        // creation happens and all dependencies resolved. For example
        // when changing the model on the tree the filter box is out
        // out of date because it had an old model passed to it.
        // There is also too much duplication with the UpGenGroupTreePanel
        // but alas this is the best I could do in the time allowed.

        // tree factory recreates the model
        view.replaceTree(getTreeFactory());
        // set the original data on the model
        model.setData(sourceGroupdata);
        searchElement.remove(filterBox);
        buildFilterBox();
        searchElement.add(filterBox);
        addSelectionChangeHandlerToModel();
        //view.getTree().setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
        // When saving new, for some reason this next call flushes pending UI stuff
        // without it when range is set in filterGroup shows the current selection
        // as the first element in the tree, but then a complete copy of the unfiltered
        // after it
        view.clearTreeSelection();
    }

    private void initForms() {
        groupTypeSelection.setForm(view.getTreeForm());
        view.getTreeForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usagepointgroups.title"), "pageSectionTitle");
        view.getTreeForm().getFormFields().add(groupTypeSelection);
        view.getTreeForm().getButtons().setVisible(false);

        view.getForm().setVisible(false);
    }

    public void viewGroupEntity(final GenGroupData group) {
        SessionCheckCallback sessionCheckCallback= new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                entityView.setGenGroup(group);
                mainLayoutPanel.showWidget(view2);
                entityView.openContactPanel();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @Override
    public void setPosition(Element element) {
        if (element != null) {
            this.mouseLeft = element.getAbsoluteLeft();
            this.mouseTop = element.getAbsoluteTop();
        }
    }

    private void loadGroupTypes() {
        logger.info("Loading group types...");
        clientFactory.getGroupRpc().getGroupTypesWithHierarchy(new ClientCallback<ArrayList<GroupTypeData>>() {
            @Override
            public void onSuccess(ArrayList<GroupTypeData> groupTypes) {
                logger.fine("Got groupTypes: " + groupTypes.size());
                boolean displayAccessGroup = false;
                boolean displayLocationGroup = false;
                ((GroupTypeSelectionPanel) groupTypeSelection).setGroupTypes(groupTypes, displayAccessGroup, displayLocationGroup);
                groupTypeSelection.selectGroupType(groupTypeId);
                popup.setWidget(basePanel);
                popup.show();
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    popup.hide();
                }
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void clearGroupHierarchies() {
        this.groupHierarchies = new ArrayList<GroupHierarchyData>();
        this.lastLevel = 0;
    }

    protected void setGroupHierarchies(ArrayList<GroupHierarchyData> data) {
        this.groupHierarchies = data;
        if (groupHierarchies.size() > 0) {
            this.lastLevel = groupHierarchies.get(groupHierarchies.size() - 1).getLevel();
        } else {
            this.lastLevel = 0;
        }
        logger.info("Set group hierarchies: lastLevel:"+lastLevel);
    }

    @Override
    public void clearGroups() {
        model.setData(new ArrayList<GenGroupData>());
    }

    @Override
    public void loadGroupHierarchies(final Long groupTypeId) {
        if (groupTypeId != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getGroupRpc().getGroupHierarchies(groupTypeId, new ClientCallback<ArrayList<GroupHierarchyData>>() {
                        @Override
                        public void onSuccess(ArrayList<GroupHierarchyData> data) {
                            setGroupHierarchies(data);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @Override
    public void updateRoot() {
        model.updateRoot();
    }

    @Override
    public void reLoadGroups(GenGroupData genGroup) {
        logger.info("RELOAD GROUPS");
        //store current path
        saveLastGenGroupPath(genGroup);

        loadGroups(groupTypeSelection.getGroupTypeId());
    }

    @Override
    public void loadGroups(final Long groupTypeId) {
        filterBox.setText("");
        if (groupTypeId != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getGroupRpc().getGroups(groupTypeId, new ClientCallback<ArrayList<GenGroupData>>() {
                        @Override
                        public void onSuccess(ArrayList<GenGroupData> result) {
                            recreateTree(result);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointgroup.noselection.grouptype"),
                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    @Override
    public void goBack() {
        hasDirtyDataManager.checkAnyDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if(confirm) {
                    hasDirtyDataManager.clearAllHasDirtyData();
                    mainLayoutPanel.showWidget(view);
                }
            }
        });
    }

    @Override
    public GroupTypeData getSelectedGroupTypeData() {
        return groupTypeSelection.getGroupTypeData();
    }

    @Override
    public Long getSelectedGroupTypeId() {
        return groupTypeSelection.getGroupTypeId();
    }

    @Override
    public String getSelectedGroupTypeName() {
        return groupTypeSelection.getGroupTypeName();
    }

    @Override
    public void addNewGroup(ClientFactory clientFactory, Long groupTypeId, GenGroupData parent, GenGroupData current) {
        //Populate the new current node with its parent and groupHierarchy attributes
        logger.info("addNewGroup(): parent: "+parent);

        current.setName("New");
        current.setRecordStatus(RecordStatus.DAC);
        current.setNewEntry(true);

        if (parent != null) {
            current.setParentId(parent.getId());
            current.setParent(parent);
        }
        GroupHierarchyData parentHierarchy = getParentHierarchy(parent);
        if (parentHierarchy == null && current.getGroupHierarchyId() != null) {
            currentHierarchy = getCurrentHierarchy(current.getGroupHierarchyId());
        } else {
            currentHierarchy = getCurrentHierarchy(parentHierarchy);
        }
        if (currentHierarchy != null) {
            current.setGroupHierarchyId(currentHierarchy.getId());
        }

        logger.info("addNewGroup current:"+current);

        StringBuilder nameLabel = new StringBuilder();
        if (parent == null) {
            nameLabel.append(UiMessagesUtil.getInstance().getNewGroupInstructions()).append(" ").append(currentHierarchy.getName());
            GenGroupData root = model.getRoot();
            model.expandNode(view.getTree().getRootTreeNode(), root.getId());
        } else {
            nameLabel.append(UiMessagesUtil.getInstance().getNewGroupInstructions()).append(" ").append(currentHierarchy.getName())
                      .append(" ").append(UiMessagesUtil.getInstance().getNewGroupFor()).append(" ").append(parent.getName());
            model.expandNode(view.getTree().getRootTreeNode(), parent.getId());
        }
        PopupPanel simplePopup = new PopupPanel(true);
        AddGroupNodePanel addGroupNodePanel = new AddGroupNodePanel(clientFactory, current, nameLabel.toString(), this, simplePopup, null);
        addGroupNodePanel.populateAddNewMrid();
        simplePopup.setGlassEnabled(true);
        simplePopup.setAutoHideEnabled(false);
        simplePopup.setAutoHideOnHistoryEventsEnabled(true);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(addGroupNodePanel);
        simplePopup.setPopupPosition(mouseLeft, mouseTop);
        simplePopup.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        simplePopup.show();
        addGroupNodePanel.setFocusOnTextBox();
        if (isLowestLevel(current)) {
            current.setRecordStatus(RecordStatus.ACT);
        }
    }

    private GroupHierarchyData getParentHierarchy(GenGroupData parent) {
        if (parent != null) {
            logger.fine("Getting parent's hierarchy: " + parent.getGroupHierarchyId());
            for (GroupHierarchyData gh : groupHierarchies) {
                if (gh.getId().equals(parent.getGroupHierarchyId())) {
                    return gh;
                }
            }
        }
        return null;
    }

    private GroupHierarchyData getCurrentHierarchy(GroupHierarchyData parentHierarchy) {
        int level = 1;
        if (parentHierarchy != null) {
            level = parentHierarchy.getLevel() + 1;
        }
        for (GroupHierarchyData gh : groupHierarchies) {
            if (gh.getLevel().intValue() == level) {
                logger.fine("Found current hierarchy: " + gh);
                return gh;
            }
        }
        logger.severe("No matching groupHierarchy found for level: " + level);
        return null;
    }

    private GroupHierarchyData getCurrentHierarchy(Long id) {
        for (GroupHierarchyData gh : groupHierarchies) {
            if (gh.getId().equals(id)) {
                logger.fine("Found current hierarchy: " + gh);
                return gh;
            }
        }
        logger.severe("No matching groupHierarchy found for id: " + id);
        return null;
    }

    public static final class GroupNameComparator implements Comparator<GenGroupData> {
        @Override
        public int compare(GenGroupData o1, GenGroupData o2) {
            return o1.getName().compareTo(o2.getName());
        }
    }

    @Override
    public void saveGenGroup(final GenGroupData groupToSave) {
        if (groupToSave != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    GroupHierarchyData currentHierarchy = getCurrentHierarchy(groupToSave.getGroupHierarchyId());
                    logger.info("Saving edited gengroup: " + groupToSave.getName() + " currentHierarchy: " + currentHierarchy);
                    boolean lastLevel = (UpGenGroupTreePanel.this.lastLevel == currentHierarchy.getLevel().intValue());
                    clientFactory.getGroupRpc().updateGenGroup(groupToSave, lastLevel, new ClientCallback<GenGroupData>() {
                        @Override
                        public void onSuccess(GenGroupData group) {
                            // Note this result is not fully populated and does not contain parent info
                            // therefore we set the id on the local object and continue working with it
                            boolean existingGroup = false;
                            if(groupToSave.getId() == null) {
                                groupToSave.setId(group.getId());
                            } else {
                                existingGroup = true;
                            }
                            // make sure both variables in sync to avoid errors further down
                            group = groupToSave;
                            logger.info("Successfully saved genGroup: " + group);

                            // Existing groups don't need to be added to the source data set
                            if(!existingGroup) {
                                // Need to add this as a child of the parent so that the selection
                                // code that displays children works
                                if(group.getParent() != null) {
                                    group.getParent().getChildren().add(group);
                                    Collections.sort(group.getParent().getChildren(), new GroupNameComparator());
                                } else {
                                    List<GenGroupData> sourceGroupdata = model.getSourceGroupdata();
                                    sourceGroupdata.add(group);
                                    Collections.sort(sourceGroupdata, new GroupNameComparator());
                                }
                                if(isLowestLevel(group)) {
                                    // We need to set the active status of the parents since
                                    // they would not have been active without a leaf node
                                    // This would have already been done on the
                                    // server, but to avoid a reload we copy the same change to
                                    // the locally cached data in the browser
                                    GenGroupData parent = group.getParent();
                                    while(parent != null) {
                                        parent.setRecordStatus(RecordStatus.ACT);
                                        parent = parent.getParent();
                                    }
                                }
                            }

                            saveLastGenGroupPath(group);
                            recreateTree();
                            filterGroup(group);
                            clientFactory.getEventBus().fireEvent(new UpdateUsagePointGroupsListEvent(groupTypeSelection.getGroupTypeId(),group.getId()));
                            notifyGroupsUpdated();
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void notifyGroupsUpdated() {
        String dataType = MeterMngStatics.GROUPSEDITEDXTREEPOPUP;
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, dataType));
    }

    private void saveLastGenGroupPath(GenGroupData group) {
        lastGenGroupPath.clear();
        lastGenGroupPath.add(group.getId());
        GenGroupData current = group.getParent();
        while (current != null) {
            lastGenGroupPath.add(0, current.getId());
            current = current.getParent();
        }
        logger.info("Saved path: " + lastGenGroupPath);
    }

    private void saveParentLastGenGroupPath(GenGroupData parent) {
        lastGenGroupPath.clear();
        if (parent != null) {
            lastGenGroupPath.add(parent.getId());
            GenGroupData current = parent.getParent();
            while (current != null) {
                lastGenGroupPath.add(0, current.getId());
                current = current.getParent();
            }
            logger.info("Saved parent's path: " + lastGenGroupPath);
        } else {
            lastGenGroupPath.add(null);
        }
    }

    @Override
    public boolean isLowestLevel(GenGroupData data) {
        GroupHierarchyData hierarchy = null;
        for (GroupHierarchyData gh : groupHierarchies) {
            if (data != null && gh.getId().equals(data.getGroupHierarchyId())) {
                hierarchy = gh;
                break;
            }
        }
        if (hierarchy != null) {
            if (hierarchy.getLevel().intValue() == lastLevel) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getLevelName(GenGroupData data) {
        if (data == null) {
            return "";
        } else {
            for (GroupHierarchyData gh : groupHierarchies) {
                if (gh.getId().equals(data.getGroupHierarchyId())) {
                    return gh.getName();
                }
            }
        }
        logger.fine("No corresponding groupHierarchy found for node: " + data.getName() + " " + data.getGroupHierarchyId());
        return "";
    }

    @Override
    public void deleteGroup(final GenGroupData genGroupData) {
        logger.info("deleteGroup: " + genGroupData);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().deleteGenGroup(genGroupData, new ClientCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        logger.info("deleteGroup: success");
                        saveParentLastGenGroupPath(genGroupData.getParent());
                        List<GenGroupData> parentList;
                        if(genGroupData.getParent() == null) {
                            // is in the root node
                            logger.info("deleteGroup: is in root node");
                            parentList = model.getSourceGroupdata();
                        } else {
                            logger.info("deleteGroup: is in sub node");
                            parentList = genGroupData.getParent().getChildren();
                        }
                        if(parentList != null && ! parentList.isEmpty()) {
                            int i = 0;
                            for (Iterator<GenGroupData> iterator = parentList.iterator(); iterator.hasNext();) {
                                i++;
                                GenGroupData gg = (GenGroupData) iterator.next();
                                if(gg.getId().equals(genGroupData.getId())) {
                                    logger.info("deleteGroup: remove from source data: " + i);
                                    iterator.remove();
                                    break;
                                }
                            }
                        }

                        if(genGroupData.getRecordStatus() == RecordStatus.ACT) {
                            // We need to set the deactivate the parents that have no
                            // other children. This would have already been done on the
                            // server, but to avoid a reload we copy the same change to
                            // the locally cached data in the browser
                            GenGroupData parent = genGroupData.getParent();
                            while(parent != null) {
                                if(parent.getChildren() == null || parent.getChildren().isEmpty()) {
                                    // does not have children so it does not have leaf nodes
                                    parent.setRecordStatus(RecordStatus.DAC);
                                } else {
                                    // parent has other children but need to check if any go down to leaf node
                                    if(! hasLeafNodeChild(parent)) {
                                        parent.setRecordStatus(RecordStatus.DAC);
                                    }
                                }
                                parent = parent.getParent();
                            }
                        }

                        filterBox.setText(null);
                        recreateTree();
                        model.expandNode(getTree().getRootTreeNode(), genGroupData.getParentId());
                        view.setSelectedTreeData(genGroupData.getParent());

                        clientFactory.getEventBus().fireEvent(new UpdateUsagePointGroupsListEvent(groupTypeSelection.getGroupTypeId()));
                        notifyGroupsUpdated();
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private boolean hasLeafNodeChild(GenGroupData group) {
        // Not if this is a lead node itself it will return false
        if(group.getChildren() != null) {
            for (GenGroupData child : group.getChildren()) {
                if(isLowestLevel(child)) {
                    return true;
                } else {
                    if(hasLeafNodeChild(child)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public CellTree getTree() {
        return view.getTree();
    }

    @Override
    public ArrayList<Long> getLastGenGroupPath() {
        return lastGenGroupPath;
    }

    @Override
    public void setPageFocus() {
        groupTypeSelection.setFocus();
    }

    @Override
    public boolean isSingleSelection() {
        return false;
    }

    @Override
    public Long getAssignedId() {
        if (currentUser != null && currentUser.getAssignedGroup() != null) {
            return currentUser.getAssignedGroup().getId();
        } else {
            return null;
        }
    }

    @Override
    public ArrayList<Long> getAssignedPath() {
        if (currentUser != null && currentUser.getGroupPath() != null) {
            return currentUser.getGroupPath();
        } else {
            return new ArrayList<Long>();
        }
    }

    @Override
    public boolean isGroupViewOnly(GenGroupData group) {
        return model.isGroupViewOnly(group);
    }


    public String getSelectionType() {
        return selectionType;
    }

    //Methods needed for HasWidgets - which is needed to add this class to popup
    @Override
    public void add(Widget w) {

    }

    @Override
    public void clear() {
    }

    @Override
    public Iterator<Widget> iterator() {
        return null;
    }

    @Override
    public boolean remove(Widget w) {
        return false;
    }

    //if change gen groups vis genGroupWorkspaceView update this list
    public void handleGroupUpdateNotification() {
        ((GroupTypeSelectionPanel) groupTypeSelection).clearFields();
        clearGroupHierarchies();
        clearGroups();
        updateRoot();
        loadGroupTypes();
    }

    @Override
    public HasDirtyDataManager getHasDirtyDataManager() {
        return hasDirtyDataManager;
    }

    @Override
    public void openGenGroupEditPanel(GenGroupData current) {
        String nameLabel = UiMessagesUtil.getInstance().getGroupEdit()+" "+ current.getDisplayString();
        PopupPanel simplePopup = new PopupPanel(true);
        AddGroupNodePanel addGroupNodePanel = new AddGroupNodePanel(clientFactory, current, nameLabel, this, simplePopup, null);
        addGroupNodePanel.populateEditInputs(current);
        simplePopup.setGlassEnabled(true);
        simplePopup.setAutoHideEnabled(false);
        simplePopup.setAutoHideOnHistoryEventsEnabled(true);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(addGroupNodePanel);
        simplePopup.setPopupPosition(mouseLeft, mouseTop);
        simplePopup.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        simplePopup.show();
        addGroupNodePanel.setFocusOnTextBox();
    }

    private void filterGroup(GenGroupData selected) {
        logger.info("filterGroup: " + selected);
        filterBox.setText(selected.getName());
        ArrayList<GenGroupData> parentGroups = new ArrayList<GenGroupData>();
        ArrayList<Long> parentGroupIds = new ArrayList<Long>();
        GenGroupData current = selected.getParent();
        while (current != null) {
            parentGroups.add(0, current);
            parentGroupIds.add(0, current.getId());
            current = current.getParent();
        }
        model.expandNode(view.getTree().getRootTreeNode(), null);
        for (GenGroupData genGroupData : parentGroups) {
            model.setVisibleRangeForGroup(genGroupData, 1, true);
        }
        model.setVisibleRangeForGroup(selected, 1, false);
        model.getSingleSelectionModel().setSelected(selected, true);
    }

}
