package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DecoratorPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.AuxAccountEvent;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEvent;
import za.co.ipay.metermng.client.event.FreeIssueTokenIssuedEvent;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.TokenGenerationRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.TokenData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class IssueNewEngineeringTokenPanel extends BaseComponent {

    private StsMeter stsMeter;
    private Long usagePointId;

    @UiField FormElement descriptionElement;
    @UiField Button btnGetToken;
    @UiField TextBox txtbxdescription;
    @UiField Label theTokenCode;
    @UiField DecoratorPanel dpBase;
    @UiField(provided=true) EngineeringTokenUserRefPanel engineeringTokenUserRefPanel;
    @UiField FormElement registerElement;
    @UiField IpayListBox lstbxClearRegister;
    @UiField FormElement unitsElement;
    @UiField BigDecimalValueBox txtbxUnitsRequired;
    @UiField DecoratorPanel dpKeyChange;
    @UiField FormElement currencyElement;
    @UiField CurrencyTextBox txtbxCurrencyRequired;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;
    @UiField Label lblUnitSymbol;
    @UiField DecoratorPanel dpPowerLimit;

    private static IssueNewEngineeringTokenPanelWidgetUiBinder uiBinder = GWT.create(IssueNewEngineeringTokenPanelWidgetUiBinder.class);

    interface IssueNewEngineeringTokenPanelWidgetUiBinder extends UiBinder<Widget, IssueNewEngineeringTokenPanel> {}

    private StsEngineeringTokenTypeE stsEngineeringTokenTypeE = null;
    private boolean allowFreeIssueUnits;
    private int freeIssueType = 1;
    private Long freeIssueAuxAccountId;
    private Long customerAgreementId;
    private BigDecimal stsUnitGenerationLimit;

    public IssueNewEngineeringTokenPanel(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        engineeringTokenUserRefPanel = new EngineeringTokenUserRefPanel(clientFactory);
        specialactionreasons = new SpecialActionsReasonComponent(clientFactory, null, SpecialActionsData.FREE_TOKEN_ISSUE);
        initWidget(uiBinder.createAndBindUi(this));
        init();
    }

    public void initEngineeringTokenUserRefPanel(AppSetting tokenIssueRefStatusAppSetting) {
        engineeringTokenUserRefPanel.setEngineeringTokenUserRefPanel(tokenIssueRefStatusAppSetting);
        engineeringTokenUserRefPanel.showPanel();
    }

    protected void init() {
        theTokenCode.setVisible(false);

        clientFactory.getEventBus().addHandler(UsagePointUpdatedEvent.TYPE, new UsagePointUpdatedEventHandler() {

            @Override
            public void updateUsagePoint(final UsagePointUpdatedEvent event) {
                UsagePointData usagePointData = event.getUsagePointData();
                if (usagePointData != null && usagePointData.getId() != null) {
                    customerAgreementId = usagePointData.getCustomerAgreementId();
                }
            }
        });
        allowFreeIssueUnits = clientFactory.getUser().hasPermission("mm_aux_free_issue");

        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.STS_UNIT_GENERATION_LIMIT,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting appSetting) {
                        stsUnitGenerationLimit = new BigDecimal(appSetting.getValue());
                    }
                });
    }

    public void setFreeIssueAuxAccountId(Long theId) {
        this.freeIssueAuxAccountId = theId;
        if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
            btnGetToken.setEnabled(freeIssueAuxAccountId != null);
        }
    }

    public void setCustomerAgreementId(Long custAgreementId) {
        this.customerAgreementId = custAgreementId;
    }

    public void setFreeIssueType(int type) {
        freeIssueType = type;
        if ( (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS || usagePointId == null)
                && allowFreeIssueUnits) {
            unitsElement.setVisible(true);
            currencyElement.setVisible(false);
            btnGetToken.setEnabled(true);
            specialactionreasons.setVisible(true);
        } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY && usagePointId != null) {
            unitsElement.setVisible(false);
            currencyElement.setVisible(true);
            btnGetToken.setEnabled(freeIssueAuxAccountId != null);
            specialactionreasons.setVisible(true);
        }

    }

    public void setStsMeter(StsMeter stsMeter) {
        this.stsMeter = stsMeter;
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
    }

    @UiHandler("btnGetToken")
    void handleGetTokenButton(final ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                handleGetTokenButtonClickEvent(event);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void handleGetTokenButtonClickEvent(ClickEvent event) {

        if (validateForm()) {
            final PopupPanel waitDialog = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon(), btnGetToken.getAbsoluteLeft()+btnGetToken.getOffsetWidth(), btnGetToken.getAbsoluteTop());

            //Map form fields to data object
            ClientCallback<TokenData> tokenSvcAsyncCallback = new ClientCallback<TokenData>() {
                @Override
                public void onSuccess(TokenData result) {
                    waitDialog.hide();
                    if (result == null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.token.retrieve"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else if (result.getErrorMsg() != null) {
                        Dialogs.displayErrorMessage((MessagesUtil.getInstance().getMessage("error.token.retrieve") + " (" + result.getResCode() + ": " + result.getErrorMsg() + ") "),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else if (result.getCustomerMsg() != null) {
                        Dialogs.displayErrorMessage((MessagesUtil.getInstance().getMessage("error.token.retrieve") + " (" + result.getCustomerMsg() + ") "),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        if (unitsElement.isVisible()) {
                            txtbxUnitsRequired.setText("");
                        }
                        clearFields();
                        String code = result.getEngineeringTokenCodes().get(0);
                        if(stsEngineeringTokenTypeE == StsEngineeringTokenTypeE.FREE_ISSUE) {
                            if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS) {
                                code = result.getEngineeringTokenCodes().get(0);
                            } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
                                code = result.getStandardTokenCodes().get(0);
                            }
                        }

                        int len = code.length();
                        int numberOfSpaces = (len / 4) + 1;
                        char[] val = code.toCharArray();
                        char[] buf = new char[len + numberOfSpaces];
                        int j = 0;
                        for (int c = 0; c < len; c++) {
                            if ((c % 4) == 0) {
                                buf[j++] = ' ';
                            }
                            buf[j++] = val[c];
                        }
                        theTokenCode.setText(MessagesUtil.getInstance().getMessage("meter.token.code") + ": " + new String(buf, 0, j).trim());
                        theTokenCode.setVisible(true);
                        if(stsEngineeringTokenTypeE == StsEngineeringTokenTypeE.FREE_ISSUE) {
                            if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS) {
                                clientFactory.getEventBus().fireEvent(new EngineeringTokenIssuedEvent(stsMeter, freeIssueType));
                            } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
                                clientFactory.getEventBus().fireEvent(new FreeIssueTokenIssuedEvent(stsMeter));
                                clientFactory.getEventBus().fireEvent(new AuxAccountEvent(customerAgreementId, freeIssueAuxAccountId, AuxAccountEvent.AUX_ACNT_UPDATED));
                            }
                        }else {
                            clientFactory.getEventBus().fireEvent(new EngineeringTokenIssuedEvent(stsMeter, -1));
                        }
                    }
                }
            };

            clearErrorMessages();
            theTokenCode.setVisible(false);
            theTokenCode.setText("");

            Integer units = null;
            if(unitsElement.isVisible() && stsEngineeringTokenTypeE != StsEngineeringTokenTypeE.FREE_ISSUE) {
                try {
                    units = Integer.valueOf(txtbxUnitsRequired.getText());
                } catch (NumberFormatException nfe) {
                    waitDialog.hide();
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.token.error"),
                            MediaResourceUtil.getInstance().getErrorIcon(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                    unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.error.units"));
                }
            }
            if (clientFactory != null) {
                TokenGenerationRpcAsync tokenGenerationRpcAsync = clientFactory.getTokenGeneratorRpc();
                switch(stsEngineeringTokenTypeE) {
                case CLEAR_CREDIT:
                    tokenGenerationRpcAsync.requestClearCreditToken(txtbxdescription.getText(), usagePointId, stsMeter, Integer.parseInt(lstbxClearRegister.getSelectedValues().get(0)), engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                    break;
                case CLEAR_REVERSE_FLAG:
                    tokenGenerationRpcAsync.requestClearReverseFlagToken(txtbxdescription.getText(), stsMeter, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                    break;
                case CLEAR_TAMPER:
                    tokenGenerationRpcAsync.requestClearTamperToken(txtbxdescription.getText(), usagePointId, stsMeter, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                    break;
                case DISABLE_TRIP_LIMIT:
                    tokenGenerationRpcAsync.requestDisableTripLimitToken(txtbxdescription.getText(), stsMeter, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                    break;
                case FREE_ISSUE:
                    BigDecimal unitsOrCurrency = null;
                    try {
                        if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS) {
                            if (allowFreeIssueUnits) {
                                unitsOrCurrency = new BigDecimal(txtbxUnitsRequired.getText().replaceAll("\\s", ""));
                            }
                        } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
                            unitsOrCurrency = txtbxCurrencyRequired.getAmount();
                        }
                    } catch (NumberFormatException nfe) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.token.error"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS) {
                            unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.error.units"));
                        } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
                            currencyElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.error.amount"));
                        }

                    }

                    if (specialactionreasons.validate()) {
                        if (unitsOrCurrency != null) {
                            SpecialActionReasonsLog logEntry = specialactionreasons.getLogEntry();
                            if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS) {
                                tokenGenerationRpcAsync.requestFreeTokenUnits(txtbxdescription.getText(), usagePointId,
                                        stsMeter, unitsOrCurrency, logEntry, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                            } else if (freeIssueType == TokenData.TOKEN_TYPE_FREE_ISSUE_CURRENCY) {
                                tokenGenerationRpcAsync.requestFreeTokenCurrency(txtbxdescription.getText(), usagePointId,
                                        stsMeter, unitsOrCurrency, logEntry, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                            }
                        } else {
                            waitDialog.hide();
                        }
                    } else {
                        waitDialog.hide();
                    }
                    break;
                case SET_PHASE:
                    tokenGenerationRpcAsync.requestSetPhaseToken(txtbxdescription.getText(), usagePointId, stsMeter, units, engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                    break;
                default:
                }
            }
        }
    }

    private boolean validateForm() {
        boolean isValidated = true;

        if (!engineeringTokenUserRefPanel.validateFormField()) {
            isValidated =false;
        }

        if(stsEngineeringTokenTypeE == StsEngineeringTokenTypeE.SET_PHASE) {
            BigDecimal units = txtbxUnitsRequired.getValue();
            if (units != null && units.compareTo(BigDecimal.ZERO) <= 0) {
                isValidated = false;
                unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.numeric.positive_not_zero"));
            } else if (units.compareTo(stsUnitGenerationLimit) == 1) {
                isValidated = false;
                unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("sts.unit.generation.limit.error",
                        new String[] { stsUnitGenerationLimit.toPlainString() }));
            }
        }else if(stsEngineeringTokenTypeE == StsEngineeringTokenTypeE.FREE_ISSUE) {
            BigDecimal units = txtbxUnitsRequired.getValue();

            if (units != null) {
                txtbxUnitsRequired.setText(units.setScale(1, RoundingMode.FLOOR).toPlainString());
                if (units.compareTo(BigDecimal.ZERO) < 0) {
                    isValidated = false;
                    unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
                } else if (ValidateUtil.getNumberOfDecimalPlaces(units) > 1) {
                    isValidated = false;
                    unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.freeunits.decimal.limit"));
                } else if (units.compareTo(stsUnitGenerationLimit) == 1) {
                    isValidated = false;
                    unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("sts.unit.generation.limit.error",
                            new String[] { stsUnitGenerationLimit.toPlainString() }));
                }
            }
        }

        return isValidated;
    }

    public void clearErrorMessages() {
        unitsElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        specialactionreasons.clearErrorMessages();
        engineeringTokenUserRefPanel.clearErrorMessage();
    }

    public void clearFields() {
        theTokenCode.setText("");
        theTokenCode.setVisible(false);
        txtbxdescription.setText("");
        txtbxUnitsRequired.setText("");
        specialactionreasons.clearFields();
        engineeringTokenUserRefPanel.clearFormField();
        txtbxCurrencyRequired.setAmount(null);
    }

    public EngineeringTokenUserRefPanel getEngineeringTokenUserRefPanel() {
        return engineeringTokenUserRefPanel;
    }

    public void updateUnitTypes(Long id) {
        String symbol = MeterMngClientUtils.getServiceResourceSymbol(id);
        Messages messagesInstance = MessagesUtil.getInstance();
        unitsElement.setHelpMsg(messagesInstance.getMessage("meter.units.help", new String[] { symbol }));
        unitsElement.setLabelText(messagesInstance.getMessage("meter.units", new String[] { symbol }) + ":");
        lblUnitSymbol.setText(symbol);
    }

    public void setTokenType(StsEngineeringTokenTypeE stsEngineeringTokenTypeE) {
        this.stsEngineeringTokenTypeE = stsEngineeringTokenTypeE;
        clearErrorMessages();
        Messages messagesInstance = MessagesUtil.getInstance();
        boolean registerElementVisible = false;
        boolean unitsElementVisible = false;
        boolean dpBaseVisible = true;
        boolean dpKeyChangeVisible = false;
        boolean specialactionreasonsVisible = false;
        boolean dpPowerLimitVisible = false;
        switch(stsEngineeringTokenTypeE) {
        case CLEAR_TAMPER:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.issue.token.description.help", new String[] {messagesInstance.getMessage("meter.cleartamper")}));
            break;
        case CLEAR_CREDIT:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.issue.token.description.help", new String[] {messagesInstance.getMessage("meter.clearcredit")}));
            lstbxClearRegister.clear();
            lstbxClearRegister.addItem(messagesInstance.getMessage("meter.clearcredit.all"), "65535");
            lstbxClearRegister.addItem(messagesInstance.getMessage("meter.clearcredit.elec"), "0");
            registerElementVisible = true;
            break;
        case CLEAR_REVERSE_FLAG:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.issue.token.description.help", new String[] {messagesInstance.getMessage("meter.clearreverseflag")}));
            break;
        case DISABLE_TRIP_LIMIT:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.issue.token.description.help", new String[] {messagesInstance.getMessage("meter.disabletriplimit")}));
            break;
        case FREE_ISSUE:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.free.description.help"));
            specialactionreasonsVisible = true;
            break;
        case KEY_CHANGE:
            dpBaseVisible = false;
            dpKeyChangeVisible = true;
            break;
        case POWER_LIMIT:
            dpBaseVisible = false;
            dpPowerLimitVisible = true;
            break;
        case SET_PHASE:
            descriptionElement.setHelpMsg(messagesInstance.getMessage("meter.setphase.description.help"));
            unitsElement.setHelpMsg(messagesInstance.getMessage("meter.units.watts.help"));
            unitsElement.setLabelText(messagesInstance.getMessage("meter.units.watts") + ":");
            lblUnitSymbol.setText(messagesInstance.getMessage("unit.watts.symbol"));
            unitsElementVisible = true;
            break;
        }
        registerElement.setVisible(registerElementVisible);
        unitsElement.setVisible(unitsElementVisible);
        dpBase.setVisible(dpBaseVisible);
        dpKeyChange.setVisible(dpKeyChangeVisible);
        specialactionreasons.setVisible(specialactionreasonsVisible);
        dpPowerLimit.setVisible(dpPowerLimitVisible);
    }
}
