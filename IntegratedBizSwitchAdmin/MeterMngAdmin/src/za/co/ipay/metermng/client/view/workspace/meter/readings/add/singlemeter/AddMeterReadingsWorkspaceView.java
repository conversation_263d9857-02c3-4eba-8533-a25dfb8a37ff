package za.co.ipay.metermng.client.view.workspace.meter.readings.add.singlemeter;

import java.util.ArrayList;
import java.util.Date;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormDisplayView;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.dto.meter.AddMeterReadingScreenDataDto;

/**
 * AddMeterReadingsWorkspaceView is used for demo purposes and allows meter readings to be generator for a specific
 * meter with a specific payment mode.
 * <AUTHOR>
 *
 */
public class AddMeterReadingsWorkspaceView extends BaseWorkspace {

    @UiField SimpleFormDisplayView view;

    public SimpleForm addMeterReadingsForm;
    private AddMeterReadingsPanel panel;
    private String energyForwardId;
    int defaultReadingTypeIndex = 0;

    private static AddMeterReadingsWorkspaceViewUiBinder uiBinder = GWT.create(AddMeterReadingsWorkspaceViewUiBinder.class);

    interface AddMeterReadingsWorkspaceViewUiBinder extends UiBinder<Widget, AddMeterReadingsWorkspaceView> {
    }

    public AddMeterReadingsWorkspaceView(ClientFactory clientFactory, AddMeterReadingsPlace addMeterReadingsPlace) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        this.addMeterReadingsForm = view.getForm();
        setPlaceString(AddMeterReadingsPlace.getPlaceAsString(addMeterReadingsPlace));
        setHeaderText(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.title"));
        initUi(addMeterReadingsPlace);
        loadInitData();
    }

    private void initUi(AddMeterReadingsPlace place) {
        panel = new AddMeterReadingsPanel(clientFactory, addMeterReadingsForm, this);
        addMeterReadingsForm.setHasDirtyDataManager(this);
        addMeterReadingsForm.getFormFields().add(panel);

        addMeterReadingsForm.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.add"));
        addMeterReadingsForm.getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onAdd();
            }
        });

        addMeterReadingsForm.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear"));
        addMeterReadingsForm.getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            onClear();
                        }
                    }
                });
            }
        });
    }

    private void loadInitData() {
        clientFactory.getLookupRpc().getAddMeterReadingScreenData(new ClientCallback<AddMeterReadingScreenDataDto>() {
            @Override
            public void onSuccess(AddMeterReadingScreenDataDto result) {
                setPanelData(result);
            }
        });
    }

    private void setPanelData(AddMeterReadingScreenDataDto data) {
        panel.readingTypesBox.clear();
        defaultReadingTypeIndex = 0;
        for(int i=0;i<data.getReadingTypes().size();i++) {
            panel.readingTypesBox.addItem(data.getReadingTypes().get(i).getName(), data.getReadingTypes().get(i).getId().toString());
            if (MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE.equals(data.getReadingTypes().get(i).getValue())) {
                defaultReadingTypeIndex = i;
                energyForwardId = data.getReadingTypes().get(i).getId().toString();
                panel.readingTypesBox.setItemSelected(i, true);
            } else {
                panel.readingTypesBox.setItemSelected(i, false);
            }
        }

        panel.readingIntervalBox.clear();
        Messages messages = MessagesUtil.getInstance();
        panel.readingIntervalBox.addItem(messages.getMessage("demo.addmeterreadings.minutes.15"), "15");
        panel.readingIntervalBox.addItem(messages.getMessage("demo.addmeterreadings.minutes.30"), "30");
        panel.readingIntervalBox.addItem(messages.getMessage("demo.addmeterreadings.minutes.60"), "60");
        panel.readingIntervalBox.addItem(messages.getMessage("tariff.cost.cycle.daily"), "1440");
        panel.readingIntervalBox.addItem(messages.getMessage("demo.addmeterreadings.weekly"), "10080");
        panel.readingIntervalBox.addItem(messages.getMessage("tariff.cost.cycle.monthly"), "44640"); // 31 days
    }

    protected void onAdd() {
        if (isValidInput()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (panel.tariffCalcBox.getValue()) {
                        clientFactory.getSearchRpc().fetchUsagePointByMeterDto(panel.selectedMeter, new ClientCallback<UsagePoint>() {

                            @Override
                            protected void onFailureClient() {
                                noTariffCalcConfirm("demo.addmeterreadings.no.usagepoint3");
                            }

                            @Override
                            public void onSuccess(UsagePoint result) {
                                view.getForm().setDirtyData(false);
                                if (result == null || (result != null && result.getCustomerAgreementId() == null)) {
                                    noTariffCalcConfirm("demo.addmeterreadings.no.usagepoint1");
                                } else {
                                    addReadings();
                                }
                            }
                        });
                    } else {
                        addReadings();
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void noTariffCalcConfirm(String message) {
        panel.disableTariffCalc();
        Dialogs.confirm (
                new String[] {MessagesUtil.getInstance().getMessage(message),
                        MessagesUtil.getInstance().getMessage("demo.addmeterreadings.no.usagepoint2"),
                        MessagesUtil.getInstance().getMessage("option.continue")},
                        ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                        ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                        ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            addReadings();
                        }
                    }
                },
                addMeterReadingsForm.getSaveBtn().getAbsoluteLeft(), addMeterReadingsForm.getSaveBtn().getAbsoluteTop());
    }

    private void addReadings() {

        Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon(),
                view.getForm().getSaveBtn().getAbsoluteLeft() + 10, view.getForm().getSaveBtn().getAbsoluteTop() - 25);
        int deleteExistingReadings = MeterMngStatics.DELETE_EXISTING_READINGS_ALL;
        if (panel.radioDeleteSelected.getValue()) {
            deleteExistingReadings = MeterMngStatics.DELETE_EXISTING_READINGS_SELECTED;
        } else if (panel.radioAppend.getValue()) {
            deleteExistingReadings = MeterMngStatics.DELETE_EXISTING_READINGS_APPEND;
        }
        int zeroInstances = -1;
        if (panel.radioZeroRandom.getValue()) {
            zeroInstances = Integer.parseInt(panel.textZeroRandom.getValue());
        }
        int missingInstances = -1;
        if (panel.radioMissingRandom.getValue()) {
            missingInstances = Integer.parseInt(panel.textMissingRandom.getValue());
        }
        ArrayList<Long> mdcChannelIds = null;
        if (panel.formMdcChannels.isVisible()) {
            mdcChannelIds = new ArrayList<Long>();
            for (int i = 0; i < panel.mdcChannelsBox.getItemCount(); i++) {
                if (panel.mdcChannelsBox.isItemSelected(i)) {
                    mdcChannelIds.add(Long.valueOf(panel.mdcChannelsBox.getValue(i)));
                }
            }
        }
        final boolean sendTariffCalc = panel.tariffCalcBox.getValue();
        clientFactory.getMeterRpc().addMeterReadings(panel.selectedMeter, panel.startBox.getValue(),
                panel.endBox.getValue(), Integer.parseInt(panel.readingIntervalBox.getSelectedValue()),
                panel.getReadingTypeIds(), deleteExistingReadings, sendTariffCalc, panel.zeroStartBox.getValue(),
                panel.zeroEndBox.getValue(), zeroInstances, panel.missingStartBox.getValue(),
                panel.missingEndBox.getValue(), missingInstances, mdcChannelIds,
                new ClientCallback<IpayResponseData>() {
                    @Override
                    public void onSuccess(IpayResponseData result) {
                        WaitingDialogUtil.getCurrentInstance().hide();
                        if (sendTariffCalc) {
                            if (result == null) {
                                showResultMessages("usagepoint.calculate.tariff.connection.error", true);
                            } else if (!result.getResCode().equals("meterMng000")) {
                                showResultMessages("usagepoint.calculate.tariff.error", true);
                            } else {
                                showResultMessages("demo.addmeterreadings.tariffCalc.success", false);
                            }
                        } else {
                            showResultMessages("demo.addmeterreadings.success", false);
                        }
                        panel.populateExistingDateRange();
                    }
                });
    }

    private void showResultMessages(String key, boolean tariffCalcFailed) {
        Messages messages = MessagesUtil.getInstance();
        String value = "link.meter.readings";
        if (panel.radioRegisterReadings.getValue()) {
            value = "register.reading.txn.label";
        }
        String meterNumber[] = new String[] { panel.selectedMeter.getNumber(), messages.getMessage(value) };
        if (tariffCalcFailed) {
            Button saveBtn = view.getForm().getSaveBtn();
            MediaResource mediaResource = MediaResourceUtil.getInstance();
            Dialogs.displayErrorMessage(messages.getMessage(key), mediaResource.getErrorIcon(),
                    saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop(), messages.getMessage("button.close"));
            Dialogs.displayWarningMessage(messages.getMessage("demo.addmeterreadings.tariffCalc.failed", meterNumber),
                    mediaResource.getWarningIcon());
        } else {
            Dialogs.displayInformationMessage(messages.getMessage(key, meterNumber),
                    MediaResourceUtil.getInstance().getInformationIcon());
        }
    }

    protected boolean isValidInput() {
        panel.clearErrors();
        boolean valid = true;

        SuggestBox meterBox = panel.getMeterBox();
        String meterBoxText = meterBox.getText();
        if (panel.selectedMeter == null || !panel.selectedMeter.getNumber().equals(meterBoxText)) {
            MeterSuggestion ms = ((MeterSuggestOracle) meterBox.getSuggestOracle()).getFirstSuggestion();
            if (ms == null) {
                if (meterBoxText != null && !meterBoxText.isEmpty()) {
                    panel.setSelected(new MeterDto(null, meterBoxText));
                }
            } else {
                MeterDto meterDto = ms.getMeter();
                if (meterDto.getNumber().equals(meterBoxText)) {
                    panel.setSelected(meterDto);
                }
            }
            if (panel.selectedMeter == null || meterBoxText.isEmpty()) {
                valid = false;
                panel.meterNumberElement
                        .setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.meter"));
            }
        }

        Date start = panel.startBox.getValue();
        String startInput = panel.startBox.getTextBox().getText();
        Date end = panel.endBox.getValue();
        String endInput = panel.endBox.getTextBox().getText();
        String format = FormatUtil.getInstance().getDateFormat() + " " + FormatUtil.getInstance().getTimeFormat();
        if (start == null && startInput == null) {
            valid = false;
            panel.startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
        } else if (start == null && startInput != null) {
            valid = false;
            panel.startElement.showErrorMsg(
                    MessagesUtil.getInstance().getMessage("meterreadings.error.start.format", new String[] { format }));
        }

        if (end == null && endInput == null) {
            valid = false;
            panel.endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
        } else if (end == null && endInput != null) {
            valid = false;
            panel.endElement.showErrorMsg(
                    MessagesUtil.getInstance().getMessage("meterreadings.error.end.format", new String[] { format }));
        }

        if (start != null && end != null && !start.before(end)) {
            valid = false;
            panel.startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
        }

        // Reading types
        if (panel.getReadingTypeIds().size() == 0) {
            valid = false;
            panel.readingTypesElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.types"));
        }

        if (panel.checkZero.getValue()) {
            Date zeroStartDate = panel.zeroStartBox.getValue();
            Date zeroEndDate = panel.zeroEndBox.getValue();
            if (zeroStartDate == null) {
                valid = false;
                panel.zeroStartElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
            } else if (start != null && zeroStartDate.before(start)) {
                valid = false;
                panel.zeroStartElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.misc.start"));
            }
            if (zeroEndDate == null) {
                valid = false;
                panel.zeroEndElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
            } else if (end != null && zeroEndDate.after(end)) {
                valid = false;
                panel.zeroEndElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.misc.end"));
            }
            if (zeroStartDate != null && zeroEndDate != null && !zeroStartDate.before(zeroEndDate)) {
                valid = false;
                panel.zeroStartElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
            }
            if (panel.radioZeroRandom.getValue()) {
                String zeroRandomText = panel.textZeroRandom.getValue();
                if (zeroRandomText.isEmpty()) {
                    valid = false;
                    panel.formZeroRandom.showErrorMsg(
                            MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.instances.required"));
                } else {
                    try {
                        int zeroRandom = Integer.parseInt(zeroRandomText);
                        if (zeroRandom < 0 || zeroRandom > 100) {
                            valid = false;
                            panel.formZeroRandom.showErrorMsg(MessagesUtil.getInstance()
                                    .getMessage("demo.addmeterreadings.error.instances.range"));
                        }
                    } catch (NumberFormatException nfe) {
                        valid = false;
                        panel.formZeroRandom.showErrorMsg(
                                MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.instances.format"));
                    }
                }
            }
        }

        if (panel.checkMissing.getValue()) {
            Date missingStartDate = panel.missingStartBox.getValue();
            Date missingEndDate = panel.missingEndBox.getValue();
            if (missingStartDate == null) {
                valid = false;
                panel.missingStartElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
            } else if (start != null && missingStartDate.before(start)) {
                valid = false;
                panel.missingStartElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.misc.start"));
            }
            if (missingEndDate == null) {
                valid = false;
                panel.missingEndElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
            } else if (end != null && missingEndDate.after(end)) {
                valid = false;
                panel.missingEndElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.misc.end"));
            }
            if (missingStartDate != null && missingEndDate != null && !missingStartDate.before(missingEndDate)) {
                valid = false;
                panel.missingStartElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
            }
            if (panel.radioMissingRandom.getValue()) {
                String missingRandomText = panel.textMissingRandom.getValue();
                if (missingRandomText.isEmpty()) {
                    valid = false;
                    panel.formMissingRandom.showErrorMsg(
                            MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.instances.required"));
                } else {
                    try {
                        int missingRandom = Integer.parseInt(missingRandomText);
                        if (missingRandom < 0 || missingRandom > 100) {
                            valid = false;
                            panel.formMissingRandom.showErrorMsg(MessagesUtil.getInstance()
                                    .getMessage("demo.addmeterreadings.error.instances.range"));
                        }
                    } catch (NumberFormatException nfe) {
                        valid = false;
                        panel.formMissingRandom.showErrorMsg(
                                MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.instances.format"));
                    }
                }
            }
        }

        if (panel.radioRegisterReadings.getValue() && panel.mdcChannelsBox.getSelectedIndex() == -1) {
            valid = false;
            panel.mdcChannelsElement
                    .showErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.mdc.channel"));
        }

        return valid;
    }

    private void onClear() {
        panel.clearErrors();
        panel.clearFields();

        if (energyForwardId != null) {
            for(int i=0;i<panel.readingTypesBox.getItemCount();i++) {
                if (panel.readingTypesBox.getValue(i).equalsIgnoreCase(energyForwardId)) {
                    panel.readingTypesBox.setItemSelected(i, true);
                } else {
                    panel.readingTypesBox.setItemSelected(i, false);
                }
            }
        }

        panel.readingIntervalBox.setSelectedIndex(0);
        panel.mdcChannelsBox.setSelectedIndex(0);
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onArrival(Place p) {
    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof AddMeterReadingsPlace) {
            AddMeterReadingsPlace p = (AddMeterReadingsPlace) place;
            if (AddMeterReadingsPlace.SINGLE_METER_TYPE.equals(p.getMeterType())) {
                return true;
            }
        }
        return false;
    }
}
