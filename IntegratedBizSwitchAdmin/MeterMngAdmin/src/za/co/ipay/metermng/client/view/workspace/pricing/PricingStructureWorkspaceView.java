package za.co.ipay.metermng.client.view.workspace.pricing;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.PricingStructurePlace;
import za.co.ipay.metermng.client.view.component.tariff.PricingStructureView;
import za.co.ipay.metermng.client.view.component.tariff.TariffView;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.pricing.PricingStructureDto;
import za.co.ipay.metermng.shared.tariff.TariffWithData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

public class PricingStructureWorkspaceView extends BaseWorkspace {
    
    @UiField DeckLayoutPanel deckPanel;
    
    @UiField SimpleTableView<PricingStructureDto> view;
    PricingStructureView pricingStructureView;
    
    @UiField SimpleTableView<TariffWithData> view2;
    TariffView tariffView;
    
//    private Place place;
        
    private Logger logger = Logger.getLogger("PricingStructureWorkspaceView");
        
    private static PricingStructureWorkspaceViewUiBinder uiBinder = GWT.create(PricingStructureWorkspaceViewUiBinder.class);

    interface PricingStructureWorkspaceViewUiBinder extends UiBinder<Widget, PricingStructureWorkspaceView> {
    }
    
    public PricingStructureWorkspaceView(ClientFactory clientFactory, PricingStructurePlace pricingStructurePlace) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(PricingStructurePlace.toPlaceString(pricingStructurePlace));
        setHeaderText(MessagesUtil.getInstance().getMessage("pricingstructure.title"));
        initUi();
    }
    
    private void initUi() {                
        pricingStructureView = new PricingStructureView(this, clientFactory, view);
        tariffView = new TariffView(clientFactory, this, view2);
        deckPanel.showWidget(view);
    }
    
    public void goToPricingStructures(Long pricingStructureId, int numberOfTariffs) {
        tariffView.clearForm();        
        pricingStructureView.updatePricingStructure(pricingStructureId, numberOfTariffs);
        deckPanel.showWidget(0);
        deckPanel.animate(getAnimationTime());
    }
    
    public void goToTariffs(PricingStructure pricingStructure) {
        tariffView.initTariffData(pricingStructure);
        deckPanel.showWidget(1);
        deckPanel.animate(getAnimationTime());
    }
    
    @Override
    public void onLeaving() {
    }
    
    @Override
    public void onSelect() {
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at pricing structure: "+place);
        pricingStructureView.onArrival(place);
    }

    @Override
    public void onClose() {
        onLeaving();
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof PricingStructurePlace;
    }
    
    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("Received notification: "+notification);
        if (MeterMngStatics.USER_CURRENT_GROUP.equals(notification.getDataType())
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            logger.info("The user's current group has changed - reloading this PricingStructure workspace...");
            pricingStructureView.clear();
            pricingStructureView.getPricingStructuresCount(true);
        } else if (NotificationType.DATA_UPDATED == notification.getNotificationType() 
                && notification.getDataType().equals("billingDet")){
            tariffView.handleBillingDetNotification();
        }
    }
    
    public TariffView getTariffView() {
        return tariffView;
    }
    
}