package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.metermng.shared.LocationHistData;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

@RemoteServiceRelativePath("secure/location.do")
public interface LocationRpc extends RemoteService {
    public ArrayList<LocationHistData> getLocationHistory(Long locationId);
}
