package za.co.ipay.metermng.client.view.component.tariff;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.gwt.common.client.widgets.TabbableTextInputCell;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.tariff.paytype.PayTypeDiscount;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.LocaleInfo;
import com.google.gwt.i18n.client.constants.NumberConstants;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

public class PayTypeDiscountPanel extends BaseComponent {

	@UiField FormElement discountElement;
	@UiField CellTable<PayTypeDiscount> discountTable;

	private Map<String, PayTypeDiscount> masterPayTypeDiscountMap = new HashMap<String, PayTypeDiscount>();
	private ListDataProvider<PayTypeDiscount> discountDataProvider;
	private DecimalInputCell discountCell;
	protected static final NumberConstants numberConstants = LocaleInfo.getCurrentLocale().getNumberConstants();

	private ContainsPayTypeDiscountPanel parentWorkspace;;
	private ArrayList<PayTypeDiscount> thisPayTypeDiscountsList;

	private static Logger logger = Logger.getLogger(PayTypeDiscountPanel.class.getName());

	private static PayTypeDiscountPanelUiBinder uiBinder = GWT.create(PayTypeDiscountPanelUiBinder.class);

	interface PayTypeDiscountPanelUiBinder extends UiBinder<Widget, PayTypeDiscountPanel> {
	}

	public PayTypeDiscountPanel(ContainsPayTypeDiscountPanel parentWorkspace) {
		this.parentWorkspace = parentWorkspace;
		discountTable = new CellTable<PayTypeDiscount>();
		initWidget(uiBinder.createAndBindUi(this));
		initUi();
	}

	public void initUi() {
		createDiscountTable();
		clearErrors();
	}    

	private void createDiscountTable() {
		if (discountDataProvider == null) {            
			discountDataProvider = new ListDataProvider<PayTypeDiscount>();

			//PayType name
			TextColumn<PayTypeDiscount> payTypeColumn = new TextColumn<PayTypeDiscount>() {
				@Override
				public String getValue(PayTypeDiscount data) {
					return data.getPayType();
				}                
			};  

			boolean numberRight = true;
			//PayType Discount
			if (numberConstants.percentPattern().startsWith("\u0025") 
					|| numberConstants.percentPattern().startsWith("\uff05")
					|| numberConstants.percentPattern().startsWith("\u066a")) {
				discountCell = new DecimalInputCell(numberConstants.percent(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
				//discountCell.setAlignment(TextAlignment.LEFT);
				numberRight = false;
			} else {
				discountCell = new DecimalInputCell("", numberConstants.percent(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
				//discountCell.setAlignment(TextAlignment.RIGHT);
			}
			final Column<PayTypeDiscount, String> discountColumn = new Column<PayTypeDiscount, String>(discountCell) {
				@Override
				public String getValue(PayTypeDiscount data) {
					if (data.getDiscountMultiplier() == null) {
						return null;
					}
					return FormatUtil.getInstance().formatDecimal(data.getDiscountMultiplier());
				}                
			};            
			discountColumn.setFieldUpdater(new FieldUpdater<PayTypeDiscount, String>() {
				@Override
				public void update(int index, PayTypeDiscount data, String value) {
				    if (value != null && !value.isEmpty() && discountCell.isNumeric(value)) {
				        parentWorkspace.getForm().setDirtyData(true);                   
				        data.setDiscountMultiplier(FormatUtil.getInstance().parseDecimal(value));
				        discountTable.redraw();
				        MeterMngClientUtils.focusOnNext(discountTable, index, discountTable.getColumnIndex(discountColumn));
				    } else {
				        if (value == null || value.isEmpty()) { // if value is empty => cleared by user
				            masterPayTypeDiscountMap.get(data.getPayType()).setDiscountMultiplier(null);
				        } else { // If not null but the value is not numeric
				            discountCell.getViewData(data).setInvalid(true);     // Mark as invalid.
				        }
				        discountTable.redraw();
				        MeterMngClientUtils.focusOnNext(discountTable, index, discountTable.getColumnIndex(discountColumn));
				    }
				}
			});
			if (numberRight) {
				discountColumn.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
			} else {
				discountColumn.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_LEFT);
			}


			//Discount description
			Column<PayTypeDiscount, String> discountDescripColumn = new Column<PayTypeDiscount, String>(new TabbableTextInputCell()) {
				@Override
				public String getValue(PayTypeDiscount data) {
					return data.getDescription();
				}                
			};  
			discountDescripColumn.setFieldUpdater(new FieldUpdater<PayTypeDiscount, String>() {
				@Override
				public void update(int index, PayTypeDiscount data, String value) {
					parentWorkspace.getForm().setDirtyData(true);                   
					data.setDescription(value);
				}
			});

			// Add the columns
			discountTable.addColumn(payTypeColumn, MessagesUtil.getInstance().getMessage("tariff.field.heading.paytype"));
			discountTable.addColumn(discountColumn, MessagesUtil.getInstance().getMessage("tariff.field.heading.discount"));
			discountTable.addColumn(discountDescripColumn, MessagesUtil.getInstance().getMessage("tariff.field.heading.description"));
			discountTable.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
			discountDataProvider.addDataDisplay(discountTable);

			initDiscountDefaultTableRows();
		}    
	}
	
	public void initDiscountDefaultTableRows() {
		clearPayTypeDiscountMasterMap();
		populateDiscountDataProviderList();
	}

	private void clearPayTypeDiscountMasterMap() {
		masterPayTypeDiscountMap.clear();
		addToMasterDiscountMap("none");
		addToMasterDiscountMap("cash");
		addToMasterDiscountMap("creditCard");
		addToMasterDiscountMap("debitCard");
		addToMasterDiscountMap("debitOrder");
		addToMasterDiscountMap("cheque");
		addToMasterDiscountMap("postalOrder");
		addToMasterDiscountMap("electronic");
		addToMasterDiscountMap("voucher");
		addToMasterDiscountMap("paymentNetwork");
		addToMasterDiscountMap("mobileWallet");
		addToMasterDiscountMap("other");
	}
	private void addToMasterDiscountMap(String payType) {
		masterPayTypeDiscountMap.put(payType, new PayTypeDiscount(payType, "", null));
	}

	private void populateDiscountDataProviderList() {
		discountDataProvider.getList().clear();
		for (PayTypeDiscount pt : masterPayTypeDiscountMap.values()) {
			discountDataProvider.getList().add(pt);
		}
	}

	public void setCalcContentsPayTypeDiscount(List<PayTypeDiscount> thisDiscountList) {
		//discounts
		discountDataProvider.getList().clear();
		initDiscountDefaultTableRows();
		if (thisDiscountList != null) {
			for (PayTypeDiscount thisPayTypeDiscount : thisDiscountList) {
				PayTypeDiscount pt = masterPayTypeDiscountMap.get(thisPayTypeDiscount.getPayType());
				pt.setDescription(thisPayTypeDiscount.getDescription());
				//check for null in case payTYpeDiscount also on a template with null value
				if (thisPayTypeDiscount.getDiscountMultiplier() != null) {
				    pt.setDiscountMultiplier(thisPayTypeDiscount.getDiscountMultiplier().movePointRight(2));
				}
				masterPayTypeDiscountMap.put(thisPayTypeDiscount.getPayType(), pt);
			}
		}
		populateDiscountDataProviderList();
		discountDataProvider.refresh();
		discountTable.redraw();
	}         

	public boolean isValid() {
		boolean valid = true;
		//Discounts
		for (PayTypeDiscount pt : discountDataProvider.getList()) {
			if (pt.getDiscountMultiplier() == null || pt.getDiscountMultiplier().equals(BigDecimal.ZERO)) {
				continue;
			}
			if (pt.getDiscountMultiplier().compareTo(BigDecimal.ZERO) == -1) {
				valid = false;
				discountElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.discount"));   // cant be < 0
			}
			if (pt.getDescription() == null || pt.getDescription().isEmpty()) {
				valid = false;
				discountElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.discount.descrip"));  //if have discount, description is required
			}
		}
		return valid;
	}    

	public void clearErrors() {        
		discountElement.setErrorMsg(null);
	}
	
    public void clear() {      
        clearErrors();
        initDiscountDefaultTableRows();
    }
	
	public ArrayList<PayTypeDiscount> getThisPayTypeDiscountsList() {
		thisPayTypeDiscountsList = new ArrayList<PayTypeDiscount>();
		for (PayTypeDiscount pt : discountDataProvider.getList()) {
			if (pt.getDiscountMultiplier() != null && pt.getDiscountMultiplier().compareTo(BigDecimal.ZERO) != 0) {
				PayTypeDiscount ptDiv = new PayTypeDiscount(pt.getPayType(), pt.getDescription(), pt.getDiscountMultiplier().movePointLeft(2));
				thisPayTypeDiscountsList.add(ptDiv);
			}
		}	

		return thisPayTypeDiscountsList;
	}
	
	public FormElement getDiscountElement() {
		return discountElement;
	}
	
}

