package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.mybatis.generated.model.StsMeter;

import com.google.gwt.event.shared.GwtEvent;

public class FreeIssueTokenIssuedEvent extends GwtEvent<FreeIssueTokenIssuedEventHandler> {

    public static Type<FreeIssueTokenIssuedEventHandler> TYPE = new Type<FreeIssueTokenIssuedEventHandler>();
    
    StsMeter meter;
    
    public FreeIssueTokenIssuedEvent(StsMeter meter) {
        this.meter = meter;
    }
    
    public StsMeter getMeter() {
        return meter;
    }
     
    @Override
    public Type<FreeIssueTokenIssuedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(FreeIssueTokenIssuedEventHandler handler) {
        handler.handleFreeIssueTokenIssuedEvent(this);
    }


}
