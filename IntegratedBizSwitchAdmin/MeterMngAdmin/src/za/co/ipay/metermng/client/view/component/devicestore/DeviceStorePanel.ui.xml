<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component">
    <ui:style>
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:FlowPanel>
        <p1:FormRowPanel>
            <p1:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getDeviceStoreName}:" helpMsg="{msg.getDeviceStoreNameHelp}" required="true">
                <g:TextBox text="" ui:field="nameTextBox" debugId="nameTextBox" title="{msg.getDeviceStoreName}"/>
            </p1:FormElement>
            <p1:FormElement ui:field="descriptionElement" debugId="descriptionElement" labelText="{msg.getDeviceStoreDescription}:" helpMsg="{msg.getDeviceStoreDescriptionHelp}">
                <g:TextBox ui:field="descriptionTextBox" debugId="descriptionTextBox" title="{msg.getDeviceStoreDescription}" visibleLength="30"/>
            </p1:FormElement>
            <p1:FormElement ui:field="activeElement" helpMsg="{msg.getDeviceStoreActiveHelp}">
                <g:CheckBox text="{msg.getDeviceStoreActive}" checked="false" ui:field="chckbxActive" debugId="chckbxActive" styleName="gwt-Label-bold-left"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        <p1:FormRowPanel ui:field="storesOtherVendorsMeterRow">
            <p1:FormElement ui:field="storesOtherVendorsMeterElement" debugId="storesOtherVendorsMeterElement" helpMsg="{msg.getDeviceStoreStoresVendorsMeterHelp}">
                <g:CheckBox text="{msg.getDeviceStoreStoresVendorsMeter}" checked="false" ui:field="storesOtherVendorsMeter" debugId="allowDSRespondCheck" styleName="gwt-Label-bold-left"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        <p1:FormRowPanel ui:field="customMsgRow">
            <p1:FormElement ui:field="customMsgElement" debugId="customMsgElement" labelText="{msg.getDeviceStoreCustomMessage}:" helpMsg="{msg.getDeviceStoreCustomMessageHelp}">
                <g:TextBox width="100%" ui:field="customMsg" debugId="customMsg" title="{msg.getDeviceStoreCustomMessage}" visibleLength="40"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        <g:DisclosurePanel ui:field="addressPanel" debugId="addressPanel" width="100%" styleName="gwt-DisclosurePanel">
            <g:header>
                <ui:text from="{msg.getDeviceStoreLocationTitle}"/>
            </g:header>
            <g:FlowPanel styleName="simple-center">
                <ipaycomp:LocationComponent ui:field="locationComponent" debugId="locationComponent"/>
            </g:FlowPanel>
        </g:DisclosurePanel>
    </g:FlowPanel>

</ui:UiBinder> 