package za.co.ipay.metermng.client.view.workspace.meter.readings.add.supermeter;

import java.util.Date;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarUtil;
import com.google.gwt.user.datepicker.client.DateBox;

public class AddSuperMeterReadingsPanel extends SimpleFormPanel {
    
    @UiField FormElement superMeterNumberElement;
    @UiField FormElement startElement;
    @UiField FormElement endElement;
    @UiField FormElement readingTypeElement;
    @UiField FormElement readingIntervalElement;   
    @UiField FormElement variationsElement;
    @UiField FormElement hourOfDayElement;
    @UiField FormElement percentageElement;
    @UiField FormElement addedVariationsElement;
    
    @UiField ListBox superMeterNumberBox;
    @UiField DateBox startBox;
    @UiField DateBox endBox;
    @UiField ListBox readingTypeBox;
    @UiField ListBox readingIntervalBox;
    @UiField CheckBox deleteBox;
    @UiField CheckBox regenerateSubMetersBox;
    @UiField ListBox hourOfDayBox;
    @UiField ListBox percentageBox;
    @UiField ListBox addedVariationsBox;
    
    private static AddSuperMeterReadingsPanelUiBinder uiBinder = GWT.create(AddSuperMeterReadingsPanelUiBinder.class);

    interface AddSuperMeterReadingsPanelUiBinder extends UiBinder<Widget, AddSuperMeterReadingsPanel> {
    }

    public AddSuperMeterReadingsPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();        
    }

    private void initUi() {
        Format format = FormatUtil.getInstance();
        StrictDateFormat strictDateFormat = new StrictDateFormat(
                DateTimeFormat.getFormat(format.getDateFormat() + " " + format.getTimeFormat()));
        startBox.setFormat(strictDateFormat);
        endBox.setFormat(strictDateFormat);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate());           
        
        hourOfDayBox.addItem("");
        for(int i=0;i<24;i++) {
            if (i < 10) {
                hourOfDayBox.addItem("0"+i);
            } else {
                hourOfDayBox.addItem(""+i);
            }
        }
        
        percentageBox.addItem("", "");
        percentageBox.addItem("90%", "0.9");
        percentageBox.addItem("85%", "0.85");
        percentageBox.addItem("80%", "0.8");
        percentageBox.addItem("75%", "0.75");
        percentageBox.addItem("70%", "0.7");
        
        addFieldHandlers();
    }
    
    @Override
    public void addFieldHandlers() {
        superMeterNumberBox.addChangeHandler(new FormDataChangeHandler(form));
        startBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        endBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        readingIntervalBox.addChangeHandler(new FormDataChangeHandler(form));
        readingTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        deleteBox.addClickHandler(new FormDataClickHandler(form));
        regenerateSubMetersBox.addClickHandler(new FormDataClickHandler(form));
        hourOfDayBox.addChangeHandler(new FormDataChangeHandler(form));
        percentageBox.addChangeHandler(new FormDataChangeHandler(form));
    }

    @Override
    public void clearFields() {
        superMeterNumberBox.setSelectedIndex(0);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate());
        readingTypeBox.setSelectedIndex(0);
        deleteBox.setValue(true);
        regenerateSubMetersBox.setValue(true);
        readingIntervalBox.setSelectedIndex(0);
        hourOfDayBox.setSelectedIndex(0);
        percentageBox.setSelectedIndex(0);
        addedVariationsBox.clear();
    }
    
    private Date getInitStartDate() {
        Date startDate = getStartPreviousDay();
        CalendarUtil.addDaysToDate(startDate, -2); 
        String start = DateTimeFormat.getFormat("dd/MM/yyyy").format(startDate);
        return DateTimeFormat.getFormat("dd/MM/yyyy HH:mm").parse(start+" 00:00");
    }
    
    private Date getInitEndDate() {
        return getStartPreviousDay();
    }
    
    protected Date getStartPreviousDay() {
        Date now = new Date();
        CalendarUtil.addDaysToDate(now, -1);
        String start = DateTimeFormat.getFormat("dd/MM/yyyy").format(now);
        return DateTimeFormat.getFormat("dd/MM/yyyy HH:mm").parse(start+" 23:59");    
    }

    @Override
    public void clearErrors() {
        superMeterNumberElement.setErrorMsg(null);
        startElement.setErrorMsg(null);
        endElement.setErrorMsg(null);
        readingTypeElement.setErrorMsg(null);
        readingIntervalElement.setErrorMsg(null);
        variationsElement.setErrorMsg(null);
        hourOfDayElement.setErrorMsg(null);
        percentageElement.setErrorMsg(null);
        addedVariationsElement.setErrorMsg(null);        
    }
    
    @UiHandler("addButton")
    public void onAdd(ClickEvent e) {
        clearErrors();
        boolean valid = true;
        if (hourOfDayBox.getSelectedIndex() < 1) {
            hourOfDayElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.hour.required"));
            valid = false;
        }
        if (percentageBox.getSelectedIndex() < 1) {
            percentageElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.percentage.required"));
            valid = false;
        }
        
        if (valid) {            
            String hour = hourOfDayBox.getValue(hourOfDayBox.getSelectedIndex());
            for(int i=0;i<addedVariationsBox.getItemCount();i++) {
                if (addedVariationsBox.getItemText(i).startsWith(hour)) {
                    valid = false;
                    hourOfDayElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.variation.duplicate"));
                }
            }
            if (valid) {
                int index = percentageBox.getSelectedIndex();
                String percentageValue = percentageBox.getValue(index);
                String percentage = percentageBox.getItemText(index);
                addedVariationsBox.addItem(hour+": "+percentage, hour+": "+percentageValue);
                hourOfDayBox.setSelectedIndex(0);
                percentageBox.setSelectedIndex(0);
            }
        }
    }
    
    @UiHandler("removeButton")
    public void onRemove(ClickEvent e) {
        for(int i=addedVariationsBox.getItemCount()-1;i>=0;i--) {
            if (addedVariationsBox.isItemSelected(i)) {
                addedVariationsBox.removeItem(i);
            }
        }
    }
}