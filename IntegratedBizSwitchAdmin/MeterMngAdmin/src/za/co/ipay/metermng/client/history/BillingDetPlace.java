package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class BillingDetPlace extends Place {
    
    public static final String ALL = "all";
    public static BillingDetPlace ALL_BILLING_DET_PLACE = new BillingDetPlace();
    
    private String name;
    
    public BillingDetPlace() {
    }
    
    public BillingDetPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(BillingDetPlace place) {
        return "billingDet:"+place.getName();
    }
    
    @Prefix(value = "billingDet")
    public static class Tokenizer implements PlaceTokenizer<BillingDetPlace> {
        
        @Override
        public String getToken(BillingDetPlace place) {
            return place.getName();
        }

        @Override
        public BillingDetPlace getPlace(String token) {
            return new BillingDetPlace(token);
        }
    }
}
