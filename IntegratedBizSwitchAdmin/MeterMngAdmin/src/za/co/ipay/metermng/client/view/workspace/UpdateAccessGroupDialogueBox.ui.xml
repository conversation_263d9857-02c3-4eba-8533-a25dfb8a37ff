<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets"
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
	xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component">
	<ui:style>
	   .errorMsg {
            color: Red;
        }
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:HTMLPanel>
		<table>
			<tr>
				<td>
					<g:VerticalPanel spacing="10">
						<g:HorizontalPanel spacing="2">
							<g:Label ui:field="accessGroupLbl" debugId="accessGroupLbl" text="Access Group:"/>
							<g:Label ui:field="accessGroupNameLbl" debugId="accessGroupNameLbl" />
						</g:HorizontalPanel>
						<ipay:Message ui:field="errorMsg" debugId="errorMsg" visible="false" styleName="{style.errorMsg}"/>
                        <ipay:Message ui:field="clearFuturePricingstructures" debugId="clearFuturePricingstructures"/>
                        <ipay:Message ui:field="clearUPAndCustomerLocationGroups" debugId="clearUPAndCustomerLocationGroups"/>
                        <ipay:Message ui:field="confirm" debugId="confirm"/> 
                        <g:FlowPanel>
                        	<g:Button ui:field="btnUpdate" debugId="btnUpdate" text="{msg.getUpdateButton}" styleName="gwt-Button-ipay"/>
							<g:Button ui:field="btnCancel" debugId="btnCancel" text="{msg.getCancelButton}" styleName="gwt-Button-ipay"/>
                        </g:FlowPanel>
					</g:VerticalPanel>
				</td>
			</tr>
		</table>
	</g:HTMLPanel>
</ui:UiBinder> 
