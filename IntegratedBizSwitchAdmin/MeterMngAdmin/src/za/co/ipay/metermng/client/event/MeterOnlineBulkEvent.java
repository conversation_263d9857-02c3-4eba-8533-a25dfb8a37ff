package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class MeterOnlineBulkEvent extends GwtEvent<MeterOnlineBulkEventHandler> {
	
	public static Type<MeterOnlineBulkEventHandler> TYPE = new Type<MeterOnlineBulkEventHandler>();
    
    private String name;
    
    public MeterOnlineBulkEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<MeterOnlineBulkEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MeterOnlineBulkEventHandler handler) {
        handler.handleEvent(this);
    }
    
}