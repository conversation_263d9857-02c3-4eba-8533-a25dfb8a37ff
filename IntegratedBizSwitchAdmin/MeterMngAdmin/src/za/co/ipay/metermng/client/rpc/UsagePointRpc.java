package za.co.ipay.metermng.client.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;

@RemoteServiceRelativePath("secure/usagepoint.do")
public interface UsagePointRpc extends RemoteService {

    UsagePointData updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation,
            LocationData usagePointLocation, List<MdcChannelReadingsDto> channelReadingsList) throws ValidationException, ServiceException;

    UsagePointData updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation, 
            LocationData usagePointLocation) throws ValidationException, ServiceException;

    void removeCustomerFromUsagePoint(UsagePointData usagePointData) throws ServiceException;

    UsagePoint assignCustomerToUsagePoint(Long usagePointId, Long customerAgreementId) throws ServiceException;

    ArrayList<UsagePointHistData> fetchUsagePointHistory(Long usagePointId) throws ServiceException;

    ArrayList<CustomerTransAlphaData> fetchTransactionHistory(Long usagePointId) throws ServiceException;

    ServiceException validateInstallationDate(Long usagePointId, Long oldMeterId, String meterNum, Date newInstallDate);
    
    UsagePoint reAssignMeterToUsagePoint(UsagePointData usagePoint, MeterData meter, List<MdcChannelReadingsDto> channelReadingsList) throws ValidationException, ServiceException;
    
    void removeMeterFromUsagePoint(UsagePointData usagePointData) throws ServiceException, ValidationException;

    String getAutoGeneratedRef();
    
    ArrayList<MdcTransData> getMdcTransByUsagePoint(Long usagePointId) throws ServiceException;
    
    Boolean checkIfInUse(Long pricingStructureId);
    
    Boolean isValidAccessGroupUpdate(Long usagePointId, Long customerAgreementId, Long accessGroupId);
    
    ArrayList<UsagePoint> fetchUsagePointsByCustomerAgreementId(Long customerAgreementId);
    
    UsagePoint getUsagePointByName(String usagePointName) throws ServiceException;
    
    CustomerTransItemOutstandCharges getCustomerTransItemFromCyclicCharges(UsagePointData usagePointData, Date date) throws ServiceException;
    
    void writeoffOutstandingCyclicCharges(Long usagePointId, Date upLastCyclicChargeDate, Date upLastBillingCyclicChargeDate, Date upNewCyclicChargeDate, String userRecEntered, CustomerTransItemOutstandCharges outstandCharges, SpecialActionReasonsLog logEntry) throws ServiceException;
    
    IpayResponseData sendMeterInspectionRequestMsg(UsagePointData usagePointData, String comment);

    ArrayList<CustomerTransAlphaDataWithTotals> fetchTransactionHistoryWithTotals(Long usagepointId) throws ServiceException;
    
    List<Integer> countUpWithMdcUsingBillingDetId(Long billingDetId, String userName) throws ServiceException;
    
    MdcChannelMatchDto countUpWithMdc(Long mdcId, String userName) throws ServiceException;
    
    MdcChannelMatchDto getMdcChannelCompatibility(Long mdcId, String userName) throws ServiceException;
    
    MdcChannelMatchDto getMdcChannelUpdateCompatibility(Long mdcId, MdcChannelDto updatedMdcChannel, String userName) throws ServiceException;
    
    MdcChannelMatchDto getMeterModelChannelCompatibility(Long meterModelId, Long newMdcId, String userName) throws ServiceException;

    ArrayList<UnitsTrans> getUnitsAccountTransactions(Long unitsAccountId);

    BigDecimal inputUnitsAccountAdjustment(UnitsTrans unitsTrans) throws ServiceException;

    IpayResponseData sendSyncUnitsAccountBalance(MeterData meterData, Long unitsAccountId);

    UnitsTrans getUnitsTransactionFromCustomerTransId(Long customerTransId);

    ArrayList<UpMeterInstallHistData> fetchUpMeterInstallHistory(Long usagePointId, Long meterId, boolean usingAccessGroup);
    
    List<UpPricingStructureData> getAllUPPricingStructures(Long usagePointId, boolean usingAccessGroup) throws ServiceException;

    Boolean deleteUpPricingStructure(Long upPricingStructureId, Long usagePointId) throws ServiceException;
    
    List<UpPricingStructureHistData> fetchUpPricingStructureHistory(Long usagePointId, boolean usingAccessGroup);

    CustomerUsagePointMiscInfo getLatestCustomerUsagePointMiscInfoByUsagePointId(long usagePointId, Date date);
}
