package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.metermng.mybatis.generated.model.CustAccNotify;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.mybatis.generated.model.GroupFeature;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.NdpScheduleData;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;
import za.co.ipay.metermng.shared.dto.user.UserAvailableGroupsData;
import za.co.ipay.metermng.shared.group.GroupTypeData;

public interface GroupRpcAsync extends SelectionDataRpcAsync {

    void getAllGroupTypes(AsyncCallback<ArrayList<GroupTypeData>> callback);
    void getGroupTypesWithHierarchy(AsyncCallback<ArrayList<GroupTypeData>> callback);
    void getActiveGroupTypesWithHierarchy(AsyncCallback<ArrayList<GroupTypeData>> callback);
    void getUsagepointGroupTypesWithHierarchy(AsyncCallback<ArrayList<GroupTypeData>> callback);
    void getGroupTypesWithNoHierarchy(AsyncCallback<ArrayList<GroupType>> callback);
    void updateGroupType(GroupTypeData groupTypeData, AsyncCallback<Boolean> callback);
    void getAccessGroupType(AsyncCallback<GroupType> callback);
    void getAllGroupFeatures(AsyncCallback<List<GroupFeature>> callback);

    void getGroupHierarchies(Long groupTypeId, AsyncCallback<ArrayList<GroupHierarchyData>> callback);
    void updateGroupHierarchy(GroupHierarchyData groupHierarchyData, AsyncCallback<GroupHierarchyData> callback);
    void deleteGroupHierarchy(Long groupHierarchyId, AsyncCallback<Void> callback);

    void getEntity(Long id, AsyncCallback<GroupEntity> callback);
    void updateEntity(Long genGroupId, GroupEntity entity, AsyncCallback<GroupEntity> callback);
    
    void getGroups(Long groupTypeId, AsyncCallback<ArrayList<GenGroupData>> callback);
    void updateGenGroup(GenGroupData genGroupData, boolean lastLevelChild, AsyncCallback<GenGroupData> callback);
    void deleteGenGroup(GenGroupData genGroupData, AsyncCallback<Void> callback);
    
    void getGroupTypes(boolean includeAccessGroup, boolean includeLocationGroups, AsyncCallback<ArrayList<SelectionDataItem>> callback);
    void getGroupType(Long groupTypeId, AsyncCallback<ArrayList<SelectionDataItem>> callback);
    void getChildrenSelectionData(SelectionDataItem item, AsyncCallback<ArrayList<SelectionDataItem>> callback);
    void getAccessGroupsForUser(Long groupTypeId, AsyncCallback<UserAvailableGroupsData> callback);
    void getLocationGroupType(AsyncCallback<GroupType> callback);
    void getLocationGroups(AsyncCallback<ArrayList<SelectionDataItem>> callback);
    
    void getGlobalThresholds(AsyncCallback<CustomerAccThresholds> callback);
    void getGenGroupThresholds(Long thresholdId, AsyncCallback<CustomerAccThresholds> callback);
    void updateThresholds(GenGroupData genGroupData, CustomerAccThresholds thresholds, AsyncCallback<CustomerAccThresholds> callback);
    void deleteThresholds(GenGroupData genGroupData, Long revertToThresholdId, AsyncCallback<CustomerAccThresholds> callback);
    
    void addNewNdpScheduleToGenGroup(GenGroupData genGroupData, AsyncCallback<NdpSchedule> callback);
    void deleteNdpSchedule(GenGroupData genGroupData, Long revertToNdpScheduleId, AsyncCallback<NdpScheduleData> callback);
    
    void updateNotifications(GenGroupData genGroupData, CustAccNotify notifications, AsyncCallback<CustAccNotify> callback);

    void convertNotifyAccountToInherit(GenGroupData genGroupData, Long oldId, AsyncCallback<CustAccNotify> callback);
    
    void getGenGroupNotifications(Long notificationsId, AsyncCallback<CustAccNotify> callback);
    
    void getEntityFromGenGroupId(Long genGroupId, AsyncCallback<GroupEntity> callBack);


    void getUpGenGroupNamesList(ArrayList<UpGenGroupLinkData> genGroupIdList, AsyncCallback<ArrayList<UpGenGroupLinkDataNames>> callback);

    void getPath(Long genGroupId, AsyncCallback<ArrayList<Long>> callback);
    
    void isGenGroupNameUnique(GenGroup genGroup, AsyncCallback<Boolean> callback);
    void isGenGroupMridUnique(GenGroup genGroup, AsyncCallback<Boolean> callback);
    void isGroupAssignedToUP(GenGroup genGroup, AsyncCallback<Boolean> callback);

    void updateGenGroupMetadata(Long genGroupId, GisMetadata metadata, AsyncCallback<GenGroupData> async);
    void getGenGroupMetadata(Long genGroupId, AsyncCallback<GisMetadata> async);

    void isEntityCustomFieldUsed(int fieldNumber, AsyncCallback<List<Long>> callback);

    void getParentGenGroupIdForAccessGroup(Long accessGroupId, AsyncCallback<Long> callback);

    void getAvailableAccessGroupsByGroupHierarchy(long genGroupHierarchyId, AsyncCallback<List<Group>> callback);

}
