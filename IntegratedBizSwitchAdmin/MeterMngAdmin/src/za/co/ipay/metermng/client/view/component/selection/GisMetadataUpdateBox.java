package za.co.ipay.metermng.client.view.component.selection;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;

import static za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata.isValidLat;
import static za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata.isValidLon;

public class GisMetadataUpdateBox extends DialogBox {
    private static GisMetadataUpdateBoxUiBinder uiBinder = GWT.create(GisMetadataUpdateBoxUiBinder.class);
    private static Logger logger = Logger.getLogger(GisMetadataUpdateBox.class.getName());

    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField TextBox longitudeBox;
    @UiField TextBox latitudeBox;
    @UiField FormElement latitudeElement;
    @UiField FormElement longitudeElement;

    private ClientFactory clientFactory;
    private Long groupId;

    public GisMetadataUpdateBox(ClientFactory clientFactory, Long groupId) {
        this.clientFactory = clientFactory;
        this.groupId = groupId;
        setWidget(uiBinder.createAndBindUi(this));
        logger.info("GisMetadataUpdateBox loading");
        setInfo();
        loadGisInfo(groupId);
        adjustPosition();
    }

    private void setInfo() {
        this.setTitle(MessagesUtil.getInstance().getMessage("metadata.upload.data.title"));
        btnSave.setText(MessagesUtil.getInstance().getMessage("button.save"));
        btnCancel.setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        latitudeElement.setHelpMsg(MessagesUtil.getInstance().getMessage("metadata.lat.help"));
        longitudeElement.setHelpMsg(MessagesUtil.getInstance().getMessage("metadata.lon.help"));
        latitudeElement.setLabelText(MessagesUtil.getInstance().getMessage("metadata.lat.label"));
        longitudeElement.setLabelText(MessagesUtil.getInstance().getMessage("metadata.lon.label"));
    }

    private void adjustPosition() {
        int popupHeight = (Window.getClientHeight() - this.getOffsetWidth()) / 3;
        int left = (Window.getClientWidth() - this.getOffsetWidth()) / 3;
        this.setPopupPosition(left, (int) (popupHeight * 0.75));
    }

    private void loadGisInfo(Long groupId) {
        clientFactory.getGroupRpc().getGenGroupMetadata(groupId, new ClientCallback<GisMetadata>() {
            @Override
            public void onSuccess(GisMetadata result) {
                if (result != null) {
                    latitudeBox.setText(String.valueOf(result.getLat()));
                    longitudeBox.setText(String.valueOf(result.getLon()));
                }
            }
        });
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                saveEntry();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        this.hide();
        clearErrors();
        clearFields();
    }

    private void clearFields() {
        latitudeBox.setText("");
        longitudeBox.setText("");
    }

    private void clearErrors() {
        latitudeElement.clearErrorMsg();
        longitudeElement.clearErrorMsg();
    }

    private void saveEntry() {
        if (isValid()) {
            GisMetadata metadata = new GisMetadata();
            metadata.setLat(Double.parseDouble(latitudeBox.getText()));
            metadata.setLon(Double.parseDouble(longitudeBox.getText()));
            clientFactory.getGroupRpc().updateGenGroupMetadata(groupId, metadata,
                    new ClientCallback<GenGroupData>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                        @Override
                        public void onSuccess(GenGroupData result) {
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("metadata.gis.saved", new String[]{result.getName()}),
                                    MediaResource.MediaResourceUtil.getInstance().getInformationIcon(),
                                    btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),
                                    MessagesUtil.getInstance().getMessage("button.close"),
                                    new ClickHandler() {
                                        @Override
                                        public void onClick(ClickEvent event) {
                                            GisMetadataUpdateBox.this.hide();
                                        }
                                    });
                        }
                    });
        }
    }

    private boolean isValid() {
        clearErrors();
        final RegExp pattern = RegExp.compile("^-?\\d+\\.\\d+?$");
        boolean valid = true;
        String latitude = latitudeBox.getText().trim();
        String longitude = latitudeBox.getText().trim();

        if (!pattern.test(latitude)) {
            latitudeElement.showErrorMsg("Latitude value incorrect");
            valid = false;
        } else if (!isValidLat(Double.parseDouble(latitude))) {
            valid = false;
            latitudeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("metadata.gis.error.invalid.lat"));
        }

        if (!pattern.test(longitudeBox.getText())) {
            longitudeElement.showErrorMsg("Longitude value incorrect");
            valid = false;
        } else if (!isValidLon(Double.parseDouble(longitude))) {
            valid = false;
            latitudeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("metadata.gis.error.invalid.lon"));
        }

        return valid;
    }

    interface GisMetadataUpdateBoxUiBinder extends UiBinder<Widget, GisMetadataUpdateBox> {
    }
}
