package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.shared.dto.dashboard.KeyIndicatorDto;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

@RemoteServiceRelativePath("secure/keyindicator.do")
public interface KeyIndicatorRpc extends RemoteService {

    ArrayList<KeyIndicatorDto> getKeyIndicators() throws ValidationException, ServiceException;
}
