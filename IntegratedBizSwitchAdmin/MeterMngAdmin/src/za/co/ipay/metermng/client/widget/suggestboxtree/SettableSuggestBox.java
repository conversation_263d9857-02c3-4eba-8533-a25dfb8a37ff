package za.co.ipay.metermng.client.widget.suggestboxtree;

import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

import za.co.ipay.metermng.client.widget.suggestboxtree.IdentifiableSuggestOracle.FindSuggestionCallback;

/**
 * This suggest box allows populating the suggestbox from an id. This in turn means that 
 * it requires a IdentifiableSuggestOracle instead of a normal oracle to work. That 
 * oracle allows looking up a suggestion based on id from the oracle's available 
 * suggestions.
 * 
 * <AUTHOR>
 * @param <T> the type of id of the suggestion eg. long or a string
 */
public class SettableSuggestBox<T> extends SuggestBox {
    private Suggestion lastSelectedValue;
    
    public SettableSuggestBox(final IdentifiableSuggestOracle<T> oracle) {
        super(oracle);
        this.addSelectionHandler(new SelectionHandler<Suggestion>() {
            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                Suggestion selectedItem = event.getSelectedItem();
                @SuppressWarnings("unchecked")
                IdentifiableSuggestOracle<T> oracle = (IdentifiableSuggestOracle<T>) getSuggestOracle();
                if(oracle.isEmptyItem(selectedItem)) {
                    lastSelectedValue = null;
                    return;
                }
                lastSelectedValue = event.getSelectedItem();
            }
        });
        
        // Tell the browser to stop trying to help
        // I find that autocomplete in chrome often obscures the actual list items
        // due to the placement of the gwt suggestion list
        getElement().setAttribute("autocomplete", "off");
        getElement().setAttribute("autocorrect", "off");
        getElement().setAttribute("autocapitalize", "off");
        setAutoSelectEnabled(false);
        getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                showSuggestionsIfNotShowingCurrently();
            }
        });
        addValueChangeHandler(new ValueChangeHandler<String>() {

            @Override
            public void onValueChange(ValueChangeEvent<String> event) {
                // This is triggered when someone actually makes a selection or clicks out
                // not on every letter typed.
                
                // Once any text has been changed one should not allow the person 
                // to click out of the box without having made a selection from the 
                // suggestions. The reason is to avoid a difference between what is 
                // displayed and what is selected, or what is eventually used/saved 
                // could be different from what the user expected.
                // 
                // clicking out triggers a value change
                // 
                // This could happen if data is entered but no selection is made and 
                // then the person clicks out.
                // It could also happen if a selection was made and then the text 
                // is edited and the person clicks out thereafter.
                //
                // The side effect is that some might initially not understand that they 
                // have to make a selection and can't just leave typed text, and therefore 
                // be annoyed because it is not accepting their input.
                // 
                // There are usability improvements possible, showing a validation failure, 
                // outlining the box with red or "shaking the box" when they click out

                Suggestion lastSelectedValue = getLastSelectedValue();

                // value would be null if they had never made a selection and then typed text
                // without making a selection
                // otherwise if the last selected value which is set before this is triggered
                // does not match the text then we clear since the display would not match the selection 
                if(lastSelectedValue == null || getText() == null || !getText().equals(lastSelectedValue.getReplacementString())) {
                    clear();
                }
            }
        });
        addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
                if(event.getNativeKeyCode() == KeyCodes.KEY_ESCAPE) {
                    hideSuggestionsIfShowingCurrently();
                }
                if(event.getNativeKeyCode() == KeyCodes.KEY_DOWN || event.getNativeKeyCode() == KeyCodes.KEY_UP || event.getNativeKeyCode() == KeyCodes.KEY_LEFT || event.getNativeKeyCode() == KeyCodes.KEY_RIGHT) {
                    // convenience if someone was typing and nothing is matching
                    // and they then want to bring back the list
                    showSuggestionsIfNotShowingCurrently();
                }
            }
        });

    }

    @SuppressWarnings("unchecked")
    public void setById(final T id, final boolean fireEvents) {
        IdentifiableSuggestOracle<T> suggestOracle = (IdentifiableSuggestOracle<T>) getSuggestOracle();
        suggestOracle.findById(id, new FindSuggestionCallback() {
            
            @Override
            public void found(Suggestion suggestion) {
                setSuggestion(suggestion, fireEvents);
            }
        });
    }

    public void setSuggestion(Suggestion suggestion, boolean fireEvents) {
        if(suggestion != null) {
            setText(suggestion.getReplacementString());
            if(fireEvents) {
                SelectionEvent.fire(this, suggestion);
            }
        }
    }
    
    public Suggestion getLastSelectedValue() {
        return lastSelectedValue;
    }

    public void setLastSelectedValue(Suggestion lastSelectedValue) {
        this.lastSelectedValue = lastSelectedValue;
    }
    
    public void showSuggestionsIfNotShowingCurrently() {
        DefaultSuggestionDisplay display = (DefaultSuggestionDisplay) getSuggestionDisplay();
        if (!display.isSuggestionListShowing()) {
            showSuggestionList();
        }
    }

    public void hideSuggestionsIfShowingCurrently() {
        DefaultSuggestionDisplay display = (DefaultSuggestionDisplay) getSuggestionDisplay();
        if (display.isSuggestionListShowing()) {
            display.hideSuggestions();
        }
    }
    
    public void clear() {
        setLastSelectedValue(null);
        setValue("");
    }

}
