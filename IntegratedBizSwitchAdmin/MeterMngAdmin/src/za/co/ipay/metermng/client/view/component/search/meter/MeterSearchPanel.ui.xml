<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets"
	         xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>
	
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    
	<g:FlowPanel>	   
		<p3:FormRowPanel>
			<p3:FormElement ui:field="meterNumberElement" labelText="{msg.getSearchMeterNumber}:">
				<g:TextBox ui:field="meterNumberBox" maxLength="255" visibleLength="25" debugId="meterNumberBox"/>
			</p3:FormElement>	
        </p3:FormRowPanel>
        <p3:FormRowPanel>
            <p3:FormElement ui:field="meterModelElement" labelText="{msg.getMeterModel}:">
                <p1:IpayListBox visibleItemCount="5" ui:field="lstbxMeterModel" styleName="gwt-ListBox-ipay" multipleSelect="true" debugId="lstbxMeterModel"/>
            </p3:FormElement>
        </p3:FormRowPanel>
        <p3:FormRowPanel>
        	<p3:FormElement ui:field="currSGCElement" labelText="{msg.getAdvancedSearchSgcLabel}">
	        	<p3:FormElement ui:field="currSGCElement_2" helpMsg="{msg.getAdvancedSearchSgcHelp}" labelText="{msg.getAdvancedSearchSgcLabel2}:">
	            	<g:ListBox debugId="meterSupplyGroupBox" visibleItemCount="1" ui:field="lstbxSgKrn" styleName="gwt-ListBox-ipay" multipleSelect="false" />
	            </p3:FormElement>
            </p3:FormElement>
        </p3:FormRowPanel>
        <p3:FormRowPanel>
        	<g:Label styleName="gwt-Label-bold" ui:field="lblSelectMeterStore" />
            <p3:FormElement ui:field="deviceStoreElement" width="100%" labelText="{msg.getMeterSelectStoreAdd}: " >
            	<g:ListBox debugId="meterStoreBox" visibleItemCount="1" ui:field="lstbxSelectStore" styleName="gwt-ListBox-ipay" multipleSelect="false"  />
            </p3:FormElement>
        </p3:FormRowPanel>
        <p3:FormRowPanel>
            <p3:FormElement ui:field="meterNoUsagePointElement" >
                <g:CheckBox text="{msg.getSearchMeterNoUsagePoint}" checked="false" ui:field="chckbxMeterNoUsagePoint" debugId="chckbxMeterNoUsagePoint"/>
            </p3:FormElement>              		
		</p3:FormRowPanel>
		<p3:FormRowPanel>
		  <p3:FormElement ui:field="searchTypeElement" labelText="{msg.getSearchType}:">
                 <g:RadioButton ui:field="startWithBox" name="meterSearchTypeBox" value="true" enabled="true" text="{msg.getSearchStartsWith}" debugId="meterStartWithBox"/>
                 <g:RadioButton ui:field="containsBox" name="meterSearchTypeBox" value="false" enabled="true" text="{msg.getSearchContains}" debugId="meterContainsBox"/> 
            </p3:FormElement>
		</p3:FormRowPanel>
	</g:FlowPanel>

</ui:UiBinder> 