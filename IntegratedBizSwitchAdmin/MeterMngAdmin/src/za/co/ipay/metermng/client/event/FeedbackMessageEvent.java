package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class FeedbackMessageEvent extends GwtEvent<FeedbackMessageEventHandler> {

    public static Type<FeedbackMessageEventHandler> TYPE = new Type<FeedbackMessageEventHandler>();

    String feedback;
    int type;
    
    public FeedbackMessageEvent(String feedbackMessage, int type) {
        this.feedback = feedbackMessage;
        this.type = type; 
    }

    public String getFeedbackMessage() {
        return feedback;
    }

    public int getType() {
        return type;
    }
    
    @Override
    public Type<FeedbackMessageEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(FeedbackMessageEventHandler handler) {
        handler.setMessage(this);
    }

}
