package za.co.ipay.metermng.client.rpc;

import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;

@RemoteServiceRelativePath("secure/blockingtype.do")
public interface BlockingTypeRpc extends RemoteService {

	public List<BlockingType> getAllBlockingTypes() throws ServiceException;

	public BlockingType getBlockingType(Long blockingTypeId) throws ServiceException, AccessControlException;

	public BlockingType addBlockingType(BlockingType blockingType) throws ValidationException, ServiceException;

	public BlockingType updateBlockingType(BlockingType blockingType) throws ValidationException, ServiceException;
}
