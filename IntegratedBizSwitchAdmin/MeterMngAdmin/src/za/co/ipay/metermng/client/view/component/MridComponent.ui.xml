<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
	xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
		
	</ui:style>
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
	<g:VerticalPanel>
	    <ipay:FormRowPanel ui:field="mridPanel">
	        <ipay:FormElement ui:field="txtbxMridElement" required="true" labelText="{msg.getMrid}:" helpMsg="{msg.getMridHelp}">
	            <g:TextBox debugId="uiMridBox" ui:field="txtbxMrid" styleName="gwt-TextBox" visibleLength="40" enabled="false" />
	        </ipay:FormElement>
	        <ipay:FormElement ui:field="mridExternalElement" helpMsg="{msg.getMridExternalHelp}" >
	            <g:CheckBox debugId="uiMridEditBox" text="{msg.getMridExternal}" checked="false" ui:field="chckbxMridExternal" />
	        </ipay:FormElement>
	    </ipay:FormRowPanel>
	</g:VerticalPanel>
</ui:UiBinder> 