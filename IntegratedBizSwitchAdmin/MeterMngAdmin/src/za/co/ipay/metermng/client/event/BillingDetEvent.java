package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class BillingDetEvent extends GwtEvent<BillingDetEventHandler> {

    public static Type<BillingDetEventHandler> TYPE = new Type<BillingDetEventHandler>();

    private String name;

    public BillingDetEvent() {
    }
    
    public BillingDetEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<BillingDetEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(BillingDetEventHandler handler) {
        handler.handleEvent(this);
    }
}
