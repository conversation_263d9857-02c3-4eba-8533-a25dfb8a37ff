<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" 
	xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
    xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
	</ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
	<g:HTMLPanel>
        <t:FormRowPanel>
            <t:FormElement ui:field="unitPriceElement" debugId="unitPriceElement" labelText="{msg.getTariffUnitPrice}:" helpMsg="{msg.getTariffUnitPriceHelp}" required="true">
                <g:Label ui:field="unitPriceCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="unitPriceTextBox" debugId="unitPriceTextBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
            <t:FormElement ui:field="minVendAmountElement" debugId="minVendAmountElement" labelText="{msg.getTariffMinVendAmtLbl}:" helpMsg="{msg.getTariffMinVendAmtHelp}">
                <g:Label ui:field="minVendAmountCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="minVendAmountBox" debugId="minVendAmountBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
            <t:FormElement ui:field="taxPercentElement" debugId="taxPercentElement" labelText="{msg.getTariffTax}:" helpMsg="{msg.getTariffTaxHelp}" required="true">
                <w:PercentageTextBox ui:field="taxPercentTextBox" debugId="taxPercentTextBox" />
            </t:FormElement>
        </t:FormRowPanel>
        <t:FormRowPanel>
            <t:FormElement ui:field="freeUnitsNameElement" debugId="freeUnitsNameElement" labelText="{msg.getFreeUnitsDescripTitle}:" required="false">
                <g:TextBox ui:field="freeUnitsNameTextBox" debugId="freeUnitsNameTextBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="freeUnitsElement" debugId="freeUnitsElement" labelText="{msg.getFreeUnitsTitle}:" helpMsg="{msg.getFreeUnitsHelp}" required="false">
                <t:BigDecimalValueBox ui:field="freeUnitsTextBox" debugId="freeUnitsTextBox" styleName="gwt-TextBox largeNumericInput"/>
            </t:FormElement>
        </t:FormRowPanel>
         
        <t:FormRowPanel>
            <t:FormElement ui:field="cycleListBoxElement" debugId="cycleListBoxElement" labelText="{msg.getTariffCycle}:" helpMsg="{msg.getTariffCycleHelp}" required="false">
                <g:ListBox ui:field="cycleListBox" debugId="cycleListBox" styleName="gwt-TextBox" />
            </t:FormElement>
            <t:FormElement ui:field="cyclicChargeNameElement" debugId="cyclicChargeNameElement" labelText="{msg.getTariffCyclicCostName}:" helpMsg="{msg.getTariffCyclicCostNameHelp}" required="false">
                <g:TextBox ui:field="cyclicChargeNameTextBox" debugId="cyclicChargeNameTextBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="cyclicChargeElement" debugId="cyclicChargeElement" labelText="{msg.getTariffCost}:" helpMsg="{msg.getTariffCostHelp}" required="false">
                <g:Label ui:field="cyclicChargeCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="cyclicChargeTextBox" debugId="cyclicChargeTextBox" styleName="gwt-TextBox largeNumericInput"/>
            </t:FormElement>
        </t:FormRowPanel>
        
        <t:FormRowPanel>
            <t:FormElement ui:field="percentChargeNameElement" debugId="percentChargeNameElement" labelText="{msg.getTariffPercentChargeName}:" helpMsg="{msg.getTariffPercentChargeNameHelp}" required="false">
                <g:TextBox ui:field="percentChargeNameTextBox" debugId="percentChargeNameTextBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="percentChargeElement" debugId="percentChargeElement" labelText="{msg.getTariffPercentCharge}:" helpMsg="{msg.getTariffPercentChargeHelp}" required="false">
                <w:PercentageTextBox ui:field="percentChargeTextBox" debugId="percentChargeTextBox" />
            </t:FormElement>
        </t:FormRowPanel>
        
        <t:FormRowPanel ui:field="paytypeDiscountFormRowPanel"></t:FormRowPanel>
        
  	</g:HTMLPanel>
</ui:UiBinder> 