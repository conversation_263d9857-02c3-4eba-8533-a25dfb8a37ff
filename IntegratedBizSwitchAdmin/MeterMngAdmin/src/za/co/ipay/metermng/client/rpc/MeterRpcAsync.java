package za.co.ipay.metermng.client.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.custom.model.RegisterReadingExt;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtra;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.MeterHistData;
import za.co.ipay.metermng.shared.STSMeterHistData;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.dashboard.MeterCountDto;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.meter.SuperSubMeterDto;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;
import za.co.ipay.metermng.shared.dto.schedule.EnergyBalancingExportScreenData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

public interface MeterRpcAsync {

    void updateMeter(MeterData meterData, AsyncCallback<MeterData> callback);

    void getStsMeterHistory(Long meterId, boolean usingAccessGroup, AsyncCallback<ArrayList<STSMeterHistData>> callback);

    void getMeterHistory(Long meterId, boolean usingAccessGroup, AsyncCallback<ArrayList<MeterHistData>> callback);

    void getTransactionHistory(Long meterId, AsyncCallback<ArrayList<CustomerTransAlphaData>> callback);

    void getTransactionHistoryWithTotals(Long meterId, AsyncCallback<ArrayList<CustomerTransAlphaDataWithTotals>> callback);

    void getVendTransactionHistory(Long meterId, AsyncCallback<ArrayList<CustomerTransAlphaData>> callback);
    
    void getTransactionCustomerTransItem(CustomerTransAlphaDataWithTotals customerTransData, AsyncCallback<List<CustomerTransItemData>> callback);

    void getTransactionCustomerTransItem(Long customerTransId, AsyncCallback<List<CustomerTransItemData>> callback);

    void getEngineeringTokenTransactions(Long meterId, boolean usingAccessGroup, AsyncCallback<ArrayList<StsEngineeringTokenData>> callback);

    void getMeter(Long meterId, AsyncCallback<MeterData> callback);

    void getTransactionCount(Long meterId, AsyncCallback<Integer> callback);
    
    void getMetersByEndDeviceStore(Long endDeviceStoreId, AsyncCallback<ArrayList<MeterData>> callback);

    void getMeterReadings(Long meterId, Long meterReadingTypeId, Date start, Date end,
            AsyncCallback<MeterReadingsDto> callback);

    void getMeterBalancingReadings(Long balancingMeterId, Long meterReadingTypeId, Date start, Date end,
            AsyncCallback<MeterReadingsDto> callback);

    void getSuperMeters(AsyncCallback<ArrayList<MeterDto>> callback);

    void checkEnergyBalancingMeters(Long meterReadingTypeId, Date startDate, Date endDate, double percentVariation,
            AsyncCallback<ArrayList<EnergyBalancingDto>> callback);

    void getMeterReadingTypes(AsyncCallback<ArrayList<MeterReadingType>> callback);

    void addMeterReadings(MeterDto meterDto, Date start, Date end, int intervalMinutes, ArrayList<Long> readingTypeIds,
            int deleteExistingReadings, boolean doTariffCalc, Date zeroStart, Date zeroEnd, int zeroInstances,
            Date missingStart, Date missingEnd, int missingInstances, ArrayList<Long> mdcChannelIds,
            AsyncCallback<IpayResponseData> callback);

    void getSubMeters(Long superMeterId, AsyncCallback<SuperSubMeterDto> callback);

    void saveSuperSubMeters(Long superMeterId, ArrayList<Long> subMeterIds, AsyncCallback<Void> callback);

    void getMeterByMeterNumber(String meterNumber, AsyncCallback<MeterDto> callback);

    void getEnergyBalancingExportScreenData(AsyncCallback<EnergyBalancingExportScreenData> callback);

    void getNewMrid(AsyncCallback<String> callback);

    void getMeterCountByModel(AsyncCallback<ArrayList<MeterCountDto>> clientCallback);

    void addSuperMeterReadings(Long superMeterId, Date start, Date end, int intervalMinutes, Long readingTypeId,
            boolean deleteExistingSuperMeterReadings, boolean regenerateSubMeterReadings,
            List<MeterReadingVariation> variations, AsyncCallback<Void> callback);

    void deleteSuperMeter(Long superMeterId, AsyncCallback<Void> callback);

    void getMdcTransByMeterId(Long meterId, AsyncCallback<ArrayList<MdcTransData>> clientCallback);
    
    void getRegisterReadingsByMeterId(Long meterId, Date fromDate, Date toDate, AsyncCallback<List<RegisterReadingExt>> clientCallback);
    
    void getRegisterReadingsByUsagePointId(Long usagePointId, Date fromDate, Date toDate, AsyncCallback<List<RegisterReadingExt>> clientCallback);

    void sendConnectDisconnectMsg(MeterData meterData, String messageType, String overrideType, String relayId, AsyncCallback<IpayResponseData> callback);
    
    void sendPowerLimitMsg(MeterData meterData, String overrideType, String powerLimit, AsyncCallback<IpayResponseData> callback);
    
    void selectLastCustAgrTransId(Long customerAgreementId, AsyncCallback<Long> callback); 
    
    void sendVendReversalMsg(CustomerTrans customerTrans, boolean allowOlderReversals, String userName,
            SpecialActionReasonsLog specialActionReasonsLog, String comment, AsyncCallback<IpayResponseData> callback);
    
    void updateCustTransLastReprintDate(Long customerTransId, Date reprintDate, String userName, AsyncCallback<Void> callback);
    
    void getCustTransIsReversed(Long customerTransId, AsyncCallback<Boolean> callback);
    
    void getCustTransExtraListOrderByReprintDateAsc(Long customerTransId, AsyncCallback<List<CustomerTransExtra>> callback);
    
    void getCustomerTransItemsForMeterId(Long meterId, AsyncCallback<List<CustomerTransItem>> callback);
    
    void getReadingsDateRangeForMeter(MeterDto meterDto, ArrayList<Long> readingTypeIds, boolean intervalReadings,
            AsyncCallback<MeterReadingDto> callback);

    void updateMeterPowerLimit(Meter meter, StsMeter stsMeter, BigDecimal powerLimit, String powerLimitLabel,
            AsyncCallback<Void> callback);
    
    void saveInitRegReadings(MeterUpMdcChannelInfo meterUpMdcChannelInfo, AsyncCallback<Void> callback);

    void getStdBsstEngineeringTokens(Long customerTransId, String meterNumber, AsyncCallback<ArrayList<StsEngineeringTokenData>> async);

    void verifyToken(String token, String meterNumber, StsMeterData stsMeterData, AsyncCallback<VerifyTokenDto> async);

    void getKeyChangeTokensForCustomerTrans(long customerTransId, AsyncCallback<StsEngineeringToken> callback);

    void getFirstEngineeringTokenByCustomerTrans(long customerTransId, AsyncCallback<StsEngineeringToken> callback);

	void updateCustTransLastReprintDateBulk(List<Long> customerTransIdsToUpdate, Date now, String userName, AsyncCallback<Void> callback);
}