package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class ImportFileEvent extends GwtEvent<ImportFileEventHandler> {
    
    public static Type<ImportFileEventHandler> TYPE = new Type<ImportFileEventHandler>();
    
    private String name;
    
    public ImportFileEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<ImportFileEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(ImportFileEventHandler handler) {
        handler.handleEvent(this);
    }
}
