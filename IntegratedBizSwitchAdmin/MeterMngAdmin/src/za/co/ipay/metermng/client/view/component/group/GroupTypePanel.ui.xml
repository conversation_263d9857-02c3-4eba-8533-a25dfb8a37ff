<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form">
  <ui:style>
       .addRemoveSpacing {
          margin-top: 1px;
          margin-bottom: 5px;
       }    
  </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

  <g:FlowPanel ui:field="contentPanel" debugId="contentPanel">
      <p3:FormRowPanel>
        <p3:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getGroupTypeName}:" required="true" helpMsg="{msg.getGroupTypeNameHelp}">
          <g:TextBox ui:field="nameBox" debugId="nameBox" visibleLength="30" />
        </p3:FormElement>
        <p3:FormElement ui:field="descriptionElement" debugId="descriptionElement" labelText="{msg.getGroupTypeDescription}:"  helpMsg="{msg.getGroupTypeDescriptionHelp}">
          <g:TextBox ui:field="descriptionBox" debugId="descriptionBox" visibleLength="50" />
        </p3:FormElement>
      </p3:FormRowPanel>
      
      <p3:FormRowPanel>
        <p3:FormElement ui:field="parentElement" debugId="parentElement" labelText="{msg.getGroupTypeParent}:"  helpMsg="{msg.getGroupTypeParentHelp}">
          <g:ListBox ui:field="parentBox" debugId="parentBox"/>
        </p3:FormElement>
      </p3:FormRowPanel>
      
      <p3:FormRowPanel>
        <p3:FormElement ui:field="activeElement" labelText="{msg.getGroupTypeActive}:"  helpMsg="{msg.getGroupTypeActiveHelp}">
          <g:CheckBox ui:field="activeBox" debugId="activeBox" checked="true"/>
        </p3:FormElement>
        
        <p3:FormElement ui:field="isRequiredElement" debugId="isRequiredElement" labelText="{msg.getGroupTypeRequired}:" helpMsg="{msg.getGroupTypeRequiredHelp}">
          <g:CheckBox ui:field="isRequiredBox" debugId="isRequiredBox"/>
        </p3:FormElement>
        
        <p3:FormElement ui:field="accessGroupElement" debugId="accessGroupElement" labelText="{msg.getGroupTypeAccessGroup}:" helpMsg="{msg.getAccessGroupTypeHelp}">
          <g:CheckBox ui:field="accessGroupBox" debugId="accessGroupBox" enabled="false" />
        </p3:FormElement>
        
        <p3:FormElement ui:field="locationGroupElement" debugId="locationGroupElement" labelText="{msg.getGroupTypeLocationGroup}:" helpMsg="{msg.getLocationGroupTypeHelp}">
          <g:CheckBox ui:field="locationGroupBox" debugId="locationGroupBox" enabled="false" />
        </p3:FormElement>
      </p3:FormRowPanel>
      
      <p3:FormRowPanel>
        <p3:FormElement ui:field="layoutOrderElement" labelText="{msg.getGroupTypeLayoutOrder}:" helpMsg="{msg.getGroupTypeLayoutOrderHelp}">
          <g:TextBox ui:field="layoutOrderTxtBox" visibleLength="4" />
        </p3:FormElement>
      </p3:FormRowPanel >

      <p3:FormRowPanel ui:field="featuresPanel" debugId="featuresPanel">
            <p3:FormElement ui:field="availableFeaturesElement" debugId="availableFeaturesElement" labelText="{msg.getAvailableGroupFeatures}:" helpMsg="{msg.getAvailableGroupFeatureHelp}">
                <g:ListBox ui:field="lstbxAvailableFeatures" debugId="lstbxAvailableFeatures" styleName="gwt-TextBox veryWideSelect"  multipleSelect="true" visibleItemCount="3" />
            </p3:FormElement>
            <p3:FormElement ui:field="buttonsElement" debugId="buttonsElement" labelText="" required="false">
                <g:VerticalPanel >
                    <g:Button ui:field="addFeatureButton" debugId="addFeatureButton" text="&gt;" styleName="gwt-Button {style.addRemoveSpacing}"/>
                    <g:Button ui:field="removeFeatureButton" debugId="removeFeatureButton" text="&lt;" />
                </g:VerticalPanel>
            </p3:FormElement>
            <p3:FormElement ui:field="assignedFeaturesElement" debugId="assignedFeaturesElement" labelText="{msg.getAssignedGroupFeatures}:" helpMsg="{msg.getAssignedGroupFeatureHelp}">
                <g:VerticalPanel>
                    <g:ListBox ui:field="lstbxAssignedFeatures" debugId="lstbxAssignedFeatures" styleName="gwt-TextBox veryWideSelect"  multipleSelect="true" visibleItemCount="3" />
                </g:VerticalPanel>
            </p3:FormElement>
      </p3:FormRowPanel>
            
  </g:FlowPanel>

</ui:UiBinder> 