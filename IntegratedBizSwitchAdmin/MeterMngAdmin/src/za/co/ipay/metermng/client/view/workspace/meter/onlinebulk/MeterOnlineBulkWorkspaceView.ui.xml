<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client" 
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace"
             xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component.onlinebulk"
             xmlns:p3="urn:import:com.google.gwt.user.datepicker.client">
    <ui:style>
       .spaceAbove {
            margin-top: 0.9em;
        }
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
      
    <g:DockLayoutPanel ui:field="mainPanel" styleName="mainPanel">
 
        <g:north size="30">
            <form:PageHeader heading="" ui:field="pageHeader" />
        </g:north>

        <g:center>
        <g:ScrollPanel styleName="{style.spaceAbove}">
            <g:FlowPanel width="99%" height="100%">
                <g:HTML ui:field="dataName" text="" styleName="dataTitle" visible="false" />
                <g:HTML ui:field="dataDescription" text="" styleName="dataDescription" visible="false" />
      
                <p2:UpGroupSelectionPanel ui:field="upGroupSelectionPanel" />
      
                
                    <g:FlowPanel>
                        <g:HTMLPanel ui:field="tablePanel">
            
                            <g:HorizontalPanel spacing="3">
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:Label text="{msg.getMeterTxnFilter}:" styleName="gwt-Label-bold"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:ListBox visibleItemCount="1" name="Filter"  ui:field="filterDropdown" debugId="meterTxnFilterDropdown"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:TextBox  ui:field="txtbxfilter" debugId="meterTxntxtbxfilter"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <p3:DateBox ui:field="filterDatebox" visible="false"/>
                                </g:Cell>
                            </g:HorizontalPanel>
                      
                            <g2:CellTable ui:field="table" />
                            <widget:TablePager ui:field="pager" styleName="pager" location="CENTER" />
            
                            <g:FlowPanel ui:field="belowTablePanel"></g:FlowPanel>
                        </g:HTMLPanel>
            
                        <form:SimpleForm ui:field="form" styleName="formPanel" />            
              
                        <g:FlowPanel ui:field="belowFormPanel"></g:FlowPanel>
                    </g:FlowPanel>
                
            </g:FlowPanel>
            </g:ScrollPanel>
        </g:center>
  </g:DockLayoutPanel>

</ui:UiBinder> 