package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class MdcEvent extends GwtEvent<MdcEventHandler> {

    public static Type<MdcEventHandler> TYPE = new Type<MdcEventHandler>();

    private String name;

    public MdcEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<MdcEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MdcEventHandler handler) {
        handler.handleEvent(this);
    }
}
