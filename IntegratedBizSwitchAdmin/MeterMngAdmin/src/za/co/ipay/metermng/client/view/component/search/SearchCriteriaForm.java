package za.co.ipay.metermng.client.view.component.search;

import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiConstructor;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.DecoratorPanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

public class SearchCriteriaForm extends Composite {
    
    @UiField DecoratorPanel mainPanel;
    @UiField FlowPanel panel;
    @UiField Label headingLabel;
    @UiField FlowPanel fieldsPanel;

    private static BaseSearchCriteriaPanelUiBinder uiBinder = GWT.create(BaseSearchCriteriaPanelUiBinder.class);

    interface BaseSearchCriteriaPanelUiBinder extends UiBinder<Widget, SearchCriteriaForm> {
    }

    @UiConstructor
    public SearchCriteriaForm(String heading) {
        initWidget(uiBinder.createAndBindUi(this));
        setHeading(heading);
        mainPanel.addStyleName(MeterMngStatics.SEARCH_STYLE);
    }    
    
    private void setHeading(String heading) {
        headingLabel.setText(heading);
    }

    public void setSearchPanel(Widget w) {
        fieldsPanel.add(w);
    }
    
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }
}
