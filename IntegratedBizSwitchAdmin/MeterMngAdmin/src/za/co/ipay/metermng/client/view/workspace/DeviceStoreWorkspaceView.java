package za.co.ipay.metermng.client.view.workspace;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.DeviceStorePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.devicestore.DeviceStoreMeters;
import za.co.ipay.metermng.client.view.component.devicestore.DeviceStoreView;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;

public class DeviceStoreWorkspaceView extends BaseWorkspace {

    private Logger logger = Logger.getLogger("DeviceStoreWorkspaceView");
    @UiField DeckLayoutPanel deckPanel;
    @UiField SimpleTableView<EndDeviceStoreData> view;
    DeviceStoreView deviceStoreView;
    @UiField(provided=true) DeviceStoreMeters deviceStoreMeters;

    private Place place;
    interface  DeviceStoreUiBinder extends UiBinder<Widget, DeviceStoreWorkspaceView> {
    }

    private static  DeviceStoreUiBinder uiBinder = GWT.create( DeviceStoreUiBinder.class);

    public static final class DeviceStoreWorkspaceFactory implements WorkspaceFactory {
        private ClientFactory clientFactory;
        private ArrayList<AppSetting> customFieldList;

        public  DeviceStoreWorkspaceFactory(ClientFactory clientFactory) {
            this.clientFactory = clientFactory;
            clientFactory.getWorkspaceContainer().register(this);
        }

        @Override
        public void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_STORE_ADMIN)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;
            }
            clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {
                @Override
                public void onSuccess(ArrayList<AppSetting> result) {
                    customFieldList = result;
                    createWorkspace(place, workspaceCreateCallback, customFieldList);
                }

                @Override
                public void onFailure(Throwable caught) {
                    if(customFieldList!=null){
                        createWorkspace(place, workspaceCreateCallback, customFieldList);
                    } else {
                        //TODO - Fix the error message to device store specific
                        workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.meter.workspace.error", true));
                    }
                }
            });
        }

        public void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback, List<AppSetting> customFields) {
            try {
                DeviceStoreWorkspaceView deviceStoreWorkspaceView = new DeviceStoreWorkspaceView(clientFactory, (DeviceStorePlace) place, customFields);
                workspaceCreateCallback.onWorkspaceCreated( deviceStoreWorkspaceView);
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        }

        @Override
        public boolean handles(Place place) {
            return place instanceof DeviceStorePlace;
        }
    }

    public DeviceStoreWorkspaceView(ClientFactory clientFactory, DeviceStorePlace place, List<AppSetting> customFields) {
        this.clientFactory = clientFactory;
        deviceStoreMeters = new DeviceStoreMeters(this, clientFactory, MessagesUtil.getInstance().getMessage("devicestore.meters.header"), MessagesUtil.getInstance().getMessage("devicestore.meters.title"), customFields);
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString("deviceStore:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("devicestore.title"));
        initUi();
        actionPermissions();
    }

    private void initUi() {
        deviceStoreView = new DeviceStoreView(this, clientFactory, view);
        deckPanel.showWidget(view);
    }

    public void goToDeviceStores() {
        deviceStoreMeters.setMeter(null);
        deckPanel.showWidget(0);
        deckPanel.animate(getAnimationTime());
    }

    public void goToAddMeters(final EndDeviceStoreData deviceStoreData) {
        deviceStoreMeters.setMetersList(deviceStoreData);
        deviceStoreMeters.resetMeterFormUI();
        deckPanel.showWidget(1);
        deckPanel.animate(getAnimationTime());
    }

//    public void goToImportMeters(final EndDeviceStoreData deviceStoreData) {
//        importDataPanel.setHeader( MessagesUtil.getInstance().getMessage("devicestore.import.meters.header",new String[]{deviceStoreData.getName()}));
//        deckPanel.showWidget(2);
//        deckPanel.animate(getAnimationTime());
//    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at device store");
        this.place = place;
        deviceStoreView.onArrival(place);
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return  place instanceof DeviceStorePlace;
    }

    @Override
    public void onSelect() {

    }

    @Override
    public void handleNotification(final WorkspaceNotification notification) {
        final String dataType = notification.getDataType();
        logger.info("Received notification: " + dataType);
        if (NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (MeterMngStatics.USER_CURRENT_GROUP.equals(dataType)) {
                        logger.info("The user's current group has changed - reloading this DeviceStore workspace...");
                        deviceStoreView.clear();
                        deviceStoreView.onArrival(place);
                    } else if (MeterMngStatics.ONLINE_BULK_METER_ADDED.equals(dataType)) {
                        logger.info("Meters have been taken from device store - refreshing the meters table");
                        deviceStoreMeters.refreshMetersList();
                    } else if (MeterMngStatics.USAGE_POINT_METER_REMOVED.equals(dataType)) {
                        logger.info("Meter has been added to device store - refreshing the meters table");
                        deviceStoreMeters.refreshMetersList();
                    } else if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(dataType)) {
                        String appSettingKey = ((String) notification.getObject()).toLowerCase();
                        if (appSettingKey.equals(AppSettings.POWER_LIMIT_SETTINGS)) {
                            deviceStoreMeters.metercomponent.updatePowerLimits();
                            deviceStoreMeters.meterInfo.refreshPowerLimitAppSettings();

                        }
                    } else if (MeterMngStatics.USER_INTERFACE_CONFIGURATION_MODIFIED.equals(dataType)) {
                        deviceStoreMeters.updateUserInterfaceComponentSettings();
                    } else if (MeterMngStatics.METER_MODEL_MODIFIED.equals(dataType)) {
                        // Similar notification in UsagePointWorkspaceView. Changes should potentially be made to both.
                        deviceStoreMeters.metercomponent.populateMeterModelListBox();
                        deviceStoreMeters.meterInfo.populateMdcTrans();
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_STORE)) {
            view.getForm().getButtons().removeFromParent();
            view.getForm().getSecondaryButtons().removeFromParent();
        }
    }

}
