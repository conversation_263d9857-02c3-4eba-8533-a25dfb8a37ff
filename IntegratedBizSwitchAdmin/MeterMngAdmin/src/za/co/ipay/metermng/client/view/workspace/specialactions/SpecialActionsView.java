package za.co.ipay.metermng.client.view.workspace.specialactions;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import com.google.gwt.cell.client.ImageResourceCell;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.SpecialActionsUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

public class SpecialActionsView extends BaseComponent implements FormManager<SpecialActions> {

    private static Logger logger = Logger.getLogger(SpecialActionsView.class.getName());
	public enum ReasonsInputType { FTXT /*Free Text*/, SLCT/*Select from list*/,BOTH/*Either Free Text or Selection*/ }

	private SpecialActions specialActions;

	private ArrayList<SpecialActions> specialActionsList;
	private SimpleTableView<SpecialActions> view;
	private SpecialActionsPanel panel;

    private ListDataProvider<SpecialActions> dataProvider = new ListDataProvider<SpecialActions>();
    private ListHandler<SpecialActions> columnSortHandler;

	private TextColumn<SpecialActions> nameColumn;

    private Button showReasonsBtn;

    private SpecialActionsWorkspaceView parentWorkspace;


    public SpecialActionsView( SpecialActionsWorkspaceView parentWorkspace,
                                ClientFactory clientFactory,
                                SimpleTableView<SpecialActions> view) {

        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new SpecialActionsPanel(view.getForm());
        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("special.actions.title"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSaveButtonClicked();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("updateButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        showReasonsBtn = new Button(MessagesUtil.getInstance().getMessage("button.viewreasons"));
        showReasonsBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onShowReasonsButtonClicked();
            }
        });
        view.getForm().setVisible(false);
        view.getForm().getSecondaryButtons().add(showReasonsBtn);
        view.getForm().getSecondaryButtons().setVisible(true);
        panel.inputTypeBox.addChangeHandler(new ChangeHandler() {
			@Override
			public void onChange(ChangeEvent event) {
			    showReasonsBtn.setVisible(!(panel.inputTypeBox.getValue(panel.inputTypeBox.getSelectedIndex()).equals(SpecialActionsView.ReasonsInputType.FTXT.toString())));
			}
		});
    }

    private void createTable() {

        nameColumn = new TextColumn<SpecialActions>() {
            @Override
            public String getValue(SpecialActions obj) {
                return obj.getSpecialActionsName();
            }
        };
        nameColumn.setSortable(true);

        TextColumn<SpecialActions> descriptionColumn = new TextColumn<SpecialActions>() {
            @Override
            public String getValue(SpecialActions obj) {
                return obj.getSpecialActionsDescription();
            }
        };

        Column<SpecialActions, ImageResource> reasonRequiredColumn = new Column<SpecialActions, ImageResource>(new ImageResourceCell()) {
    		@Override
    		public ImageResource getValue(SpecialActions object) {
    			if (object.isReasonRequired()) {
    				return MediaResourceUtil.getInstance().getTickImage();
    			}
    			return MediaResourceUtil.getInstance().getTransparentImage();
    		}
		};

        TextColumn<SpecialActions> reasonInputTypeColumn = new TextColumn<SpecialActions>() {
            @Override
            public String getValue(SpecialActions obj) {
            	if (obj.getInputType().equals(ReasonsInputType.FTXT.toString())) {
            		return MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.freetext");
            	} else if (obj.getInputType().equals(ReasonsInputType.SLCT.toString())) {
            		return MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.selected");
            	} else {
            		return MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.both");
            	}

            }
        };

        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("special.actions.field.name"));
        view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("special.actions.field.description"));
        view.getTable().addColumn(reasonRequiredColumn, MessagesUtil.getInstance().getMessage("special.actions.field.reason.required"));
        view.getTable().addColumn(reasonInputTypeColumn, MessagesUtil.getInstance().getMessage("special.actions.field.reason.input.type"));

        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        view.getTable().setPageSize(getPageSize());
    }

    private void setSpecialActions(SpecialActions spAction) {
        panel.clearFields();
        panel.clearErrors();
        this.specialActions = spAction;
        if (specialActions == null) {
            view.getForm().setVisible(false);
            view.clearTableSelection();
        } else {
            view.getForm().setVisible(true);
            panel.nameTextBox.setText(specialActions.getSpecialActionsName());
            panel.descriptionTextArea.setText(specialActions.getSpecialActionsDescription());
            for (int i=0; i<panel.inputTypeBox.getItemCount(); i++) {
                if (panel.inputTypeBox.getValue(i).equals(specialActions.getInputType())) {
                    panel.inputTypeBox.setSelectedIndex(i);
                    break;
                }
            }
            showReasonsBtn.setVisible(!(panel.inputTypeBox.getValue(panel.inputTypeBox.getSelectedIndex()).equals(SpecialActionsView.ReasonsInputType.FTXT.toString())));
            panel.reasonRequiredBox.setValue(specialActions.isReasonRequired());
        }
    }

    public void refreshTable() {
        clientFactory.getSpecialActionsRpc().getSpecialActions(new ClientCallback<ArrayList<SpecialActions>>() {
            @Override
            public void onSuccess(ArrayList<SpecialActions> result) {
                specialActionsList = result;
                dataProvider.setList(specialActionsList);
                dataProvider.refresh();
                if (columnSortHandler == null || columnSortHandler.getList() == null) {
                    columnSortHandler = new ListHandler<SpecialActions>(dataProvider.getList());
                    columnSortHandler.setComparator(nameColumn, new Comparator<SpecialActions>() {
                        public int compare(SpecialActions o1, SpecialActions o2) {
                            if (o1 == o2) {
                                return 0;
                            }
                            if (o1 != null && o1.getSpecialActionsName() != null) {
                                return (o2 != null && o2.getSpecialActionsName() != null) ? o1.getSpecialActionsName().compareTo(o2.getSpecialActionsName()) : 1;
                            }
                            return -1;
                        }
                    });
                    view.getTable().addColumnSortHandler(columnSortHandler);
                    view.getTable().getColumnSortList().push(nameColumn);
                    ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
                } else {
                    columnSortHandler.setList(dataProvider.getList());
                    ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
                }
                if (specialActions != null) {
                    view.getTable().getSelectionModel().setSelected(specialActions, true);
                }
            }
        });
    }

    private void onSaveButtonClicked() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                SpecialActions specialActionsNew = new SpecialActions();
                updateSpecialActions(specialActionsNew);
                if (isValidInput(specialActionsNew)) {
                    updateSpecialActions(specialActions);
                    clientFactory.getSpecialActionsRpc().saveSpecialActions(specialActions, new ClientCallback<SpecialActions>(
                            view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                        @Override
                        public void onSuccess(SpecialActions result) {
                            setSpecialActions(result);
                            refreshTable();
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("special.action") }), MediaResourceUtil.getInstance()
                                            .getInformationIcon(), view.getForm().getSaveBtn().getAbsoluteLeft(), view
                                            .getForm().getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance()
                                            .getMessage("button.close"));
                            clientFactory.getEventBus().fireEvent(new SpecialActionsUpdatedEvent());
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            if (caught instanceof AccessControlException) {
                                clientFactory.getWorkspaceContainer().closeWorkspaceNow(SpecialActionsPlace.ALL);
                            }
                            super.onFailure(caught);
                        };
                    });
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void updateSpecialActions(SpecialActions specialActions) {
        specialActions.setSpecialActionsName(panel.nameTextBox.getText());
        specialActions.setSpecialActionsDescription(panel.descriptionTextArea.getText());
        specialActions.setInputType(panel.inputTypeBox.getValue(panel.inputTypeBox.getSelectedIndex()));
        specialActions.setReasonRequired(panel.reasonRequiredBox.getValue());
    }

    private boolean isValidInput(SpecialActions specialActions) {
        boolean valid = true;
        panel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(specialActions, "specialActionsName", panel.nameElement)) {
            valid = false;
            logger.info("Invalid  name: "+specialActions.getSpecialActionsName());
        }

        if (!ClientValidatorUtil.getInstance().validateField(specialActions, "specialActionsDescription", panel.descriptionElement)) {
            valid = false;
        }

        return valid;
    }

    private void onShowReasonsButtonClicked() {
    	view.getForm().checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    view.getForm().setDirtyData(false);
                    parentWorkspace.goToSpecialActionReasons(specialActions);
                }
            }
        });
    }

    public void onWorkspaceArrival() {
        refreshTable();
    }

    @Override
    public void displaySelected(SpecialActions selected) {
        setSpecialActions(selected);
    }






}
