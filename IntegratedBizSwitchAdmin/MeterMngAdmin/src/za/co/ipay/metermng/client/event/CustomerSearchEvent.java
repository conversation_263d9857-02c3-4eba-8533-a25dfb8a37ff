package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.client.history.CustomerPlace;

import com.google.gwt.event.shared.GwtEvent;

public class CustomerSearchEvent extends GwtEvent<CustomerSearchEventHandler> {

    public static Type<CustomerSearchEventHandler> TYPE = new Type<CustomerSearchEventHandler>();
    private final CustomerPlace customerPlace;

    public CustomerSearchEvent(CustomerPlace customerPlace) {
        this.customerPlace = customerPlace;
    }

    public CustomerPlace getCustomerPlace() {
        return customerPlace;
    }

    public boolean isSearchFromURL() {
        return customerPlace.isFromURL();
    }

    @Override
    public Type<CustomerSearchEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(CustomerSearchEventHandler handler) {
        handler.searchByCustomer(this);
    }

}
