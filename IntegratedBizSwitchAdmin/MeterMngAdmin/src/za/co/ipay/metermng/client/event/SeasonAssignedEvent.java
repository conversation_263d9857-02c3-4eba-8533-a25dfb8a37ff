package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SeasonAssignedEvent extends GwtEvent<SeasonAssignedEventHandler> {

    public static Type<SeasonAssignedEventHandler> TYPE = new Type<SeasonAssignedEventHandler>();
    
    public SeasonAssignedEvent() {
        
    }
    
	@Override
    public Type<SeasonAssignedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SeasonAssignedEventHandler handler) {
        handler.processSeasonAssignedEvent(this);
    }


}
