package za.co.ipay.metermng.client.view.component.dashboard;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.dto.dashboard.KeyIndicatorDto;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.CellPreviewEvent.Handler;
import com.google.gwt.view.client.ListDataProvider;

public class KeyIndicatorPanel extends DashPanel {
    
    private static final int DEFAULT_PAGE_SIZE = 15;
    
	private ListDataProvider<KeyIndicatorDto> dataProvider;
	private CellTable<KeyIndicatorDto> dashTable;

	protected boolean dirtyData = false;

	private static Logger logger = Logger.getLogger(KeyIndicatorPanel.class.getName());
	
	private TextColumn<KeyIndicatorDto> indicatorCol;	
	private TextColumn<KeyIndicatorDto> valueTodayCol;	
	private TextColumn<KeyIndicatorDto> valueMonthToDateCol;	
	private TextColumn<KeyIndicatorDto> valueLastMonthCol;
	

    public KeyIndicatorPanel() {}
    
    public KeyIndicatorPanel(ClientFactory clientFactory) {
    	super(clientFactory);
        createTable();
        initHeaders();
        initTable();
    }
    
    private void initHeaders() {
    	setDashTitle(MessagesUtil.getInstance().getMessage("dashboard.key.indicator.title"));
		setDashDescription(MessagesUtil.getInstance().getMessage("dashboard.key.indicator.description"));
	}

	protected void createTable() {
    	tablePlaceHolder.setVisible(true);
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            dashTable = new CellTable<KeyIndicatorDto>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
        	dashTable = new CellTable<KeyIndicatorDto>(DEFAULT_PAGE_SIZE);
        }
        tablePlaceHolder.add(dashTable);
    }
    
    protected void initTable() {
        if (dataProvider == null) {
        	
        	indicatorCol = new TextColumn<KeyIndicatorDto>() {
                @Override
                public String getValue(KeyIndicatorDto data) {
                    if (data.getIndicator() != null) {
                        return data.getIndicator();
                    }
                    return " ? ";
                }
            };
            
            indicatorCol.setSortable(false);  
            
            valueTodayCol = new TextColumn<KeyIndicatorDto>() {
                @Override
                public String getValue(KeyIndicatorDto data) {
                    if (data.getValueToday() != null) {
                        return data.getValueToday();
                    }
                    return " ? ";
                }
            };
            
            valueTodayCol.setSortable(false);   
            
            valueMonthToDateCol = new TextColumn<KeyIndicatorDto>() {
                @Override
                public String getValue(KeyIndicatorDto data) {
                    if (data.getValueMonthToDate() != null) {
                        return data.getValueMonthToDate();
                    }
                    return " ? ";
                }
            };
            
            valueMonthToDateCol.setSortable(false);
            
            valueLastMonthCol = new TextColumn<KeyIndicatorDto>() {
                @Override
                public String getValue(KeyIndicatorDto data) {
                    if (data.getValueLastMonth() != null) {
                        return data.getValueLastMonth();
                    }
                    return " ? ";
                }
            };
            
            valueLastMonthCol.setSortable(false);

           

            // Add the columns.
            dashTable.addColumn(indicatorCol, MessagesUtil.getInstance().getMessage("dashboard.key.indicator.indicator"));
            dashTable.addColumn(valueTodayCol, MessagesUtil.getInstance().getMessage("dashboard.key.indicator.value.today"));
            dashTable.addColumn(valueMonthToDateCol, MessagesUtil.getInstance().getMessage("dashboard.key.indicator.value.monthtodate"));
            dashTable.addColumn(valueLastMonthCol, MessagesUtil.getInstance().getMessage("dashboard.key.indicator.value.lastmonth"));
            
            dashTable.getColumn(1).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);            
            dashTable.getColumn(2).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);            
            dashTable.getColumn(3).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);
            
            dataProvider = new ListDataProvider<KeyIndicatorDto>();
            dataProvider.addDataDisplay(dashTable);
            
            dashTable.setTableLayoutFixed(true);
            
            dashTable.addCellPreviewHandler(new Handler<KeyIndicatorDto>() {
                @Override
                public void onCellPreview(CellPreviewEvent<KeyIndicatorDto> event) {
                    if ("mouseover".equals(event.getNativeEvent().getType())) {
                    	KeyIndicatorDto cellElement = event.getValue();
                    	dashTable.getRowElement(event.getIndex()).getCells().getItem(event.getColumn()).setTitle(cellElement.getToolTip());//
                      }
                }
            });
            
            logger.info("Created Key Indicator table");
        }
        refresh();
    }

    public void refresh() {
        clientFactory.getKeyIndicatorRpc().getKeyIndicators(new ClientCallback<ArrayList<KeyIndicatorDto>>() {
            @Override
            public void onSuccess(ArrayList<KeyIndicatorDto> result) {
                setKeyIndicatorList(result);
            }
        });
    }

	public void setKeyIndicatorList(ArrayList<KeyIndicatorDto> theData) {
		dataProvider.setList(theData);
		
	}
	

}
