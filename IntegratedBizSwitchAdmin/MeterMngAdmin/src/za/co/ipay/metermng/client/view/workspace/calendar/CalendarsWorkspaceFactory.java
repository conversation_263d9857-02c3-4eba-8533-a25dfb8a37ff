package za.co.ipay.metermng.client.view.workspace.calendar;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.Place;

public class CalendarsWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;

    public CalendarsWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_PS_CALENDAR_ADMIN)) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                    MediaResourceUtil.getInstance().getLockedIcon(), 
                    MessagesUtil.getInstance().getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;               
        }
        try {
            CalendarsWorkspaceView calendarsWorkspaceView = new CalendarsWorkspaceView(clientFactory, (CalendarPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(calendarsWorkspaceView);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof CalendarPlace 
                && (CalendarPlace.CALENDARS_PLACE.getCalendarPlaceType().equals(((CalendarPlace) place).getCalendarPlaceType()))) {
            return true;
        } else {
            return false;
        }
    }

}