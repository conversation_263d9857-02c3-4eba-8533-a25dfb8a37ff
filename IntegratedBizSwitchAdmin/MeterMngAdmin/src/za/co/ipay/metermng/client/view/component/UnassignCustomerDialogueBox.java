package za.co.ipay.metermng.client.view.component;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;

public class UnassignCustomerDialogueBox extends DialogBox {

    @UiField Button btnUnassignCustomer;
    @UiField Button btnWriteoff;
    @UiField Button btnCancel;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;

    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    private UsagePointWorkspaceView parentWorkspace;

    private HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();

    private static UnassignCustomerDialogueBoxUiBinder uiBinder = GWT.create(UnassignCustomerDialogueBoxUiBinder.class);

    interface UnassignCustomerDialogueBoxUiBinder extends UiBinder<Widget, UnassignCustomerDialogueBox> {
    }

    public UnassignCustomerDialogueBox(ClientFactory clientFactory, UsagePointWorkspaceView parentWorkspace) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        specialactionreasons = new SpecialActionsReasonComponent(clientFactory, null,
                SpecialActionsData.UNASSIGN_CUSTOMER);
        setWidget(uiBinder.createAndBindUi(this));
        addHandlers();
        hasDirtyData.setDirtyData(false);
        btnWriteoff.setVisible(clientFactory.getUser()
                .hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_OUTSTANDING_CHARGE_WRITEOFF));
    }

    protected void addHandlers() {
        FormDataChangeHandler formDataChangeHandler = new FormDataChangeHandler(hasDirtyData);
        specialactionreasons.lstbxReasons.addChangeHandler(formDataChangeHandler);
        specialactionreasons.txtbxReasons.addChangeHandler(formDataChangeHandler);
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
    }

    @UiHandler("btnUnassignCustomer")
    void handleUnassignCustomer(ClickEvent event) {
        processUnassignCustomer(UsagePointUpdatedEvent.UNASSIGN_CUSTOMER);
    }

    private void processUnassignCustomer(int usagePointUpdatedEvent) {
        if (isValid()) {
            usagePointData.setUnassignCustomerReasonsLog(specialactionreasons.getLogEntry());
            clientFactory.getEventBus()
                    .fireEvent(new UsagePointUpdatedEvent(parentWorkspace, usagePointData, usagePointUpdatedEvent));
            hide();
        }
    }

    private boolean isValid() {
        clearErrorMessages();
        boolean valid = true;
        if (specialactionreasons.isAttached() && specialactionreasons.isVisible() && !specialactionreasons.validate()) {
            valid = false;
        }
        return valid;
    }

    @UiHandler("btnCancel")
    void handleCancelButton(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            hide();
        }
    }

    public void clearErrorMessages() {
        specialactionreasons.clearErrorMessages();
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("btnWriteoff")
    void handleWriteoff(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                processUnassignCustomer(UsagePointUpdatedEvent.WRITEOFF_CHARGES);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }
}
