<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:p2="urn:import:za.co.ipay.gwt.common.client.form">
    <ui:style>

    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:HTMLPanel>
        <table>
            <tr>
                <td>
                    <g:VerticalPanel horizontalAlignment="ALIGN_CENTER" spacing="10">
                        <p1:Message ui:field="feedBack" debugId="feedback" />
                        <p2:FormRowPanel>
                            <p2:FormElement labelText="{msg.getCustomerSearchListboxLabel}:" styleName="gwt-Label-bold">
                                <g:ListBox ui:field="searchCriterionOptions" debugId="searchCriterionOptions" styleName="gwt-ListBox-ipay" visibleItemCount="1" />
                            </p2:FormElement>
                        </p2:FormRowPanel>
                        <g:FlowPanel>
                            <g:SuggestBox debugId="assignCustomerBoxSurname" ui:field="suggestionBoxAssignCustomerSurname" animationEnabled="true" autoSelectEnabled="false" styleName="gwt-TextBox-ipay" visible="true" />
                            <g:SuggestBox debugId="assignCustomerBoxIdNum" ui:field="suggestionBoxAssignCustomerIdNum" animationEnabled="true" autoSelectEnabled="false" styleName="gwt-TextBox-ipay" visible="false" />
                            <g:SuggestBox debugId="assignCustomerBoxAgrRef" ui:field="suggestionBoxAssignCustomerAgrRef" animationEnabled="true" autoSelectEnabled="false" styleName="gwt-TextBox-ipay" visible="false" />
                        </g:FlowPanel>
                        <g:FlowPanel>
                            <g:Button debugId="assignCustomerButton" text="{msg.getCustomerAssignShort}" styleName="gwt-Button-ipay" ui:field="btnAssignCustomer" />
                            <g:Button debugId="btnCancel" styleName="gwt-Button-ipay" ui:field="btnCancel" visible="true" text="{msg.getCancelButton}" />
                        </g:FlowPanel>
                    </g:VerticalPanel>
                </td>
            </tr>
        </table>
    </g:HTMLPanel>
</ui:UiBinder> 