package za.co.ipay.metermng.client.view.component.group.entity;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleFormView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.client.view.component.group.entity.ndp.NonDisconnectPeriodPanel;
import za.co.ipay.metermng.mybatis.generated.model.GroupFeature;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class EntityView extends BaseComponent {
    
    @UiField DisclosurePanel contactDisclosurePanel;
    @UiField Image contactOpenorclosearrow;
    @UiField Image contactImage;
    
    @UiField DisclosurePanel thresholdDisclosurePanel;
    @UiField Image thresholdOpenorclosearrow;
    @UiField Image thresholdImage;
    
    @UiField DisclosurePanel ndpDisclosurePanel;
    @UiField Image ndpOpenorclosearrow;
    @UiField Image ndpImage;
    @UiField Label ndpDisclosureComponentLabel;
    
    @UiField DisclosurePanel notificationDisclosurePanel;
    @UiField Image notificationOpenorclosearrow;
    @UiField Image notificationImage;
    
    @UiField(provided=true) EntityContactPanel contactPanel;
    @UiField(provided=true) ThresholdsPanel thresholdsPanel;
    @UiField(provided=true) NonDisconnectPeriodPanel ndpPanel;
    @UiField(provided=true) NotificationsPanel notificationsPanel;

    private GenGroupParentComponent parentWorkspace;
    private SimpleFormView view;
    
    private static EntityViewUiBinder uiBinder = GWT.create(EntityViewUiBinder.class);

    interface EntityViewUiBinder extends UiBinder<Widget, EntityView> {
    }
    
    private static Logger logger = Logger.getLogger(EntityView.class.getName());
    
    public EntityView(ClientFactory clientFactory, GenGroupParentComponent parentWorkspace, SimpleFormView view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
         
        contactPanel = new EntityContactPanel(parentWorkspace, clientFactory);
        thresholdsPanel = new ThresholdsPanel(parentWorkspace, clientFactory);
        ndpPanel = new NonDisconnectPeriodPanel(parentWorkspace, clientFactory);
        notificationsPanel = new NotificationsPanel(parentWorkspace, clientFactory);
        
        initWidget(uiBinder.createAndBindUi(this));
        initView();
        initUi();
    }
    
    private void initView() {
        //Note: the SimpleFormView layout is changed here. SimpleForm is removed from the layout and replaced with EntityView
        //to enable the disclosure panels to have width 100% and look same as elsewhere, by not being inside the 'panel'....
        view.getForm().removeFromParent();
        view.getSimpleFormViewPanel().add(this);
    }
    
    private void initUi() {
        initContactForm();
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_CUST_ACC_THRESHOLDS_ADMIN)) {
            thresholdDisclosurePanel.removeFromParent();
        } else {
            initThresholdForm();
        }
        
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_NDP_GROUP_ADMIN) 
                && !clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP) ) {
            ndpDisclosurePanel.removeFromParent();
        } else {  
            initNdpForm();
        }
        
        initNotificationForm();
        
        String entityHeading = "usagepointgroups.header";
        if (parentWorkspace.getSelectionType().equals(MeterMngStatics.ACCESS_GROUP_TYPE)) {
            entityHeading = "accessgroups.title";
        } else if (parentWorkspace.getSelectionType().equals(MeterMngStatics.LOCATION_GROUP_TYPE)) {
            entityHeading = "locationgroups.title";
        }
       
        Anchor anchor = new Anchor(MessagesUtil.getInstance().getMessage(entityHeading));
        anchor.addClickHandler(new ClickHandler() {            
            @Override
            public void onClick(ClickEvent event) {
                parentWorkspace.goBack();
            }
        });
        view.getPageHeader().addPageHeaderLink(anchor);
    }
    
    private void initContactForm() {
        //Contact Details Panel
        contactOpenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        contactImage.setResource(MediaResourceUtil.getInstance().getUserSmallIcon());
        
        this.contactDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                contactOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
            }
        });

        this.contactDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                contactOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
            }
        });
    }
    
    public void  openContactPanel() {
        this.contactDisclosurePanel.setOpen(true);
    }
    
    private void initThresholdForm() {
        //Contact Details Panel
        thresholdOpenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        thresholdImage.setResource(MediaResourceUtil.getInstance().getUserSmallIcon());
        
        this.thresholdDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                thresholdOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
            }
        });

        this.thresholdDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                thresholdOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
            }
        });
    }
    
    private void initNdpForm() {
        //Contact Details Panel
        ndpOpenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        ndpImage.setResource(MediaResourceUtil.getInstance().getClockImage());
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_NDP_GROUP_ADMIN) 
                && clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP) ) {
        }
        
        this.ndpDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                ndpOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
            }
        });

        this.ndpDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                ndpOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
            }
        });
    }
    
    private void initNotificationForm() {
        //Contact Details Panel
        notificationOpenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        notificationImage.setResource(MediaResourceUtil.getInstance().getUserSmallIcon());
        
        this.notificationDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                notificationOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
            }
        });

        this.notificationDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                notificationOpenorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
            }
        });
    }
    
    public void setGenGroup(GenGroupData genGroupData) {
        logger.info("EntityView: setGenGroup=" + genGroupData.getName());
        view.setDataDetails(genGroupData.getName(), null);

        boolean permissionToEdit = clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_GROUP_ENTITY);

        contactPanel.changeButtonsPanelVisibility(permissionToEdit);
        contactPanel.loadEntity(genGroupData);
        
        //RC if access / location group --> hide the thresholds panel!!
        boolean allowThresholds = false;
        boolean allowNdp = false;
        boolean allowNotifications = false;
        
        for (GroupFeature gf : parentWorkspace.getSelectedGroupTypeData().getFeaturesList()) {
            if (gf.getGroupFeatureValue().equals("CUSTOMER_ACC_THRESHOLDS")) {
                allowThresholds = true;
            }
            if (gf.getGroupFeatureValue().equals("NDP")) {
                allowNdp = true;
            }
            if (gf.getGroupFeatureValue().equals("ACCOUNT_NOTIFICATIONS")) {
                allowNotifications = true;
            }
        }
        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_CUST_ACC_THRESHOLDS_ADMIN)) {
            if (parentWorkspace.getSelectionType().equals(MeterMngStatics.ACCESS_GROUP_TYPE) 
                    || parentWorkspace.getSelectionType().equals(MeterMngStatics.LOCATION_GROUP_TYPE)
                    || !allowThresholds) {
                thresholdDisclosurePanel.setVisible(false);
            } else {
                thresholdDisclosurePanel.setVisible(true);
                thresholdsPanel.loadThresholds(genGroupData);
            }
        }
        
        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_NDP_GROUP_ADMIN) 
             || clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP) ) {
            if (parentWorkspace.getSelectionType().equals(MeterMngStatics.ACCESS_GROUP_TYPE) 
                    || parentWorkspace.getSelectionType().equals(MeterMngStatics.LOCATION_GROUP_TYPE)
                    || !allowNdp) {
                ndpDisclosurePanel.setVisible(false);
            } else {
                ndpDisclosurePanel.setVisible(true);
                ndpPanel.loadNdps(genGroupData);
            }
        }
        
        if (parentWorkspace.getSelectionType().equals(MeterMngStatics.ACCESS_GROUP_TYPE) 
                || parentWorkspace.getSelectionType().equals(MeterMngStatics.LOCATION_GROUP_TYPE)
                || !allowNotifications) {
            notificationDisclosurePanel.setVisible(false);
        } else {
            notificationDisclosurePanel.setVisible(true);
            notificationsPanel.loadNotificationData(genGroupData);
        }
    }
    
    public void refreshGlobalThresholds() {
        thresholdsPanel.getGlobalSettings();
    }
    
    public void refreshCustomComponents() {
        contactPanel.loadCustomComponents(null);
    }
}
