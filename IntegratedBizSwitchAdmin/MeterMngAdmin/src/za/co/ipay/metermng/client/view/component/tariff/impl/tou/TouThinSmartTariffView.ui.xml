<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	         xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace">
	  <ui:style> 
        @external touReadingTypePadding;  /* Prevents GWT from obfuscating the class name */
        .touReadingTypePadding td {
            padding-top: 1.5em !important;
        }
    </ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
	<g:HTMLPanel>
	
	    <t:FormRowPanel>
            <t:FormElement ui:field="taxElement" debugId="touTaxElement" labelText="{msg.getTariffTax}:" helpMsg="{msg.getTariffTaxHelp}" required="true">
                <w:PercentageTextBox ui:field="taxBox" debugId="touTaxBox" />
            </t:FormElement>  
        </t:FormRowPanel>
        
        <t:FormRowPanel>
            <t:FormElement ui:field="monthlyDemandChargeElement" debugId="monthlyDemandChargeElement" labelText="{msg.getMonthlyDemandCharge}:" helpMsg="{msg.getMonthlyDemandChargeHelp}">
                <g:Label ui:field="monthlyDemandChargeCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="monthlyDemandChargeBox" debugId="monthlyDemandChargeBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>            
            <t:FormElement ui:field="monthlyDemandTypeElement" debugId="monthlyDemandTypeElement" labelText="{msg.getMonthlyDemandType}:" helpMsg="{msg.getMonthlyDemandTypeHelp}">
                <g:ListBox ui:field="monthlyDemandTypeBox" debugId="monthlyDemandTypeBox" styleName="gwt-TextBox" />
            </t:FormElement>            
        </t:FormRowPanel>

        <g:FlowPanel ui:field="cyclicChargesPanel">
        </g:FlowPanel>
        
        <g:FlowPanel ui:field="percentChargesPanel">
        </g:FlowPanel>
        
        <t:FormRowPanel ui:field="paytypeDiscountFormRowPanel"></t:FormRowPanel>
                
        <t:FormRowPanel>
            <t:FormElement ui:field="calendarElement" debugId="calendarElement" helpMsg="{msg.getTouThinCalendarHelp}" labelText="{msg.getTouThinCalendar}" required="true">
                <g:ListBox visibleItemCount="1" ui:field="calendarBox" debugId="calendarBox" styleName="gwt-TextBox" multipleSelect="false"/>
            </t:FormElement>
            <t:FormElement ui:field="enableReadingTypesElement" debugId="enableReadingTypesElement" labelText="{msg.getEnableReadingTypes}:" helpMsg="{msg.getEnableReadingTypesHelp}" required="true">
                <g:ListBox ui:field="enableReadingTypesBox" debugId="enableReadingTypesBox" multipleSelect="true" visibleItemCount="5" styleName="gwt-TextBox" />
            </t:FormElement>                                
            <t:FormElement ui:field="chargesButtonElement">
                <g:Button ui:field="chargesButton" debugId="chargesButton" text="{msg.getGenerateChargesButton}" />
            </t:FormElement>   
        </t:FormRowPanel>
        
        <t:FormRowPanel>
            <t:FormElement ui:field="chargesElement" debugId="chargesElement" helpMsg="{msg.getTouThinChargesHelp}" labelText="{msg.getTouThinCharges}" required="true">
                <g2:CellTable ui:field="chargesTable" debugId="chargesTable" />
             </t:FormElement>
        </t:FormRowPanel>
                
  	</g:HTMLPanel>
</ui:UiBinder> 