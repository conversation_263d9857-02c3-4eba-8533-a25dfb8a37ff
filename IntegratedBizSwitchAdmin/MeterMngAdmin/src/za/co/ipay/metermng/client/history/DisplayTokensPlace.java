package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class DisplayTokensPlace extends Place {

    public static DisplayTokensPlace ALL_DISPLAY_TOKENS_PLACE = new DisplayTokensPlace();

    public DisplayTokensPlace() {
    }

    @Prefix(value = "displayTokens")
    public static class Tokenizer implements PlaceTokenizer<DisplayTokensPlace> {
        @Override
        public String getToken(DisplayTokensPlace place) {
            return "all";
        }

        @Override
        public DisplayTokensPlace getPlace(String token) {
            return new DisplayTokensPlace();
        }
    }
}
