package za.co.ipay.metermng.client.view.workspace.meter.readings.add.supermeter;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;

import com.google.gwt.place.shared.Place;

public class AddSuperMeterReadingsWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;
    
    private static Logger logger = Logger.getLogger(AddSuperMeterReadingsWorkspaceFactory.class.getName());

    public AddSuperMeterReadingsWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        try {
            AddSuperMeterReadingsWorkspaceView view = new AddSuperMeterReadingsWorkspaceView(clientFactory, (AddMeterReadingsPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof AddMeterReadingsPlace) {
            AddMeterReadingsPlace p = (AddMeterReadingsPlace) place;
            logger.info(""+p.toString());
            if (AddMeterReadingsPlace.SUPER_METER_TYPE.equals(p.getMeterType())) {
                return true;
            }
        }
        return false;
    }
}
