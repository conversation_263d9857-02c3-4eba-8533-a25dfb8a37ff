package za.co.ipay.metermng.client.view.component.group;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class ChangeGroupSelectionPanel extends SimpleFormPanel {
    
    @UiField FormElement groupElement;
    @UiField TextBox usernameBox;
    @UiField TextBox assignedGroupBox;
    @UiField HorizontalPanel groupPanel;
    SelectionDataWidget userGroupBox;
        
    private static ChangeGroupSelectionPanelUiBinder uiBinder = GWT.create(ChangeGroupSelectionPanelUiBinder.class);

    interface ChangeGroupSelectionPanelUiBinder extends UiBinder<Widget, ChangeGroupSelectionPanel> {
    }

    public ChangeGroupSelectionPanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
    }

    @Override
    public void addFieldHandlers() {
        
    }

    @Override
    public void clearFields() {
        usernameBox.setText("");
        assignedGroupBox.setText("");        
        for(int i=groupPanel.getWidgetCount()-1;i>=0;i--) {
            groupPanel.remove(i);
        }
    }

    @Override
    public void clearErrors() {
        groupElement.showErrorMsg(null);
    }
}
