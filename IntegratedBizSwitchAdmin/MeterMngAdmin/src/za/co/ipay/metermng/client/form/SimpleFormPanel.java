package za.co.ipay.metermng.client.form;

import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.form.SimplePanel;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;

import com.google.gwt.uibinder.client.UiFactory;

public abstract class SimpleFormPanel extends SimplePanel {
    
    public SimpleFormPanel(SimpleForm form) {
        super(form);
    }

    /**
     * This is a Ui factory method that allows all the workspace's  UiBinder XML files to use the same instance of the UiMessages.
     * @return The UiMessages used to get i18n strings for the UiBinder XML files.
     */
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }
}
