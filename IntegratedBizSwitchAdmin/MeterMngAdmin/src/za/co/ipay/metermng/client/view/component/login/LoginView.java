package za.co.ipay.metermng.client.view.component.login;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.FormPanel;
import com.google.gwt.user.client.ui.FormPanel.SubmitCompleteEvent;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback.SessionCheckResolution;
import za.co.ipay.metermng.client.event.ChangePasswordEvent;
import za.co.ipay.metermng.client.event.SelectAccessGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.ChangePasswordPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class LoginView extends BaseComponent {

    public static class LoginViewUtil {
        private static LoginView instance;
        public static LoginView getInstance(ClientFactory clientFactory, SessionCheckCallback sessionCheckCallback) {
            if (instance == null) {
                instance = new LoginView(clientFactory,sessionCheckCallback);
            } else {
                // This is safe because the browser app is single threaded
                instance.sessionCheckCallback = sessionCheckCallback;
            }
            return instance;
        }
        public static LoginView getInstance(ClientFactory clientFactory) {
            return getInstance(clientFactory, null);
        }
    }

    private PopupPanel popup;
    private LoginPanel panel;
    private SessionCheckCallback sessionCheckCallback;

    private static Logger logger = Logger.getLogger(LoginView.class.getName());

    private static LoginViewUiBinder uiBinder = GWT.create(LoginViewUiBinder.class);

    interface LoginViewUiBinder extends UiBinder<Widget, LoginView> {
    }

    private LoginView(ClientFactory clientFactory, SessionCheckCallback sessionCheckCallback) {
        this.sessionCheckCallback = sessionCheckCallback;
        setClientFactory(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    public void displayLoginView() {
        String userName = clientFactory.getUser().getUserName();
        if (userName != null && !userName.trim().isEmpty()) {
            panel.getUsernameBox().setText(userName);
            panel.getUsernameBox().setReadOnly(true);
        }
        String sessionAuthId = clientFactory.getUser().getSessionAuthId();
        if (sessionAuthId != null && !sessionAuthId.trim().isEmpty()) {
            panel.getSessionAuthId().setValue(sessionAuthId);
        }
        panel.passwordBox.setText("");
        if (!popup.isShowing()) {
            popup.setPopupPosition(Dialogs.getLeft(panel), Dialogs.TOP);
            popup.show();
        }
    }

    private void initUi() {
        panel = new LoginPanel();
        panel.instructions.setText(MessagesUtil.getInstance().getMessage("login.session.timedout"));
        panel.loginButton.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onLogin();
            }
        });
        panel.logoutButton.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                popup.hide();
                clientFactory.setUser(null);
                Window.Location.assign("j_spring_security_logout");
            }
        });
        panel.passwordBox.addKeyDownHandler(new EnterKeyHandler() {
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                onLogin();
            }
        });
        //Handle the form submits
        panel.form.addSubmitCompleteHandler(new FormPanel.SubmitCompleteHandler() {
            public void onSubmitComplete(SubmitCompleteEvent event) {
                if (event.getResults().indexOf(ClientCallback.SPRING_SECURITY_ACTION) > -1) {
                    logger.info("Logon was not successful");
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.login"), MediaResourceUtil.getInstance().getErrorIcon());
                } else {
                    popup.hide();
                    logger.info("Logged on successfully");
                    final PopupPanel waitingDialog = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon());
                    clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
                        @Override
                        public void onSuccess(MeterMngUser user) {
                            waitingDialog.hide();
                            logger.info("Got current user: "+user.getUserName()+" currentGroup: "+user.getCurrentGroup());
                            //Does the user need to update their password?
                            if (user.isPasswordExpired() || user.isPasswordRequiresReset()) {
                                clientFactory.getEventBus().fireEvent(new ChangePasswordEvent(ChangePasswordPlace.LOGGED_IN_USER));
                            } else {
                                //Set the user's user name and current group in the header
                                clientFactory.setUser(user);
                                logger.info("Relogged in user: "+user.getUserName());
                                if(sessionCheckCallback != null) {
                                    sessionCheckCallback.callback(SessionCheckResolution.CONTINUE);
                                }
                                if (user.isChooseCurrentGroupleafNode()) {
                                    clientFactory.getEventBus().fireEvent(new SelectAccessGroupEvent());
                                }
                                if (clientFactory.isEnableAccessGroups() && user.getSessionGroupName() != null) {
                                    clientFactory.displayUserGroup(user.getSessionGroupName());
                                } else if (!clientFactory.isEnableAccessGroups() && user.getAssignedGroup() != null) {
                                    clientFactory.displayUserGroup(user.getAssignedGroup().getName());
                                } else {
                                    clientFactory.displayUserGroup("");
                                }
                            }
                        }
                    });
                }
            }
        });

        //Display in a pop up
        popup = new PopupPanel(false, true);
        popup.setModal(true);
        popup.setHeight("250px");
        popup.setWidth("240px");
        popup.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        popup.setWidget(panel);
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        String currentPassword = panel.passwordBox.getText();
        String username = panel.usernameBox.getText();

        if (currentPassword == null || currentPassword.trim().equals("")) {
            valid = false;
            panel.passwordElement.setErrorMsg(MessagesUtil.getInstance().getMessage("password.required"));
        }
        if (username == null || username.trim().equals("")) {
            valid = false;
            panel.usernameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("username.required"));
        }

        return valid;
    }

    private void onLogin() {
        if (isValidInput()) {
            panel.form.submit();
        }
    }
}
