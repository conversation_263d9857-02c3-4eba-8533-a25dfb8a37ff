package za.co.ipay.metermng.client.view.workspace;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.AuxTypesUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AuxTypePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

public class AuxTypeWorkspaceView extends BaseWorkspace implements FormManager<AuxType> {

    private Logger logger = Logger.getLogger("AuxTypeWorkspaceView");

    private ClientFactory clientFactory;

    private AuxType auxType;

    private List<AuxType> auxTypeList;
    @UiField SimpleTableView<AuxType> view;
    AuxTypePanel panel;

    private ListDataProvider<AuxType> dataProvider = new ListDataProvider<AuxType>();

    private static AuxTypeUiBinder uiBinder = GWT.create(AuxTypeUiBinder.class);

    public static final class AuxTypeWorkspaceFactory implements WorkspaceFactory {
        private ClientFactory clientFactory;

        public AuxTypeWorkspaceFactory(ClientFactory clientFactory) {
            this.clientFactory = clientFactory;
            clientFactory.getWorkspaceContainer().register(this);
        }

        @Override
        public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_AUX_TYPE_ADMIN)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;
            }
            try {
                AuxTypeWorkspaceView auxTypeWorkspaceView = new AuxTypeWorkspaceView(clientFactory, (AuxTypePlace) place);
                workspaceCreateCallback.onWorkspaceCreated(auxTypeWorkspaceView);
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        }

        @Override
        public boolean handles(Place place) {
            return place instanceof AuxTypePlace;
        }

    }


    interface AuxTypeUiBinder extends UiBinder<Widget, AuxTypeWorkspaceView> {
    }

    public AuxTypeWorkspaceView(ClientFactory clientFactory, AuxTypePlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString("auxType:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("auxillarytype.title"));
        initUi();
        actionPermissions();
    }

    private void initUi() {
        initView();
        initForm();
        populate();
        initUI();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new AuxTypePanel(view.getForm());
        panel.mridComponent.initMrid(clientFactory);
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxillarytype.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSaveButtonClick();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");
    }

    private void initUI() {
        TextColumn<AuxType> nameColumn = new TextColumn<AuxType>() {
            @Override
            public String getValue(AuxType auxType) {
                return auxType.getName();
            }
        };
        nameColumn.setSortable(true);
        TextColumn<AuxType> descriptionColumn = new TextColumn<AuxType>() {
            @Override
            public String getValue(AuxType auxType) {
                return auxType.getDescription();
            }
        };
        StatusTableColumn<AuxType> statusColumn = new StatusTableColumn<AuxType>();

        // Add the columns.
        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("auxtype.field.name"));
        view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("auxtype.field.description"));
        view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("auxtype.field.status"));
        view.getTable().ensureDebugId("auxTypeTable");

        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        view.getTable().setPageSize(getPageSize());
    }

    private void setAuxType(AuxType type) {
        panel.clearErrors();
        panel.clearFields();
        this.auxType = type;
        if (auxType != null) {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxillarytype.title.update"));
            panel.mridComponent.setMrid(auxType.getMrid());
            panel.mridComponent.setIsExternal(auxType.isMridExternal());
        } else {
            auxType = new AuxType();
            auxType.setRecordStatus(RecordStatus.DAC);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxillarytype.title.add"));
            view.clearTableSelection();
            panel.mridComponent.initMrid(clientFactory);
        }

        panel.nameTextBox.setText(auxType.getName());
        panel.descriptionTextBox.setText(auxType.getDescription());
        panel.activeBox.setValue(RecordStatus.ACT.equals(auxType.getRecordStatus()));
    }

    public void populate() {
        clientFactory.getAuxTypeRpc().getAuxTypes(new ClientCallback<List<AuxType>>() {
            @Override
            public void onSuccess(List<AuxType> result) {
                auxTypeList = result;
                dataProvider.setList(auxTypeList);
                dataProvider.refresh();
            }
        });
    }

    @Override
    public void onLeaving() {

    }

    void onSaveButtonClick() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (view.getForm().getSaveBtn().getText().equals(MessagesUtil.getInstance().getMessage("button.create"))){
                    addAuxType();
                } else {
                    updateAuxType();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void update(AuxType auxType) {
        auxType.setName(panel.nameTextBox.getText());
        auxType.setDescription(panel.descriptionTextBox.getText());
        auxType.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        auxType.setMrid(panel.mridComponent.getMrid());
        auxType.setMridExternal(panel.mridComponent.isExternal());
    }

    private boolean isValid() {
        boolean valid = true;

        panel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(auxType, "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxType, "description", panel.descriptionElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxType, "mrid", panel.mridComponent.getTxtbxMridElement())) {
            valid = false;
        }
        if(!panel.mridComponent.validate()) {
            valid = false;
        }

        return valid;
    }

    public void addAuxType() {
        auxType = new AuxType();
        update(auxType);
        if (isValid()) {
            clientFactory.getAuxTypeRpc().addAuxType(auxType, new ClientCallback<AuxType>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(AuxType result) {
                    if (result != null) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("auxillarytype.title") }),
                                MediaResourceUtil.getInstance().getInformationIcon(),
                                view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        auxTypeList.add((AuxType) result);
                        dataProvider.setList(auxTypeList);
                        dataProvider.refresh();
                        setAuxType(null);
                        clientFactory.getEventBus().fireEvent(new AuxTypesUpdatedEvent());
                    }
                }

                @Override
                public void onFailure(Throwable caught) {
                    if (caught instanceof AccessControlException) {
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(AuxTypePlace.ALL_AUX_TYPE_PLACE);
                    }
                    super.onFailure(caught);
                };
            });
        }
    }

    public void updateAuxType() {
        AuxType auxType = new AuxType();
        auxType.setId(this.auxType.getId());
        update(auxType);
        if (isValid()) {
                clientFactory.getAuxTypeRpc().updateAuxType(auxType, new ClientCallback<AuxType>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                    @Override
                    public void onSuccess(AuxType result) {
                        if (result != null) {
                        AuxTypeWorkspaceView.this.auxType = result;
                            int index = -1;
                            for (int i = 0; i < auxTypeList.size(); i++) {
                                if (auxTypeList.get(i).getId().equals(result.getId())){
                                    index = i;
                                    break;
                                }
                            }
                            if (index > -1) {
                                setAuxType(null);
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("auxillarytype.title") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                auxTypeList.set(index, result);
                                dataProvider.setList(auxTypeList);
                                dataProvider.refresh();
                                clientFactory.getEventBus().fireEvent(new AuxTypesUpdatedEvent());
                            }
                        }
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        if (caught instanceof AccessControlException) {
                            clientFactory.getWorkspaceContainer().closeWorkspaceNow(AuxTypePlace.ALL_AUX_TYPE_PLACE);
                        }
                        super.onFailure(caught);
                    };
                });
            }
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at aux type");
        clientFactory.getAuxTypeRpc().getAuxTypes(new ClientCallback<List<AuxType>>() {
            @Override
            public void onSuccess(List<AuxType> result) {
                List<AuxType> list = dataProvider.getList();
                list.clear();
                list.addAll(result);
                dataProvider.refresh();
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(AuxTypePlace.ALL_AUX_TYPE_PLACE);
                }
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return  place instanceof AuxTypePlace;
    }

    @Override
    public void onSelect() {

    }

    @Override
    public void displaySelected(AuxType selected) {
        setAuxType(selected);
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_AUX_TYPE)) {
            view.getForm().getButtons().removeFromParent();
        }
    }
}
