package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataBigDecimalDto;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataCountTableDto;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

@RemoteServiceRelativePath("secure/dashboard.do")
public interface DashBoardRpc extends RemoteService {

    ArrayList<TsDataCountTableDto> getVendingActivity() throws ValidationException, ServiceException;
    ArrayList<TsDataBigDecimalDto> getBuyingIndexData() throws ValidationException, ServiceException;
}
