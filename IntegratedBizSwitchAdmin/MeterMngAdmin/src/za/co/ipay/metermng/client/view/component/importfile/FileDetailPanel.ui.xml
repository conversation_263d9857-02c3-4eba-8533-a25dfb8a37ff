<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component.importfile.customwidgets">

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <ui:style>
        .label_border {
            border: 1px solid lightgray;
            padding-bottom: 0.2em;
        }

    </ui:style>


    <g:FlowPanel>
        <g:HTML ui:field="dataName" text="" styleName="dataTitle"/>
        <g:FlowPanel styleName="formElementsPanel">
            <p1:FormRowPanel>
                <p2:LabelGroupElement ui:field="fileNameGrpEl" headerLabel="{msg.getFileNameLabel}:"/>
                <p2:LabelGroupElement ui:field="fileTypeNameGrpEl" headerLabel="{msg.getFileTypeLabelText}:"/>
                <p2:LabelGroupElement ui:field="numItemsGrpEl" headerLabel="{msg.getNumItemsLabel}:"/>
            </p1:FormRowPanel>

            <p1:FormRowPanel>
                <p2:LabelGroupElement ui:field="upLoadStartDateGrpEl" headerLabel="{msg.getUploadStartLabel}:"/>
                <p2:LabelGroupElement ui:field="upLoadEndDateGrpEl" headerLabel="{msg.getUploadEndLabel}:"/>
                <p2:LabelGroupElement ui:field="uploadUserNameGrpEl" headerLabel="{msg.getUploadUsernameLabel}:"/>
                <p2:LabelGroupElement ui:field="uploadnumFailedGrpEl" headerLabel="{msg.getUploadNumFailedLabel}:"/>
            </p1:FormRowPanel>

            <p1:FormRowPanel>
                <p2:LabelGroupElement ui:field="lastImportStartDateGrpEl" headerLabel="{msg.getLastImportStartLabel}:"/>
                <p2:LabelGroupElement ui:field="lastImportEndDateGrpEl" headerLabel="{msg.getLastImportEndLabel}:"/>
                <p2:LabelGroupElement ui:field="lastImportUserNameGrpEl"
                                      headerLabel="{msg.getLastImportUserNameLabel}:"/>
            </p1:FormRowPanel>

            <p1:FormRowPanel ui:field="bulkRefRowPanel" visible="false">
                <p2:LabelGroupElement ui:field="bulkRefGrpEl" headerLabel="{msg.getKeyChangeBulkRefLabel}:"/>
            </p1:FormRowPanel>

            <g:HTML ui:field="regReadReminder" text="{msg.getFileItemPanelRegReadReminder}"
                    styleName="dataDescription"/>

            <g:HTML ui:field="stillBusyText" styleName="errorInline"/>
            <g:HTML ui:field="warnMaxLicenseExceeded" styleName="errorInline"/>

        </g:FlowPanel>
    </g:FlowPanel>
</ui:UiBinder> 