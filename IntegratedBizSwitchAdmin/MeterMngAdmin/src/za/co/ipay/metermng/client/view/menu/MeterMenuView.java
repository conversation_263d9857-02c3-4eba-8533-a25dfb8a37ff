package za.co.ipay.metermng.client.view.menu;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;

public class MeterMenuView extends BaseComponent {
    
    @UiField FlowPanel meterMenu;
    @UiField FlowPanel addMeterOnlineBulkLink;
    @UiField FlowPanel addMeterLink;
    @UiField FlowPanel manufacturerLink;
    @UiField FlowPanel meterModelLink;
    @UiField FlowPanel mdcLnk;
    @UiField FlowPanel energyBalancingMetersLink;
    @UiField FlowPanel demoMeterReadingsLink;
    @UiField FlowPanel demoSuperMeterReadingsLink;
    @UiField FlowPanel supplyGroupLnk;
    @UiField FlowPanel deviceStoreLnk;
    @UiField FlowPanel blockingTypeLink;
    @UiField FlowPanel displayTokensLnk;
    @UiField FlowPanel meterCustUPBulkUploadLink;
    @UiField FlowPanel meterBulkUploadLink;
    
    private static MeterMenuViewUiBinder uiBinder = GWT.create(MeterMenuViewUiBinder.class);
    interface MeterMenuViewUiBinder extends UiBinder<Widget, MeterMenuView> {
    }

    public MeterMenuView(ClientFactory clientFactory) {
        //this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
         
        checkEnableSTS(clientFactory.isEnableSTS());
        checkDemoMode(clientFactory.isDemoMode());
    }
    
    public void checkDemoMode(boolean demoMode) {
        if (!demoMode) {
            demoMeterReadingsLink.removeFromParent();
            demoSuperMeterReadingsLink.removeFromParent();
        } else {
            demoMeterReadingsLink.setVisible(demoMode);
            demoSuperMeterReadingsLink.setVisible(demoMode);
        }
    }
    
    public void checkEnableSTS(boolean enableSTS) {
        if (!enableSTS) {
            displayTokensLnk.removeFromParent();
            supplyGroupLnk.removeFromParent();
        }    
    }
    
    public void checkPermissions(MeterMngUser user) {
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_ONLINE_BULK)) {
            addMeterOnlineBulkLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_NEW_METER)) {
            addMeterLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_MANUFACTURER_ADMIN)) {
            manufacturerLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_MODEL_ADMIN)) {
            meterModelLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_ADMIN)) {
            mdcLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_ENERGY_BALANCING_METER_ADMIN)) {
            energyBalancingMetersLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_SUPPLY_GROUP_ADMIN)) {
            supplyGroupLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_STORE_ADMIN)) {
            deviceStoreLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_BLOCKING_TYPE_ADMIN)) {
            blockingTypeLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GEN_STS_NON_METER_SPEC)) {
            displayTokensLnk.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GENERATE_METERCUSTUP_UPLOAD_TEMPLATE)) {
            meterCustUPBulkUploadLink.removeFromParent();
        }
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_TRANS_UPLOAD)) {
        	meterBulkUploadLink.removeFromParent();
        }
    }
    
    public boolean isMeterMenuEmpty() {
        if (meterMenu.getWidgetCount() < 1) {
            return true;   //IS empty
        } else {
            return false;  //has elements
        }
    }
}
