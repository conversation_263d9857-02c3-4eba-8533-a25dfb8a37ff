package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.EnergyBalancingMeterEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class EnergyBalancingMeterActivity extends AbstractActivity {

    private EnergyBalancingMeterPlace place;
    private ClientFactory clientFactory;
    
    public EnergyBalancingMeterActivity(EnergyBalancingMeterPlace place, ClientFactory clientFactory) {
        super();
        this.place = place;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new EnergyBalancingMeterEvent(place.getMeter()));
    }
}
