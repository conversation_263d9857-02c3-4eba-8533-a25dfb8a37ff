<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>

        <p1:FormRowPanel>
            <p1:FormElement ui:field="nameElement" labelText="{msg.getTaskScheduleName}:" helpMsg="{msg.getTaskScheduleNameHelp}" required="true">
                <g:TextBox text="" ui:field="nameTextBox" title="{msg.getTaskScheduleName}" width="250px" />
            </p1:FormElement>              
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="activeElement" labelText="{msg.getTaskScheduleActive}:" helpMsg="{msg.getTaskScheduleActiveHelp}">
                <g:CheckBox ui:field="activeBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:HorizontalPanel>
	            <p1:FormElement ui:field="scheduleElement" labelText="{msg.getTaskScheduleSchedule}:" helpMsg="{msg.getTaskScheduleScheduleHelp}" required="true">
	                <g:ListBox ui:field="scheduleBox" title="{msg.getTaskScheduleSchedule}" />
	            </p1:FormElement>
	            <g:VerticalPanel ui:field="schedulePanel"></g:VerticalPanel>
            </g:HorizontalPanel>
        </p1:FormRowPanel>        

    </g:FlowPanel>

</ui:UiBinder> 