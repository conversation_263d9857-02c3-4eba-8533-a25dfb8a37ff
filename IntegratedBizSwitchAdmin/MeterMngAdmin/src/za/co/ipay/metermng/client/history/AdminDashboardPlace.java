package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class AdminDashboardPlace extends Place {

    public static AdminDashboardPlace ALL_ADMIN_DASHBOARD_PLACE = new AdminDashboardPlace();
    public static final String ADMIN_DASHBOARD_PLACE_PREFIX = "admin_dashboard"; 

    public AdminDashboardPlace() {
    }

    @Prefix(value = ADMIN_DASHBOARD_PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<AdminDashboardPlace> {
        @Override
        public String getToken(AdminDashboardPlace place) {
            return "all";
        }

        @Override
        public AdminDashboardPlace getPlace(String token) {
            return new AdminDashboardPlace();
        }
    }
}
