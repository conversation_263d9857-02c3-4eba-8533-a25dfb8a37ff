package za.co.ipay.metermng.client.view.workspace.meter.onlinebulk;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PushButton;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEventHandler;
import za.co.ipay.metermng.client.event.MeterModelChangedEvent;
import za.co.ipay.metermng.client.event.MeterModelChangedEventHandler;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEvent;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEventHandler;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEvent;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.client.view.component.group.ContainsUPGroupSelectionComponent;
import za.co.ipay.metermng.client.view.component.meter.EngineeringTokenUserRefPanel;
import za.co.ipay.metermng.client.view.component.onlinebulk.TariffPanelDialogueBox;
import za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupSelectionPanel;
import za.co.ipay.metermng.client.widget.pricingstructure.PricingStructureLookup;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;
import za.co.ipay.metermng.client.view.workspace.specialactions.SpecialActionsView;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.shared.MeterInDeviceStoreSuggestOracle;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterOnlineBulkData;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.util.MeterMngSharedUtils;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MeterOnlineBulkPanel extends SimpleFormPanel implements ContainsUPGroupSelectionComponent{

    @UiField FlowPanel mobPanel;

    @UiField IpayListBox lstbxSelectStore;

    @UiField FormElement suggestBoxMeterElement;
    @UiField(provided=true) SuggestBox suggestBoxMeterNumber;

    @UiField FormElement meterModelElement;
    @UiField IpayListBox lstbxMeterModel;

    @UiField FormRowPanel breakerIdPanel;
    @UiField FormElement breakerIdElement;
    @UiField TextBox txtbxBreakerId;
    @UiField TextBox txtbxMeterUriAddress;
    @UiField BigDecimalValueBox txtbxMeterUriPort;
    @UiField TextBox txtbxMeterUriProtocol;
    @UiField TextArea txtbxMeterUriParams;
    @UiField FormGroupPanel uriPanel;
    @UiField FormElement meterUriAddressElement;
    @UiField FormElement meterUriPortElement;
    @UiField FormElement meterUriProtocolElement;
    @UiField FormElement meterUriParamsElement;

    @UiField FormGroupPanel stsContainer;
    @UiField FormElement supplyGrpCdeElement;
    @UiField IpayListBox lstbxSupplyGrpCde;
    @UiField FormElement algCodeElement;
    @UiField IpayListBox lstbxAlgCode;
    @UiField FormElement tokTecCodeElement;
    @UiField IpayListBox lstbxTokTecCode;
    @UiField FormElement currTariffIndxElement;
    @UiField TextBox txtbxCurrTariffIndx;
    @UiField FormRowPanel encryptionKeyRow;
    @UiField TextBox txtbxEncryptionKey;
    @UiField FormElement encryptionKeyElement;

    //FormGroupPanel upContainer;
    @UiField FormElement installationDateElement;
    @UiField DateBox dtbxMeterInstallationDate;
    // FormGroupPanel : PricingStructure
    @UiField FormElement currentPSElement;
    @UiField FormElement futurePSElement;
    @UiField FormElement  currentPSStartDateElement;
    @UiField FormElement  futurePSStartDateElement;
    @UiField(provided = true) PricingStructureLookup currentPricingStructureLookup;
    @UiField(provided = true) PricingStructureLookup futurePricingStructureLookup;
    @UiField DateBox currentPricingStructureStartDate;
    @UiField DateBox futurePricingStructureStartDate;

    @UiField PushButton showCurrTariffBtn;
    @UiField FormRowPanel pricingChangeReasonPanel;
    @UiField FormRowPanel futurePSRowPanel;

    @UiField FlowPanel locationGroupPanel;
    @UiField FormElement suiteNumberElement;
    @UiField TextBox txtbxSuiteNumber;

    @UiField FormElement surnameElement;
    @UiField TextBox txtbxSurname;
    @UiField FormElement phoneNumberElement;
    @UiField TextBox txtbxPhone;

    //Free Issue Token Container
    @UiField FormGroupPanel freeIssueContainer;
    @UiField FormElement genFreeIssueTokenElement;
    @UiField CheckBox chkBxGenFreeIssueToken;

    @UiField FormElement unitsElement;
    @UiField BigDecimalValueBox txtbxFreeIssueUnits;

    @UiField(provided=true) SpecialActionsReasonComponent freeIssueTokenReasonsComponent;

    @UiField FormElement smsTokenElement;
    @UiField CheckBox chkBxSmsToken;

    @UiField(provided=true) EngineeringTokenUserRefPanel engineeringTokenUserRefPanel;
    @UiField Label lblUnitSymbol;

    private MeterInDeviceStoreSuggestOracle metersInDeviceStoresSuggestOracle;

    private MeterOnlineBulkData meterOnlineBulkData;

    private final ClientFactory clientFactory;
    private boolean isStsMeter = false;

    private boolean displayUnusualMeterMsg = true;           //see comment at populatePanel()
    private MeterOnlineBulkWorkspaceView parentWorkspace;

    private SpecialActionsReasonComponent pricingChangeReasonsComponent;
    private UpGroupSelectionPanel editUpGroupSelectionPanel;

    private static final Logger logger = Logger.getLogger(MeterOnlineBulkPanel.class.getName());

    private static MeterOnlineBulkPanelUiBinder uiBinder = GWT.create(MeterOnlineBulkPanelUiBinder.class);

    interface MeterOnlineBulkPanelUiBinder extends UiBinder<Widget, MeterOnlineBulkPanel> {
    }

    public MeterOnlineBulkPanel(SimpleForm form, ClientFactory clientFactory, MeterOnlineBulkWorkspaceView parentWorkspace) {
        super(form);
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        metersInDeviceStoresSuggestOracle = new MeterInDeviceStoreSuggestOracle(clientFactory);
        suggestBoxMeterNumber = new SuggestBox(metersInDeviceStoresSuggestOracle);
        pricingChangeReasonsComponent = new SpecialActionsReasonComponent(clientFactory, form, SpecialActionsData.CHANGE_PRICING_STRUCTURE,
                MessagesUtil.getInstance().getMessage("usagepoint.current.pricing.change.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.current.pricing.change.select.reason"));
        freeIssueTokenReasonsComponent = new SpecialActionsReasonComponent(clientFactory, form, SpecialActionsData.FREE_TOKEN_ISSUE);
        currentPricingStructureLookup = new PricingStructureLookup(true, form, clientFactory);
        futurePricingStructureLookup = new PricingStructureLookup(false, form, clientFactory);

        initEngineeringTokenUserRefPanel();
        initWidget(uiBinder.createAndBindUi(this));
        init();
        addFieldHandlers();
    }

    private void initEngineeringTokenUserRefPanel() {
        engineeringTokenUserRefPanel = new EngineeringTokenUserRefPanel(clientFactory);
    }

    public void init() {
        StrictDateFormat format = new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        dtbxMeterInstallationDate.setFormat(format);
        currentPricingStructureStartDate.setFormat(format);
        futurePricingStructureStartDate.setFormat(format);
        metersInDeviceStoresSuggestOracle.setLstbxSelectStore(lstbxSelectStore);
        initPanel();
    }


    private void initPanel() {
        txtbxPhone.getElement().setPropertyString("placeholder", FormatUtil.getInstance().getCellphonePlaceholder());
        populatePanelBoxes();

        showCurrTariffBtn.getUpFace().setImage(new Image(MediaResourceUtil.getInstance().getInfoImage()));
        showCurrTariffBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                final int left = showCurrTariffBtn.getAbsoluteLeft() + showCurrTariffBtn.getOffsetWidth();
                final int top = showCurrTariffBtn.getAbsoluteTop() + showCurrTariffBtn.getOffsetHeight();
                String pricingStructureId = getPricingStructureId();
                if (pricingStructureId == null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.no.selection"),
                            MediaResourceUtil.getInstance().getErrorIcon(), left, top, null);
                    return;
                }
                new TariffPanelDialogueBox(clientFactory, Long.valueOf(pricingStructureId), left, top);
            }
        });
    }

    //************************************************************************
    public void populatePanelBoxes() {
        populateStoresListBox();
        populateMeterModelListBox();

        populateSupplyGrpCodeListBox();
        populateAlgCodeListBox();
        populateTokTecCodeListBox();
        txtbxCurrTariffIndx.setText("01");
        txtbxEncryptionKey.setValue(null);
        dtbxMeterInstallationDate.setValue(new Date());
        populatePricingStructureLookups();
        populateLocationGroups();
        txtbxSuiteNumber.setText("1");
        txtbxSurname.setText("1");
        txtbxPhone.setText("");
    }

    private void populateStoresListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                //if more than 1 device store, put an empty one at the top to force selection
                if (result.size() > 1) {
                    LookupListItem emptyLLI = new LookupListItem("-1", "");
                    result.add(0, emptyLLI);
                }
                lstbxSelectStore.setLookupItems(result);

                //if only one device store, select the one
                if (result.size() == 1) {
                    lstbxSelectStore.selectItemByValue(result.get(0).getValue());
                }
            }

        };
        if (clientFactory != null) {
            lstbxSelectStore.clear();
            clientFactory.getLookupRpc().getEndDeviceStoresLookupList(lookupSvcAsyncCallback);
        }
    }

    public void populateMeterModelListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                /*
                 *                      LookupListItem(String value                       , String text)
                 * lookupListItem = new LookupListItem(String.valueOf(list.get(i).getId()), list.get(i).getName());
                 * lookupListItem.setExtraInfo(String.valueOf(list.get(i).getMeterTypeId()));
                 * lookupListItem.setExtraInfo2(String.valueOf(list.get(i).isNeedsBreakerId()));
                 */
                if (result.size() > 1) {
                    LookupListItem emptyLLI = new LookupListItem("-1", "");
                    result.add(0, emptyLLI);
                }
                lstbxMeterModel.setLookupItems(result);
                checkBreakerIdRequired();
                handleEncryptionKey();
                checkUriIsPresent();

                //if only one meter model, select the one
                if (result.size() == 1) {
                    lstbxMeterModel.setSelectedIndex(0);
                } else if (meterOnlineBulkData != null) {
                    lstbxMeterModel.selectItemByValue(meterOnlineBulkData.getMeterModelId().toString());
                    checkBreakerIdRequired();
                    handleEncryptionKey();
                    checkUriIsPresent();
                } else {
                    lstbxMeterModel.setSelectedIndex(-1);
                }
                logger.info("populateMeterModelListBox() onSuccess: lstbxMeterModel.getItemCount()=" + lstbxMeterModel.getItemCount() + " result.size=" + result.size());
                setIsStsMeter();
            }

        };
        if (clientFactory != null) {
            lstbxMeterModel.clear();
            if (meterOnlineBulkData == null || meterOnlineBulkData.getCurrentPricingStructureId() == null) {
                clientFactory.getLookupRpc().getMeterModelLookupList(lookupSvcAsyncCallback);
            } else {
                clientFactory.getLookupRpc().getMeterModelByPricingStructureIdLookupList(meterOnlineBulkData.getCurrentPricingStructureId(), lookupSvcAsyncCallback);
            }
        }
    }
    private void handleEncryptionKey() {
        if (meterOnlineBulkData != null) {
            if (meterModelNeedsEncryptionKey()) {
                encryptionKeyRow.setVisible(true);
                txtbxEncryptionKey.setText(meterOnlineBulkData.getEncKey());
            } else {
                encryptionKeyRow.setVisible(false);
                txtbxEncryptionKey.setText(null);
            }
        } else {
            encryptionKeyRow.setVisible(meterModelNeedsEncryptionKey());
            txtbxEncryptionKey.setText(null);
        }

    }
    private boolean meterModelNeedsEncryptionKey() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex < 0) {
            return false;
        }
        boolean hasEncryptionKey = "true".equals(lstbxMeterModel.getItem(selectedIndex).getExtraInfo3());
        return hasEncryptionKey;
    }

    @SuppressWarnings("deprecation")
    private void setIsStsMeter() {
        if (lstbxMeterModel.getSelectedIndex() == -1
                || lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()).isEmpty()) {
            stsContainer.setVisible(true);
        } else {
            LookupListItem item = lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex());
            isStsMeter = (item.getExtraInfo().equals(String.valueOf(MeterTypeE.STS.getId())));
            stsContainer.setVisible(isStsMeter && clientFactory.isEnableSTS());
        }
    }

    @SuppressWarnings("deprecation")
    private void checkBreakerIdRequired() {
        int index = lstbxMeterModel.getSelectedIndex();
        if (index != -1) {
            LookupListItem item = lstbxMeterModel.getItem(index);
            if (item != null && "true".equals(item.getExtraInfo2())) {
                breakerIdPanel.setVisible(true);
                txtbxBreakerId.setText("");
                if (meterOnlineBulkData != null && meterOnlineBulkData.getBreakerId() != null) {
                    txtbxBreakerId.setText(meterOnlineBulkData.getBreakerId());
                }
            } else {
                breakerIdPanel.setVisible(false);
                txtbxBreakerId.setText("");
            }
        }
    }

    private void checkUriIsPresent() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex != -1) {
            LookupListItem item = lstbxMeterModel.getItem(selectedIndex);
            if (item != null && item.getExtraInfoMap() != null && item.getExtraInfoMap().get("uriPresent").equals("true")) {
                uriPanel.setVisible(true);
                if (meterOnlineBulkData != null) {
                    setMeterUriFields();
                } else {
                    resetMeterUriFields();
                }
            }
        }
    }

    public void populatePricingStructureLookups() {
        if (meterOnlineBulkData != null && meterOnlineBulkData.getCurrentPricingStructureId() != null
                && meterOnlineBulkData.getUpPricingStructureData() != null) {
            currentPricingStructureLookup.setSavedPricingStructureId(meterOnlineBulkData.getCurrentPricingStructureId());
            currentPricingStructureStartDate.setValue(meterOnlineBulkData.getUpPricingStructureData()
                    .getUpPricingStructure().getStartDate());
        }
        if (meterOnlineBulkData != null && meterOnlineBulkData.getUpPricingStructureData() != null &&
                meterOnlineBulkData.getUpPricingStructureData().getFutureUpPricingStructureData() != null) {
            futurePSRowPanel.setVisible(true);
            UpPricingStructure upPS = meterOnlineBulkData.getUpPricingStructureData()
                    .getFutureUpPricingStructureData().getUpPricingStructure();
            futurePricingStructureLookup.setSavedPricingStructureId(upPS.getPricingStructureId());
            futurePricingStructureStartDate.setValue(upPS.getStartDate());
        }
        if (clientFactory != null) {
            Long selectedMeterModelId = getSelectedMeterModelId();
            if (selectedMeterModelId != null) {
                currentPricingStructureLookup.updateLookupList(selectedMeterModelId);
            } else {
                currentPricingStructureLookup.updateLookupList();
            }
        }
    }

    private void populateSupplyGrpCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxSupplyGrpCde.setLookupItems(result);

                //if only one result, select the one
                if (result.size() == 1) {
                    lstbxSupplyGrpCde.selectItemByValue(result.get(0).getValue());
                }
                if (meterOnlineBulkData != null && meterOnlineBulkData.getStsCurrSupplyGroupCodeId() != null) {
                    lstbxSupplyGrpCde.selectItemByValue(meterOnlineBulkData.getStsCurrSupplyGroupCodeId().toString());
                }
            }
        };
        if (clientFactory != null) {
            lstbxSupplyGrpCde.clear();
            clientFactory.getLookupRpc().getSgKrnLookupList(lookupSvcAsyncCallback);
        }
    }

    private void populateAlgCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                //this result already contains empty first option
                lstbxAlgCode.setLookupItems(result);

                //if only one result, select the one
                if (result.size() == 2) {
                    lstbxAlgCode.selectItemByValue(result.get(1).getValue());
                }

                if (meterOnlineBulkData != null && meterOnlineBulkData.getStsAlgorithmCodeId() != null) {
                    lstbxAlgCode.selectItemByValue(meterOnlineBulkData.getStsAlgorithmCodeId().toString());
                }
            }

        };
        if (clientFactory != null) {
            lstbxAlgCode.clear();
            clientFactory.getLookupRpc().getAlgCodeLookupList(lookupSvcAsyncCallback);
        }
    }

    private void populateTokTecCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                //this result already contains empty first option
                lstbxTokTecCode.setLookupItems(result);

                //if only one result, select the one
                if (result.size() == 2) {
                    lstbxTokTecCode.selectItemByValue(result.get(1).getValue());
                }

                if (meterOnlineBulkData != null && meterOnlineBulkData.getStsTokenTechCodeId() != null) {
                    lstbxTokTecCode.selectItemByValue(meterOnlineBulkData.getStsTokenTechCodeId().toString());
                }
            }

        };
        if (clientFactory != null) {
            lstbxTokTecCode.clear();
            clientFactory.getLookupRpc().getTtCodeLookupList(lookupSvcAsyncCallback);
        }
    }

    public void populateLocationGroups() {   //final boolean select) {
        clientFactory.getGroupRpc().getLocationGroups(new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                while (locationGroupPanel.getWidgetCount() > 0) {
                    locationGroupPanel.remove(0);
                }

                SelectionDataItem item;
                boolean addit = true;

                Collections.sort(result);
                SelectionDataWidget sdw;
                for(int i=0;i<result.size();i++) {
                    item = result.get(i);
                    for (int j=0; j<locationGroupPanel.getWidgetCount(); j++) {
                        addit = true;
                        if (locationGroupPanel.getWidget(j) instanceof SelectionDataWidget) {
                            sdw = (SelectionDataWidget)locationGroupPanel.getWidget(j);
                            if (sdw.getGroupTypeId().equals(item.getActualId())) {
                                addit=false;
                                break;
                            }
                        }
                    }
                    if (addit) {
                        sdw = new SelectionDataWidget(clientFactory, clientFactory.getGroupRpc(), item, false, false,null,true, form);
                        locationGroupPanel.add(sdw);
                    }
                }
            }
        });

    }
    //*****************************************************************************
    public void addFieldHandlers() {
        lstbxSelectStore.addChangeHandler(new FormDataChangeHandler(form));
        suggestBoxMeterNumber.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
        lstbxMeterModel.addChangeHandler(new FormDataChangeHandler(form));
        txtbxBreakerId.addChangeHandler(new FormDataChangeHandler(form));
        txtbxMeterUriAddress.addChangeHandler(new FormDataChangeHandler(form));
        txtbxMeterUriPort.addChangeHandler(new FormDataChangeHandler(form));
        txtbxMeterUriProtocol.addChangeHandler(new FormDataChangeHandler(form));
        txtbxMeterUriParams.addChangeHandler(new FormDataChangeHandler(form));
        lstbxSupplyGrpCde.addChangeHandler(new FormDataChangeHandler(form));
        lstbxAlgCode.addChangeHandler(new FormDataChangeHandler(form));
        lstbxTokTecCode.addChangeHandler(new FormDataChangeHandler(form));
        txtbxCurrTariffIndx.addChangeHandler(new FormDataChangeHandler(form));
        dtbxMeterInstallationDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        txtbxSuiteNumber.addChangeHandler(new FormDataChangeHandler(form));
        txtbxSurname.addChangeHandler(new FormDataChangeHandler(form));
        txtbxPhone.addChangeHandler(new FormDataChangeHandler(form));
        chkBxGenFreeIssueToken.addClickHandler(new FormDataClickHandler(form));
        txtbxFreeIssueUnits.addChangeHandler(new FormDataChangeHandler(form));
        chkBxSmsToken.addClickHandler(new FormDataClickHandler(form));;
        suggestBoxMeterNumber.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                suggestBoxMeterElement.setErrorMsg(null);
                suggestBoxMeterNumber.showSuggestionList();
                setDisplayUnusualMeterMsg(true);
                meterOnlineBulkData = new MeterOnlineBulkData();
            }
        });

        chkBxGenFreeIssueToken.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                if (chkBxGenFreeIssueToken.getValue()) {
                    unitsElement.setVisible(true);
                    smsTokenElement.setVisible(true);
                    freeIssueTokenReasonsComponent.setVisible(true);
                    if (freeIssueTokenReasonsComponent.getReasonsInputType().equals(SpecialActionsView.ReasonsInputType.SLCT.toString())) {
                        freeIssueTokenReasonsComponent.setDefaultListBoxReasonsSelection();
                    }

                    engineeringTokenUserRefPanel.showPanel();

                } else {
                    unitsElement.setVisible(false);
                    engineeringTokenUserRefPanel.hidePanel();
                    smsTokenElement.setVisible(false);
                    freeIssueTokenReasonsComponent.setVisible(false);
                    engineeringTokenUserRefPanel.hidePanel();
                }
            }
        });

        //----------------------------------

        clientFactory.getEventBus().addHandler(SupplyGroupAddedEvent.TYPE, new SupplyGroupAddedEventHandler() {
            @Override
            public void processSupplyGroupAddedEvent(SupplyGroupAddedEvent event) {
                populateSupplyGrpCodeListBox();
            }
        });


        clientFactory.getEventBus().addHandler(EndDeviceStoreUpdatedEvent.TYPE, new EndDeviceStoreUpdatedEventHandler() {
            @Override
            public void processEndDeviceStoreUpdatedEvent(EndDeviceStoreUpdatedEvent event) {
                populateStoresListBox();
            }
        });


        clientFactory.getEventBus().addHandler(MeterModelChangedEvent.TYPE, new MeterModelChangedEventHandler() {
            @Override
            public void processEvent(MeterModelChangedEvent event) {
                populatePricingStructureLookups();
            }
        });

        clientFactory.getEventBus().addHandler(PricingStructureUpdateEvent.TYPE, new PricingStructureUpdateEventHandler() {
            @Override
            public void processPricingStructureUpdatedEvent(PricingStructureUpdateEvent event) {
                populatePricingStructureLookups();
            }
        });

        txtbxPhone.addKeyDownHandler(new EnterKeyHandler() {
           @Override
           public void enterKeyDown(KeyDownEvent event) {
                Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
                    @Override
                    public void execute() {
                        parentWorkspace.setFocusOnSave();
                    }
                });
           }
       });

        currentPricingStructureLookup.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                handlePricingStructureControlFocus(currentPricingStructureLookup);
            }
        });
    }

    private void handlePricingStructureControlFocus(PricingStructureLookup pricingStructureLookupWidget) {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT) &&
                meterOnlineBulkData != null && meterOnlineBulkData.getCurrentPricingStructureId() != null
                && getPricingStructureId() == null) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.pricing.structure.accessdenied"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    pricingStructureLookupWidget.getAbsoluteLeft() + pricingStructureLookupWidget.getOffsetWidth(),
                    pricingStructureLookupWidget.getAbsoluteTop() + pricingStructureLookupWidget.getOffsetHeight(),
                    null);
        }
    }

    @UiHandler("suggestBoxMeterNumber")
    void handleSuggestBox(SelectionEvent<Suggestion> se) {
        if (se.getSelectedItem() instanceof MeterSuggestion) {
            //meterNum = ((MeterSuggestion) se.getSelectedItem()).getMeter().getNumber().trim();
            populatePanel();
        }
    }

    @UiHandler("lstbxMeterModel")
    void handleMeterModelChange(ChangeEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                doPanelMeterModelChange();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }


    @SuppressWarnings("deprecation")
    private void doPanelMeterModelChange() {
        meterModelElement.clearErrorMsg();
        setIsStsMeter();
        if (lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()).equals("-1")) {
            meterModelElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.metermodelid.null"));
        } else {
            if (lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getExtraInfo2().equals("true")) {
                breakerIdPanel.setVisible(true);
                txtbxBreakerId.setText("");
            } else {
                if (meterOnlineBulkData != null && meterOnlineBulkData.getBreakerId() != null) {
                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage(
                                    MessagesUtil.getInstance().getMessage("meter.breakerid.remove.question")),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        meterOnlineBulkData.setBreakerId(null);
                                        txtbxBreakerId.setText("");
                                        breakerIdPanel.setVisible(false);
                                    } else {
                                        lstbxMeterModel.selectItemByValue(String.valueOf(meterOnlineBulkData.getMeterModelId()));
                                        // Breaker Id - if meterModel.isRequired == true
                                        checkBreakerIdRequired();
                                        checkUriIsPresent();
                                        setIsStsMeter();
                                        return;
                                    }
                                }
                            }, lstbxMeterModel.getAbsoluteLeft() + lstbxMeterModel.getOffsetWidth(),
                            lstbxMeterModel.getAbsoluteTop());
                } else {
                    txtbxBreakerId.setText("");
                    breakerIdPanel.setVisible(false);
                }
            }

            if (lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getExtraInfoMap().get("uriPresent").equals("true")) {
                uriPanel.setVisible(true);
                if (meterOnlineBulkData != null) {
                    setMeterUriFields();
                } else {
                    resetMeterUriFields();
                }
            } else {
                if (meterOnlineBulkData != null && (meterOnlineBulkData.getMeterUriAddress() != null || meterOnlineBulkData.getMeterUriPort() != null || meterOnlineBulkData.getMeterUriProtocol() != null ||
                        meterOnlineBulkData.getMeterUriParams() != null || !ValidateUtil.isNullOrBlank(txtbxMeterUriAddress.getText()) ||
                        !ValidateUtil.isNullOrBlank(txtbxMeterUriPort.getText()) || !ValidateUtil.isNullOrBlank(txtbxMeterUriProtocol.getText()) ||
                        !ValidateUtil.isNullOrBlank(txtbxMeterUriParams.getText()))) {

                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage(
                                    MessagesUtil.getInstance().getMessage("meter.uri.remove.question")),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        meterOnlineBulkData.setMeterUriAddress(null);
                                        meterOnlineBulkData.setMeterUriPort(null);
                                        meterOnlineBulkData.setMeterUriProtocol(null);
                                        meterOnlineBulkData.setMeterUriParams(null);
                                        resetMeterUriFields();
                                        clearMeterUriFieldErrors();
                                        uriPanel.setVisible(false);
                                    } else {
                                        lstbxMeterModel.selectItemByValue(String.valueOf(meterOnlineBulkData.getMeterModelId()));
                                        checkBreakerIdRequired();
                                        checkUriIsPresent();
                                        setIsStsMeter();
                                    }
                                }
                            }, lstbxMeterModel.getAbsoluteLeft() + lstbxMeterModel.getOffsetWidth(),
                            lstbxMeterModel.getAbsoluteTop());
                } else {
                    uriPanel.setVisible(false);
                    resetMeterUriFields();
                }
            }

            clientFactory.getMeterModelRpc().getMeterModelById(Long.valueOf(lstbxMeterModel.getSelectedValues().get(0)),
                    new ClientCallback<MeterModelData>() {
                        @Override
                        public void onSuccess(MeterModelData meterModelData) {
                            String symbol = MeterMngClientUtils
                                    .getServiceResourceSymbol(meterModelData.getServiceResourceId());
                            Messages messagesInstance = MessagesUtil.getInstance();
                            unitsElement.setHelpMsg(
                                    messagesInstance.getMessage("meter.units.help", new String[] { symbol }));
                            unitsElement.setLabelText(
                                    messagesInstance.getMessage("meter.units", new String[] { symbol }) + ":");
                            lblUnitSymbol.setText(symbol);
                        }
                    });
            handleEncryptionKey();
            clientFactory.getEventBus().fireEvent(new MeterModelChangedEvent());

        }
    }

    private void setMeterUriFields() {
        if (meterOnlineBulkData.getMeterUriAddress() != null) {
            txtbxMeterUriAddress.setText(meterOnlineBulkData.getMeterUriAddress());
        }
        if (meterOnlineBulkData.getMeterUriPort() != null) {
            txtbxMeterUriPort.setText(meterOnlineBulkData.getMeterUriPort().toString());
        }
        if (meterOnlineBulkData.getMeterUriProtocol() != null) {
            txtbxMeterUriProtocol.setText(meterOnlineBulkData.getMeterUriProtocol());
        }
        if(meterOnlineBulkData.getMeterUriParams() != null) {
            txtbxMeterUriParams.setText(meterOnlineBulkData.getMeterUriParams());
        }
    }

    private void resetMeterUriFields() {
        txtbxMeterUriAddress.setText("");
        txtbxMeterUriPort.setText("");
        txtbxMeterUriProtocol.setText("");
        txtbxMeterUriParams.setText("");
    }

    @UiHandler("currentPricingStructureLookup")
    void handlePricingStructureChange(ValueChangeEvent<String> event) {
        currentPSElement.clearErrorMsg();

        boolean removeReasons = true;
        if (meterOnlineBulkData != null && meterOnlineBulkData.getCurrentPricingStructureId() != null) {
            if (checkPricingStructureDirty()) {
                if (pricingChangeReasonsComponent.getSpecialActions() != null && pricingChangeReasonsComponent.getParent() == null) {
                    pricingChangeReasonPanel.add(pricingChangeReasonsComponent);
                    pricingChangeReasonPanel.setVisible(true);
                }
                removeReasons = false;
            }
        }
        if (removeReasons) {
            pricingChangeReasonPanel.clear();
            pricingChangeReasonPanel.setVisible(false);
        }
        if (lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()).equals("-1")) {
            currentPSElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.ps.meter.model.empty"));
            return;
        }

        //check correct meterModel combo pricing structure
        if (lstbxMeterModel.getSelectedIndex() > -1 && !lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()).isEmpty()) {
            String value = getPricingStructureId();
            if (value == null) {
                currentPSElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.pricingstructure.required"));
            } else {
                final Long pricingStructureId = Long.parseLong(getPricingStructureId());
                if (pricingStructureId.compareTo(-1L) != 0) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            clientFactory.getMeterModelRpc().getMeterModelIds(pricingStructureId, new ClientCallback<ArrayList<Long>>() {
                        @Override
                        public void onSuccess(ArrayList<Long> meterModelResult) {
                            Long meterModelId = Long.parseLong(lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()));
                            if (!meterModelResult.contains(meterModelId)) {
                                populatePricingStructureLookups();
                                currentPSElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.model.new.pricingstructure.required"));
                            }
                        }
                    });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        }
    }

    protected boolean checkPricingStructureDirty() {
        boolean dirty = false;
        String selectedvalue = getPricingStructureId();
        Long existingvalue = meterOnlineBulkData.getCurrentPricingStructureId();
        if ((existingvalue != null && selectedvalue == null) || (existingvalue == null && selectedvalue != null)
                || (selectedvalue != null && !Long.valueOf(selectedvalue).equals(existingvalue))) {
            dirty = true;
        }
        return dirty;
    }

    protected boolean checkMeterModelDirty() {
        boolean dirty = false;
        String selectedvalue = null;
        if (lstbxMeterModel.getSelectedIndex() > 0) {
            selectedvalue = lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex());
        }

        Long existingvalue = meterOnlineBulkData.getMeterModelId();
        if ((existingvalue != null && selectedvalue == null) || (existingvalue == null && selectedvalue != null)
                || (selectedvalue != null && !Long.valueOf(selectedvalue).equals(existingvalue))) {
            dirty = true;
        }
        return dirty;
    }

    //************************************************************************
    public void setDisplayUnusualMeterMsg(boolean bool) {
        displayUnusualMeterMsg = bool;
    }

    /*
     * Need to action Suggestbox selection whether  enter on suggest dropdrown from keyboard or whether click with mouse
     * Keydownhandler(workspaceview) is actioned from enter key - where it populatesPanel and turns focus to Save button(workspaceview).  (taborder is also set to suggestbox 1 (panel), phone num1, save button 2(workspaceview))
     *              Keydownhandler(workspaceview) also actions the Suggestbox(panel) selection event
     *
     * SuggestBox(panel) eventhandler is executed when click with mouse - so will do populatePanel() - but does not execute the EnterKey - seems to autotab to Save button, so IF do focushere also - behaviour is it auto saves --> NOT DESIRABLE!!
     *
     * So hence keep track of when the messages display in populatePanel - so don't display them twice.
     */
    public void populatePanel() {
        if (!displayUnusualMeterMsg) {
            //already done from Enterkeyhandler
            setDisplayUnusualMeterMsg(true);
            return;
        }

        setDisplayUnusualMeterMsg(false);

        //get meter info from Device Store
        final String meterNum = suggestBoxMeterNumber.getValue().trim();
        if (meterNum.trim().isEmpty()) {
            logger.info("MeterOnLineBulkPaneL: no meter number entered");
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.field.required"),
                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                        suggestBoxMeterNumber.getAbsoluteLeft() + suggestBoxMeterNumber.getOffsetWidth(),
                                        suggestBoxMeterNumber.getAbsoluteTop() + suggestBoxMeterNumber.getOffsetHeight(),
                                        null);
            return;
        }

        final Long deviceStoreId = Long.parseLong(lstbxSelectStore.getValue(lstbxSelectStore.getSelectedIndex()));

        clientFactory.getSearchRpc().getMeterOnlineBulkForMeterNum(meterNum, deviceStoreId, new ClientCallback<MeterOnlineBulkData>() {
            @Override
            public void onSuccess(MeterOnlineBulkData result) {
                final MeterOnlineBulkData mobResult = result;
                //if deviceStoreId is already null, the meter s already linked to a UP
                if (result.getDeviceStoreId() == null ) {
                    Dialogs.confirm (
                            new String[]{
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("online.bulk.panel.error.meter.already.linked"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.continue.open.up.page")},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        History.newItem(clientFactory.getPlaceHistoryMapper().getToken(new MeterPlace(mobResult.getMeterNum(), null)));
                                    }
                                    clearSomePanelFields(true);
                                    return;
                                }
                            });
                } else {
                    populatePanel3(deviceStoreId, mobResult);
                }
            }
        });
    }


    private void populatePanel3(final Long deviceStoreId, final MeterOnlineBulkData result) {

        //check if meter in a different device store to the one selected
        if (result.getDeviceStoreId().compareTo(deviceStoreId) != 0 ) {
            Dialogs.confirm (
                    new String[]{
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("online.bulk.panel.error.meter.linked.to.diff.store",
                                    new String[] {result.getDeviceStoreName()}),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.continue.save.anyway")},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                mapMeterInfoToForm(result);
                            } else {
                                clearSomePanelFields(false);
                                return;
                            }
                        }
                    });
        } else {
            mapMeterInfoToForm(result);
        }
    }

    public void mapMeterInfoToForm(MeterOnlineBulkData mobData) {
        this.meterOnlineBulkData = mobData;
        if (mobData.getDeviceStoreId() != null
                && mobData.getDeviceStoreId().compareTo(Long.parseLong(lstbxSelectStore.getValue(lstbxSelectStore.getSelectedIndex()))) != 0) {
            lstbxSelectStore.selectItemByValue(mobData.getDeviceStoreId().toString());
        }
        setIsStsMeter();

        if (lstbxMeterModel.getSelectedIndex() == -1 || mobData.getMeterModelId().compareTo(Long.parseLong(lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()))) != 0) {
            lstbxMeterModel.selectItemByValue(mobData.getMeterModelId().toString());
            checkBreakerIdRequired();
            checkUriIsPresent();
            doPanelMeterModelChange();
        }

        if (isStsMeter) {
            lstbxSupplyGrpCde.selectItemByValue(mobData.getStsCurrSupplyGroupCodeId().toString());
            lstbxAlgCode.selectItemByValue(mobData.getStsAlgorithmCodeId().toString());
            lstbxTokTecCode.selectItemByValue(mobData.getStsTokenTechCodeId().toString());
            txtbxCurrTariffIndx.setValue(mobData.getStsCurrTariffIndex());


        }
        handleEncryptionKey();
        dtbxMeterInstallationDate.setValue(new Date());
        //leave everything else as preselected; validation on save will trap errors...
    }

    //BOTH EDIT & ADD functions
    public void mapFormToData() {
        meterOnlineBulkData.setMeterNum(suggestBoxMeterNumber.getValue().trim());
        meterOnlineBulkData.setMeterModelId(Long.parseLong(lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex())));
        meterOnlineBulkData.setMeterModelName(lstbxMeterModel.getItemText(lstbxMeterModel.getSelectedIndex()));
        meterOnlineBulkData.setHasChannels(false);
        Map<String, Object> extraInfoMap = lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getExtraInfoMap();
        if (extraInfoMap != null && !extraInfoMap.isEmpty()) {
            String hasChannelsStr = (String)extraInfoMap.get("hasChannels");
            meterOnlineBulkData.setHasChannels("true".equals(hasChannelsStr));
            String mdcIdStr= (String)extraInfoMap.get("mdcId");
            if (mdcIdStr != null && !mdcIdStr.isEmpty()) {
                meterOnlineBulkData.setMdcId(Long.valueOf(mdcIdStr));
                meterOnlineBulkData.setMdcName((String)extraInfoMap.get("mdcName"));
            }
        }

        if (breakerIdPanel.isVisible() && !txtbxBreakerId.getText().isEmpty()) {
            meterOnlineBulkData.setBreakerId(txtbxBreakerId.getText());
        } else {
            meterOnlineBulkData.setBreakerId(null);
        }

        //Add Encryption key only if meter model Requires
        if (encryptionKeyRow.isVisible() && !txtbxEncryptionKey.getText().isEmpty()) {
            meterOnlineBulkData.setEncKey(txtbxEncryptionKey.getText());
        } else {
            meterOnlineBulkData.setEncKey(null);
        }

        if(uriPanel.isVisible()) {
            if(!txtbxMeterUriAddress.getText().isEmpty()){
                meterOnlineBulkData.setMeterUriAddress(txtbxMeterUriAddress.getText());
            } else {
                meterOnlineBulkData.setMeterUriAddress(null);
            }
            if(txtbxMeterUriPort.getValue() != null){
                meterOnlineBulkData.setMeterUriPort(txtbxMeterUriPort.getValue());
            } else {
                meterOnlineBulkData.setMeterUriPort(null);
            }
            if(!txtbxMeterUriProtocol.getText().isEmpty()){
                meterOnlineBulkData.setMeterUriProtocol(txtbxMeterUriProtocol.getText());
            } else {
                meterOnlineBulkData.setMeterUriProtocol(null);
            }
            if(!txtbxMeterUriParams.getText().isEmpty()){
                meterOnlineBulkData.setMeterUriParams(txtbxMeterUriParams.getText());
            } else {
                meterOnlineBulkData.setMeterUriParams(null);
            }
        } else {
            meterOnlineBulkData.setMeterUriAddress(null);
            meterOnlineBulkData.setMeterUriPort(null);
            meterOnlineBulkData.setMeterUriProtocol(null);
            meterOnlineBulkData.setMeterUriParams(null);
        }

        if (isStsMeter) {
            meterOnlineBulkData.setStsCurrSupplyGroupCodeId(Long.parseLong(lstbxSupplyGrpCde.getValue(lstbxSupplyGrpCde.getSelectedIndex())));
            meterOnlineBulkData.setStsAlgorithmCodeId(Long.parseLong(lstbxAlgCode.getValue(lstbxAlgCode.getSelectedIndex())));
            meterOnlineBulkData.setStsTokenTechCodeId(Long.parseLong(lstbxTokTecCode.getValue(lstbxTokTecCode.getSelectedIndex())));
            meterOnlineBulkData.setStsCurrTariffIndex(txtbxCurrTariffIndx.getText().trim());
        }
        meterOnlineBulkData.setInstallationDate(dtbxMeterInstallationDate.getValue());
        meterOnlineBulkData.setCurrentPricingStructureId(Long.parseLong(getPricingStructureId()));
        meterOnlineBulkData.setCurrentPricingStructureName(getPricingStructureName());

        SelectionDataWidget sdw = null;
        for (int j=0; j<locationGroupPanel.getWidgetCount(); j++) {            //there should only be one anyway!
            if (locationGroupPanel.getWidget(j) instanceof SelectionDataWidget ) {
                sdw = (SelectionDataWidget)locationGroupPanel.getWidget(j);
                break;
            }
        }
        Long selectedGroupId = sdw == null ? null : sdw.getSelectedGroup();
        meterOnlineBulkData.setLocationGroupTypeId(selectedGroupId);

        if (MeterMngSharedUtils.isNumber(txtbxSuiteNumber.getText())) {
            meterOnlineBulkData.setSuiteNum(MessagesUtil.getInstance().getMessage("online.bulk.panel.suite.no.text")+" "+txtbxSuiteNumber.getText());
        } else {
            meterOnlineBulkData.setSuiteNum(txtbxSuiteNumber.getText());
        }
        if (MeterMngSharedUtils.isNumber(txtbxSurname.getText())) {
            meterOnlineBulkData.setSurname(MessagesUtil.getInstance().getMessage("online.bulk.panel.tenant.text")+" "+txtbxSurname.getText());
        } else {
            meterOnlineBulkData.setSurname(txtbxSurname.getText());
        }
        meterOnlineBulkData.setPhone1(txtbxPhone.getText());
    }

    //ADD function
    public void mapFormFreeIssueToData() {
        if (chkBxGenFreeIssueToken.getValue()) {
            meterOnlineBulkData.setFreeIssueUnits(new BigDecimal(txtbxFreeIssueUnits.getText()));
            meterOnlineBulkData.setFreeIssueTokenReasonsLog(freeIssueTokenReasonsComponent.getLogEntry());
            meterOnlineBulkData.setFreeIssueTokenUserReference(engineeringTokenUserRefPanel.getUserReferenceValue());
            meterOnlineBulkData.setSendSms(chkBxSmsToken.getValue());
        }
    }

    //EDIT function
    public void mapDataToForm(MeterOnlineBulkData mobData) {
        resetPanelFields();
        this.meterOnlineBulkData = mobData;
        populateMeterModelListBox();
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT)) {
            UpPricingStructureData upPricingStructureData = meterOnlineBulkData.getUpPricingStructureData();
            if (meterOnlineBulkData.getCurrentPricingStructureId() != null && upPricingStructureData != null) {

                currentPricingStructureLookup.updateForDisplayOnly(new LookupListItem(upPricingStructureData.getPricingStructure().getName(),
                        upPricingStructureData.getPricingStructure().getId().toString()));
                currentPricingStructureStartDate.setValue(upPricingStructureData.getUpPricingStructure().getStartDate());

                if (upPricingStructureData.getFutureUpPricingStructureData() != null) {
                    UpPricingStructureData futureUPPSData = upPricingStructureData.getFutureUpPricingStructureData();
                    futurePricingStructureLookup.updateForDisplayOnly(new LookupListItem(futureUPPSData.getPricingStructure().getName(),
                            futureUPPSData.getPricingStructure().getId().toString()));
                    futurePricingStructureStartDate.setValue(upPricingStructureData.getUpPricingStructure().getStartDate());
                }
            }
        } else {
            populatePricingStructureLookups();
        }
        currentPricingStructureLookup.setEnabled(false);
        lstbxSelectStore.setEnabled(false);
        suggestBoxMeterNumber.setText(mobData.getMeterNum());
        suggestBoxMeterNumber.setEnabled(false);

        if (mobData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
             lstbxSupplyGrpCde.selectItemByValue(mobData.getStsCurrSupplyGroupCodeId().toString());
             lstbxAlgCode.selectItemByValue(mobData.getStsAlgorithmCodeId().toString());
             lstbxTokTecCode.selectItemByValue(mobData.getStsTokenTechCodeId().toString());
             txtbxCurrTariffIndx.setText(mobData.getStsCurrTariffIndex());

            lstbxSupplyGrpCde.setEnabled(false);
             txtbxCurrTariffIndx.setEnabled(false);
        }

        dtbxMeterInstallationDate.setValue(mobData.getInstallationDate());
        dtbxMeterInstallationDate.setEnabled(false);

        setLocationGroup(mobData.getLocationGroupTypeId(), mobData.getLocationDepthList());

        if (mobData.getSuiteNum() != null) {
            if (mobData.getSuiteNum().contains(MessagesUtil.getInstance().getMessage("online.bulk.panel.suite.no.text") + " ")) {
                txtbxSuiteNumber.setText(mobData.getSuiteNum().replace(MessagesUtil.getInstance().getMessage("online.bulk.panel.suite.no.text") + " ", ""));
            } else {
                txtbxSuiteNumber.setText(mobData.getSuiteNum());
            }
        }
        if(mobData.getSurname().contains(MessagesUtil.getInstance().getMessage("online.bulk.panel.tenant.text") + " ")){
        	txtbxSurname.setText(mobData.getSurname().replace(MessagesUtil.getInstance().getMessage("online.bulk.panel.tenant.text") + " ", ""));
        } else {
        	txtbxSurname.setText(mobData.getSurname());
        }

        txtbxPhone.setText(mobData.getPhone1());

        editUpGroupSelectionPanel = new UpGroupSelectionPanel(clientFactory, this, form, false, true, mobData.getSelectedGroups(), false);
        mobPanel.add(editUpGroupSelectionPanel);
    }

    private void setLocationGroup(Long locationGroupTypeId, ArrayList<Long> locationGroupDepthList) {
         if (locationGroupTypeId != null) {
            SelectionDataWidget sdw = null;
            for (int j=0; j<locationGroupPanel.getWidgetCount(); j++) {
                if (locationGroupPanel.getWidget(j) instanceof SelectionDataWidget ) {

                    sdw = (SelectionDataWidget)locationGroupPanel.getWidget(j);
                    if (locationGroupDepthList != null) {
                        sdw.setSelectedGroup(locationGroupDepthList, locationGroupTypeId);
                    } else {
                        sdw.setSelectedGroup(null, locationGroupTypeId);
                    }
                    break;

                }
            }
        }
    }

    @SuppressWarnings("deprecation")
    public boolean isValidData() {
        boolean validData = true;
        String meterNum = suggestBoxMeterNumber.getValue().trim();

        //meterModel is required
        if (lstbxMeterModel.getSelectedIndex() < 0) {
            meterModelElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.metermodelid.null"));
            validData = false;
        }

        // pricingStructure is required
        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT) &&
                getPricingStructureId() == null) {
            currentPSElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.pricingstructure.required"));
            validData = false;
        }

        if (pricingChangeReasonsComponent.isAttached()
                && pricingChangeReasonPanel.isVisible()) {
            boolean isvalid = pricingChangeReasonsComponent.validate();
            if (!isvalid) {
                validData = isvalid;
            }
        }

        //meterModelId - pricing structure combo cannot be invalid at this point. Can't select one without changing the other on the panel...
        //so it is only necessary to check that both are present not that they are a valid combo...

        setIsStsMeter();
        // Validate encryption key for both STS and AMI meters
        boolean needsEncryptionKey = meterModelNeedsEncryptionKey();
        if (needsEncryptionKey && txtbxEncryptionKey.getText().trim().isEmpty()) {
            validData = false;
            encryptionKeyElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.encryptionkey.error"));
        }

        if (isStsMeter) {
            if (lstbxSupplyGrpCde.getSelectedIndex() < 1) {
                supplyGrpCdeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.supply.grpcode.empty"));
                validData = false;
            }
            if (lstbxAlgCode.getSelectedIndex() < 1) {
                algCodeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.algorithm.code.empty"));
                validData = false;
            }
            if (lstbxTokTecCode.getSelectedIndex() < 1) {
                tokTecCodeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.token.tech.code.empty"));
                validData = false;
            }

            String currTariffIndx = txtbxCurrTariffIndx.getText().trim();
            if (currTariffIndx == null || currTariffIndx.isEmpty()) {
                currTariffIndxElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.tariff.indx.empty"));
                validData = false;
            } else if (currTariffIndx.length() > 2){
                currTariffIndxElement.setErrorMsg(MessagesUtil.getInstance().getMessage("online.bulk.panel.error.tariff.indx.empty"));
            }

            if (isStsMeter) {
                boolean isProprietaryMeterAlgCode = false;
                try {
                    String algorithmCode = lstbxAlgCode.getItem(lstbxAlgCode.getSelectedIndex()).getExtraInfo();
                    if(algorithmCode.equals("89")) {
                        isProprietaryMeterAlgCode = true;
                    }
                } catch (Exception e) {
                    // just catching as I don't know what could possible be thrown here and tight timeline
                    // they need to upload meters NOW.
                    logger.log(Level.WARNING, "Could not get alg code to check for proprietary meter code", e);
                }

                if(! isProprietaryMeterAlgCode) {
                    if (!MeterMngCommonUtil.stsLengthCheck(meterNum)) {
                        validData = false;
                        suggestBoxMeterElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.sts.length"));
                    } else if (!MeterMngCommonUtil.luhnCheck(meterNum)) {
                        validData = false;
                        suggestBoxMeterElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.luhncheck.failed"));
                    }
                }
            }
        }

        Date installDt = dtbxMeterInstallationDate.getValue();
        String installationDateText = dtbxMeterInstallationDate.getTextBox().getValue();
        if (installDt == null && installationDateText.isEmpty()) {
            installationDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.installation.date.required"));
            validData = false;
        } else if (!installationDateText.isEmpty() && !MeterMngClientUtils.isValidDate(installationDateText)) {
            installationDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.installdate.invalid", new String[]{FormatUtil.getInstance().getDateTimeFormat()}));
            validData = false;
        } else if (installDt.after(new Date())) {
            installationDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.installdate.future"));
            validData = false;
        }

        if (txtbxSurname.getText().length() > 255) {
            surnameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.surname.max"));
            validData = false;
        }

        if (txtbxSurname.getText() == null || txtbxSurname.getText().trim().isEmpty()) {
            surnameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
            validData = false;
        }

        if (txtbxPhone.getText() != null && !txtbxPhone.getText().isEmpty() && txtbxPhone.getText().length() > 40) {
            phoneNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.phone1.max"));
            validData = false;
        }

        //check if location Group was required & is it selected?? Does sdw take care of that? see above RC
        SelectionDataWidget sdw = null;
        boolean isRequiredLocGrp = false;
        for (int j=0; j<locationGroupPanel.getWidgetCount(); j++) {
            if (locationGroupPanel.getWidget(j) instanceof SelectionDataWidget ) {
                sdw = (SelectionDataWidget)locationGroupPanel.getWidget(j);
                if (sdw.isRequired()) {
                    isRequiredLocGrp = true;
                }
                break;
            }
        }
        Long selectedGroupId = sdw == null ? null : sdw.getSelectedGroup();
        if (selectedGroupId == null && isRequiredLocGrp) {
            sdw.setError(MessagesUtil.getInstance().getMessage("groups.error.select.at.minimum", new String[]{ sdw.getLastLevelLabel()}));
            validData = false;
        }

        //breakerId  Size(max = 100, message = "{error.field.breakerid.max}")
        int index = lstbxMeterModel.getSelectedIndex();
        if (index != -1) {
            LookupListItem item = lstbxMeterModel.getItem(index);
            if (item != null && "true".equals(item.getExtraInfo2())) {
                if(txtbxBreakerId.getText().isEmpty()) {
                    breakerIdElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.breakerid.error"));
                    validData = false;
                } else if (txtbxBreakerId.getText().length() > 100) {
                    breakerIdElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.breakerid.max"));
                    validData = false;
                }
            }
        }

        //Meter URI fields
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex != -1) {
            LookupListItem item = lstbxMeterModel.getItem(selectedIndex);
            if (item != null && item.getExtraInfoMap() != null && item.getExtraInfoMap().get("uriPresent").equals("true")) {
                Meter meter = new Meter();
                if (!txtbxMeterUriAddress.getText().isEmpty()) {
                    meter.setMeterUriAddress(txtbxMeterUriAddress.getText());
                }
                if (txtbxMeterUriPort.getValue() != null) {
                    meter.setMeterUriPort(txtbxMeterUriPort.getValue());
                }
                if (!txtbxMeterUriProtocol.getText().isEmpty()) {
                    meter.setMeterUriProtocol(txtbxMeterUriProtocol.getText());
                }
                if (!txtbxMeterUriParams.getText().isEmpty()) {
                    meter.setMeterUriParams(txtbxMeterUriParams.getText());
                }

                if (txtbxMeterUriPort.getValue() != null) {
                    if(!MeterMngCommonUtil.isValidPort(String.valueOf(txtbxMeterUriPort.getValue()))) {
                        meterUriPortElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.uri.port.error"));
                        validData = false;
                    } else if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriPort", meterUriPortElement)) {
                        validData = false;
                    }
                }
                if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriAddress", meterUriAddressElement)) {
                    validData = false;
                }
                if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriProtocol", meterUriProtocolElement)) {
                    validData = false;
                }
                if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriParams", meterUriParamsElement)) {
                    validData = false;
                }
            }
        }

        //Free issue token
        if (chkBxGenFreeIssueToken.getValue()) {
            if (!MeterMngSharedUtils.isNumber(txtbxFreeIssueUnits.getText())) {
                unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.online.bulk.free.issue.invalid.units"));
                validData = false;
            } else {
                if (new BigDecimal(txtbxFreeIssueUnits.getText()).compareTo(BigDecimal.ZERO) < 1) {
                    unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.online.bulk.free.issue.invalid.units"));
                    validData = false;
                }
            }

            if (!engineeringTokenUserRefPanel.validateFormField()) {
                validData =false;
            }

            if (chkBxSmsToken.getValue()) {
                if (txtbxPhone.getText() == null || txtbxPhone.getText().isEmpty()) {
                    smsTokenElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.online.bulk.free.issue.sms.invalid.phone"));
                    validData = false;
                } else {
                    if (!MeterMngSharedUtils.isValidCellPhone(txtbxPhone.getText()) ) {
                        String errorMsgKey = "cellPhone.pattern.description";
                        if (FormatUtil.getInstance().getCellRegexPattern().getSource().equals(ValidateUtil.TELEPHONE_NUMBER)) {
                            errorMsgKey="error.field.validity.phone";
                        }
                        phoneNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage(errorMsgKey));
                        validData = false;
                    }
                }
            }

            if (!freeIssueTokenReasonsComponent.validate()) {
                validData = false;
            }
        }
        if (editUpGroupSelectionPanel != null && editUpGroupSelectionPanel.isAttached()) {
            if(!editUpGroupSelectionPanel.areGroupsAllSelected()) {
                validData = false;
            }
        }

        return validData;
    }

    @Override
    public void clearFields() {
        clearAllPanelFields();
    }

    public void clearSomePanelFields(boolean clearMeterNumber) {
        if (clearMeterNumber) {
            suggestBoxMeterNumber.setText("");
            txtbxEncryptionKey.setText("");
        }
        dtbxMeterInstallationDate.setValue(new Date());
        pricingChangeReasonPanel.clear();
        pricingChangeReasonPanel.setVisible(false);

        checkBreakerIdRequired();
        checkUriIsPresent();
        resetMeterUriFields();
    }

    private void addOne(TextBox textBox) {
        Integer newNo = Integer.parseInt(textBox.getValue());
        newNo++;
        textBox.setValue(newNo.toString());
    }

    public void clearAllPanelFields() {
        resetPanelFields();

        meterOnlineBulkData = null;
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                populatePanelBoxes();
    }
};
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
setIsStsMeter();
    }

    public void resetPanelFields() {
        clearErrors();
        enablePanelFields();
        futurePSRowPanel.setVisible(false);
        lstbxSelectStore.setSelectedIndex(0);
        suggestBoxMeterNumber.setText("");
        lstbxMeterModel.setSelectedIndex(0);
        txtbxBreakerId.setText("");
        breakerIdPanel.setVisible(false);
        txtbxEncryptionKey.setText(null);


        resetMeterUriFields();
        uriPanel.setVisible(false);

        lstbxSupplyGrpCde.setSelectedIndex(0);
        lstbxAlgCode.setSelectedIndex(0);
        lstbxTokTecCode.setSelectedIndex(0);
        txtbxCurrTariffIndx.setText("01");

        dtbxMeterInstallationDate.setValue(new Date());
        currentPricingStructureLookup.setEnabled(true);
        currentPricingStructureLookup.clearSelection();
        currentPricingStructureStartDate.setValue(null);

        futurePricingStructureLookup.clearSelection();
        futurePricingStructureStartDate.setValue(null);

        pricingChangeReasonPanel.clear();
        pricingChangeReasonPanel.setVisible(false);
        pricingChangeReasonsComponent.clearFields();

        txtbxSuiteNumber.setValue("1");
        txtbxSurname.setValue("1");
        txtbxPhone.setText("");
        clearLocationGroup();

        chkBxGenFreeIssueToken.setValue(false);
        txtbxFreeIssueUnits.setText("");
        engineeringTokenUserRefPanel.clearFormField();
        freeIssueTokenReasonsComponent.clearFields();
        chkBxSmsToken.setValue(false);

        if (editUpGroupSelectionPanel != null && editUpGroupSelectionPanel.isAttached()) {
            editUpGroupSelectionPanel.removeFromParent();
        }
    }

    private void clearLocationGroup() {
        //clear location group
        SelectionDataWidget sdw = null;
        for (int j=0; j<locationGroupPanel.getWidgetCount(); j++) {
            if (locationGroupPanel.getWidget(j) instanceof SelectionDataWidget ) {
                sdw = (SelectionDataWidget)locationGroupPanel.getWidget(j);
                break;
            }
        }
        if (sdw != null) {
            sdw.clear();
        }
    }

    private void enablePanelFields() {
        lstbxSelectStore.setEnabled(true);
        suggestBoxMeterNumber.setEnabled(true);
        dtbxMeterInstallationDate.setEnabled(true);
        lstbxSupplyGrpCde.setEnabled(true);
        txtbxCurrTariffIndx.setEnabled(true);
    }

    public void showFreeIssueContainer() {
        if (clientFactory.getUser().hasPermission("mm_aux_free_issue")) {
            freeIssueContainer.setVisible(true);
            unitsElement.setVisible(false);
            engineeringTokenUserRefPanel.hidePanel();
            smsTokenElement.setVisible(false);
            freeIssueTokenReasonsComponent.setVisible(false);
        }
    }

    public void hideFreeIssueContainer() {
        if (clientFactory.getUser().hasPermission("mm_aux_free_issue")) {
            freeIssueContainer.setVisible(false);
        } else {
            freeIssueContainer.removeFromParent();
        }
    }

    public boolean includesFreeIssueToken() {
        return chkBxGenFreeIssueToken.getValue();
    }

    public boolean isSmsFreeIssueToken() {
        return chkBxSmsToken.getValue();
    }

    @Override
    public void clearErrors() {
        suggestBoxMeterElement.clearErrorMsg();
        meterModelElement.clearErrorMsg();
        supplyGrpCdeElement.clearErrorMsg();
        algCodeElement.clearErrorMsg();
        tokTecCodeElement.clearErrorMsg();
        currTariffIndxElement.clearErrorMsg();
        installationDateElement.clearErrorMsg();
        currentPSElement.clearErrorMsg();
        suiteNumberElement.clearErrorMsg();
        surnameElement.clearErrorMsg();
        phoneNumberElement.clearErrorMsg();
        breakerIdElement.clearErrorMsg();
        genFreeIssueTokenElement.clearErrorMsg();
        unitsElement.clearErrorMsg();
        encryptionKeyElement.clearErrorMsg();
        clearMeterUriFieldErrors();

        engineeringTokenUserRefPanel.clearErrorMessage();
        smsTokenElement.clearErrorMsg();
        freeIssueTokenReasonsComponent.clearErrorMessages();
        pricingChangeReasonsComponent.clearErrorMessages();
    }

    private void clearMeterUriFieldErrors() {
        meterUriAddressElement.clearErrorMsg();
        meterUriPortElement.clearErrorMsg();
        meterUriProtocolElement.clearErrorMsg();
        meterUriParamsElement.clearErrorMsg();
    }

    public MeterOnlineBulkData getMeterOnlineBulkData() {
        return meterOnlineBulkData;
    }

    public HashMap<Long, UpGenGroupLinkData> getEditedUpGenGroupsMap() {
        return editUpGroupSelectionPanel == null ? null : editUpGroupSelectionPanel.getUpGenGroupsMap();
    }

    public Long getCurrentMeterModelId() {
        return Long.parseLong(lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()));
    }

    public Long getCurrentPricingStructureId() {
        return Long.parseLong(getPricingStructureId());
    }

    public Date getCurrentInstallationDate() {
        return dtbxMeterInstallationDate.getValue();
    }

    public String getcurrentMeterNum() {
        return suggestBoxMeterNumber.getValue().trim();
    }

    //Methods from ContainsUPGroupSelectionComponent
    @Override
    public void selectBtnProcess() {
    }

    @Override
    public void clearAll() {
    }

    @Override
    public void clearTable() {
    }

    public EngineeringTokenUserRefPanel getEngineeringTokenUserRefPanel() {
        return engineeringTokenUserRefPanel;
    }

    public Long getSelectedMeterModelId() {
        Long meterModelId = null;
        String selectedValue = null;
        if (meterOnlineBulkData != null && (meterModelId = meterOnlineBulkData.getMeterModelId()) != null) {
            return meterModelId;
        } else if (lstbxMeterModel.getSelectedIndex() > -1
                && !(selectedValue = lstbxMeterModel.getSelectedValues().get(0)).equals("-1")) {
            return Long.parseLong(selectedValue);
        }
        return null;
    }

    private String getPricingStructureId() {
        String value = null;
        if (currentPricingStructureLookup.getSelectedPricingStructureItem() != null) {
            value = currentPricingStructureLookup.getSelectedPricingStructureItem().getValue();
        }
        if (value != null && value.isEmpty()) {
            value = null;
        }
        return value;
    }

    private String getPricingStructureName() {
        String value = null;
        if (currentPricingStructureLookup.getSelectedPricingStructureItem() != null) {
            value = currentPricingStructureLookup.getSelectedPricingStructureItem().getDisplayString();
        }
        if (value != null && value.isEmpty()) {
            value = null;
        }
        return value;
    }

    //should only be called when a meter was successfully added
    protected void incrementSurnameAndOrSuite() {
        if (!MeterMngSharedUtils.isNumber(txtbxSuiteNumber.getValue())) {
            //was changed to a string, now reset it to the last unitNo that was not used....
            txtbxSuiteNumber.setValue("0");
        }
        addOne(txtbxSuiteNumber);

        if (!MeterMngSharedUtils.isNumber(txtbxSurname.getValue())) {
            //was changed to a string (perhaps a surname), now reset it to the last tenantNo that was not used....
            txtbxSurname.setValue("0");
        }
        addOne(txtbxSurname);
        txtbxPhone.setText("");
    }
}
