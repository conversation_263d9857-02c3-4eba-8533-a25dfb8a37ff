package za.co.ipay.metermng.client.widget.tree;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.cell.client.CompositeCell;
import com.google.gwt.cell.client.HasCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.GenGroupData;

import static com.google.gwt.dom.client.BrowserEvents.BLUR;
import static com.google.gwt.dom.client.BrowserEvents.CLICK;
import static com.google.gwt.dom.client.BrowserEvents.DBLCLICK;
import static com.google.gwt.dom.client.BrowserEvents.KEYDOWN;
import static com.google.gwt.dom.client.BrowserEvents.KEYUP;

public class GroupDataCell extends CompositeCell<GenGroupData> {

    private GroupDataTreeHandler treeHandler;
    private ClientFactory clientFactory;

    public GroupDataCell(ClientFactory clientFactory, GroupDataTreeHandler handler, List<HasCell<GenGroupData, ?>> hasCells) {
        super(hasCells);
        this.treeHandler = handler;
        this.clientFactory = clientFactory;
    }

    @Override
    public void render(Context context, GenGroupData value, SafeHtmlBuilder sb) {
        sb.appendHtmlConstant("<table><tbody><tr>");
        super.render(context, value, sb);
        sb.appendHtmlConstant("</tr></tbody></table>");
    }

    @Override
    protected Element getContainerElement(Element parent) {
        return parent.getFirstChildElement().getFirstChildElement().getFirstChildElement();
    }

    @Override
    protected <X> void render(Context context, GenGroupData value, SafeHtmlBuilder sb, HasCell<GenGroupData, X> hasCell) {
        Cell<X> cell = hasCell.getCell();
        sb.appendHtmlConstant("<td>");
        cell.render(context, hasCell.getValue(value), sb);
        sb.appendHtmlConstant("</td>");
    }

    @Override
    public Set<String> getConsumedEvents() {
        Set<String> events = new HashSet<String>();
        events.add(CLICK);
        events.add(DBLCLICK);
        events.add(KEYUP);
        events.add(KEYDOWN);
        events.add(BLUR);
        return events;
    }

    @Override
    public void onBrowserEvent(Context context, Element parent, final GenGroupData currNode, NativeEvent event, ValueUpdater<GenGroupData> valueUpdater) {
        if (currNode == null) {
            return;
        }
        if (event.getType().equals(BrowserEvents.CLICK)) {
            Element eventTarget = event.getEventTarget().cast();
            if (TreeStatics.IMG_TAG.equalsIgnoreCase(eventTarget.getTagName())) {
                final Long groupTypeId = treeHandler.getSelectedGroupTypeId();
                if (TreeStatics.ADD_ALT_TEXT.equalsIgnoreCase(eventTarget.getAttribute(TreeStatics.ALT_TEXT_ATTR))) {
                    treeHandler.setPosition(eventTarget);
                    treeHandler.addNewGroup(clientFactory, groupTypeId, currNode, new GenGroupData());
                } else if (TreeStatics.DELETE_ALT_TEXT.equalsIgnoreCase(eventTarget.getAttribute(TreeStatics.ALT_TEXT_ATTR))) {
                    final ConfirmHandler handler = new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                treeHandler.deleteGroup(currNode);
                            }
                        }
                    };
                    final int parentLeft = parent.getAbsoluteLeft();
                    final int parentTop = parent.getAbsoluteTop() + parent.getOffsetHeight();
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution sessionCheckResolution) {
                            clientFactory.getGroupRpc().isGroupAssignedToUP(currNode, new ClientCallback<Boolean>() {

                                @Override
                                public void onSuccess(Boolean result) {
                                    if (result) {
                                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.group.contains.usagepoint"),
                                                MediaResourceUtil.getInstance().getErrorIcon(),
                                                parentLeft,
                                                parentTop, null);
                                    } else {
                                        Dialogs.confirm(MessagesUtil.getInstance().getMessage("group.delete.ask", new String[] { currNode.getName() }),
                                                MessagesUtil.getInstance().getMessage("button.yes"),
                                                MessagesUtil.getInstance().getMessage("button.no"),
                                                MediaResourceUtil.getInstance().getQuestionIcon(),
                                                handler,
                                                parentLeft, parentTop);
                                    }
                                }

                            });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                } else if (TreeStatics.VIEW_ALT_TEXT.equalsIgnoreCase(eventTarget.getAttribute(TreeStatics.ALT_TEXT_ATTR))) {
                    treeHandler.viewGroupEntity(currNode);
                } else {
                    super.onBrowserEvent(context, parent, currNode, event, valueUpdater);
                }
            } else {
                super.onBrowserEvent(context, parent, currNode, event, valueUpdater);
            }
        } else {
            super.onBrowserEvent(context, parent, currNode, event, valueUpdater);
        }
    }
}
