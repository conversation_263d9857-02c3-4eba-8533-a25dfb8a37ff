package za.co.ipay.metermng.client.view.component.usagepoint;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.NoSelectionModel;
import com.google.gwt.view.client.SelectionChangeEvent;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.TranslateDbTerms;
import za.co.ipay.metermng.client.view.component.usercustomfields.ContainsUserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.component.usercustomfields.CustomItemsWidget;
import za.co.ipay.metermng.client.view.component.usercustomfields.UserCustomFieldsComponent;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;

public class UsagePointHistoryView extends BaseComponent implements IpayDataProviderFilter<UsagePointHistData>, ContainsUserCustomFieldsComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    interface UsagePointHistoryWidgetUiBinder extends UiBinder<Widget, UsagePointHistoryView> {
    }

    private static UsagePointHistoryWidgetUiBinder uiBinder = GWT.create(UsagePointHistoryWidgetUiBinder.class);

    @UiField(provided=true) CellTable<UsagePointHistData> clltblHistory;
    @UiField TablePager smplpgrHistory;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    @UiField HTML dataName;
    @UiField HTML dataDescription;

    private IPayDataProvider<UsagePointHistData> dataProvider;
    private ArrayList<UsagePointHistData> thehistorydata;

    private Column<UsagePointHistData, Date>    dateModifiedColumn;
    private Column<UsagePointHistData, String>  modifiedbyColumn;
    private ListHandler<UsagePointHistData> columnSortHandler;

    private Map<String, CustomFieldDto> customFieldsHistoryRequiredMap = new HashMap<String, CustomFieldDto>();
    int l;
    int t;
    private boolean viewConstructed = false;

    public UsagePointHistoryView(ArrayList<AppSetting> customFieldList, ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        prepareCustomFieldHeadings(customFieldList);
    }

    private void initUi() {
        initView();
        initTable(false);

        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("usagepoint.hist.user"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("usagepoint.hist.date"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("usagepoint.hist.meter"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("usagepoint.hist.customer"));
        dataName.setText(MessagesUtil.getInstance().getMessage("usagepoint.history"));
        dataDescription.setText(MessagesUtil.getInstance().getMessage("usagepoint.history.description"));
    }

    protected void createTable() {
         clltblHistory = new CellTable<UsagePointHistData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    void initTable(boolean refresh) {
        final Messages messages = MessagesUtil.getInstance();
        if (dataProvider == null && refresh == false) {
            dataProvider = new IPayDataProvider<UsagePointHistData>(this);

            DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
            dateModifiedColumn = new Column<UsagePointHistData, Date>(dateCell) {
                @Override
                public Date getValue(UsagePointHistData data) {
                    Format format = FormatUtil.getInstance();
                    return format.parseDateTime(format.formatDateTime(data.getDateRecModified()));
                }
            };
            dateModifiedColumn.setSortable(true);
            dateModifiedColumn.setDefaultSortAscending(false);

            modifiedbyColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    return data.getUserRecEntered();
                }
            };
            modifiedbyColumn.setSortable(true);


            TextColumn<UsagePointHistData> actionColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    return TranslateDbTerms.translateDbAction(data.getUserAction());
                }
            };

            TextColumn<UsagePointHistData> statusColumn = new StatusTableColumn<UsagePointHistData>() {
                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    String style = super.getCellStyleNames(context, data);
                    if (data.isRecordStatusChanged()) {
                        style += " cellChanged ";
                    }
                    return style;
                }
            };

            TextColumn<UsagePointHistData> statusReasonColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    if (data.getStatusReasonLogText() == null || data.getStatusReasonLogText().trim().isEmpty()) {
                        return "";
                    } else {
                        return data.getStatusReasonLogText().trim();
                    }
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isActiveStatusReasonLogIdChanged());
                }
            };

            TextColumn<UsagePointHistData> usagepointname = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    return data.getName();
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isNameChanged());
                }
            };

            TextColumn<UsagePointHistData> meterNumberColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    return data.getMeterNumber();
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isMeterIdChanged());
                }
            };

            TextColumn<UsagePointHistData> customerNameColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    if (data.getCustomerAgreementRef() != null) {
                        return data.getCustomerAgreementRef() +" ("+data.getCustomerName()+")";
                    }
                    return "";
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isCustomerAgreementIdChanged());
                }
            };

            TextColumn<UsagePointHistData> serviceLocationColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    return data.getServiceLocationErf();
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isServiceLocationIdChanged());
                }
            };

            TextColumn<UsagePointHistData> replaceReasonColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    if (data.getReplaceReasonLogText() == null || data.getReplaceReasonLogText().trim().isEmpty()) {
                        return "";
                    } else {
                        return data.getReplaceReasonLogText().trim();
                    }
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isReplaceReasonLogIdChanged());
                }
            };

            TextColumn<UsagePointHistData> blockingTypeColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    if (data.getBlockingTypeName() == null || data.getBlockingTypeName().trim().isEmpty()) {
                        return messages.getMessage("usagepoint.field.blocking.type.default");
                    } else {
                        return data.getBlockingTypeName();
                    }
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isBlockingTypeIdChanged());
                }
            };

            TextColumn<UsagePointHistData> blockReasonColumn = new TextColumn<UsagePointHistData>() {
                @Override
                public String getValue(UsagePointHistData data) {
                    if (data.getBlockReasonLogText() == null || data.getBlockReasonLogText().trim().isEmpty()) {
                        return "";
                    } else {
                        return data.getBlockReasonLogText().trim();
                    }
                }

                @Override
                public String getCellStyleNames(Context context, UsagePointHistData data) {
                    return getCellStyleName(data.isBlockReasonLogIdChanged());
                }
            };

            clltblHistory.addColumn(dateModifiedColumn, messages.getMessage("usagepoint.hist.datemod"));
            clltblHistory.addColumn(modifiedbyColumn, messages.getMessage("usagepoint.hist.user"));
            clltblHistory.addColumn(actionColumn, messages.getMessage("usagepoint.hist.action"));
            clltblHistory.addColumn(statusColumn, messages.getMessage("usagepoint.hist.status"));
            clltblHistory.addColumn(statusReasonColumn, messages.getMessage("usagepoint.hist.status.reason"));
            clltblHistory.addColumn(usagepointname, messages.getMessage("usagepoint.hist.name"));
            clltblHistory.addColumn(meterNumberColumn, messages.getMessage("usagepoint.hist.meter"));
            clltblHistory.addColumn(customerNameColumn, messages.getMessage("usagepoint.hist.custagree"));
            clltblHistory.addColumn(serviceLocationColumn, messages.getMessage("usagepoint.hist.service"));
            clltblHistory.addColumn(blockingTypeColumn, messages.getMessage("usagepoint.hist.blocking.name"));
            clltblHistory.addColumn(blockReasonColumn, messages.getMessage("usagepoint.hist.blocking.reason"));
            clltblHistory.addColumn(replaceReasonColumn, messages.getMessage("usagepoint.hist.reason"));

            dataProvider.addDataDisplay(clltblHistory);
            smplpgrHistory.setDisplay(clltblHistory);

            final NoSelectionModel<UsagePointHistData> mySelectionModel = new NoSelectionModel<UsagePointHistData>();
            clltblHistory.setSelectionModel(mySelectionModel);
            mySelectionModel.addSelectionChangeHandler( new SelectionChangeEvent.Handler() {

                @Override
                public void onSelectionChange(SelectionChangeEvent event) {
                    CustomItemsWidget<UsagePointHistData> customItemsWidget = new CustomItemsWidget<UsagePointHistData>("usagepoint", customFieldsHistoryRequiredMap);
                    customItemsWidget.setTransactionList(mySelectionModel.getLastSelectedObject());
                    customItemsWidget.show(l,t);
                }
            });
            clltblHistory.addDomHandler(new ClickHandler()
            {
                @Override
                public void onClick(ClickEvent event)
                {
                    l = clltblHistory.getAbsoluteLeft();
                    t = event.getClientY()+16;
                }
            }, ClickEvent.getType());
            t = clltblHistory.getAbsoluteTop()-(clltblHistory.getBodyHeight()+clltblHistory.getHeaderHeight());

        } else {
            clltblHistory.redraw();
        }
        dateFilter.setDataProvider(dataProvider);
    }

    private String getCellStyleName(boolean changed) {
        String style = "";
        if (changed) {
            style = "cellChanged ";
        }
        return style;
    }

    public void setUsagePointHistoryList(ArrayList<UsagePointHistData> thedata) {
        thehistorydata = thedata;
        dataProvider.setList(thehistorydata);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<UsagePointHistData>(dataProvider.getList());
            columnSortHandler.setComparator(dateModifiedColumn, new Comparator<UsagePointHistData>() {
                public int compare(UsagePointHistData o1, UsagePointHistData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getDateRecModified().compareTo(o2.getDateRecModified()) : 1;
                    }
                    return -1;
                }
            });
            clltblHistory.addColumnSortHandler(columnSortHandler);
            columnSortHandler.setComparator(modifiedbyColumn, new Comparator<UsagePointHistData>() {
                public int compare(UsagePointHistData o1, UsagePointHistData o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getUserRecEntered().compareTo(o2.getUserRecEntered()) : 1;
                    }
                    return -1;
                }
            });

            clltblHistory.addColumnSortHandler(columnSortHandler);

            // We know that the data is sorted alphabetically by default.
            clltblHistory.getColumnSortList().push(dateModifiedColumn);
            ColumnSortEvent.fire(clltblHistory, clltblHistory.getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(clltblHistory, clltblHistory.getColumnSortList());
        }
    }

    @Override
    public boolean isValid(UsagePointHistData value, String filter) {
        String filterOption = filterDropdown.getValue(filterDropdown.getSelectedIndex());
        Messages messages = MessagesUtil.getInstance();
        if (filterOption.equals(messages.getMessage("usagepoint.hist.date"))) {
            return dateFilter.isValid(value.getDateRecModified(), filter);
        }
        filter = filter.toLowerCase();
        if (filterOption.equals(messages.getMessage("usagepoint.hist.meter"))) {
            return value.getMeterNumber().toLowerCase().contains(filter);
        }
        if (filterOption.equals(messages.getMessage("usagepoint.hist.customer"))) {
            return value.getCustomerName().toLowerCase().contains(filter);
        }
        return value.getUserRecEntered().toLowerCase().contains(filter);
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(thehistorydata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrHistory.firstPage();
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("usagepoint.hist.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }

    public void refreshCustomAppSettings() {
        clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {

            @Override
            public void onFailure(Throwable caught) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("user.custom.fields.error.get", new String[] {"usage point"}),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }

            @Override
            public void onSuccess(ArrayList<AppSetting> result) {
                prepareCustomFieldHeadings(result);
            }
        });
    }

    private void prepareCustomFieldHeadings(ArrayList<AppSetting> customFieldList) {
        UserCustomFieldsComponent ucc = new UserCustomFieldsComponent(clientFactory, this, null);
        ucc.getCustomFieldsWithHistoryVisibilityMap(customFieldList, "usagepoint");
    }

    @Override
    public void setHistoryVisibilityMap(Map<String, CustomFieldDto> result) {
        customFieldsHistoryRequiredMap = result;
    }
    @Override
    public void removeUserCustomFieldsComponent() {
    }
    @Override
    public void addUserCustomFieldsComponent() {
    }
}
