<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>	
	</ui:style>
	
	  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	  <g:FlowPanel>
	    <p1:FormRowPanel>
	      <p1:FormElement debugId="nameElement" ui:field="nameElement" labelText="{msg.getAppSettingName}:" helpMsg="{msg.getAppSettingNameHelp}" required="true">
	        <g:TextBox debugId="nameBox" text="" ui:field="nameTextBox" title="{msg.getAppSettingName}" width="55" visibleLength="35"/>
	      </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
          <p1:FormElement debugId="valueElement" ui:field="valueElement" labelText="{msg.getAppSettingValue}:" helpMsg="{msg.getAppSettingValueHelp}" required="true">
                <g:TextBox debugId="valueBox" text="" ui:field="valueTextBox" title="{msg.getAppSettingValue}"/>
                <p2:IpayListBox debugId="statusListBox" visibleItemCount="1" ui:field="statusListBox" styleName="gwt-ListBox-ipay" multipleSelect="false" visible="false"/>
          </p1:FormElement>
          <p1:FormElement debugId="powerLimitTableElement" ui:field="powerLimitTableElement" visible="false"/>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
          <p1:FormElement debugId="groupTypeElement" ui:field="groupTypeElement" labelText="{msg.getAppSettingValue}:" helpMsg="{msg.getAppSettingValueHelp}" required="true" visible="false">
                <p2:IpayListBox debugId="groupTypeListBox" visibleItemCount="1" ui:field="groupTypeListBox" styleName="gwt-ListBox-ipay" multipleSelect="false"/>
          </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
          <p1:FormElement debugId="dataTypeElement" ui:field="dataTypeElement" labelText="{msg.getAppSettingDataType}:" helpMsg="{msg.getAppSettingDataTypeHelp}" required="true" visible="false">
                <p2:IpayListBox debugId="dataTypeListBox" visibleItemCount="1" ui:field="dataTypeListBox" styleName="gwt-ListBox-ipay" multipleSelect="false"/>
          </p1:FormElement>
          <p1:FormElement ui:field="dataTypeViewListBtnElement">
                <g:Button ui:field="dataTypeViewListBtn" text="{msg.getAppSettingDataTypeListItems}" />
          </p1:FormElement>
	    </p1:FormRowPanel>
	    
        <p1:FormRowPanel>
          <p1:FormElement debugId="descriptionElement" ui:field="descriptionElement" labelText="{msg.getAppSettingDescription}:" helpMsg="{msg.getAppSettingDescriptionHelp}" required="true">
            <g:TextArea debugId="descriptionBox" ui:field="descriptionTextArea" title="{msg.getAppSettingDescription}" visibleLines="5" characterWidth="30" styleName="gwt-TextBox"/>
          </p1:FormElement>
	    </p1:FormRowPanel>
	    
	  </g:FlowPanel>
	
</ui:UiBinder> 