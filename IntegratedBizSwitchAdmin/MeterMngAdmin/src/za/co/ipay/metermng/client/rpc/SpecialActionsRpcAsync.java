package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

import com.google.gwt.user.client.rpc.AsyncCallback;

public interface SpecialActionsRpcAsync {

    void getSpecialActions(AsyncCallback<ArrayList<SpecialActions>> callback);
    
    void saveSpecialActions(SpecialActions specialAction, AsyncCallback<SpecialActions> callback);

	void getSpecialActionReasons(long specialActionId,AsyncCallback<ArrayList<SpecialActionReasons>> callback);
	
	void getActiveSpecialActionReasons(long specialActionId,AsyncCallback<ArrayList<SpecialActionReasons>> callback);

	void addSpecialActionReason(SpecialActionReasons specialActionReasons, AsyncCallback<SpecialActionReasons> callback);
	
	void updateSpecialActionReason(SpecialActionReasons specialActionReasons, AsyncCallback<SpecialActionReasons> callback);

	void getSpecialActionsByValue(String specialActionValue, AsyncCallback<SpecialActions> specialActionsSvcAsyncCallback);

}
