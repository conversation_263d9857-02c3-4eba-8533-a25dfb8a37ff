package za.co.ipay.metermng.client.view.component.onlinebulk;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.tariff.TariffPanel;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.shared.dto.tariff.TariffPanelDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;

public class TariffPanelDialogueBox extends DialogBox {

    @UiField FlowPanel mainPanel;
    @UiField ScrollPanel scrollPanel;
    @UiField SimpleForm form;

    private TariffPanel tariffPanel;
    private int left;
    private int top;
    private int heightAvail;

    private static Logger logger = Logger.getLogger(TariffPanelDialogueBox.class.getName());
    private static TariffPanelDialogueBoxUiBinder uiBinder = GWT.create(TariffPanelDialogueBoxUiBinder.class);

    interface TariffPanelDialogueBoxUiBinder extends UiBinder<Widget, TariffPanelDialogueBox> {
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    public TariffPanelDialogueBox(ClientFactory clientFactory, Long pricingStructureId, int left, int top) {
        this.left = left;
        this.top = top;
        setWidget(uiBinder.createAndBindUi(this));
        tariffPanel = new TariffPanel(form, clientFactory);
        form.getFormFields().add(tariffPanel);
        form.getButtons().removeFromParent();

        clientFactory.getPricingStructureRpc().getCurrentTariffDtoByPricingStructureId(pricingStructureId, new ClientCallback<TariffPanelDto>() {
            @Override
            public void onSuccess(TariffPanelDto result) {
                prepareAndDisplayPanel(result);
            }
        });
    }

    private void prepareAndDisplayPanel(final TariffPanelDto dto) {
        //set the tariffClass
        List<TariffClass> tariffClassList = new ArrayList<TariffClass>();
        tariffClassList.add(dto.getTariffClass());
        Tariff tariff = (dto.getTariffWithData()!=null)?dto.getTariffWithData().getTariff():null;
        tariffPanel.initData(dto.getPricingStructure(), tariffClassList);

        tariffPanel.setTariffWithData(dto.getTariffWithData());

        this.setAnimationEnabled(true);
        this.setTitle(MessagesUtil.getInstance().getMessage("online.bulk.panel.tariff.title"));
        this.setGlassEnabled(true);
        this.setModal(true);
        this.setAutoHideEnabled(true);
        left = left + 50;
        top = (Window.getClientHeight() - top) / 3;
        heightAvail = Window.getClientHeight() - top -100;

        this.setPopupPosition(left, top);
        this.show();

        //for initial layout must checkHeight as well - but only makes sense after Dom created, so defer
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {
            @Override
            public void execute() {
              checkHeight(dto.getPricingStructure().getPaymentModeId());
            }
          });
    }

    public void checkHeight(Long paymentModeId) {
        //for some reason Payment Mode THIN misreports on the offSetHeight - for now for PaymentMode THIN just show scrollPanel
        if (this.getOffsetHeight() > heightAvail || paymentModeId.equals(PaymentModeE.THIN.getId())) {
            scrollPanel.setHeight(heightAvail  + "px");
        }
    }
}
