package za.co.ipay.metermng.client.view.workspace.group.all;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.Place;

public class GenGroupWorkspaceFactory implements WorkspaceFactory {

    private ClientFactory clientFactory;

    public GenGroupWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        if ((!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GROUP_ADMIN) 
                    && GroupPlace.USAGE_POINT_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType())) 
            || (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_LOCATION_GROUP_ADMIN) 
                    && GroupPlace.LOCATION_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType()))
            || (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_ACCESS_GROUP_ADMIN) 
                    && GroupPlace.ACCESS_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType()))  ){
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                    MediaResourceUtil.getInstance().getLockedIcon(), 
                    MessagesUtil.getInstance().getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;               
        }
        try {
            GenGroupWorkspaceView view = new GenGroupWorkspaceView(clientFactory, (GroupPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof GroupPlace 
                && (GroupPlace.USAGE_POINT_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType())
                    ||  GroupPlace.ACCESS_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType())
                    ||  GroupPlace.LOCATION_GROUPS_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType()))) {
            return true;
        } else {
            return false;
        }
    }
}
