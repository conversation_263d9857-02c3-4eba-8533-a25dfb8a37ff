package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.mybatis.generated.model.ModelChannelConfig;
import za.co.ipay.metermng.mybatis.generated.model.TimeInterval;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;
import za.co.ipay.metermng.shared.dto.meter.ModelChannelConfigDto;

@RemoteServiceRelativePath("secure/mdcChannel.do")
public interface MdcChannelRpc extends RemoteService {

    public List<MdcChannel> getMdcChannels(Boolean enabled, Long mdcId) throws ServiceException;
    
    public List<MdcChannel> getMdcChannelsNotOverriddenByMeter(Long mdc_id, Long meter_model_id) throws ServiceException;
    
    public List<MdcChannelDto> getMdcChannelDtos(Long mdcId) throws ServiceException;
    
    public MdcChannelDto getMdcChannelDto(Long mdcChannelId) throws ServiceException;
    
    public MdcChannelDto saveMdcChannel(MdcChannelDto mdcChannelDto) throws ValidationException, ServiceException, AccessControlException;

    public List<MdcChannel> getMdcChannelsForReadingTypesAndMeter(ArrayList<Long> readingTypeIds, Long meterId);
    
    public List<TimeInterval> getTimeIntervals() throws ServiceException;
    
    public List<ModelChannelConfigDto> getModelChannelConfigs(Long meterModelId) throws ServiceException;
    
    public ModelChannelConfigDto saveModelChannelConfig(ModelChannelConfigDto modelChannelConfigDto) throws ValidationException, ServiceException;
    
    public void deleteModelChannelConfig(ModelChannelConfig modelChannelConfig) throws ServiceException;
    
    public void deleteAllModelChannelConfigForMeter(Long meter_model_id) throws ServiceException;
}
