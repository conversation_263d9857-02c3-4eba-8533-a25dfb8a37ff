package za.co.ipay.metermng.client.view.workspace.search;


import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.SimplePager.TextLocation;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.factory.ResourcesFactory;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.AppSettingRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.search.customer.CustomerSearchForm;
import za.co.ipay.metermng.client.view.component.search.location.LocationSearchForm;
import za.co.ipay.metermng.client.view.component.search.meter.MeterSearchForm;
import za.co.ipay.metermng.client.view.component.search.usagepoint.UsagePointSearchForm;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.widget.ComboHeader;
import za.co.ipay.metermng.client.widget.ComboHeaderNames;
import za.co.ipay.metermng.client.widget.search.SearchResultTextColumn;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.search.SearchResultData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * AdvancedSearchWorkspaceView provides the advanced search functionality.
 * <AUTHOR>
 */
public class AdvancedSearchWorkspaceView extends BaseWorkspace {

    private SearchPlace searchPlace;

    @UiField HTML dataDescription;
    @UiField MeterSearchForm meterSearchForm;
    @UiField CustomerSearchForm customerSearchForm;
    @UiField UsagePointSearchForm usagePointSearchForm;
    @UiField LocationSearchForm locationSearchForm;
    @UiField CheckBox chckbxGetTotal;
    @UiField Button saveBtn;
    @UiField Button clearBtn;
    @UiField Button btnExportCsv;
    @UiField FlowPanel searchResults;
    @UiField(provided=true) CellTable<SearchResultData> table;
    @UiField(provided=true) TablePager pager;
    @UiField HTML stillCountingMessage;

    private AsyncDataProvider<SearchResultData> dataProvider;
    private Integer totalSearchResults; //currently available total search results
    private Column<SearchResultData, SearchResultData> custom1Column;
    private Header<ComboHeaderNames> usagepointCustom1Header;

    private static final String UNAVAILABLE = MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable");

    private SearchData currentSearchCriteria = null;
    private boolean countCurrentlyRunning = false;
    private boolean isEverythingLoaded = false;

    private static Logger logger = Logger.getLogger(AdvancedSearchWorkspaceView.class.getName());

    private static AdvancedSearchWorkspaceViewUiBinder uiBinder = GWT.create(AdvancedSearchWorkspaceViewUiBinder.class);

    interface AdvancedSearchWorkspaceViewUiBinder extends UiBinder<Widget, AdvancedSearchWorkspaceView> {
    }

    public AdvancedSearchWorkspaceView(ClientFactory clientFactory, SearchPlace searchPlace) {
        this.clientFactory = clientFactory;
        setPlaceString(SearchPlace.getPlaceAsString(searchPlace));
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        setHeaderText(MessagesUtil.getInstance().getMessage("search.advanced.title"));
        initUi();
    }

    @UiFactory
    public MeterSearchForm constructMeterSearchForm() {
        return new MeterSearchForm(clientFactory, this);
    }

    @UiFactory
    public CustomerSearchForm constructCustomerSearchForm() {
        return new CustomerSearchForm(this);
    }

    @UiFactory
    public UsagePointSearchForm constructUsagePointSearchForm() {
        return new UsagePointSearchForm(clientFactory, this);
    }

    @UiFactory
    public LocationSearchForm constructLocationSearchForm() {
        return new LocationSearchForm(clientFactory);
    }

    public MeterSearchForm getMeterSearchForm() {
        return meterSearchForm;
    }

    public CustomerSearchForm getCustomerSearchForm() {
        return customerSearchForm;
    }

    public UsagePointSearchForm getUsagePointSearchForm() {
        return usagePointSearchForm;
    }

    public LocationSearchForm getLocationSearchForm() {
        return locationSearchForm;
    }

    private void createTable() {
        ResourcesFactory factory = ResourcesFactoryUtil.getInstance();
        if (factory != null && factory.getCellTableResources() != null) {
            table = new CellTable<SearchResultData>(MeterMngStatics.DEFAULT_PAGE_SIZE, factory.getCellTableResources());
        } else {
            table = new CellTable<SearchResultData>(MeterMngStatics.DEFAULT_PAGE_SIZE);
        }
        pager = new TablePager(TextLocation.CENTER, false);    //showLastPageButton = false always for Advanced Search
        pager.setMany(true);
    }

    private void initUi() {
        dataDescription.setText(MessagesUtil.getInstance().getMessage("search.advanced.instructions"));
        initCriteriaForm();
        initTable();
        initDefaultClick();
    }

    private void initCriteriaForm() {
        saveBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onSearch(true);
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        clearBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onClear();
            }
        });
    }

    private void initTable() {
        if (dataProvider != null) {
            return;
        }

        //Meter column
        Header<ComboHeaderNames> meterHeader = new ComboHeader(
                new ComboHeaderNames(UiMessagesUtil.getInstance().getMeterSearchResultHeader(),
                                     UiMessagesUtil.getInstance().getSearchMeterNumber()));
        table.addColumn(new SearchResultTextColumn(clientFactory,
                                                    null,
                                                    SearchResultType.METER,
                                                    MeterMngStatics.METER_NUMBER_SEARCH),
                        meterHeader);

        //Customer column
        Column<SearchResultData, SearchResultData> customerColumn = new SearchResultTextColumn(
                                                    clientFactory,
                                                    null,
                                                    SearchResultType.CUSTOMER,
                                                    new String[] {
                                                        MeterMngStatics.CUSTOMER_TITLE_SEARCH,
                                                        MeterMngStatics.CUSTOMER_NAME_SEARCH,
                                                        MeterMngStatics.CUSTOMER_SURNAME_SEARCH,
                                                        MeterMngStatics.CUSTOMER_ID_NUMBER_SEARCH});

        Header<ComboHeaderNames> customerHeader = new ComboHeader(
                                                        new ComboHeaderNames(
                                                                UiMessagesUtil.getInstance().getCustomerSearchResultHeader(),
                                                                UiMessagesUtil.getInstance().getSearchCustomerTitle(),
                                                                UiMessagesUtil.getInstance().getSearchCustomerName(),
                                                                UiMessagesUtil.getInstance().getSearchCustomerSurname(),
                                                                UiMessagesUtil.getInstance().getSearchCustomerIdNumber()));
        table.addColumn(customerColumn, customerHeader);

        // Customer phone number column
        Header<ComboHeaderNames> customerPhoneNumberHeader = new ComboHeader(
                new ComboHeaderNames(UiMessagesUtil.getInstance().getCustomerSearchResultHeader(),
                        UiMessagesUtil.getInstance().getSearchPhoneNumber()));
        table.addColumn(new SearchResultTextColumn(clientFactory,
                        null,
                        SearchResultType.CUSTOMER,
                        MeterMngStatics.CUSTOMER_PHONE_NUMBER_SEARCH),
                customerPhoneNumberHeader);

        //Custom Agreement column
        Header<ComboHeaderNames> agreementHeader = new ComboHeader(
                new ComboHeaderNames(UiMessagesUtil.getInstance().getSearchCustomerAgreement(),
                                     UiMessagesUtil.getInstance().getSearchAgreementReference()));
        table.addColumn(new SearchResultTextColumn(clientFactory,
                                                   null,
                                                   SearchResultType.CUSTOMER_AGREEMENT,
                                                   MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH),
                        agreementHeader);

        //Custom Account column
        Header<ComboHeaderNames> accountHeader = new ComboHeader(
                new ComboHeaderNames(UiMessagesUtil.getInstance().getSearchAccount(),
                                     UiMessagesUtil.getInstance().getSearchAccountName()));
        table.addColumn(new SearchResultTextColumn(clientFactory,
                                                   null,
                                                   SearchResultType.ACCOUNT_NAME,
                                                   MeterMngStatics.ACCOUNT_NAME_SEARCH),
                        accountHeader);

        //Usage Point column
        Header<ComboHeaderNames> usagePointHeader = new ComboHeader(
                new ComboHeaderNames(UiMessagesUtil.getInstance().getUsagePointSearchResultHeader(),
                                     UiMessagesUtil.getInstance().getSearchUsagePointName()));
        table.addColumn(new SearchResultTextColumn(clientFactory,
                                                    null,
                                                    SearchResultType.USAGE_POINT,
                                                    MeterMngStatics.USAGE_POINT_NAME_SEARCH),
                        usagePointHeader);
        custom1Column = new SearchResultTextColumn(
                clientFactory,
                null,
                SearchResultType.USAGE_POINT,
                new String[] {MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1 });

        updateCustom1ColumnVisibility();

        //Data provider
        table.setVisibleRange(0, getPageSize());
        dataProvider = new AsyncDataProvider<SearchResultData>() {
            @Override
            protected void onRangeChanged(HasData<SearchResultData> display) {
                    search(display.getVisibleRange().getStart());
            }
        };
        dataProvider.addDataDisplay(table);
        pager.setDisplay(table);
        dataProvider.updateRowCount(0, true);
    }

    // Allows visibility to be set by event handler in workspace.
    private void updateCustom1ColumnVisibility() {
        final AppSettingRpcAsync appSettingRpc = clientFactory.getAppSettingRpc();
        appSettingRpc.getAppSettingByKey(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1_STATUS,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        if (!result.getValue().equals(UNAVAILABLE)) {
                            appSettingRpc.getAppSettingByKey(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1_LABEL,
                                    new ClientCallback<AppSetting>() {
                                        @Override
                                        public void onSuccess(AppSetting customLabelResult) {
                                            // Customer custom text field column
                                            usagepointCustom1Header = new ComboHeader(new ComboHeaderNames(
                                                    UiMessagesUtil.getInstance().getUsagePointSearchResultHeader(),
                                                    customLabelResult.getValue()));
                                            showCustom1Column();
                                        }
                                    });
                        } else {
                            hideCustom1Column();
                        }
                    }
                });
    }

    private void initDefaultClick() {
        KeyDownHandler returnKeyHandler = new KeyDownHandler() {
            @Override
            public void onKeyDown(KeyDownEvent event) {
                if (event.getNativeKeyCode() == KeyCodes.KEY_ENTER) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            onSearch(true);
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        };
        meterSearchForm.addDefaultKeyHandler(returnKeyHandler);
        customerSearchForm.addDefaultKeyHandler(returnKeyHandler);
        usagePointSearchForm.addDefaultKeyHandler(returnKeyHandler);
        locationSearchForm.addDefaultKeyHandler(returnKeyHandler);
    }

    private void onSearch(boolean isEverythingLoaded) {
        if (isEverythingLoaded) {
            this.isEverythingLoaded = true;
        }
        logger.info("onSearch()");
        clearResults();
        logger.info("Cleared results");
        if (meterSearchForm.isValidInput() || customerSearchForm.isValidInput() || usagePointSearchForm.isValidInput()
                || locationSearchForm.isValidInput()) {
            search(0);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("search.error.no.criteria"), MediaResourceUtil.getInstance().getErrorIcon());
        }
    }

    private void search(int start) {
        logger.info("search: start:"+start);
        logger.info("search: start:"+start + "  startmillis=" +System.currentTimeMillis());
        if (meterSearchForm.isValidInput() || customerSearchForm.isValidInput() || usagePointSearchForm.isValidInput() || locationSearchForm.isValidInput()) {
            int pageSize = MeterMngStatics.DEFAULT_PAGE_SIZE;
            final SearchData searchCriteria = new SearchData(start, pageSize, totalSearchResults);
            meterSearchForm.populateSearchCriteria(searchCriteria, isEverythingLoaded);
            customerSearchForm.populateSearchCriteria(searchCriteria);
            usagePointSearchForm.populateSearchCriteria(searchCriteria);
            locationSearchForm.populateSearchCriteria(searchCriteria);

            logger.info("Searching: "+searchCriteria);

            if (chckbxGetTotal.getValue()) {
                if (currentSearchCriteria != null && searchCriteria.getCriteria().equals(currentSearchCriteria.getCriteria()) && countCurrentlyRunning) {
                    //do nothing
                } else { //start a new count
                    stillCountingMessage.setText(UiMessagesUtil.getInstance().getSearchCountLabel());
                    countCurrentlyRunning = true;
                    totalSearchResults = null;
                    logger.info("search: doAdvancedSearchTotalCount START   =" +System.currentTimeMillis());
                    clientFactory.getSearchRpc().doAdvancedSearchTotalCount(searchCriteria, new ClientCallback<SearchData>() {
                        @Override
                        public void onSuccess(SearchData result) {
                            logger.info("search: doAdvancedSearchTotalCount CALLBACK =" +System.currentTimeMillis());
                            //Set the actual total search results count
                            logger.info("callback: searchCriteria.getCriteria().equals(currentSearchCriteria.getCriteria()=" + (searchCriteria.getCriteria().equals(currentSearchCriteria.getCriteria())));
                            if (result.getTotalResults() != null && (currentSearchCriteria == null ||searchCriteria.getCriteria().equals(currentSearchCriteria.getCriteria()))) {
                                totalSearchResults = result.getTotalResults();
                                changeToFullPager(totalSearchResults);
                                stillCountingMessage.setText("");
                                logger.info("Set total count for criteria= "+result.getTotalResults() + "   for searchCriteria="+searchCriteria);
                            }
                            countCurrentlyRunning = false;
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            countCurrentlyRunning = false;
                            stillCountingMessage.setText("");
                            totalSearchResults = null;
                            logger.log(Level.WARNING, "doAdvancedSearchTotalCount Failure. searchCriteria=" + searchCriteria);
                            super.onFailure(caught);
                        }
                    });

                }
            }

            currentSearchCriteria = searchCriteria;

            searchCriteria.setPageSize(MeterMngStatics.DEFAULT_PAGE_SIZE + 1);
            logger.info("search: doAdvancedSearch START   =" +System.currentTimeMillis());
            clientFactory.getSearchRpc().doAdvancedSearch(searchCriteria, new ClientCallback<SearchData>() {
                @Override
                public void onSuccess(SearchData result) {
                    logger.info("search: doAdvancedSearch CALLBACK=" +System.currentTimeMillis());
                    //logger.info("doAdvancedSearch: result.getStart() = " + result.getStart() + "  result.getResults().size()= " + result.getResults().size());
                    if (totalSearchResults == null) {
                        if (result.getResults().size() > MeterMngStatics.DEFAULT_PAGE_SIZE) {
                            if (result.getTotalResults() != null) {
                                dataProvider.updateRowCount(result.getTotalResults(), true);   //set rowcount to total + 1 so see nextPage button
                            } else {
                                dataProvider.updateRowCount(result.getResults().size(), false);
                            }
                        } else {
                            totalSearchResults = result.getStart() + result.getResults().size();
                            changeToFullPager(totalSearchResults);
                        }
                    }

                    //Display the results
                    dataProvider.updateRowData(result.getStart(), result.getResults());
                    logger.info("Set dataProvider data: start:"+result.getStart()+" size:"+result.getResults().size());
                    logger.info("search: END out to UI endMillis=" +System.currentTimeMillis());
                    //no results
                    if (result.getResults().size() == 0) {
                        Dialogs.displayInformationMessage(UiMessagesUtil.getInstance().getNoSearchResultsMessage(),
                                                          MediaResourceUtil.getInstance().getInformationIcon(),
                                                          saveBtn.getAbsoluteLeft(),
                                                          saveBtn.getAbsoluteTop(),
                                                          UiMessagesUtil.getInstance().getCloseButton());
                    }
                }
            });
        }
    }

    private void changeToFullPager(Integer count) {
        TablePager oldPager = pager;
        pager.removeFromParent();
        pager = new TablePager(TextLocation.CENTER); //a usual pager and update the rowCount, so it displays old style
        pager.copyDisplay(oldPager);
        searchResults.add(pager);
        dataProvider.updateRowCount(count, true);
    }
    private void changeToReducedPager() {
        pager.removeFromParent();
        pager = new TablePager(TextLocation.CENTER, false);    //showLastPageButton = false always for Advanced Search
        pager.setMany(true);
        pager.setDisplay(table);
        searchResults.add(pager);
    }

    private void onClear() {
        meterSearchForm.clear();
        customerSearchForm.clear();
        usagePointSearchForm.clear();
        locationSearchForm.clear();
        chckbxGetTotal.setValue(false);

        clearResults();
    }

    private void clearResults() {
        this.totalSearchResults = null;
        currentSearchCriteria = null;
        stillCountingMessage.setText("");
        countCurrentlyRunning = false;
        changeToReducedPager();
        if (dataProvider != null) {
            dataProvider.updateRowCount(0, true);
            dataProvider.updateRowData(0, new ArrayList<SearchResultData>(0));
            table.setVisibleRangeAndClearData(new Range(0, MeterMngStatics.DEFAULT_PAGE_SIZE), false);
        }
    }

    @Override
    public void onArrival(Place place) {
        if (place instanceof SearchPlace) {
            SearchPlace sPlace = (SearchPlace) place;
            logger.info("onArrival SearchPlace: "+sPlace);
            if (searchPlace != null && searchPlace.equals(sPlace)) {
                logger.info("Returning to same search: "+searchPlace);
            } else {
                this.searchPlace = new SearchPlace(sPlace.getSearchType(), sPlace.getDataType(), sPlace.getSearchText());
                logger.info("Initialising new search: "+searchPlace);
                setPlaceString(SearchPlace.getPlaceAsString(searchPlace));
                History.newItem(SearchPlace.getPlaceAsString(searchPlace), false);
                onClear();
                setSearchCriteria();
            }
        }
    }

    private void setSearchCriteria() {
        //Set any criteria from the searchPlace and if any is set, then search
        if (meterSearchForm.displayCriteria(searchPlace)
                || customerSearchForm.displayCriteria(searchPlace)
                || usagePointSearchForm.displayCriteria(searchPlace)
                || locationSearchForm.displayCriteria(searchPlace)) {
            logger.info("Performing initial search: " + searchPlace);
            onSearch(false);
        }
    }

    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("Received notification: " + notification);
        if (NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            String dataType = notification.getDataType();
            if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(dataType)) {
                String key = notification.getObject().toString();
                if (MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1_STATUS.equals(key)) {
                    usagePointSearchForm.getUsagePointSearchPanel().toggleCustomText1Visibility();
                    updateCustom1ColumnVisibility();
                }
            } else if (MeterMngStatics.METER_MODEL_MODIFIED.equals(dataType)) {
                meterSearchForm.getPanel().populateMeterModeListbox();;
            }
        }
    }

    public ClientFactory getClientFactory() {
        return clientFactory;
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof SearchPlace
                && SearchPlace.ADVANCED_SEARCH_TYPE.equals(((SearchPlace) place).getSearchType())) {
            return true;
        } else {
            return false;
        }
    }

    private void showCustom1Column() {
        if (table.getColumnIndex(custom1Column) == -1) {
            table.insertColumn(6, custom1Column, usagepointCustom1Header);
        }
    }

    private void hideCustom1Column() {
        if (table.getColumnIndex(custom1Column) != -1) {
            table.removeColumn(custom1Column);
        }
    }

    @UiHandler("btnExportCsv")
    void handleExportCsvButton(ClickEvent clickEvent) {
        if(currentSearchCriteria != null) {
            String searchCriteria = currentSearchCriteria.getCriteriaAsJsonString();
            String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL()).withTargetUrl("advancedsearchexport")
                    .addParam(MeterMngStatics.EXPORT_TYPE, MeterMngStatics.EXPORT_ADVANCED_SEARCH)
                    .addParam(MeterMngStatics.EXPORT_FILE_NAME_PREFIX, "meters")
                    .addParam(MeterMngStatics.EXPORT_FILTER_VALUE, searchCriteria).toEncodedUrl();
            if (encodedUrl != null) {
                Window.open(encodedUrl, "_blank", "");
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error.nodata"),
                    MediaResourceUtil.getInstance().getErrorIcon());
        }
    }

}
