package za.co.ipay.metermng.client.rpc;

import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.shared.dto.MeterMngAppData;
import za.co.ipay.metermng.shared.dto.time.TimeZoneData;

@RemoteServiceRelativePath("secure/app.do")
public interface MeterMngAppRpc extends RemoteService {
    
    public MeterMngAppData getConfigAndMessages(String localeName) throws ServiceException;
    
    public List<TimeZoneData> getTimeZones(String localeName) throws ServiceException;

    String getServerTimeZoneJson() throws ServiceException;
    
}
