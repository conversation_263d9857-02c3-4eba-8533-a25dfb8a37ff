package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.EndDeviceStoreHistData;

@RemoteServiceRelativePath("secure/deviceStores.do")
public interface DeviceStoreRpc extends RemoteService {

    List<EndDeviceStoreData> getDeviceStores() throws ValidationException, ServiceException, AccessControlException;

    EndDeviceStoreData getDeviceStore(Long deviceStoreId) throws ValidationException, ServiceException, AccessControlException;

    EndDeviceStoreData getDeviceStoreByMeter(String meterNumber) throws ValidationException, ServiceException, AccessControlException;

    EndDeviceStoreData addDeviceStore(EndDeviceStoreData addMe) throws ValidationException, ServiceException, AccessControlException;

    EndDeviceStoreData updateDeviceStore(EndDeviceStoreData updated) throws ValidationException, ServiceException, AccessControlException;

    ArrayList<EndDeviceStoreHistData> getEndDeviceStoreHistory(Long endDeviceStoreId) throws ServiceException;
}
