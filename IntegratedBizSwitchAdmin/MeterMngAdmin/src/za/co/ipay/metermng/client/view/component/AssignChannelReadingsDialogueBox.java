package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

public class AssignChannelReadingsDialogueBox extends DialogBox {
    
    private static final int DEFAULT_PAGE_SIZE = 10;

    private static Logger logger = Logger.getLogger(AssignChannelReadingsDialogueBox.class.getName());
    private static AssignChannelReadingsDialogueBoxUiBinder uiBinder = GWT.create(AssignChannelReadingsDialogueBoxUiBinder.class);
    
    @UiField FormElement readingDateElement;
    @UiField DateBox readingDateBox;
    
    @UiField HTMLPanel tablePanel;
    @UiField HTML channelHdMeterNum;
    @UiField HTML channelHdUsagePointName;
    @UiField HTML channelHdInstallDate;
    @UiField HTML channelHdMeterModel;
    @UiField HTML channelHdMdcName;
    @UiField HTML channelHdPriceStructName;
    
    @UiField TextArea preExistingReadingsMessage;
    
    @UiField CellTable<MdcChannelReadingsDto> table;
    @UiField TablePager pager;
    //@UiField HTML errorMsg;
    @UiField Button saveBtn;
    @UiField Button cancelBtn;
    
    private String resourceSymbol;
    private TextColumn<MdcChannelReadingsDto> channelValueCol;
    private TextColumn<MdcChannelReadingsDto> errorCol;
    private ListDataProvider<MdcChannelReadingsDto> dataProvider = new ListDataProvider<MdcChannelReadingsDto>();
    private ListHandler<MdcChannelReadingsDto> columnSortHandler = null;
    
    private AssignChannelReadingsComponent parentComponent;
    private MeterUpMdcChannelInfo meterUpMdcChannelInfo;
    Format format = FormatUtil.getInstance();

    interface AssignChannelReadingsDialogueBoxUiBinder extends UiBinder<Widget, AssignChannelReadingsDialogueBox> {
    }

    public AssignChannelReadingsDialogueBox(AssignChannelReadingsComponent parentComponent, MeterUpMdcChannelInfo meterUpMdcChannelInfo, 
                                            String meterNum, String usagePointName, String meterModelName, String mdcName, String pricingStructureName,
                                            Long serviceResourceId) {
        ServiceResourceE resource = ServiceResourceE.fromId(serviceResourceId);
        resourceSymbol = MeterMngStatics.symbolFromServiceResource(resource);
        this.parentComponent = parentComponent;
        this.meterUpMdcChannelInfo = meterUpMdcChannelInfo;
        table = new CellTable<MdcChannelReadingsDto>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        setWidget(uiBinder.createAndBindUi(this));
        table.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
        
        this.setGlassEnabled(true);
        initUi(meterNum, usagePointName, meterModelName, mdcName, pricingStructureName);
        addFieldHandlers();
        initTable();
        loadData(meterUpMdcChannelInfo.getChannelList());
    }
    
    private void initUi(String meterNum, String usagePointName, String meterModelName, String mdcName, String pricingStructureName) {
        channelHdMeterNum.setText(meterNum);
        channelHdUsagePointName.setText(usagePointName);
        channelHdInstallDate.setText(format.formatDateTime(meterUpMdcChannelInfo.getInstallationDate()));
        channelHdMeterModel.setText(meterModelName);
        channelHdPriceStructName.setText(pricingStructureName);
        channelHdMdcName.setText(mdcName);
        
        readingDateBox.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        
        //check if pre_Existing Readings
        if (meterUpMdcChannelInfo.isPreExistingReadings()) {
            String preExistingReadings = new String(MessagesUtil.getInstance().getMessage("channel.readings.preExisting.note"));
            if (!meterUpMdcChannelInfo.isPreExistingDiffMdc()) {
                //readings are from same Mdc Channels as current
                preExistingReadings += (MessagesUtil.getInstance().getMessage("channel.readings.preExisting.same.mdc.channels"));
                
            } else {
                preExistingReadings += (MessagesUtil.getInstance().getMessage("channel.readings.preExisting.diff.mdc.channels", 
                                          new String[] {format.formatDateTime(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())}));
            }
            preExistingReadings += (MessagesUtil.getInstance().getMessage("channel.readings.preExisting.note.end"));
            preExistingReadingsMessage.setText(preExistingReadings);
            preExistingReadingsMessage.setVisible(true);
            preExistingReadingsMessage.setEnabled(false);
            readingDateBox.setValue(format.parseDateTime(format.formatDateTime(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())));
        } else {
            readingDateBox.setValue(format.parseDateTime(format.formatDateTime(meterUpMdcChannelInfo.getInstallationDate())));
        }
        
    }
    
    private void addFieldHandlers() {
        readingDateBox.addValueChangeHandler(new ValueChangeHandler<Date>() {
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                readingDateElement.clearErrorMsg();
            }
        });
        
        //put a seperate valuechangeHandler on the DATEPICKER - will always add now time. 
        //But when change time manually on startDatebox, this changehandler is not triggered, so user can change time only
        readingDateBox.getDatePicker().addValueChangeHandler(new ValueChangeHandler<Date>() {
            @SuppressWarnings("deprecation")
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                Date now = new Date();
                Date selectedDate = event.getValue();
                //if chosen date is today; set the time to now time
                //else choosing a date in the future; leave the time as 00:00:00.0  
                //NOTE NOTE GWT doesn't use Calendar, so must use deprecated methods here to set time
                if (selectedDate.getDay() == now.getDay() 
                        && selectedDate.getMonth() == now.getMonth()
                        && selectedDate.getYear() == now.getYear()) {
                    selectedDate.setHours(now.getHours());
                    selectedDate.setMinutes(now.getMinutes());
                    selectedDate.setSeconds(now.getSeconds());
                    readingDateBox.setValue(selectedDate);
                    readingDateElement.clearErrorMsg();
                }
            }
        });
    }
    
    private void initTable() {
        channelValueCol  = new TextColumn<MdcChannelReadingsDto>() {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                return data.getValue();
            }
        };
        channelValueCol.setSortable(true);

        TextColumn<MdcChannelReadingsDto> channelNameCol = new TextColumn<MdcChannelReadingsDto>() {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                return data.getName();
            }
        };

        TextColumn<MdcChannelReadingsDto> readingTypeCol = new TextColumn<MdcChannelReadingsDto>() {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                return data.getReadingTypeName();
            }
        };

        TextColumn<MdcChannelReadingsDto> billingDetNameNameCol = new TextColumn<MdcChannelReadingsDto>() {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                StringBuilder bldr = new StringBuilder();
                for (String s: data.getBillingDetNameList()) {
                    bldr.append(s).append(", ");
                }
                String billingDetNames = bldr.toString();
                if (!billingDetNames.isEmpty()) {
                    billingDetNames = billingDetNames.substring(0, billingDetNames.length() - 2);
                }
                return billingDetNames;
            }
        };

        final DecimalInputCell readingCell = new DecimalInputCell("", resourceSymbol);
        final Column<MdcChannelReadingsDto, String> readingCol = new Column<MdcChannelReadingsDto, String>(readingCell) {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                if (data == null || data.getInitialReading() == null) {
                    return null;
                } 
                return format.formatDecimal(data.getInitialReading());
            }
        };
        readingCol.setFieldUpdater(new FieldUpdater<MdcChannelReadingsDto, String>() {
            @Override
            public void update(int index, MdcChannelReadingsDto object, String value) {
                readingCell.getViewData(object).setInvalid(false); // Reset all errors.
                if (value != null && !value.isEmpty() && readingCell.isNumeric(value)) {
                    object.setInitialReadingInput(value);
                    object.setInitialReading(format.parseDecimal(value).movePointRight(3));
                    dataProvider.refresh();
                    table.redraw();
                    MeterMngClientUtils.focusOnNext(table, index, table.getColumnIndex(readingCol));
                } else if (value == null || value.isEmpty()) {
                    object.setInitialReadingInput(null);
                    object.setInitialReading(null);
                    dataProvider.refresh();
                    table.redraw();
                    MeterMngClientUtils.focusOnNext(table, index, table.getColumnIndex(readingCol));
                } else {
                    object.setInitialReadingInput(value);
                    object.setInitialReading(null);
                    table.redraw();
                    MeterMngClientUtils.focusOnNext(table, index, table.getColumnIndex(readingCol));
                }
            }
        });
        
                
        errorCol = new TextColumn<MdcChannelReadingsDto>() {
            @Override
            public String getValue(MdcChannelReadingsDto data) {
                return data.getError();
            }
        };

        table.addColumn(channelValueCol, MessagesUtil.getInstance().getMessage("channel.field.value"));
        table.addColumn(channelNameCol, MessagesUtil.getInstance().getMessage("channel.field.name"));
        table.addColumn(readingTypeCol, MessagesUtil.getInstance().getMessage("channel.field.meter.reading.type.title"));
        table.addColumn(billingDetNameNameCol, MessagesUtil.getInstance().getMessage("channel.field.billingdet"));
        table.addColumn(readingCol, MessagesUtil.getInstance().getMessage("channel.readings.table.heading"));
        table.addColumn(errorCol, MessagesUtil.getInstance().getMessage("channel.readings.table.error.heading"));

        dataProvider.addDataDisplay(table);

        // Create the table's pager
        pager.setDisplay(table);

    }
    
    private void loadData(List<MdcChannelReadingsDto> data) {
        //dataProvider.getList().clear();
        dataProvider.getList().addAll(data);
        dataProvider.refresh();

        if (columnSortHandler == null || columnSortHandler.getList() == null) {   
            columnSortHandler = new ListHandler<MdcChannelReadingsDto>(dataProvider.getList());
            columnSortHandler.setComparator(channelValueCol, new Comparator<MdcChannelReadingsDto>() {
                public int compare(MdcChannelReadingsDto o1, MdcChannelReadingsDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getValue().compareTo(o2.getValue()) : 1;
                    }
                    return -1;
                }
            });
            table.addColumnSortHandler(columnSortHandler);

            // We know that the data is sorted by value by default.
            table.getColumnSortList().push(channelValueCol);
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(table, table.getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(table, table.getColumnSortList());
        }
    }
    
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("saveBtn")
    void handleSave(ClickEvent event) {
        if (!isValidReadings()) {
            return;
        }
        List<MdcChannelReadingsDto> channelReadingsList = getNewReadings();
        if (channelReadingsList.isEmpty() || channelReadingsList.size() < dataProvider.getList().size()) {
            Dialogs.confirm(MessagesUtil.getInstance().getMessage("channel.readings.partial.entry"),
                    MessagesUtil.getInstance().getMessage("option.positive"),
                    MessagesUtil.getInstance().getMessage("option.negative"),
                    MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) { // yes go ahead & save
                                onSaveContinue();
                            } else { // no, do not save
                                return;
                            }
                        }
                    });
        } else {
            onSaveContinue();
        }
    }

    @UiHandler("cancelBtn")
    void handleCancel(ClickEvent event) {
        logger.info("Cancelling capture of init readings - none will be stored.");
        Messages messages = MessagesUtil.getInstance();
        Dialogs.confirm(messages.getMessage("reg.read.init.readings.cancel.confirm"),
                messages.getMessage("button.yes"),
                messages.getMessage("button.no"),
                MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if(confirm) {
                            parentComponent.fireUpdateEvent(null);
                            hideThis();
                        } else {
                            return;
                        }
                    }
                });
    }
    
    private void hideThis() {
        this.hide();
    }

    private void onSaveContinue() {
        meterUpMdcChannelInfo.setChannelList(getNewReadings());
        parentComponent.fireUpdateEvent(meterUpMdcChannelInfo);
        this.hide();
    }

    private boolean isValidReadings() {
        if (table.getColumnCount() > 5) {
            table.removeColumn(errorCol);
        }
        boolean isValid = true;

        Date readingDate = readingDateBox.getValue();
        String[] dateFormatParam = {format.getDateTimeFormat()};
        if (readingDate == null || readingDateBox.getTextBox().getText().trim().isEmpty()) {
            isValid = false;
            readingDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.datetime.invalid", dateFormatParam));
        } else if (!MeterMngClientUtils.isDateBoxValueValid(readingDateBox.getTextBox().getText())) {
            isValid = false;
            readingDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.datetime.invalid", dateFormatParam));
        }
        if (isValid) {
            if (meterUpMdcChannelInfo.isPreExistingReadings()) {
                if (!readingDate.after(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())) {
                    isValid = false;
                    readingDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("channel.readings.timestamp.previous.date", new String[] {format.formatDateTime(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())}));
                }
            } else if (readingDate.before(meterUpMdcChannelInfo.getInstallationDate())) {
                isValid = false;
                readingDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("channel.readings.timestamp.install.date"));
            }    
        }
        
        for(MdcChannelReadingsDto mcr : dataProvider.getList()) {
            //check reading value
            if (mcr.getInitialReading() == null && mcr.getInitialReadingInput() != null) {
                mcr.setError(MessagesUtil.getInstance().getMessage("error.numeric.value"));
                isValid = false;
            } else if (mcr.getInitialReading() != null &&
                    (mcr.getInitialReading().doubleValue() < 0.0 
                    || mcr.getInitialReading().compareTo(mcr.getMaxSize()) > 0)) {
                logger.info("Invalid intial reading: "+mcr.getInitialReading()+" channel="+mcr.getName());
                mcr.setError(MessagesUtil.getInstance().getMessage("channel.readings.reading.error", new String[] {mcr.getValue(), mcr.getMaxSize().toString()}));
                isValid = false;
            } else {
                mcr.setError("");
            }
        }
        
         if (!isValid) {
             table.addColumn(errorCol, MessagesUtil.getInstance().getMessage("channel.readings.table.error.heading"));
         } else {
             for(MdcChannelReadingsDto mcr : dataProvider.getList()) {
                 mcr.setReadingTimestamp(readingDate);
             }
         }
         
         dataProvider.refresh();
         table.redraw();

        return isValid;
    }
    
    private List<MdcChannelReadingsDto> getNewReadings() {
        List<MdcChannelReadingsDto> channelReadingsList = new ArrayList<MdcChannelReadingsDto>();
        for(MdcChannelReadingsDto mcr : dataProvider.getList()) {
            if (mcr.getInitialReading() != null) {
                channelReadingsList.add(mcr);
            }
        }    
        return channelReadingsList;
    }
}
