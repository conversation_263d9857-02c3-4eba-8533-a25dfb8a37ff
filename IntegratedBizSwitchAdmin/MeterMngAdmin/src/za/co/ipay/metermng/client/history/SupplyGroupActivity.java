package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenSupplyGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class SupplyGroupActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    
    public SupplyGroupActivity(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new OpenSupplyGroupEvent());
    }
}
