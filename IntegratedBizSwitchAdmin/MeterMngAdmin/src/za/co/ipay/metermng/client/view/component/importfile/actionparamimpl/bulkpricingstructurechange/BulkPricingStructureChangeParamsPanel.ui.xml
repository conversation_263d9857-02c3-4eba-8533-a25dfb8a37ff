<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
	xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:igwtw="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:ipay="urn:import:za.co.ipay.metermng.client.view.component.meter">

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:VerticalPanel ui:field="bulkPricingStructureChangeParamsPanel" spacing="10">
		<p1:FormRowPanel>
			<g:Label ui:field="bulkPricingStructureChangeParamHeading" styleName="gwt-Label-header" />
		</p1:FormRowPanel>
		<p1:FormRowPanel>
			<p1:FormElement ui:field="startDateElement" labelText="{msg.getBulkPricingStructureChangeStartDate}:" required="true" helpMsg="{msg.getBulkPricingStructureChangeStartDateHelp}">
				<igwtw:IpayDateBox ui:field="startDateBox" styleName="gwt-TextBox" />
			</p1:FormElement>
		</p1:FormRowPanel>
		<p1:FormRowPanel>
			<p1:FormElement ui:field="pricingStructureElement" required="true" labelText="{msg.getBulkPricingStructureChangePricingStructure}:" helpMsg="{msg.getBulkPricingStructureChangePricingStructureHelp}">
				<p3:IpayListBox ui:field="pricingStructureBox" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" enabled="false" />
			</p1:FormElement>
		</p1:FormRowPanel>
	</g:VerticalPanel>
</ui:UiBinder>