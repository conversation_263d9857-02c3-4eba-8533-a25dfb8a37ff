package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactory;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.Range;

public class MeterReadingsTablePanel extends BaseComponent {
    
    @UiField FlowPanel readingsPanel;
    @UiField FlowPanel detailsPanel;
    @UiField Label superMeterLabel;
    @UiField Label subMetersLabel;
    @UiField(provided=true) CellTable<MeterReadingDto> table;
    @UiField TablePager pager;
    private ListDataProvider<MeterReadingDto> dataProvider;
        
    private static MeterreadingsTablePanelUiBinder uiBinder = GWT.create(MeterreadingsTablePanelUiBinder.class);

    interface MeterreadingsTablePanelUiBinder extends UiBinder<Widget, MeterReadingsTablePanel> {
    }

    public MeterReadingsTablePanel() {
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initTable();
    }

    private void createTable() {
        ResourcesFactory factory = ResourcesFactoryUtil.getInstance();
        if (factory != null && factory.getCellTableResources() != null) {
            table = new CellTable<MeterReadingDto>(MeterMngStatics.REPORT_PAGE_SIZE, factory.getCellTableResources());
        } else {
            table = new CellTable<MeterReadingDto>(MeterMngStatics.REPORT_PAGE_SIZE);
        }
    }
    
    private void initTable() {
        if (dataProvider == null) {            
            
            dataProvider = new ListDataProvider<MeterReadingDto>();     
            
            TextColumn<MeterReadingDto> meterColumn = new TextColumn<MeterReadingDto>() {
                @Override
                public String getValue(MeterReadingDto object) {
                    return object.getMeterNumber();
                }
            };
            
            TextColumn<MeterReadingDto> dateColumn = new TextColumn<MeterReadingDto>() {
                @Override
                public String getValue(MeterReadingDto object) {
                    return FormatUtil.getInstance().formatDate(object.getCreated());
                }
            };
            
            TextColumn<MeterReadingDto> startColumn = new TextColumn<MeterReadingDto>() {
                @Override
                public String getValue(MeterReadingDto object) {
                    return FormatUtil.getInstance().formatDateTime(object.getStart());
                }
            };
            startColumn.setSortable(true);
            startColumn.setDefaultSortAscending(false);
            
            TextColumn<MeterReadingDto> endColumn = new TextColumn<MeterReadingDto>() {
                @Override
                public String getValue(MeterReadingDto object) {
                    return FormatUtil.getInstance().formatDateTime(object.getEnd());
                }
            };
            endColumn.setSortable(true);

            //Reading
            TextColumn<MeterReadingDto> readingColumn = new TextColumn<MeterReadingDto>() {
                @Override
                public String getValue(MeterReadingDto data) {
                    return FormatUtil.getInstance().formatDecimal(data.getReading() / 1000);
                }                
            };        
            readingColumn.setSortable(true);

            //ReceiptNum
            TextColumn<MeterReadingDto> receiptNumColumn = new TextColumn<MeterReadingDto>() {
            	@Override
            	public String getValue(MeterReadingDto data) {
            		return data.getReceiptNum();
            	}
            };

            // Add the columns
            table.addColumn(meterColumn, MessagesUtil.getInstance().getMessage("meterreadings.table.meter"));
            table.addColumn(dateColumn, MessagesUtil.getInstance().getMessage("meterreadings.table.date"));
            table.addColumn(startColumn, MessagesUtil.getInstance().getMessage("meterreadings.table.start"));
            table.addColumn(endColumn, MessagesUtil.getInstance().getMessage("meterreadings.table.end"));
            table.addColumn(readingColumn, MessagesUtil.getInstance().getMessage("meterreadings.table.reading"));
            table.addColumn(receiptNumColumn, MessagesUtil.getInstance().getMessage("readings.table.receiptnum"));
            
            readingColumn.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
            
            dataProvider.addDataDisplay(table);                    
            pager.setDisplay(table);
            
            //Start sorter
            ListHandler<MeterReadingDto> columnSortHandler = new ListHandler<MeterReadingDto>(dataProvider.getList());
            columnSortHandler.setComparator(startColumn, new StartDateComparator());
            table.addColumnSortHandler(columnSortHandler);
            //End sorter
            columnSortHandler = new ListHandler<MeterReadingDto>(dataProvider.getList());
            columnSortHandler.setComparator(endColumn, new Comparator<MeterReadingDto>() {
                  public int compare(MeterReadingDto o1, MeterReadingDto o2) {
                    if (o1 != null) {
                      return (o2 != null) ? o1.getEnd().compareTo(o2.getEnd()) : 1;
                    }
                    return -1;
                  }
                });
            table.addColumnSortHandler(columnSortHandler);
            //Reading sorter
            columnSortHandler = new ListHandler<MeterReadingDto>(dataProvider.getList());
                columnSortHandler.setComparator(readingColumn, new Comparator<MeterReadingDto>() {
                      public int compare(MeterReadingDto o1, MeterReadingDto o2) {
                        if (o1 != null) {
                          return (o2 != null) ? Double.valueOf(o1.getReading()).compareTo(Double.valueOf(o2.getReading())) : 1;
                        }
                        return -1;
                      }
                    });
            table.addColumnSortHandler(columnSortHandler);
        }
    }
 
    protected void displayMeterReadings(String graphType, String singleMeterNumber, MeterReadingsDto readings, boolean singleMeter) {
        clearTable();
        if (readings.getReadings().size() == 0) {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meterreadings.noreadings"), MediaResourceUtil.getInstance().getInformationIcon());           
        } else {            
            readingsPanel.setVisible(true);            
            //Display single meter or super/sub meters
            if (singleMeter) {
                Iterator<String> numbers = readings.getReadings().keySet().iterator();
                String name = "";
                while(numbers.hasNext()) {
                    name = numbers.next();
                    ArrayList<MeterReadingDto> r = readings.getReadings().get(name);
                    dataProvider.getList().addAll(r);
                }
            } else {
                superMeterLabel.setText(readings.getBalancingMeter());
                StringBuilder meters = new StringBuilder();
                for(int i=0;i<readings.getSubMeters().size();i++) {
                    if (i > 0) {
                        meters.append(", ");                        
                    }
                    meters.append(readings.getSubMeters().get(i));                    
                }
                subMetersLabel.setText(meters.toString());
                detailsPanel.setVisible(true);
                
                Iterator<String> numbers = readings.getReadings().keySet().iterator();
                String name = "";
                ArrayList<MeterReadingDto> all = new ArrayList<MeterReadingDto>();
                while (numbers.hasNext()) {                
                    name = numbers.next();
                    ArrayList<MeterReadingDto> r = readings.getReadings().get(name);
                    all.addAll(r);
                }
                Collections.sort(all, new StartDateComparator());
                dataProvider.getList().addAll(all);
            }
        }
        ColumnSortEvent.fire(table, table.getColumnSortList());
    }    
    
    protected void clear() {
        readingsPanel.setVisible(false);
        detailsPanel.setVisible(false);
        clearTable();
    }
    
    private void clearTable() {
        if (dataProvider != null) {
            dataProvider.getList().clear();
            table.getColumnSortList().clear();
            table.setVisibleRangeAndClearData(new Range(0, MeterMngStatics.REPORT_PAGE_SIZE), true);
        }
    }
}
