package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceContainerDto;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataBigDecimalDto;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataCountTableDto;

import com.google.gwt.user.client.rpc.AsyncCallback;

public interface DashBoardRpcAsync {

    void getVendingActivity(AsyncCallback<ArrayList<TsDataCountTableDto>> callback);
    void getBuyingIndexData(AsyncCallback<ArrayList<TsDataBigDecimalDto>> callback);

}
