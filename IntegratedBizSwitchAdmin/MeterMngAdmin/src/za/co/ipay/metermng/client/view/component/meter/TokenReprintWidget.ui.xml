<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:ip="urn:import:za.co.ipay.gwt.common.client.form">

    <ui:style>
        .popup {
            border: 3px solid darkgrey;
            padding: 10px;
            background-color: azure;
        }
        .header{
            margin-bottom: 10px;
            font-weight: bold;
        }
        .header2{
            font-weight: bold;
            font-size: 14px;
        }
        .bold{
            font-weight: bold;
        }
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:FlowPanel styleName='{style.popup}'>
        <g:Label text="{msg.getTokenReprintRequest}" styleName="{style.header}" horizontalAlignment="center"/>
        <g:HorizontalPanel width="100%">
            <g:FlowPanel>
	            <ip:FormRowPanel>
	                <ip:FormGroupPanel>
	                    <g:Label text="{msg.getReprintWarningLine1}" horizontalAlignment="center"/>
	                    <g:Label ui:field="lblWarningLine2" horizontalAlignment="center"/>
	                    <g:Label text="{msg.getReprintWarningLine3}" horizontalAlignment="center"/>
	                    <g:Label ui:field="lblWarningLine4" horizontalAlignment="center"/>
	                    <g:Label ui:field="lblWarningLine5" horizontalAlignment="center"/>
	                    <g:VerticalPanel ui:field="pnlCreditToken" horizontalAlignment="center" visible="false" width="300px">
	                        <g:Label text="{msg.getReprintCreditVendTaxInvoice}" horizontalAlignment="center"/>
	                        <ip:FormGroupPanel ui:field="pnlVendKeyChange" visible="false">
		                        <g:Label text="{msg.getReprintKeyChangeNoticeLine1}" horizontalAlignment="center"/>
		                        <g:Label text="{msg.getReprintKeyChangeNoticeLine2}" horizontalAlignment="center"/>
	                        </ip:FormGroupPanel>
	                        <ip:FormGroupPanel>
		                        <g:HorizontalPanel width="300px">
		                            <g:Label text="{msg.getReprintUtilName}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblUtilName" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getReprintUtilDistId}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblUtilDistId" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getReprintUtilVatNo}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblUtilVatNo" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getReprintUtilAddress}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblUtilAddress" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getReprintIssued}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblIssued" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getSearchAgreementReference}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblReference" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                         <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getReprintCustomer}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblCustomerNames" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getCustomerAgreementRef}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblCustomerAgreementRef" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getUsagePointName}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblUsagePointName" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
		                       	<g:HorizontalPanel width="100%">
		                            <g:Label ui:field="lblAddress" horizontalAlignment="center"/>
		                        </g:HorizontalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterNumber}:" horizontalAlignment="left" styleName="gwt-Label-bold-left"/>
		                            <g:Label ui:field="lblCreditMeterNumber" horizontalAlignment="right" styleName="{style.bold}"/>
		                        </g:HorizontalPanel>
	                            <g:VerticalPanel ui:field="pnlStsMeterInfo" width="100%" visible="false">
			                        <g:HorizontalPanel width="100%">
			                            <g:Label text="{msg.getReprintTokenTech}:" horizontalAlignment="left"/>
			                            <g:Label ui:field="lblTokenTech" horizontalAlignment="right"/>
			                        </g:HorizontalPanel>
			                        <g:HorizontalPanel width="100%">
			                            <g:Label text="{msg.getReprintAlg}:" horizontalAlignment="left"/>
			                            <g:Label ui:field="lblAlg" horizontalAlignment="right"/>
			                        </g:HorizontalPanel>
			                        <g:HorizontalPanel width="100%">
			                            <g:Label text="{msg.getReprintSgc}:" horizontalAlignment="left"/>
			                            <g:Label ui:field="lblSgc" horizontalAlignment="right"/>
			                        </g:HorizontalPanel>
			                        <g:HorizontalPanel width="100%">
			                            <g:Label text="{msg.getMeterTariffIndexColumn}:" horizontalAlignment="left"/>
			                            <g:Label ui:field="lblTi" horizontalAlignment="right"/>
			                        </g:HorizontalPanel>
			                        <g:HorizontalPanel width="100%">
			                            <g:Label text="{msg.getReprintKrn}:" horizontalAlignment="left"/>
			                            <g:Label ui:field="lblKrn" horizontalAlignment="right"/>
			                        </g:HorizontalPanel>
		                        </g:VerticalPanel>
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getTariffTitle}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblTariff" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                        </ip:FormGroupPanel>
	                        <g:Label ui:field="lblYourResourceToken" horizontalAlignment="center" visible="false"/>
	                        <g:VerticalPanel ui:field="pnlStandardToken" width="100%" visible="false">
		                        <g:Label text="{msg.getReprintStandardToken}" horizontalAlignment="center"/>
		                        <g:Label ui:field="lblStandardToken" styleName="{style.bold}" horizontalAlignment="center"/>
		                        <ip:FormGroupPanel>
		                            <g:HorizontalPanel width="300px">
		                                <g:Label text="{msg.getReprintReceiptNr}:" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblStandardTokenReceiptNr" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel width="100%">
		                                <g:Label ui:field="lblStandardTokenUnitsHeader" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblStandardTokenUnits" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel ui:field="pnlUnitsBalance" width="100%" visible="false">
		                                <g:Label ui:field="lblUnitsBalanceHeader" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblUnitsBalance" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel width="100%">
		                                <g:Label text="{msg.getUsagePointTxnAmt}:" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblStandardTokenAmount" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel width="100%">
		                                <g:Label text="{msg.getCustomerTxnTax}:" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblStandardTokenTax" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
                                    <g:HorizontalPanel width="100%">
                                        <g:Label text="{msg.getTariffTitle}:" horizontalAlignment="left"/>
                                        <g:Label ui:field="lblTariffBreakdown" horizontalAlignment="right"/>
                                    </g:HorizontalPanel>
		                        </ip:FormGroupPanel>
	                        </g:VerticalPanel>
	                        <g:VerticalPanel ui:field="pnlFreeBasicResource" width="100%" visible="false">
		                        <g:Label ui:field="lblFreeBasicResourceHeader" horizontalAlignment="center"/>
		                        <g:Label ui:field="lblFreeBasicResourceToken" styleName="{style.bold}" horizontalAlignment="center"/>
		                        <ip:FormGroupPanel>
		                            <g:HorizontalPanel width="300px">
		                                <g:Label text="{msg.getReprintReceiptNr}:" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblFreeBasicResourceReceiptNr" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel width="100%">
		                                <g:Label text="{msg.getMeterTxnDate}:" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblFreeBasicResourceDate" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                            <g:HorizontalPanel width="100%">
		                                <g:Label ui:field="lblFreeBasicResourceUnitsHeader" horizontalAlignment="left"/>
		                                <g:Label ui:field="lblFreeBasicResourceUnits" horizontalAlignment="right"/>
		                            </g:HorizontalPanel>
		                        </ip:FormGroupPanel>
	                        </g:VerticalPanel>
	                        <g:VerticalPanel ui:field="pnlDeposit" width="100%" visible="false">
	                            <g:Label text="{msg.getReprintDeposit}" styleName="{style.bold}" horizontalAlignment="center"/>
	                        </g:VerticalPanel>
	                        <g:VerticalPanel ui:field="pnlRefund" width="100%" visible="false">
	                            <g:Label text="{msg.getAuxAccountBalanceRefundLbl}" styleName="{style.bold}" horizontalAlignment="center"/>
	                        </g:VerticalPanel>
	                        <g:VerticalPanel ui:field="pnlDebtItems" width="100%" visible="false">
	                            <g:Label text="{msg.getReprintDebtItems}" styleName="{style.bold}" horizontalAlignment="center"/>
	                        </g:VerticalPanel>
	                        <g:VerticalPanel ui:field="pnlFixedItems" width="100%" visible="false">
	                            <g:Label text="{msg.getReprintFixedItems}" styleName="{style.bold}" horizontalAlignment="center"/>
	                        </g:VerticalPanel>
	                        <ip:FormGroupPanel>
	                            <g:HorizontalPanel width="300px">
	                                <g:Label text="{msg.getReprintTotalTax}" horizontalAlignment="left"/>
	                                <g:Label ui:field="lblTotalTax" horizontalAlignment="right"/>
	                            </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
	                                <g:Label text="{msg.getReprintTotalTaxIncl}" horizontalAlignment="left"/>
	                                <g:Label ui:field="lblTotalTaxIncl" horizontalAlignment="right"/>
	                            </g:HorizontalPanel>
	                        </ip:FormGroupPanel>
	                    </g:VerticalPanel>
	                    <g:VerticalPanel ui:field="pnlEngineeringToken" horizontalAlignment="center" visible="false" width="300px">
	                        <g:Label ui:field="lblUtilityName" styleName="{style.header2}"/>
	                        <g:Label ui:field="lblUtilityAddress" styleName="{style.header2}"/>
	                        <g:Label ui:field="lblTokenType" styleName="{style.header2}"/>
	                        <g:HorizontalPanel width="100%">
	                            <g:Label text="{msg.getMeterTxnDate}:" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblDate" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
	                        <g:HorizontalPanel width="100%">
	                            <g:Label text="{msg.getMeterTxnUsagepoint}:" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblUsagePoint" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
	                        <g:HorizontalPanel width="100%">
	                            <g:Label text="{msg.getMeterNumber}:" horizontalAlignment="left" styleName="gwt-Label-bold-left"/>
	                            <g:Label ui:field="lblMeterNumber" horizontalAlignment="right" styleName="{style.bold}"/>
	                        </g:HorizontalPanel>
							<g:VerticalPanel ui:field="pnlStsEngMeterInfo" width="100%" visible="false">
								<g:HorizontalPanel width="100%">
									<g:Label text="{msg.getReprintSgc}:" horizontalAlignment="left"/>
									<g:Label ui:field="lblEngSgc" horizontalAlignment="right"/>
								</g:HorizontalPanel>
								<g:HorizontalPanel width="100%">
									<g:Label text="{msg.getMeterTariffIndexColumn}:" horizontalAlignment="left"/>
									<g:Label ui:field="lblEngTi" horizontalAlignment="right"/>
								</g:HorizontalPanel>
								<g:HorizontalPanel width="100%">
									<g:Label text="{msg.getReprintKrn}:" horizontalAlignment="left"/>
									<g:Label ui:field="lblEngKrn" horizontalAlignment="right"/>
								</g:HorizontalPanel>
							</g:VerticalPanel>
	                        <g:HorizontalPanel ui:field="pnlUnits" width="100%" visible="false">
	                            <g:Label ui:field="lblUnitsHeader" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblUnits" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
	                        <g:HorizontalPanel ui:field="pnlPowerLimit" width="100%" visible="false">
	                            <g:Label text="{msg.getPowerLimitUnitsW}:" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblPowerLimit" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
	                        <g:VerticalPanel ui:field="pnlKeyChange" width="100%" visible="false">
		                        <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterOldSupplygroupcode}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblOldSupplyGroupCode" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterNewSupplygroupcode}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblNewSupplyGroupCode" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterOldKeyrevisionnum}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblOldKeyRevisionNumber" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterNewKeyrevisionnum}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblNewKeyRevisionNumber" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterOldTariffindex}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblOldTariffIndex" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                            <g:HorizontalPanel width="100%">
		                            <g:Label text="{msg.getMeterNewTariffindex}:" horizontalAlignment="left"/>
		                            <g:Label ui:field="lblNewTariffIndex" horizontalAlignment="right"/>
		                        </g:HorizontalPanel>
	                        </g:VerticalPanel>
	                        <g:HorizontalPanel width="100%">
	                            <g:Label text="{msg.getMeterDescription}:" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblDescription" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
	                        <g:HorizontalPanel width="100%">
	                            <g:Label text="{msg.getMeterTxnUser}:" horizontalAlignment="left"/>
	                            <g:Label ui:field="lblUser" horizontalAlignment="right"/>
	                        </g:HorizontalPanel>
		                    <g:VerticalPanel ui:field="pnlKeyChangeTokens" horizontalAlignment="center" width="300px">
		                        <g:Label ui:field="lblTokenCode1Header" text="{msg.getMeterTokenCode}:" styleName="{style.bold}"/>
		                        <g:Label ui:field="lblTokenCode1" styleName="{style.header2}"/>
		                        <g:Label ui:field="lblTokenCode2Header" text="{msg.getMeterTokenCode2}:" styleName="{style.bold}" visible="false"/>
		                        <g:Label ui:field="lblTokenCode2" styleName="{style.header2}" visible="false"/>
								<g:Label ui:field="lblTokenCode3Header" text="{msg.getMeterTokenCode3}:" styleName="{style.bold}" visible="false"/>
								<g:Label ui:field="lblTokenCode3" styleName="{style.header2}" visible="false"/>
								<g:Label ui:field="lblTokenCode4Header" text="{msg.getMeterTokenCode4}:" styleName="{style.bold}" visible="false"/>
								<g:Label ui:field="lblTokenCode4" styleName="{style.header2}" visible="false"/>
		                    </g:VerticalPanel>
	                    </g:VerticalPanel>
	                </ip:FormGroupPanel>
	            </ip:FormRowPanel>
		        <g:HTMLPanel styleName="mainButtons">
		            <g:Button ui:field="btnPrint" debugId="btnPrint" text="{msg.getReprintPrint}"/>
	                <g:Button ui:field="btnSaveToPdf" debugId="btnSaveToPdf" text="{msg.getReprintSaveToPdf}"/>
	            </g:HTMLPanel>
            </g:FlowPanel>
            <g:HTML width="10px"/>
            <g:FlowPanel>
	            <g:HTML text="{msg.getMessagingTypeEmail}" styleName="{style.header}"/>
	            <ip:FormRowPanel>
	                <ip:FormElement labelText="{msg.getMessageRecipient}"
	                                ui:field="reprintEmailMessageRecipientElement">
	                    <g:TextBox ui:field="reprintEmailMessageRecipient" debugId="reprintEmailRecipientContact"
	                               visibleLength="40"
	                               styleName="gwt-TextBox"/>
	                </ip:FormElement>
	            </ip:FormRowPanel>
	            <ip:FormRowPanel>
	                <ip:FormElement labelText="{msg.getMessageLabel}" ui:field="reprintEmailMessageElement">
	                    <g:TextArea ui:field="reprintEmailMessageBox" debugId="reprintEmailMessageBox" visibleLines="21"
	                                characterWidth="40" styleName="gwt-TextBox"/>
	                </ip:FormElement>
	            </ip:FormRowPanel>
	            <ip:FormRowPanel>
	                <g:Button ui:field="btnSendEmail" debugId="btnSendEmail" text="{msg.getSendButton}"/>
	            </ip:FormRowPanel>
	            <g:HTML text="{msg.getMessagingTypeSms}" styleName="{style.header}"/>
	            <ip:FormRowPanel>
	                <ip:FormElement labelText="{msg.getMessageRecipient}"
	                                ui:field="reprintSmsMessageRecipientElement" helpMsg="{msg.getMessageRecipientHelp}">
	                    <g:TextBox ui:field="reprintSmsMessageRecipient" debugId="reprintSmsRecipientContact"
	                               visibleLength="40"
	                               styleName="gwt-TextBox"/>
	                </ip:FormElement>
	            </ip:FormRowPanel>
	            <ip:FormRowPanel>
	                <ip:FormElement labelText="{msg.getMessageLabel}" ui:field="reprintSmsMessageElement">
	                    <g:TextArea ui:field="reprintSmsMessageBox" debugId="reprintSmsMessageBox" visibleLines="14"
	                                characterWidth="40" styleName="gwt-TextBox"/>
	                </ip:FormElement>
	            </ip:FormRowPanel>
	            <ip:FormRowPanel>
	                <g:Button ui:field="btnSendSms" debugId="btnSendSms" text="{msg.getSendButton}"/>
	            </ip:FormRowPanel>
	        </g:FlowPanel>
        </g:HorizontalPanel>
        <g:Button ui:field="btnClose" debugId="btnClose" text="{msg.getCloseButton}"/>
    </g:FlowPanel>
</ui:UiBinder>
