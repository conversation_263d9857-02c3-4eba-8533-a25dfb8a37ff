package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkstoremovement;

import java.util.ArrayList;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkstoremovement.BulkStoreMovementParamRecord;

public class BulkStoreMovementParamsPanel extends ActionParamsComponent
        implements ProvidesResize, RequiresResize {
    
    @UiField VerticalPanel bulkStoreMovementParamsPanel;
    @UiField Label bulkStoreMovementParamHeading;
    @UiField FormElement storeMovementElement;
    @UiField IpayListBox storeMovementBox;

    private BulkStoreMovementParamRecord bulkStoreMovementParamRecord;
    private HasDirtyData hasDirtyData;
    HasDirtyDataManager hasDirtyDataManager;
    
    private static BulkStoreMovementParamsPanelUiBinder uiBinder = GWT
            .create(BulkStoreMovementParamsPanelUiBinder.class);

    interface BulkStoreMovementParamsPanelUiBinder
            extends UiBinder<Widget, BulkStoreMovementParamsPanel> {
    }

    public BulkStoreMovementParamsPanel(ClientFactory clientFactory, String fileName,
            BulkStoreMovementParamRecord bulkStoreMovementParamRecord) {
        super();
        this.clientFactory = clientFactory;
        this.bulkStoreMovementParamRecord = bulkStoreMovementParamRecord;
        hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
        initWidget(uiBinder.createAndBindUi(this));
        bulkStoreMovementParamHeading.setText(MessagesUtil.getInstance().getMessage("bulk.device.store.movement.header", new String[] { fileName }));
        addFieldHandlers();
        mapDataToForm();
    }

    private void addFieldHandlers() {
        storeMovementBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    private void refreshStoreMovementBox(String storeMovementIdSaved) {
        final String storeMovementId;
        if (storeMovementIdSaved == null) {
            int storeMovementIndex = storeMovementBox.getSelectedIndex();
            if (storeMovementIndex > 0) {
                storeMovementId = storeMovementBox.getItem(storeMovementIndex).getValue();
            } else {
                storeMovementId = null;
            }
        } else {
            storeMovementId = storeMovementIdSaved;
        }
        clientFactory.getLookupRpc().getDeviceStoreLookupListForAccessGroup(clientFactory.getUser().getSessionGroupId(),
                new ClientCallback<ArrayList<LookupListItem>>() {
                    @Override
                    public void onSuccess(ArrayList<LookupListItem> result) {
                        storeMovementBox.clear();
                        result.add(0, new LookupListItem("-1", ""));
                        if (result != null) {
                            storeMovementBox.setLookupItems(result);
                            if (storeMovementId != null) {
                                storeMovementBox.selectItemByValue(storeMovementId);
                            }
                        }
                    }
                });
    }

    @Override
    public void mapDataToForm() {
        clearErrorMessages();
        if (bulkStoreMovementParamRecord != null && bulkStoreMovementParamRecord.getToDeviceStoreId() != null) {
            refreshStoreMovementBox(bulkStoreMovementParamRecord.getToDeviceStoreId().toString());
        } else {
            refreshStoreMovementBox(null);
        }
    }

    private void clearErrorMessages() {
        storeMovementElement.clearErrorMsg();
    }

    @Override
    public boolean validateForm() {
        clearErrorMessages();
        boolean isValidated = true;
        if (storeMovementBox.getSelectedIndex() < 1) {
            Messages messages = MessagesUtil.getInstance();
            storeMovementElement.setErrorMsg(messages.getMessage("error.field.is.required",
                    new String[] { messages.getMessage("bulk.device.store.movement.param") }));
            isValidated = false;
        }
        return isValidated;
    }

    @Override
    public BulkParamRecord mapFormToData() {
        bulkStoreMovementParamRecord = new BulkStoreMovementParamRecord();
        bulkStoreMovementParamRecord.setToDeviceStoreId(
                Long.valueOf(storeMovementBox.getItem(storeMovementBox.getSelectedIndex()).getValue()));
        return bulkStoreMovementParamRecord;
    }

    @Override
    public boolean checkDirtyData() {
        return hasDirtyData.isDirtyData();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                bulkStoreMovementParamsPanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
