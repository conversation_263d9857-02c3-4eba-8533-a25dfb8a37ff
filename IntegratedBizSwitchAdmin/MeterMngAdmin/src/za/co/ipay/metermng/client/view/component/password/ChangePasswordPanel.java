package za.co.ipay.metermng.client.view.component.password;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PasswordTextBox;
import com.google.gwt.user.client.ui.Widget;

public class ChangePasswordPanel extends SimpleFormPanel {
    
    @UiField FormElement currentPasswordElement;
    @UiField FormElement newPasswordElement;
    @UiField FormElement confirmPasswordElement;
    
    @UiField PasswordTextBox currentPasswordBox;
    @UiField PasswordTextBox newPasswordBox;
    @UiField PasswordTextBox confirmPasswordBox;
    
    @UiField Label instructions;
    
    private static ChangePasswordPanelUiBinder uiBinder = GWT.create(ChangePasswordPanelUiBinder.class);

    interface ChangePasswordPanelUiBinder extends UiBinder<Widget, ChangePasswordPanel> {
    }

    public ChangePasswordPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
    }

    @Override
    public void addFieldHandlers() {
        currentPasswordBox.addChangeHandler(new FormDataChangeHandler(form));
        newPasswordBox.addChangeHandler(new FormDataChangeHandler(form));
        confirmPasswordBox.addChangeHandler(new FormDataChangeHandler(form));
    }

    @Override
    public void clearFields() {
        currentPasswordBox.setText("");
        newPasswordBox.setText("");
        confirmPasswordBox.setText("");
        instructions.setText("");
    }

    @Override
    public void clearErrors() {
        currentPasswordElement.setErrorMsg(null);
        newPasswordElement.setErrorMsg(null);
        confirmPasswordElement.setErrorMsg(null);
    }
}
