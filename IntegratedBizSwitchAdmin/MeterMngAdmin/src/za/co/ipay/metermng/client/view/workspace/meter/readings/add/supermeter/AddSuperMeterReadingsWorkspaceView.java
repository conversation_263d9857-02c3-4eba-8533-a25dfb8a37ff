package za.co.ipay.metermng.client.view.workspace.meter.readings.add.supermeter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormDisplayView;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.AddSuperMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;

public class AddSuperMeterReadingsWorkspaceView extends BaseWorkspace {

    @UiField SimpleFormDisplayView view;
    private SimpleForm addSuperMeterReadingsForm;
    private AddSuperMeterReadingsPanel panel;
    private String energyForwardId;

    private static Logger logger = Logger.getLogger(AddSuperMeterReadingsWorkspaceView.class.getName());

    private static AddSuperMeterReadingsWorkspaceViewUiBinder uiBinder = GWT.create(AddSuperMeterReadingsWorkspaceViewUiBinder.class);

    interface AddSuperMeterReadingsWorkspaceViewUiBinder extends UiBinder<Widget, AddSuperMeterReadingsWorkspaceView> {
    }

    public AddSuperMeterReadingsWorkspaceView(ClientFactory clientFactory, AddMeterReadingsPlace addMeterReadingsPlace) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        this.addSuperMeterReadingsForm = view.getForm();
        setPlaceString(AddMeterReadingsPlace.getPlaceAsString(addMeterReadingsPlace));
        setHeaderText(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.title"));
        initUi(addMeterReadingsPlace);
        loadInitData();
    }

    private void initUi(AddMeterReadingsPlace place) {
        panel = new AddSuperMeterReadingsPanel(clientFactory, addSuperMeterReadingsForm);
        addSuperMeterReadingsForm.setHasDirtyDataManager(this);
        addSuperMeterReadingsForm.getFormFields().add(panel);

        addSuperMeterReadingsForm.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.add"));
        addSuperMeterReadingsForm.getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onAdd();
            }
        });

        addSuperMeterReadingsForm.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear"));
        addSuperMeterReadingsForm.getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            onClear();
                        }
                    }
                });
            }
        });

        addSuperMeterReadingsForm.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.title.criteria"), "pageSectionTitle");
    }

    private void loadInitData() {
        clientFactory.getLookupRpc().getAddSuperMeterReadingScreenData(new ClientCallback<AddSuperMeterReadingScreenDataDto>() {
            @Override
            public void onSuccess(AddSuperMeterReadingScreenDataDto result) {
                setPanelData(result);
            }
        });
    }

    private void setPanelData(AddSuperMeterReadingScreenDataDto data) {
        panel.readingTypeBox.clear();
        for(int i=0;i<data.getReadingTypes().size();i++) {
            panel.readingTypeBox.addItem(data.getReadingTypes().get(i).getName(), data.getReadingTypes().get(i).getId().toString());
            if (MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE.equals(data.getReadingTypes().get(i).getValue())) {
                energyForwardId = data.getReadingTypes().get(i).getId().toString();
                panel.readingTypeBox.setItemSelected(i, true);
            } else {
                panel.readingTypeBox.setItemSelected(i, false);
            }
        }

        panel.readingIntervalBox.clear();
        panel.readingIntervalBox.addItem(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.minutes.15"), "15");
        panel.readingIntervalBox.addItem(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.minutes.30"), "30");
        panel.readingIntervalBox.addItem(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.minutes.60"), "60");

        panel.superMeterNumberBox.clear();
        panel.superMeterNumberBox.addItem("");
        for(MeterDto m : data.getSuperMeters()) {
            panel.superMeterNumberBox.addItem(m.getNumber(), m.getId().toString());
        }
    }

    protected void onAdd() {
        if (isValidInput()) {
            Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon(),
                                                                     view.getForm().getSaveBtn().getAbsoluteLeft() + 10,
                                                                     view.getForm().getSaveBtn().getAbsoluteTop() - 25);
            final Long superMeterId = Long.valueOf(panel.superMeterNumberBox.getValue(panel.superMeterNumberBox.getSelectedIndex()));
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getMeterRpc()
                            .addSuperMeterReadings(superMeterId,
                                    panel.startBox.getValue(), panel.endBox.getValue(),
                                    getReadingInterval(),
                                    getReadingTypeId(),
                                    panel.deleteBox.getValue(),
                                    panel.regenerateSubMetersBox.getValue(),
                                    getVariations(),
                                    new ClientCallback<Void>() {
                                        @Override
                                        public void onSuccess(Void result) {
                                            view.getForm().setDirtyData(false);
                                            WaitingDialogUtil.getCurrentInstance().hide();
                                            Dialogs.displayInformationMessage(
                                                    MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.success"),
                                                    MediaResourceUtil.getInstance().getInformationIcon());
                                        }
                                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private ArrayList<MeterReadingVariation> getVariations() {
        ArrayList<MeterReadingVariation> variations = new ArrayList<MeterReadingVariation>();
        MeterReadingVariation v = null;
        for(int i=0;i<panel.addedVariationsBox.getItemCount();i++) {
            String value = panel.addedVariationsBox.getValue(i);
            String[] values = value.split("\\: ");
            if (values != null && values.length == 2) {
                v = new MeterReadingVariation(Integer.valueOf(values[0]), new BigDecimal(values[1]));
                variations.add(v);
            }
        }
        logger.info("Variations: "+variations.toString());
        return variations;
    }

    private Integer getReadingInterval() {
        int index = panel.readingIntervalBox.getSelectedIndex();
        if (index > -1) {
            return Integer.valueOf(panel.readingIntervalBox.getValue(index));
        } else {
            return null;
        }
    }

    private Long getReadingTypeId() {
        for(int i=0;i<panel.readingTypeBox.getItemCount();i++) {
            if (panel.readingTypeBox.isItemSelected(i)) {
                return Long.valueOf(panel.readingTypeBox.getValue(i));
            }
        }
        return null;
    }

    protected boolean isValidInput() {
        panel.clearErrors();
        boolean valid = true;

        String superMeterId = null;
        int index = panel.superMeterNumberBox.getSelectedIndex();
        if (index > 0) {
            superMeterId = panel.superMeterNumberBox.getValue(index);
        }
        if (ValidateUtil.isNullOrBlank(superMeterId)) {
            valid = false;
            panel.superMeterNumberElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.error.supermeter"));
        }

        Date start = panel.startBox.getValue();
        String startInput = panel.startBox.getTextBox().getText();
        Date end = panel.endBox.getValue();
        String endInput = panel.endBox.getTextBox().getText();
        String format = FormatUtil.getInstance().getDateFormat() + " " +FormatUtil.getInstance().getTimeFormat();
        if (start == null && startInput == null) {
            valid = false;
            panel.startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
        } else if (start == null && startInput != null) {
            valid = false;
            panel.startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start.format", new String[]{format}));
        }

        if (end == null && endInput == null) {
            valid = false;
            panel.endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
        } else if (end == null && endInput != null) {
            valid = false;
            panel.endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end.format", new String[]{format}));
        }

        if (start != null && end != null && !start.before(end)) {
            valid = false;
            panel.startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
        }

        //Reading interval
        if (getReadingInterval() == null) {
            valid = false;
            panel.readingIntervalElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.interval"));
        }

        //Reading type
        if (getReadingTypeId() == null) {
            valid = false;
            panel.readingTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("demo.addsupermeterreadings.error.type"));
        }

        return valid;
    }

    private void onClear() {
        panel.clearErrors();
        panel.clearFields();

        if (energyForwardId != null) {
            for(int i=0;i<panel.readingTypeBox.getItemCount();i++) {
                if (panel.readingTypeBox.getValue(i).equalsIgnoreCase(energyForwardId)) {
                    panel.readingTypeBox.setItemSelected(i, true);
                } else {
                    panel.readingTypeBox.setItemSelected(i, false);
                }
            }
        }

        panel.readingIntervalBox.setSelectedIndex(0);
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onArrival(Place place) {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof AddMeterReadingsPlace) {
            AddMeterReadingsPlace p = (AddMeterReadingsPlace) place;
            if (AddMeterReadingsPlace.SUPER_METER_TYPE.equals(p.getMeterType())) {
                return true;
            }
        }
        return false;
    }
}
