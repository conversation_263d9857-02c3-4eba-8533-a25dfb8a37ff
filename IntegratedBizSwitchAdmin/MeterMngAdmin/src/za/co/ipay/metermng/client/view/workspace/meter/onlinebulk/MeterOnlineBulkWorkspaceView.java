package za.co.ipay.metermng.client.view.workspace.meter.onlinebulk;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.ActionCell;
import com.google.gwt.cell.client.ButtonCell;
import com.google.gwt.cell.client.Cell;
import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.dom.client.Style.TextDecoration;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.ColumnSortList.ColumnSortInfo;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DockLayoutPanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.NoSelectionModel;
import com.google.gwt.view.client.SelectionChangeEvent;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEvent;
import za.co.ipay.metermng.client.event.LinkToEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterOnlineBulkPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.AssignChannelReadingsComponent;
import za.co.ipay.metermng.client.view.component.AssignChannelReadingsDialogueBox;
import za.co.ipay.metermng.client.view.component.group.ContainsUPGroupSelectionComponent;
import za.co.ipay.metermng.client.view.component.onlinebulk.ContainsMeterUpCustComponent;
import za.co.ipay.metermng.client.view.component.onlinebulk.MeterUpCustWidget;
import za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupSelectionPanel;
import za.co.ipay.metermng.client.view.component.pricingstructure.MeterOnlineBulkValidatePStoMM;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterOnlineBulkData;
import za.co.ipay.metermng.shared.TokenData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.MeterOnlineBulkDto;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;

/*
 * Note this WorkspaceView deviates from the general pattern in that it combines both a Search function to view meters by groups, AND an Add/Edit function for individual meters.
 * - It has a panel at the top to enter Usage point group details to search on; new groups can also be added dynamically here.
 * - The table can be populated from the group selection: Note that clicking on the table line does NOT populate the panel below as on the other pages -
 *                      instead it displays a popup with more information to view.
 *                      The table has a column with an EDIT button for the line which then populates the panel below to edit only certain fields from the Meter - Customer - Usage Point.
 *                      To avoid duplication of code, the REMOVE button simply opens the UP page as well.
 *                      Table has extensive filtering and sort functionality to aid call centre users. Watch the manipulation of total results on the pager to avoid errors.
 * - The panel at the bottom also has dual functionality. It can be used to add meters from device stores to the usage point group selection. In this case it does not clear fields
 *                      inbetween adding - but remembers last entries - so that meters with the same config can be quickly added by only filling in unique fields like the telephone number.
 *                      This uses the save(..) methods. These cascade to accomodate async processing.
 *                      The second function is to edit fields when the EDIT button is pressed on the line in the table. This uses the update(..) methods.
 *                              To avoid excessive duplication of code, on edit, the STS supply group info cannot be amended - fields are disabled
 *                              and the help comment points user to the main Usage Point Page - which can easily be reached vis the link in the table.
 *
 * Permissions: mm_meter_online_bulk_admin: enables the page
 *              mm_meter_online_bulk_edit: if removed, will only be able to search & view - the panel is not shown at all for add / edit. Edit column is removed from table.
 *
 *
 * Note re Table ordering & sorting: GWT cellTable has Table Column index - i.e. index of column in table
 *                      AND a columnSortlist which contains the last column sorted in index 0.
 *                      If you push the COLUMN onto the sortlist stack - it will flip the ascending bit every time. needed for clicking on column heading to sort on that column.
 *                      If you push the COLUMNSORTINFO it will not. Desired behaviour for paging controls.
 *
 * author: Rencia
 */

public class MeterOnlineBulkWorkspaceView extends BaseWorkspace implements FormManager<MeterOnlineBulkData>,
        ContainsUPGroupSelectionComponent, ContainsMeterUpCustComponent, AssignChannelReadingsComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField DockLayoutPanel mainPanel;
    @UiField PageHeader pageHeader;
    @UiField(provided=true) UpGroupSelectionPanel upGroupSelectionPanel;
    //@UiField HTML tableTitle;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField(provided=true) CellTable<MeterOnlineBulkData> table;
    @UiField TablePager pager;
    @UiField SimpleForm form;
    @UiField HTMLPanel tablePanel;
    @UiField FlowPanel belowTablePanel;
    @UiField FlowPanel belowFormPanel;

    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateBox filterDatebox;

    private MeterOnlineBulkPanel panel;

    private AsyncDataProvider<MeterOnlineBulkData> dataProvider;
    private AsyncHandler columnSortHandler;
    private NoSelectionModel<MeterOnlineBulkData> selectionModel;

    private Integer totalResults;
    private int filterTableColIndx;
    private String filterColumnName;
    private String filterString;
    private Date filterDate;
    private boolean isPermissionForEdit = false;
    private boolean forceSelectionRequiredGroups = false;
    private boolean smsWarning = false;
    private boolean isSelectButtonClick = false;
    private boolean validateAllPricingStructureToMeterModel = false;

    private TextColumn<MeterOnlineBulkData> meterNumCol;
    private TextColumn<MeterOnlineBulkData> metermodelCol;
    private TextColumn<MeterOnlineBulkData> supplyGroupCodeCol;
    private Column<MeterOnlineBulkData, Date> installDateCol;
    private TextColumn<MeterOnlineBulkData> pricingStructureCol;
    private TextColumn<MeterOnlineBulkData> suiteNumCol;
    private TextColumn<MeterOnlineBulkData> statusCol;

    private static Logger logger = Logger.getLogger(MeterOnlineBulkWorkspaceView.class.getName());

    private static MeterOnlineBulkWorkspaceViewUiBinder uiBinder = GWT.create(MeterOnlineBulkWorkspaceViewUiBinder.class);

    interface MeterOnlineBulkWorkspaceViewUiBinder extends UiBinder<Widget, MeterOnlineBulkWorkspaceView> {
    }

    public MeterOnlineBulkWorkspaceView(ClientFactory clientFactory, MeterOnlineBulkPlace place) {
        this.clientFactory = clientFactory;
        //set up the group selection panel at the top above table
        upGroupSelectionPanel = new UpGroupSelectionPanel(clientFactory, this, true, true);
        upGroupSelectionPanel.setButtonText("meter.online.bulk.select.meters.button");
        initTable();
        initWidget(uiBinder.createAndBindUi(this));

        setPlaceString(MeterOnlineBulkPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("meter.online.bulk.header"));
        initUi();
    }

    private void initTable() {
        if (ResourcesFactoryUtil.getInstance() != null
                && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            table = new CellTable<MeterOnlineBulkData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            table = new CellTable<MeterOnlineBulkData>(DEFAULT_PAGE_SIZE);
        }
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        getValidatePsSetting();
    }

    private void getValidatePsSetting() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.VALIDATE_ALL_BILLING_DETERMINANTS_MODEL_VS_TARIFF,
                new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                validateAllPricingStructureToMeterModel = Boolean.parseBoolean(result.getValue());
            }
        });
    }

    private void initView() {
        filterDropdown.addItem("");
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.number"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.models.name"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("supplygroup.name"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("usagepoint.field.pricingstructure"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("location.field.suitenumber"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.online.bulk.usagepoint.status"));
        filterDatebox
                .setFormat(new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat())));
        filterDatebox.getTextBox().addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleDateFilter();
            }
        });

        setPageHeader(MessagesUtil.getInstance().getMessage("meter.online.bulk.header"));
    }

    private void initForm() {
        dataName.setText(MessagesUtil.getInstance().getMessage("meter.online.bulk.title"));
        dataName.setVisible(true);
        dataDescription.setVisible(false);

        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_ONLINE_BULK)) {
            addCaptureEditPanel();
        } else {
            belowTablePanel.removeFromParent();
            form.removeFromParent();
            belowFormPanel.removeFromParent();
        }
    }

    private void addCaptureEditPanel() {

        //set up the detail panel under table
        panel = new MeterOnlineBulkPanel(form, clientFactory, this);
        panel.suggestBoxMeterNumber.addKeyDownHandler(new EnterKeyHandler() {
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
                    @Override
                    public void execute() {
                        panel.populatePanel();
                        setFocusOnPhoneNo();
                    }
                });
            }
        });
        panel.suggestBoxMeterNumber.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                panel.dtbxMeterInstallationDate.setValue(new Date());
            }
        });
        form.setHasDirtyDataManager(this);
        form.getFormFields().add(panel);

        form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("meter.online.bulk.button.add"));
        form.getSaveBtn().ensureDebugId("addMaterBtn");
        form.getSaveBtn().setTabIndex(3);
        form.getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        if (form.getSaveBtn().getText().equals(MessagesUtil.getInstance()
                        .getMessage("meter.online.bulk.button.add"))) {
                    onSave();
                } else {
                    onUpdate();
                }
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        form.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear.panel"));
        form.getOtherBtn().ensureDebugId("clearPanelBtn");
        form.getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                form.checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            form.setDirtyData(false);
                            displayMeterOnlineBulkData(null, false);
                            meterNumCol.setDefaultSortAscending(true);
                            int meterNumColIndx = table.getColumnIndex(meterNumCol);

                            table.getColumnSortList().push(table.getColumnSortList().get(meterNumColIndx));    //meterNumCol);  //pushing columnSortInfo doesn't flip the ascending sorting bit
                            form.getSaveBtn().setEnabled(true);
                        }
                    }
                });
            }
        });

        form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.online.bulk.add.meter"));
    }

    public void setFocusOnPhoneNo() {
        panel.txtbxPhone.setFocus(true);
    }

    public void setFocusOnSave() {
        form.getSaveBtn().setFocus(true);
    }

    private void createTable(){
        if (dataProvider == null) {

            meterNumCol = new TextColumn<MeterOnlineBulkData>() {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return data.getMeterNum();
                }
            };
            meterNumCol.setSortable(true);
            meterNumCol.setDefaultSortAscending(true);

            metermodelCol = new TextColumn<MeterOnlineBulkData>() {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return data.getMeterModelName();
                }
            };
            metermodelCol.setSortable(true);

            supplyGroupCodeCol = new TextColumn<MeterOnlineBulkData>() {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return Objects.equals(data.getMeterTypeId(), MeterTypeE.STS.getId())? data.getStsCurrSupplyGroupCode() : "";
                }
            };
            supplyGroupCodeCol.setSortable(true);

            DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
            installDateCol = new Column<MeterOnlineBulkData, Date>(dateCell) {
                @Override
                public Date getValue(MeterOnlineBulkData data) {
                    Format format = FormatUtil.getInstance();
                    return format.parseDateTime(format.formatDateTime(data.getInstallationDate()));
                }
            };
            installDateCol.setSortable(true);

            pricingStructureCol = new TextColumn<MeterOnlineBulkData>() {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return data.getCurrentPricingStructureName();
                }
            };
            pricingStructureCol.setSortable(true);

            suiteNumCol = new TextColumn<MeterOnlineBulkData>() {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return data.getSuiteNum();
                }
            };
            suiteNumCol.setSortable(true);
            //Empty String passed on creation of ActionCell, text to be displayed will be set at render
			ActionCell<MeterOnlineBulkData> editBtnActionCell = new ActionCell<MeterOnlineBulkData>(" ", new ActionCell.Delegate<MeterOnlineBulkData>() {
				@Override
				public void execute(MeterOnlineBulkData object) {
					// We don't need to perform anything here as the actions are handled by the onBrowserEvents
				}
			});

            Column<MeterOnlineBulkData,MeterOnlineBulkData> editBtnCol = new Column<MeterOnlineBulkData,MeterOnlineBulkData>(editBtnActionCell) {
              public MeterOnlineBulkData getValue(MeterOnlineBulkData object) {
                return object;
              }

              @Override
              public void render(Context context, MeterOnlineBulkData meterOnlineBulkData, SafeHtmlBuilder sb){
                  if (!meterOnlineBulkData.getRecordStatus().equals(RecordStatus.ACT)) {
                      //just don't render the button
                  } else {
                      sb.appendHtmlConstant("<button type=\"button\" id=\"addMtrsToGrpEditBtn\" tabindex=\"-1\">");
                      sb.appendEscaped("Edit");
                      sb.appendHtmlConstant("</button>");
                 }
              }

              @Override
              public void onBrowserEvent(Context context, Element elem, MeterOnlineBulkData object, NativeEvent event) {
                  //event.preventDefault();
                  super.onBrowserEvent(context, elem, object, event);
                  //Reason why went with Browserevent and not fieldupdater is was getting IndexOutOfBoundsException - pages only have 10, so absolute index 12 should become 2!!
                  //logger.info(">>>RC: editBTNCELL on browserEvent type = " + event.getType() + "  context.getIndex()= " + context.getIndex() + "   table.getVisibleRange().getStart()= " + table.getVisibleRange().getStart());
                  //INFO: >>>RC: editBTNCELL on browserEvent type = click  context.getIndex()= 12   table.getVisibleRange().getStart()= 10

                  // 2023-10-29 Zach: I removed KEYDOWN because otherwise if one presses keys after having pressed
                  // the button it refreshing the page, for example I was pressing the ALT key to switch windows
                  // right after clicking and it kept refreshing the page as many times as I pressed ALT.
                  // Click is enough (google ButtonCell only handles click)
                  boolean display = false;
                  if (BrowserEvents.CLICK.equals(event.getType())) {
                      display = true;
                  }
                  if(BrowserEvents.KEYDOWN.equals(event.getType())) {
                      if(event.getKeyCode() == KeyCodes.KEY_ENTER) {
                          display = true;
                      }
                  }
                  if(display && object.getMeterNum() != null) {
                      // can only edit if there is a meter on the usage point
                      // it disables the button but one can still action edit
                      // with the keyboard
                      Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.no.edit"), MediaResourceUtil.getInstance().getErrorIcon(), null);
                      displaySelected(object);
                  }
              }
            };

            ButtonCell removeBtnCell = new ButtonCell(){
                @Override
                public void render(Context context, SafeHtml data, SafeHtmlBuilder sb) {

                        sb.appendHtmlConstant("<button type=\"button\" id=\"addMtrsToGrpRemoveBtn\" tabindex=\"-1\">");
                        if (data != null) {
                            sb.append(data);
                        }
                        sb.appendHtmlConstant("</button>");

                }
            };

            Column<MeterOnlineBulkData,String> removeBtnCol = new Column<MeterOnlineBulkData,String>(removeBtnCell) {
              public String getValue(MeterOnlineBulkData object) {
                return "Remove";
              }

              @Override
              public void onBrowserEvent(Cell.Context context, Element elem, MeterOnlineBulkData object, NativeEvent event) {
                  //event.preventDefault();
                  super.onBrowserEvent(context, elem, object, event);

                  panel.clearAllPanelFields();
                  setPanelAddLabels();
                  int indx = context.getIndex() - table.getVisibleRange().getStart();
                  if (object.getMeterNum() == null || object.getMeterNum().isEmpty()) {
                      Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.no.remove"),
                              MediaResourceUtil.getInstance().getErrorIcon(),
                              table.getRowElement(indx).getCells().getItem(7).getOffsetLeft(),
                              table.getRowElement(indx).getAbsoluteTop() + table.getRowElement(indx).getOffsetHeight(),
                              null);
                  } else {
                      //History.newItem(clientFactory.getPlaceHistoryMapper().getToken(new MeterPlace(object.getMeterNum(), null)));
                      LinkToEvent linkToEvent = new LinkToEvent(LinkToEvent.REMOVE_METER_COMPONENT, object.getMeterNum(), LinkToEvent.METER_NUM);
                      clientFactory.getEventBus().fireEvent(linkToEvent);
                  }
              }
            };

            Column<MeterOnlineBulkData, String> usagePointNameCol = new Column<MeterOnlineBulkData, String>(new SearchClickableCell(clientFactory.getPlaceHistoryMapper(), panel, this)) {
                @Override
                public String getValue(MeterOnlineBulkData data) {
                    return data.getUsagePointName();
                }
            };

            statusCol = new StatusTableColumn<MeterOnlineBulkData>();
            statusCol.setSortable(true);


            table.addColumn(meterNumCol, MessagesUtil.getInstance().getMessage("meter.number"));
            table.addColumn(metermodelCol, MessagesUtil.getInstance().getMessage("meter.models.name"));
            table.addColumn(supplyGroupCodeCol, MessagesUtil.getInstance().getMessage("supplygroup.name"));
            table.addColumn(installDateCol, MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate"));
            table.addColumn(pricingStructureCol, MessagesUtil.getInstance().getMessage("usagepoint.field.pricingstructure"));
            table.addColumn(suiteNumCol, MessagesUtil.getInstance().getMessage("location.field.suitenumber"));
            //NB see also getColumnName(int index) and filterfield in handleFilterDropdownSelection()
            if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_ONLINE_BULK)) {
                table.addColumn(editBtnCol, "");
                table.addColumn(removeBtnCol, "");
                isPermissionForEdit = true;
            }
            table.addColumn(usagePointNameCol, MessagesUtil.getInstance().getMessage("usagepoint.field.name"));
            table.addColumn(statusCol, MessagesUtil.getInstance().getMessage("meter.online.bulk.usagepoint.status"));


            // Set the range to display
            table.setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<MeterOnlineBulkData>() {
                @Override
                protected void onRangeChanged(HasData<MeterOnlineBulkData> display) {
                    //maintain current sort arrangement, so pick the last ColumnSortInfo off the top of the sortList
                    ColumnSortList sortList = table.getColumnSortList();
                    int columnIndex = table.getColumnIndex(meterNumCol);
                    boolean isAscending = true;

                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<MeterOnlineBulkData, ?> sColumn = (Column<MeterOnlineBulkData, ?>) sortList.get(0).getColumn();
                        columnIndex = table.getColumnIndex(sColumn);
                        isAscending = sortList.get(0).isAscending();
                    }

                    if(totalResults != null) {                     //can only change range, if actually HAVE something in table!
                        getTableData(display.getVisibleRange().getStart(), columnIndex, isAscending );
                    }
                }
            };
            dataProvider.addDataDisplay(table);

            // Create the table's pager
            pager.setDisplay(table);

            // Set the table's column sorter handler
            columnSortHandler = new AsyncHandler(table) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    @SuppressWarnings("unchecked")
                    int sortIndex = table.getColumnIndex((Column<MeterOnlineBulkData, ?>) event.getColumn());
                    boolean isAscending = event.isSortAscending();

                    getTableData(0, sortIndex, isAscending);
                }
            };

            selectionModel = new NoSelectionModel<MeterOnlineBulkData>();
            CellPreviewEvent.Handler<MeterOnlineBulkData> handler = new CellPreviewEvent.Handler<MeterOnlineBulkData>() {
                final CellPreviewEvent.Handler<MeterOnlineBulkData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
                @Override
                public void onCellPreview(final CellPreviewEvent<MeterOnlineBulkData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (isPermissionForEdit) {
                            if (event.getColumn() != 6 && event.getColumn() != 7 && event.getColumn() != 8) { //these columns have buttons / links of their own
                                selectionEventManager.onCellPreview(event);
                            }
                        } else {
                            if (event.getColumn() != 6) {         // col 6 now has the Up link in it
                                selectionEventManager.onCellPreview(event);
                            }
                        }
                    } else {
                        selectionEventManager.onCellPreview(event);
                    }
                }};
            table.setSelectionModel(selectionModel, handler);
            selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
                public void onSelectionChange(SelectionChangeEvent event) {
                    final MeterOnlineBulkData selected = selectionModel.getLastSelectedObject();             //.getSelectedObject();
                    if (selected != null) {
                        showMeterInfoPopup(selected.getUsagePointName());
                     }
                }
            });

            table.addColumnSortHandler(columnSortHandler);
            table.getColumnSortList().push(metermodelCol);
            table.getColumnSortList().push(supplyGroupCodeCol);
            table.getColumnSortList().push(installDateCol);
            table.getColumnSortList().push(pricingStructureCol);
            table.getColumnSortList().push(suiteNumCol);
            table.getColumnSortList().push(meterNumCol);   //last here so first time it sorts on meter num (top of stack!)

            dataProvider.updateRowCount(0, true);
            table.setRowCount(0, true);

            clearFilter();
        }
    }

    private String getColumnName(int index) {
        //returns fieldName from database passed through to "order by" clause on server side (GroupSearchQueryBuilder)
        switch (index) {
        case 0: return "me.meter_num";
        case 1: return "mm.model_name";
        case 2: return "st.sts_curr_supply_group_code";
        case 3: return "up.installation_date";
        case 4: return "ps.pricing_structure_name";
        case 5: return "lo.suite_num";
        case 7:
            if (!isPermissionForEdit) {   //if not edit permission, 2 columns will not be in table!
                return "up.record_status";
            }
        case 9: return "up.record_status";
        default: return null;
        }
    }

    public void selectBtnProcess() {
        isSelectButtonClick = true;
        panel.clearAllPanelFields();
        clearTable();
        clearFilter();

        final int start = 0;
        meterNumCol.setDefaultSortAscending(true);

        int meterNumColIndx = table.getColumnIndex(meterNumCol);
        setTableSortOrder(meterNumColIndx);
        getTableData(start, meterNumColIndx, true);
    }

    private Integer findColIndxInSortList(int tableColIndx) {
        for (int i=0; i<table.getColumnSortList().size(); i++) {
            @SuppressWarnings("unchecked")
            Column<MeterOnlineBulkData, ?> sColumn = (Column<MeterOnlineBulkData, ?>) table.getColumnSortList().get(i).getColumn();
            if (table.getColumnIndex(sColumn) == tableColIndx) {
                return i;
            }
        }
        return null;
    }

    private boolean setTableSortOrder(int tableColIndx) {
        Integer colIndxInSortList = findColIndxInSortList(tableColIndx);
        if (colIndxInSortList != null) {
            ColumnSortInfo colSortInfo = table.getColumnSortList().get(colIndxInSortList);
            if (colSortInfo.isAscending()) {          //already ascending...
                table.getColumnSortList().push(colSortInfo);
            } else {
                table.getColumnSortList().push(table.getColumn(tableColIndx));
            }
        } else {
            table.getColumnSortList().push(table.getColumn(tableColIndx));
        }

        return table.getColumnSortList().get(0).isAscending();
    }

    private void getTableData(final int start, int tableColIndex, Boolean isAscending) {
        clearTable();
        String sortColumnName = getColumnName(tableColIndex);

        final int pageSize = getPageSize();
        String order = "ASC";
        if (!isAscending) {
            order = "DESC";
        }

        //get selected groupTypeId + leaf-node genGroupId
        ArrayList<UpGenGroupLinkData> selectedGroupsList = upGroupSelectionPanel.getSelectedGroups();
        logger.info("getTableData(): ArrayList<UpGenGroupLinkData> selectedGroupsList: " + selectedGroupsList.toString()+ "  sortColumn = " + sortColumnName + "  filterString=" + filterString + "  filterDate=" + (filterDate == null ? "" : filterDate.toString()) + "   start=" + start + "   pageSize=" + pageSize + "  order=" + order);

        clientFactory.getSearchRpc().getOnlineBulkUpDataFromUpGroupSelection(start, pageSize, selectedGroupsList, sortColumnName, filterColumnName, filterString, filterDate, order, new ClientCallback<MeterOnlineBulkDto>() {
            @Override
            public void onSuccess(MeterOnlineBulkDto resultDto) {
                pager.setPageStart(start);

                List<MeterOnlineBulkData> result = resultDto.getMoblist();
                //Set the actual total search results count ... done this way because will only send back the 10 results from start so can't use .size()
                totalResults = resultDto.getResultCount();
                dataProvider.updateRowCount(totalResults, true);
                table.setRowCount(totalResults, true);

                //Display the results
                dataProvider.updateRowData(start, result);
                logger.info("Set dataProvider data: start:" + start + " size:" + result.size());

                //no results and is result of clicking the meter selection button
                if (result.isEmpty() && isSelectButtonClick) {
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.search.no.results"),
                            MediaResourceUtil.getInstance().getInformationIcon());
                }
                isSelectButtonClick = false;
            }
        });
    }

    public void displaySelected(MeterOnlineBulkData selected) {
        displayMeterOnlineBulkData(selected, true);
    }

    private void displayMeterOnlineBulkData(MeterOnlineBulkData selected, boolean isEdit) {
        if (!isEdit) {         //from clearpanel button
            clearPanel();
            setPanelAddLabels();

        } else {
            form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.online.bulk.edit.meter"));
            panel.hideFreeIssueContainer();
            //RC do edit in panel setup
            panel.mapDataToForm(selected);
        }
    }

    protected void setPanelAddLabels() {
        form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("meter.online.bulk.button.add"));
        form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.online.bulk.add.meter"));
        panel.showFreeIssueContainer();
    }

    private void clearPanel() {
        clearTableSelection();

        panel.clearFields();
        panel.clearErrors();
    }


    public void clearTableSelection() {
        MeterOnlineBulkData selected = selectionModel.getLastSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    @Override
    public void clearAll() {
        clearTable();
        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_ONLINE_BULK)) {
            //check permission here because if not, panel won't be visible.
            panel.clearAllPanelFields();
            setPanelAddLabels();
            clearFilter();
        }
    }

    @Override
    public void clearTable() {
        clearTableSelection();

        dataProvider.updateRowData(0, new ArrayList<MeterOnlineBulkData>());
        dataProvider.updateRowCount(0, true);
        totalResults = null;
        table.setRowCount(0, true);

        logger.info("clearTable(): Set dataProvider data: start:0  new ArrayList<MeterOnlineBulkData>()");
    }

    public void setPageHeader(String header) {
        pageHeader.setHeading(header);
    }

    //*******************************************************************************************************
    private void clearFilter() {
        filterColumnName = null;
        filterString = null;
        filterDate = null;

        filterDropdown.setSelectedIndex(0);
        txtbxfilter.setText("");
        txtbxfilter.setVisible(true);
        filterDatebox.getTextBox().setText("");
        filterDatebox.setVisible(false);
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent event) {
        if (filterDropdown.getSelectedIndex() < 1) {
            filterString = null;
            filterTableColIndx = table.getColumnIndex(meterNumCol);
        } else {
            String textFilter = txtbxfilter.getText().trim();
            filterString = textFilter.isEmpty() ? null : textFilter;
            if (filterTableColIndx == table.getColumnIndex(statusCol)) {    //recordStatus
                RecordStatus recordStatus = textFilter.toLowerCase().startsWith("in") ? RecordStatus.DAC : RecordStatus.ACT;
                filterString = recordStatus.toString();
            }
        }
        changeFilter(filterTableColIndx);
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        filterColumnName = null;
        filterString = null;
        filterDate = null;

        txtbxfilter.setText("");
        filterDatebox.getTextBox().setText("");
        filterDatebox.setVisible(filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate")));
        txtbxfilter.setVisible(!filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate")));

        //If have cleared the filter dropdown, leave filterColumnName null and reset the filtering
        if (filterDropdown.getSelectedIndex() < 1) {
            filterTableColIndx = table.getColumnIndex(meterNumCol);
            changeFilter(table.getColumnIndex(meterNumCol));
            return;
        }

        //establish WHICH column to filter on; using db field names to pass to group select
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.number"))) {
            filterTableColIndx = table.getColumnIndex(meterNumCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.models.name"))) {
            filterTableColIndx = table.getColumnIndex(metermodelCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("supplygroup.name"))) {
            filterTableColIndx = table.getColumnIndex(supplyGroupCodeCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate"))) {
            filterTableColIndx = table.getColumnIndex(installDateCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("usagepoint.field.pricingstructure"))) {
            filterTableColIndx = table.getColumnIndex(pricingStructureCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("location.field.suitenumber"))) {
            filterTableColIndx = table.getColumnIndex(suiteNumCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.online.bulk.usagepoint.status"))) {
            filterTableColIndx = table.getColumnIndex(statusCol);
        } else {
            return;
        }

        //Only set filterColumnName if there WAS actually a selection.
        filterColumnName=getColumnName(filterTableColIndx);
    }

    @UiHandler("filterDatebox")
    void handleDateFilterChange(ValueChangeEvent<Date> event) {
        handleDateFilter();
    }

    protected void handleDateFilter() {
        if (filterDatebox.getTextBox().getText().trim().isEmpty()) {
            clearFilter();
            changeFilter(0);
        } else {
            filterDate = filterDatebox.getValue();
            changeFilter(table.getColumnIndex(installDateCol));
        }
    }

    private void changeFilter(int tableColIndx) {
        final int start = 0;      //table.getVisibleRange().getStart();
        Integer colIndxInSortList = findColIndxInSortList(tableColIndx);

        if (colIndxInSortList != null) {
            ColumnSortInfo colSortInfo = table.getColumnSortList().get(colIndxInSortList);
            table.getColumnSortList().push(colSortInfo);
        } else {
            table.getColumnSortList().push(table.getColumn(tableColIndx));
        }

        getTableData(start, tableColIndx, table.getColumnSortList().get(0).isAscending());
    }

    //*******************************************************************************************************
    private void showMeterInfoPopup(String usagePointName) {
        clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(usagePointName, new ClientCallback<UsagePointData>() {
            @Override
            public void onSuccess(UsagePointData result) {
                new MeterUpCustWidget(clientFactory, MeterOnlineBulkWorkspaceView.this, result, table.getAbsoluteLeft(), Window.getScrollTop() + 120);
            }
        });

    }

    //*******************************************************************************************************
    private void onSave() {
        panel.clearErrors();
        //Todo eich! have to redo the populatePanel meterNum checks here - because if type in number & CLICK on save() (without entering or using suggestbox) BOOM will do the save() process.
        //Without checking for dups
        //also if ctrl-v in a meter number and CLICK on save....
        //its the enter on the suggestbox or selectionevent there - that does the populatePanel & the dup checks. Sigh.

        //Meternum is required
        String meterNum = panel.suggestBoxMeterNumber.getValue().trim();
        if (meterNum.isEmpty()) {
            panel.suggestBoxMeterElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
            return;
        }
        if (!panel.isValidData()) {
            form.getSaveBtn().setEnabled(true);
            return;
        }

        form.getSaveBtn().setEnabled(false);
        final Long selectedDeviceStoreId = Long.parseLong(panel.lstbxSelectStore
                .getValue(panel.lstbxSelectStore.getSelectedIndex()));
        clientFactory.getSearchRpc().getMeterOnlineBulkForMeterNum(meterNum, selectedDeviceStoreId, new ClientCallback<MeterOnlineBulkData>() {
            @Override
            public void onSuccess(final MeterOnlineBulkData meterOnlineBulkData) {

                //meterNum not found
                if (meterOnlineBulkData == null) {
                    Dialogs.confirm (
                            new String[]{
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("online.bulk.panel.error.meter.num.not.found"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.continue.new")},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (!confirm) {
                                        panel.clearSomePanelFields(false);
                                        form.getSaveBtn().setEnabled(true);
                                        return;
                                    }
                                    save2CheckIfRequiresValidation(selectedDeviceStoreId, meterOnlineBulkData);
                                }
                            });
                } else {
                    save2CheckIfRequiresValidation(selectedDeviceStoreId, meterOnlineBulkData);
                }
            }
        });
    }

    private void save2CheckIfRequiresValidation(final Long selectedDeviceStoreId,
                                             final MeterOnlineBulkData meterOnlineBulkData) {
        // Check for regex configuration on the required fields and deny creating the meter if regex is configured for these fields (30157)
        clientFactory.getUserInterfaceRpcAsync().getFormFields(new ClientCallback<Map<String, FormFields>>() {
            @Override
            public void onSuccess(Map<String, FormFields> result) {
                if (result.get(UserInterfaceFormFields.CUST_ACC_NAME).getValidationRegex() != null
                        || result.get(UserInterfaceFormFields.UP_NAME).getValidationRegex() != null
                        || result.get(UserInterfaceFormFields.CUST_AGR_REF).getValidationRegex() != null) {

                    Dialogs.displayInformationMessages(new String[] {ResourcesFactoryUtil.getInstance().getMessages()
                            .getMessage("online.bulk.panel.error.regex.validation1"),
                            ResourcesFactoryUtil.getInstance().getMessages()
                            .getMessage("online.bulk.panel.error.regex.validation2")},
                            MediaResourceUtil.getInstance().getErrorIcon());
                    form.getSaveBtn().setEnabled(true);
                } else {
                    if (meterOnlineBulkData == null) {
                        save3CheckIfTariffStartDateIsBeforeInstallationDate();// meterOnlineBulkData is null
                    } else {
                        save2CheckIfMeterAlreadyLinkedToUsagePoint(meterOnlineBulkData, selectedDeviceStoreId);
                    }
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                form.getSaveBtn().setEnabled(true);
            }
        });
    }

    private void save2CheckIfMeterAlreadyLinkedToUsagePoint(final MeterOnlineBulkData meterOnlineBulkData,
                                                            Long selectedDeviceStoreId) {
        //if deviceStoreId is already null, the meter is already linked to a UP
        if (meterOnlineBulkData.getDeviceStoreId() == null ) {
            Dialogs.confirm (
                    new String[]{
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("online.bulk.panel.error.meter.already.linked"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.continue.open.up.page")},
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            panel.clearSomePanelFields(true);
                            form.getSaveBtn().setEnabled(true);
                            if (confirm) {
                                History.newItem(clientFactory.getPlaceHistoryMapper().getToken(new MeterPlace(meterOnlineBulkData.getMeterNum(),
                                        null))); //end here
                            }
                        }
                    });
        } else {
            save3CheckIfMeterInSelectedDeviceStore(meterOnlineBulkData, selectedDeviceStoreId);
        }
    }

    private void save3CheckIfMeterInSelectedDeviceStore(final MeterOnlineBulkData meterOnlineBulkData, Long selectedDeviceStoreId) {
        //check if meter in a different device store to the one selected
        if (!meterOnlineBulkData.getDeviceStoreId().equals(selectedDeviceStoreId)) {
            Dialogs.confirm (
                    new String[]{
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("online.bulk.panel.error.meter.linked.to.diff.store",
                                    new String[] {meterOnlineBulkData.getDeviceStoreName()}),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.continue.save.anyway")},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                //selected device store is not the same as in meterOnlineBulkData, continue save anyway
                                save3CheckIfTariffStartDateIsBeforeInstallationDate();
                            } else {
                                panel.clearSomePanelFields(false);
                                form.getSaveBtn().setEnabled(true); //ends here
                            }
                        }
                    });
        } else { //selected device store is the same as in meterOnlineBulkData, continue save
            save3CheckIfTariffStartDateIsBeforeInstallationDate();
        }
    }

    private void save3CheckIfTariffStartDateIsBeforeInstallationDate() {
        //If installation date is before the tariff starts,
        // tariff charges that are due before tariff start date will not be calculated.
        LookupListItem currentPricingStructureItem =
                panel.currentPricingStructureLookup.getSelectedPricingStructureItem();
        if (currentPricingStructureItem != null) {
            Date installationDate = panel.dtbxMeterInstallationDate.getValue();
            Date firstTariffStartDate = (Date) currentPricingStructureItem.getExtraInfoMap().get("firstTariffStartDate");
            if (installationDate != null && !panel.dtbxMeterInstallationDate.getTextBox().getValue().isEmpty()
                    && installationDate.before(firstTariffStartDate)) {
                Messages messages = MessagesUtil.getInstance();
                Dialogs.confirm(
                        UsagePointWorkspaceView.constructMessageInstallDtVsTariffStart(firstTariffStartDate, null),
                        messages.getMessage("button.yes"), messages.getMessage("button.no"),
                        MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    save4CheckMeterModelAndPricingStructureAreCompatible();
                                } else {
                                    form.getSaveBtn().setEnabled(true); //ends here
                                }
                            }
                        });
            } else {
                save4CheckMeterModelAndPricingStructureAreCompatible();
            }
        } else {
            save4CheckMeterModelAndPricingStructureAreCompatible();
        }
    }

    private void save4CheckMeterModelAndPricingStructureAreCompatible() {
        if (panel.checkMeterModelDirty() || panel.checkPricingStructureDirty()) {
            //check compatibility for regReads billing dets
            LookupListItem selectedMeterModel = panel.lstbxMeterModel.getItem(panel.lstbxMeterModel.getSelectedIndex());
            String hasChannels = (String)selectedMeterModel.getExtraInfoMap().get("hasChannels");
            boolean meterModelHasMdcChannels = Boolean.parseBoolean(hasChannels);
            String mdcIdStr = (String)selectedMeterModel.getExtraInfoMap().get("mdcId");
            Long mdcId = null;
            if (mdcIdStr != null) {
                mdcId = Long.valueOf(mdcIdStr);
            }

            LookupListItem selectedPricingStructure = panel.currentPricingStructureLookup.getSelectedPricingStructureItem();
            String psRegRead = (String)selectedPricingStructure.getExtraInfoMap().get("regReadPS");
            boolean isRegReadPricingStructure = (psRegRead != null && !psRegRead.equals("false")) ? Boolean.TRUE : false; //todo defaults to true?

            if (isRegReadPricingStructure || (meterModelHasMdcChannels && validateAllPricingStructureToMeterModel)) {
                //confirm corresponding mdcChannels billingdets to tariff billingdets
                List<PSDto> pricingStructureDtoList = new ArrayList<>();
                //for currentPS sending in new date and not installDate because on takeOn of data from other systems,
                //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
                pricingStructureDtoList.add(new PSDto(Long.valueOf(selectedPricingStructure.getValue()), new Date()));

                new MeterOnlineBulkValidatePStoMM(this).isregReadPsSameBillingDetsAsMeterModel(clientFactory,
                        pricingStructureDtoList, mdcId, clientFactory.getUser().getUserName(), logger, "save");
                //todo not clear what happens here, is this now the end of the save method?
                // (it actually calls methods on this parent class to continue)
            } else {
                save5ConfirmSendFreeTokenBySms();
            }
        } else {
            save5ConfirmSendFreeTokenBySms();
        }

    }

    //Start Methods for MeterComponentValidatePStoMM when SAVE
    public void saveErrorOrNonMatchPsToMm() {
        resetMeterModelAndPricingStructureSelection();
        form.getSaveBtn().setEnabled(true);
    }
    public void savePartialMatchDenyContinue() {
        form.getSaveBtn().setEnabled(true);
    }
    //End Methods for MeterComponentValidatePStoMM when SAVE

    public void save5ConfirmSendFreeTokenBySms() {
        if (!smsWarning && panel.includesFreeIssueToken() && !panel.isSmsFreeIssueToken()) {
            smsWarning = true;
            Dialogs.confirm (
                    new String[]{
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.online.bulk.free.issue.check.sms.not.selected"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.continue")},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                 save6ValidateGroupSelection();
                            } else {
                                form.getSaveBtn().setEnabled(true);
                            }
                        }
                    });
        } else {
            save6ValidateGroupSelection();
        }
    }

    private void save6ValidateGroupSelection() {
        if (isValidGroupSelection()) {
            panel.setDisplayUnusualMeterMsg(true);

            //if was !isValidGroupSelection() on previous save attempt, then the start & totalResults will be out of kilter for the new selection - so now reset these
            if (forceSelectionRequiredGroups) {
                forceSelectionRequiredGroups = false;
                String sortColumn = getColumnName(3);
                int start = 0;
                final int pageSize = getPageSize();
                String order = "ASC";

                ArrayList<UpGenGroupLinkData> selectedGroupsList = upGroupSelectionPanel.getSelectedGroups();
                clientFactory.getSearchRpc().getOnlineBulkUpDataFromUpGroupSelectionCount(start, pageSize, selectedGroupsList, sortColumn, filterColumnName, filterString, filterDate, order, new ClientCallback<Integer>() {
                    @Override
                    public void onSuccess(Integer result) {
                        if (result == null) {
                            result = 0;
                        }
                        totalResults = result;
                        int start = totalResults + 1 - pageSize;
                        if (start < 0) {
                            start = 0;
                        }
                        save7ConfirmIfChannelsValidationRequired();
                    }
                    @Override
                    public void onFailure(Throwable caught) {
                        form.getSaveBtn().setEnabled(true);
                    }
                });
            } else {
                save7ConfirmIfChannelsValidationRequired();
            }
        } else {
            form.getSaveBtn().setEnabled(true);
        }
    }

    private void save7ConfirmIfChannelsValidationRequired() {
        String hasChannelsStr = (String)panel.lstbxMeterModel.getItem(panel.lstbxMeterModel.getSelectedIndex())
                .getExtraInfoMap().get("hasChannels");
        if (Objects.equals(hasChannelsStr, "true")) {
            panel.mapFormToData();
            validateChannelInfo(panel.getMeterOnlineBulkData());
        } else {
            saveOrUpdate();
        }
    }

    private void validateChannelInfo(final MeterOnlineBulkData selectedMeterOnlineBulkData) {
        final MeterOnlineBulkWorkspaceView parent = this;
        clientFactory.getMeterModelRpc().getMeterModelById(selectedMeterOnlineBulkData.getMeterModelId(),
                new ClientCallback<MeterModelData>() {
            @Override
            public void onSuccess(final MeterModelData mmData) {
                MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(selectedMeterOnlineBulkData.getMeterId(),
                        selectedMeterOnlineBulkData.getMeterModelId(),
                        selectedMeterOnlineBulkData.getUsagePointId(),
                        null,
                        selectedMeterOnlineBulkData.getUpMeterInstallId(),
                        selectedMeterOnlineBulkData.getInstallationDate());
                clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo,
                        new ClientCallback<MeterUpMdcChannelInfo>() {
                    @Override
                    public void onSuccess(MeterUpMdcChannelInfo result) {
                        if (result.getChannelList() == null || result.getChannelList().isEmpty()) {
                            logger.info("getMeterUpMdcChannelInfo: result is null");
                            fireUpdateEvent(null);
                        } else {
                            logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size="
                                    + result.getChannelList().size());

                            //get initial readings for the channels
                            final AssignChannelReadingsDialogueBox assignChannelReadings =
                                    new AssignChannelReadingsDialogueBox(parent, result,
                                            selectedMeterOnlineBulkData.getMeterNum(),
                                            selectedMeterOnlineBulkData.getUsagePointName(),
                                            selectedMeterOnlineBulkData.getMeterModelName(),
                                            selectedMeterOnlineBulkData.getMdcName(),
                                            selectedMeterOnlineBulkData.getCurrentPricingStructureName(),
                                            mmData.getServiceResourceId());
                                            Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                                @Override
                                                public void execute() {
                                                    assignChannelReadings.center();
                                                    assignChannelReadings.show();
                                                }
                                            });
                        }
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        super.onFailure(caught);
                    }
                });
            }

            @Override
            public void onFailure(Throwable caught) {
                super.onFailure(caught);
            }
        });
    }

    /*
     * Save button in AssignChannelReadingsDialogueBox triggers this method hence we Override it here.
     * @see za.co.ipay.metermng.client.view.component.AssignChannelReadingsComponent#fireUpdateEvent
     */
    @Override
    public void fireUpdateEvent(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        if (meterUpMdcChannelInfo != null) {
            List<MdcChannelReadingsDto> channelReadingsList = meterUpMdcChannelInfo.getChannelList();
            if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                panel.getMeterOnlineBulkData().setMdcChannelReadingsDtoList(channelReadingsList);
            }
        }
        saveOrUpdate();
    }

    private void saveOrUpdate() {
        if (form.getSaveBtn().getText().equals(MessagesUtil.getInstance()
                .getMessage("meter.online.bulk.button.add"))) {
            save8SaveMeterOnlineBulkData();
        } else {
            update();
        }
    }

    private void save8SaveMeterOnlineBulkData() {
        panel.mapFormToData();
        panel.mapFormFreeIssueToData();
        panel.getMeterOnlineBulkData().setUpgengroups(upGroupSelectionPanel.getNewUpGenGroupsMap());

        clientFactory.getSearchRpc().saveMeterOnlineBulkData(panel.getMeterOnlineBulkData(), new ClientCallback<MeterOnlineBulkData>() {
            @Override
            public void onSuccess(MeterOnlineBulkData result) {
                form.setDirtyData(false);
                logger.info("MeterOnlineBulkWorkspaceView.onSave(). Added meter=" + result.getMeterNum());
                boolean includesFreeIssueToken = panel.includesFreeIssueToken();

                panel.clearSomePanelFields(true);
                panel.incrementSurnameAndOrSuite();
                panel.suggestBoxMeterNumber.setFocus(true);
                sendNotification(MeterMngStatics.ONLINE_BULK_METER_ADDED, result.getMeterNum());
                form.getSaveBtn().setEnabled(true);

                if (totalResults == null) {            //first time / cleared table
                    populateEmptyTableToAddNew();
                } else {
                    afterSave();
                }

                if (includesFreeIssueToken) {
                    if (result.getTokenData() == null) {
                        logger.info("saveMeterOnlineBulkData(): token is null");
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.free.issue.token.null"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else if (result.getTokenData().getErrorMsg() != null) {
                        logger.info("saveMeterOnlineBulkData(): token has error: resCode=" + result.getTokenData().getResCode()+", errorMessage="+result.getTokenData().getErrorMsg());
                        Dialogs.displayErrorMessage( MessagesUtil.getInstance().getMessage("meter.online.bulk.free.issue.token.error", new String[]{result.getTokenData().getErrorMsg()}),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        clientFactory.getEventBus().fireEvent(new EngineeringTokenIssuedEvent(result.getStsMeter(), TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS));
                    }
                }
            }
            @Override
            public void onFailure(Throwable caught) {
                form.getSaveBtn().setEnabled(true);
            }
        });
    }

    private boolean isValidGroupSelection() {
        if (!upGroupSelectionPanel.areGroupsAllSelected()) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("online.bulk.meter.error.groups.not.selected"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
            forceSelectionRequiredGroups = true;
            return false;
        } else {
            return true;
        }
    }

    private void populateEmptyTableToAddNew() {
        String sortColumnName = getColumnName(table.getColumnIndex(installDateCol));

        int start = 0;
        final int pageSize = getPageSize();
        String order = "ASC";

        ArrayList<UpGenGroupLinkData> selectedGroupsList = upGroupSelectionPanel.getSelectedGroups();
        clientFactory.getSearchRpc().getOnlineBulkUpDataFromUpGroupSelectionCount(start, pageSize, selectedGroupsList, sortColumnName, filterColumnName, filterString, filterDate, order, new ClientCallback<Integer>() {
            @Override
            public void onSuccess(Integer result) {
                if (result == null) {
                    result = 0;
                }
                totalResults = result;
                dataProvider.updateRowCount(totalResults, true);
                table.setRowCount(totalResults, true);
                afterSave();
            }
        });

    }

    private void afterSave() {
        final int pageSize = getPageSize();
        int start = totalResults + 1 - pageSize;
        if (start < 0) {
            start = 0;
        }

        installDateCol.setDefaultSortAscending(true);
        int installDateColIndx = table.getColumnIndex(installDateCol);
        getTableData(start, installDateColIndx, setTableSortOrder(installDateColIndx));
    }

    //*******************************************************************************************************
    private void onUpdate() {
        final MeterOnlineBulkWorkspaceView parent = this;
        panel.clearErrors();
        if (panel.isValidData()) {
            if (panel.checkMeterModelDirty() || panel.checkPricingStructureDirty()) {
                //check compatibility for regReads billing dets
                boolean isMdcChannels = "true".equals(panel.lstbxMeterModel.getItem(panel.lstbxMeterModel.getSelectedIndex()).getExtraInfoMap().get("hasChannels")) ? true : false;

                LookupListItem psLui = panel.currentPricingStructureLookup.getSelectedPricingStructureItem();
                String psRegRead = (String)psLui.getExtraInfoMap().get("regReadPS");
                boolean isRegReadPS = (psRegRead != null && psRegRead != "false") ? Boolean.TRUE : false;

                if (isRegReadPS || (isMdcChannels && validateAllPricingStructureToMeterModel)) {
                    //confirm corresponding mdcChannels billingdets to tariff billingdets
                    List<PSDto> pSDtos = new ArrayList<>();
                    //for currentPS sending in new date and not installDate because on takeOn of data from other systems,
                    //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
                    pSDtos.add(new PSDto(Long.valueOf(Long.valueOf(psLui.getValue())), new Date()));
                    String mdcIdStr = (String) panel.lstbxMeterModel.getItem(panel.lstbxMeterModel.getSelectedIndex()).getExtraInfoMap().get("mdcId");

                    new MeterOnlineBulkValidatePStoMM(parent).isregReadPsSameBillingDetsAsMeterModel(clientFactory, pSDtos,
                            (mdcIdStr == null ? null : Long.parseLong(mdcIdStr)),
                            clientFactory.getUser().getUserName(), logger, "update");

                } else {
                    update();
                }
            } else {
                update();
            }
        }
    }

    //Start Methods for MeterComponentValidatePStoMM when UPDATE
    public void updateErrorOrNonMatchPsToMm() {
        panel.currentPricingStructureLookup.setSavedPricingStructureId(
                panel.getMeterOnlineBulkData().getCurrentPricingStructureId());
    }

    public void updatePassTocheckChannelInfoB4Save() {
        validateChannelInfo(panel.getMeterOnlineBulkData());
    }
    //End Methods for MeterComponentValidatePStoMM when UPDATE

    private void update() {
        panel.setDisplayUnusualMeterMsg(true);
        panel.mapFormToData();
        panel.getMeterOnlineBulkData().setUpgengroups(panel.getEditedUpGenGroupsMap());
        form.getSaveBtn().setEnabled(false);
        clientFactory.getSearchRpc().updateMeterOnlineBulkData(panel.getMeterOnlineBulkData(), new ClientCallback<MeterOnlineBulkData>() {
            @Override
            public void onSuccess(MeterOnlineBulkData result) {
                form.setDirtyData(false);
                logger.info("MeterOnlineBulkWorkspaceView.onUpdate(). Updated meter=" + result.getMeterNum());
                panel.clearAllPanelFields();
                form.getSaveBtn().setEnabled(true);

                table.setVisibleRangeAndClearData(table.getVisibleRange(), true);     //to refresh table range
                sendNotification(MeterMngStatics.ONLINE_BULK_MODIFIED, result.getMeterNum());

                //reset the panel header....
                setPanelAddLabels();

                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.meter.updated",
                        new String[]{result.getMeterNum()}),
                        MediaResourceUtil.getInstance().getInformationIcon());
            }
            @Override
            public void onFailure(Throwable caught) {
                form.getSaveBtn().setEnabled(true);
            }
        });
    }


    public void resetMeterModelAndPricingStructureSelection() {
        //reset meterModel & ps
        MeterOnlineBulkData meterOnlineBulkData = panel.getMeterOnlineBulkData();
        if (meterOnlineBulkData != null) {
            if (meterOnlineBulkData.getMeterModelId() != null) {
                panel.lstbxMeterModel.selectItemByValue(panel.getMeterOnlineBulkData().getMeterModelId().toString());
            }
            if (meterOnlineBulkData.getCurrentPricingStructureId() != null) {
                panel.currentPricingStructureLookup.setSavedPricingStructureId(panel.getMeterOnlineBulkData()
                        .getCurrentPricingStructureId());
            } else {
                panel.currentPricingStructureLookup.clearSelection();
            }
        }
    }
    //*******************************************************************************************************
    private void sendNotification(String dataType, String meterNum) {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED,
                                                    dataType,
                                                    meterNum));
    }

    @Override
    public void onArrival(Place place) {
    }


    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof MeterOnlineBulkPlace);
    }

    @Override
    public void handleNotification(final WorkspaceNotification notification) {
        logger.info("Received notification: "+notification+"  datatype= " + notification.getDataType());
        if (NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (notification.getDataType().equals(MeterMngStatics.METER_MODEL_MODIFIED)){
                panel.populateMeterModelListBox();
            } else if (MeterMngStatics.GROUPS.equals(notification.getDataType())
                    || MeterMngStatics.GROUP_TYPE.equals(notification.getDataType()) ) {
                clearTable();
                //it is not necessary to try & update a treePopup - because the treepopups are modal & glassEnabled - have to close them to go to the GenGroup page
            } else if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(notification.getDataType())) {
                panel.populateLocationGroups();
            } else if (MeterMngStatics.METER_CUST_UP_MODIFIED.equals(notification.getDataType())) {           //from usagePOint Page
                table.setVisibleRangeAndClearData(table.getVisibleRange(), true);
                String meterNum = (String)notification.getObject();
                if (meterNum != null && panel.getMeterOnlineBulkData() != null
                        && meterNum.equals(panel.getMeterOnlineBulkData().getMeterNum())) {
                    panel.clearAllPanelFields();
                    setPanelAddLabels();
                }
            } else if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(notification.getDataType())
                    && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
                 if (((String) notification.getObject()).toLowerCase().contains("engineering.token.issue.user.reference.status")) {
                    panel.getEngineeringTokenUserRefPanel().getAppSettingAndUpdateEngineeringTokenUserRefStatus();
                }
            }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    //**************************************************************************************************
    static class SearchClickableCell extends AbstractCell<String> {
        /**
         * The HTML templates used to render the cell.
         */
        interface Templates extends SafeHtmlTemplates {
          /**
           * The template for this Cell, which includes styles and a value.
           *
           * @param styles the styles to include in the style attribute of the div
           * @param value the safe value. Since the value type is {@link SafeHtml},
           *          it will not be escaped before including it in the template.
           *          Alternatively, you could make the value type String, in which
           *          case the value would be escaped.
           * @return a {@link SafeHtml} instance
           */
            @SafeHtmlTemplates.Template("<div style=\"{0}; cursor: pointer;\">{1}</div>")
            SafeHtml cell(SafeStyles styles, SafeHtml value);
        }

        /**
         * Create a singleton instance of the templates used to render the cell.
         */
        private static Templates templates = GWT.create(Templates.class);

        private PlaceHistoryMapper historyMapper;
        private MeterOnlineBulkPanel mob;
        private MeterOnlineBulkWorkspaceView parent;

        public SearchClickableCell(PlaceHistoryMapper historyMapper, MeterOnlineBulkPanel mob, MeterOnlineBulkWorkspaceView parent) {
          /*
           * Sink the click and keydown events. We handle click events in this
           * class. AbstractCell will handle the keydown event and call
           * onEnterKeyDown() if the user presses the enter key while the cell is
           * selected.
           */
          super("click", "keydown");
          this.historyMapper = historyMapper;
          this.mob = mob;
          this.parent = parent;
        }

        /**
         * Called when an event occurs in a rendered instance of this Cell. The
         * parent element refers to the element that contains the rendered cell, NOT
         * to the outermost element that the Cell rendered.
         */
        @Override
        public void onBrowserEvent(Context context, Element parent, String value, NativeEvent event,
            ValueUpdater<String> valueUpdater) {
          // Let AbstractCell handle the keydown event.
          super.onBrowserEvent(context, parent, value, event, valueUpdater);

          // Handle the click event.
          if ("click".equals(event.getType())) {
              doAction(value, valueUpdater);
          }
        }

        @Override
        public void render(Context context, String value, SafeHtmlBuilder sb) {
          /*
           * Always do a null check on the value. Cell widgets can pass null to
           * cells if the underlying data contains a null, or if the data arrives
           * out of order.
           */
          if (value == null) {
            return;
          }

          // If the value comes from the user, we escape it to avoid XSS attacks.
          SafeHtml safeValue = SafeHtmlUtils.fromString(value);

          // Use the template to create the Cell's html.
          SafeStyles styles = SafeStylesUtils.forTextDecoration(TextDecoration.UNDERLINE);
          SafeHtml rendered = templates.cell(styles, safeValue);
          sb.append(rendered);
        }

        /**
         * onEnterKeyDown is called when the user presses the ENTER key will the
         * Cell is selected. You are not required to override this method, but its a
         * common convention that allows your cell to respond to key events.
         */
        @Override
        protected void onEnterKeyDown(Context context, Element parent, String value, NativeEvent event,
            ValueUpdater<String> valueUpdater) {
          doAction(value, valueUpdater);
        }

        private void doAction(String value, ValueUpdater<String> valueUpdater) {
                  String link = null;
                  if (value != null) {
                      link = historyMapper.getToken(new UsagePointPlace(value, MeterOnlineBulkPlace.getPlaceAsString()));
                  }
                  if (link != null) {
                      History.newItem(link);
                      if (mob != null) {
                          mob.clearAllPanelFields();
                          parent.setPanelAddLabels();
                      }
                      return;
                  }

          // Trigger a value updater. In this case, the value doesn't actually
          // change, but we use a ValueUpdater to let the app know that a value
          // was clicked.
          valueUpdater.update(value);
        }
      }

    @Override
    public void setSelectedGroups(ArrayList<UpGenGroupLinkData> upGenGroupLinkData, boolean copiedFromOtherMeter) {
        upGroupSelectionPanel.setSelectedGroups(upGenGroupLinkData, copiedFromOtherMeter);
    }

}
