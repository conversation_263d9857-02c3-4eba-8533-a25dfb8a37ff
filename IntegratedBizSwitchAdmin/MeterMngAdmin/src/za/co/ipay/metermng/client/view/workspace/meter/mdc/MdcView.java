package za.co.ipay.metermng.client.view.workspace.meter.mdc;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;

public class MdcView implements FormManager<Mdc> {

    private MdcWorkspaceView parentWorkspace;
    private ClientFactory clientFactory;

    private SimpleTableView<Mdc> view;
    private MdcPanel panel;
    private Button viewBtn;

    private AsyncDataProvider<Mdc> dataProvider;
    private Mdc mdc;

    private static Logger logger = Logger.getLogger(MdcView.class.getName());

    public MdcView(ClientFactory clientFactory, MdcWorkspaceView parentWorkspace, SimpleTableView<Mdc> view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
        initView();
        initForm();
        createTable();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new MdcPanel(view.getForm());
        panel.activeBox.setValue(true);

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);
        view.getTable().ensureDebugId("mdcTable");
        view.getForm().getSaveBtn().ensureDebugId("saveButton");
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        viewBtn = new Button(MessagesUtil.getInstance().getMessage("mdc.button.viewchannels"));
        viewBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                final SessionCheckCallback callback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        view.getForm().checkDirtyData(new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    if (view.getForm().isDirtyData()) {
                                        view.getForm().setDirtyData(false);
                                        displayMdc(mdc);
                                    }
                                    onViewClick();
                                }
                            }
                        });
                    }
                };
                clientFactory.handleSessionCheckCallback(callback);
            }
        });
        viewBtn.setVisible(false);
        viewBtn.ensureDebugId("viewBtn");
        view.getForm().getSecondaryButtons().add(viewBtn);
        view.getForm().getSecondaryButtons().setVisible(true);
        view.getForm().getSecondaryButtons().ensureDebugId("secondaryButtonsPanel");

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.mdc.title.add"));
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<Mdc, ?> nameColumn = createNameColumn();
            Column<Mdc, ?> descriptionColumn = createDescriptionColumn();
            StatusTableColumn<Mdc> statusColumn = new StatusTableColumn<Mdc>();
            Column<Mdc, ?> valueColumn = createValueColumn();
            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("meter.mdc.field.name"));
            view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("meter.mdc.field.description"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("meter.mdc.field.status"));
            view.getTable().addColumn(valueColumn, MessagesUtil.getInstance().getMessage("meter.mdc.field.value"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<Mdc>() {
                @Override
                protected void onRangeChanged(HasData<Mdc> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<Mdc, ?> sColumn = (Column<Mdc, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                    }
                    clientFactory.getMdcRpc().getMdcs(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<Mdc>>() {
                                @Override
                                public void onSuccess(ArrayList<Mdc> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<Mdc, ?>) event.getColumn());
                    String sortColumn = getColumnName(sortIndex);
                    boolean isAscending = event.isSortAscending();
                    clientFactory.getMdcRpc().getMdcs(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<Mdc>>() {
                                public void onSuccess(ArrayList<Mdc> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(nameColumn);
        }
    }

    private int getPageSize() {
        return parentWorkspace.getPageSize();
    }

    private String getColumnName(int index) {
        return "name";
    }

    private Column<Mdc, ?> createNameColumn() {
        TextColumn<Mdc> nameColumn = new TextColumn<Mdc>() {
            @Override
            public String getValue(Mdc object) {
                return object.getName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<Mdc, ?> createDescriptionColumn() {
        return new TextColumn<Mdc>() {
            @Override
            public String getValue(Mdc m) {
                return m.getDescription();
            }
        };
    }

    private Column<Mdc, ?> createValueColumn() {
        return new TextColumn<Mdc>() {
            @Override
            public String getValue(Mdc m) {
                return m.getValue();
            }
        };
    }

    public void onArrival(Place place) {
        getMdcCount(false);
    }

    private void getMdcCount(final boolean refreshTable) {
        clientFactory.getMdcRpc().getMdcCount(new ClientCallback<Integer>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }
            @Override
            public void onSuccess(Integer result) {
                logger.fine("getMdcCount(): Got count: " + result + " for dataProvider: " + dataProvider);
                view.getTable().setRowCount(result, true);
                dataProvider.updateRowCount(result, true);
            }
        });
    }

    public void onViewClick() {
        if (mdc != null && mdc.getId() != null) {
                parentWorkspace.showMdcChannels(mdc);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("mdc.error.noneselected"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    @Override
    public void displaySelected(Mdc selected) {
        displayMdc(selected);
    }

    private void displayMdc(Mdc selected) {
        clear();
        this.mdc = selected;
        if (mdc == null) {
            mdc = new Mdc();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.mdc.title.add"));
            viewBtn.setVisible(false);
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.mdc.title.update"));
            viewBtn.setVisible(true);
        }

        panel.nameTextBox.setText(mdc.getName());
        panel.descriptionTextBox.setText(mdc.getDescription());
        panel.activeBox.setValue(RecordStatus.ACT.equals(mdc.getRecordStatus()));
        panel.valueTextBox.setValue(mdc.getValue());
    }

    private void clear() {
        mdc = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.mdc.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
        viewBtn.setVisible(false);
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        Mdc dto = updateMdc();

        if (!ClientValidatorUtil.getInstance().validateField(dto, "name", panel.nameElement)) {
            valid = false;
            logger.info("Invalid dto name: "+dto.getName());
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto, "value", panel.valueElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto, "description", panel.descriptionElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto, "recordStatus", panel.activeElement)) {
            valid = false;
        }

        return valid;
    }

    private Mdc updateMdc() {
        Mdc m = new Mdc();
        updateMdc(m);
        return m;
    }

    private void updateMdc(Mdc dto) {
        if (mdc != null) {
            dto.setId(mdc.getId());
        }
        dto.setName(panel.nameTextBox.getText());
        dto.setDescription(panel.descriptionTextBox.getText());
        dto.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        dto.setValue(panel.valueTextBox.getText());
    }

    private void onSave() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (isValidInput()) {
                    if (mdc != null && view.getForm().isDirtyData()) {
                        clientFactory.getUsagePointRpc().getMdcChannelCompatibility(mdc.getId(), clientFactory.getUser().getUserName(), new ClientCallback<MdcChannelMatchDto>() {
                            @Override
                            public void onSuccess(MdcChannelMatchDto result) {
                                Integer act = result.getActiveUpCount();
                                Integer dac = result.getInactiveUpCount();
                                boolean isOnlyThinUnits = (act + dac) == result.getUpWithThinUnitsPsCount();
                                final Messages messages = MessagesUtil.getInstance();
                                RecordStatus newMdcRecordStatus = panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC;
                                boolean deactivatedMdc = newMdcRecordStatus.equals(RecordStatus.DAC) && !mdc.getRecordStatus().equals(newMdcRecordStatus);
                                logger.info("MdcView: on change mdcChannel or add new one in an MDC: : match = " + result.getMatch().toString() + " countActiveUpWithMdc = " + act.toString() + " inactive=" + dac.toString() + " Potential WARNING or ERROR displayed to user: " + clientFactory.getUser().getUserName());
                                //if any active RegReadPS error, cannot change status or value
                                if (ChannelCompatibilityE.NO_DATA.equals(result.getMatch())) {
                                    //could be TOU PS with an MDC;  ChannelCompatibilityE only compares to regRead, else returns NO_DATA
                                    if (!isOnlyThinUnits && act + dac > 0) {
                                        displayWarningMessage (messages, "warning.change.mdc.NO.DATA", act, dac);
                                    } else {
                                        onSaveContinue();
                                    }
                                } else  if (deactivatedMdc && (act + dac > 0)) {
                                    Dialogs.centreErrorMessage(messages.getMessage("error.deactivated.mdc.active.up", new String[] {act.toString(), dac.toString()}),
                                            MediaResourceUtil.getInstance().getErrorIcon(),
                                            messages.getMessage("button.close"),
                                            new ClickHandler() {
                                                @Override
                                                public void onClick(ClickEvent arg0) {
                                                    displayMdc(mdc);
                                                }
                                            });
                                    return;
                                } else if (act + dac > 0) {
                                    displayWarningMessage (messages, "warning.change.mdc.inactive.with.regreadPS", act, dac);
                                } else {
                                    onSaveContinue();
                                }
                            }
                        });
                    } else {
                        onSaveContinue();
                    }
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void displayWarningMessage (Messages messages, String messageKey, Integer act, Integer dac) {
        Dialogs.confirm(messages.getMessage(messageKey, new String[] {act.toString(), dac.toString()}),
                messages.getMessage("button.yes"),
                messages.getMessage("button.no"),
                MediaResourceUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if(confirm) {
                            onSaveContinue();
                        } else {
                            //redisplay selected
                            displayMdc(mdc);
                            return;
                        }
                    }
                },
                null, null);  //if make position null, will centre confirm
    }

    private void onSaveContinue() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                Mdc m = updateMdc();
                final Long id = m.getId(); // keep track whether the table's total row count is increasing or not
                clientFactory.getMdcRpc().saveMdc(
                        m,
                        new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                            @Override
                            public void onSuccess(Void result) {
                                view.getForm().setDirtyData(false);
                                boolean refresh = false;
                                if (id == null) {
                                    refresh = true;
                                }
                                refreshTable(refresh);
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("meter.mdc.name") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"),
                                        null);
                                sendNotification();
                                clear();
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void sendNotification() {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.MDC_DATA));
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be
    //reloaded due to other changes like disabled user.
    protected void refreshTable(boolean insertedNew) {
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_MDC)) {
            view.getForm().getButtons().removeFromParent();
            viewBtn.removeFromParent();
        }
    }
}
