<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client"
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:component="urn:import:za.co.ipay.metermng.client.view.component">
    
	<ui:style>
		.cellTable {
	      border-bottom: 1px solid #ccc;
	      text-align: left;
	      margin-bottom: 4px;
	    }
	    .mdcMarginLeft {
          margin-left: 20px;
        }    
		.connectDisconnectMarginTop {
          margin-top: 20px;  
        }
        .overrideSelect {
	       width:110px;
	    }
	    .overrideSelect option {
           width:110px;
        }
	</ui:style>
  
   <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
	<g:FlowPanel>
	    
		<g:VerticalPanel>
		    <g:HTML ui:field="dataName" text="Data Name" styleName="dataTitle" />
            <g:HTML ui:field="dataDescription" text="Data Description" styleName="dataDescription" />
		  <g:HorizontalPanel spacing="3">
            <g:Cell verticalAlignment="ALIGN_MIDDLE">
                <g:Label text="{msg.getMeterTxnFilter}:" styleName="gwt-Label-bold"/>
            </g:Cell>
            <g:Cell verticalAlignment="ALIGN_MIDDLE">
                <g:ListBox visibleItemCount="1" name="Filter" ui:field="filterDropdown"/>
            </g:Cell>
            <g:Cell verticalAlignment="ALIGN_MIDDLE">
                <g:TextBox ui:field="txtbxfilter" visible="false"/>
            </g:Cell>
            <component:DateRangeFilterPanel ui:field="dateFilter"/>
            <g:Cell verticalAlignment="ALIGN_MIDDLE" >
                <g:CheckBox text="{msg.getMdcTxnConnectDisconnect}" ui:field="showConnectDisconnect" checked="false" styleName="{style.mdcMarginLeft}" />
            </g:Cell>
            <g:Cell verticalAlignment="ALIGN_MIDDLE">
                <g:CheckBox text="{msg.getMdcTxnBalanceMessages}" ui:field="showBalanceMessages" checked="false" styleName="{style.mdcMarginLeft}" />
            </g:Cell>
        </g:HorizontalPanel>
		
        <c:CellTable ui:field="clltblTransactions"/>
	    <p2:TablePager ui:field="smplpgrTransactions"  styleName="pager" location="CENTER" />
     
        <g:HTMLPanel ui:field="connectDisconnectPanel" styleName="disclosureContent formElementsPanel {style.connectDisconnectMarginTop}" >
            <ipay:FormGroupPanel labelText="{msg.getMdcSendMessageTitle}">
                <ipay:FormRowPanel>
                    <ipay:FormElement ui:field="connectDisconnectElement" helpMsg="{msg.getMeterMessageTypeHelp}" required="true" labelText="{msg.getMeterMessageType}:" >         
                        <p2:IpayListBox debugId="lstbxConnectDisconnect" visibleItemCount="1" ui:field="lstbxConnectDisconnect" styleName="gwt-ListBox-ipay" multipleSelect="false"  />
                    </ipay:FormElement>
                </ipay:FormRowPanel>
                <ipay:FormRowPanel ui:field="mdcOverridePanel">
                    <ipay:FormElement ui:field="mdcOverrideElement" helpMsg="{msg.getMdcTxnOverrideHelp}" labelText="{msg.getMdcTxnOverrideLbl}:" >         
                        <p2:IpayListBox debugId="lstbxMdcOverride" visibleItemCount="1" ui:field="lstbxMdcOverride" styleName="gwt-ListBox-ipay {style.overrideSelect}" multipleSelect="false"/>
                    </ipay:FormElement>
                    </ipay:FormRowPanel>
                <ipay:FormRowPanel ui:field="powerLimitPanel">
                    <ipay:FormElement ui:field="powerLimitElement" visible="false" helpMsg="{msg.getMeterPowerLimitHelp}" labelText="{msg.getMeterPowerLimit}:">
                         <p2:IpayListBox ui:field="powerLimitListBox" visibleItemCount="1" styleName="gwt-ListBox-ipay" width="100px" debugId="powerLimitListBoxMdcTxn" multipleSelect="false"/>
                    </ipay:FormElement>
                </ipay:FormRowPanel>
                <ipay:FormRowPanel ui:field="relayPanel">
                    <ipay:FormElement ui:field="relaySelectionElement" visible="false" helpMsg="{msg.getMdcTransactionRelayHelp}" labelText="{msg.getMdcTransactionRelayTitle}:"> 
                        <g:RadioButton ui:field="relayRadioMain" name="relayRadioGroup" value="false" enabled="true" text="{msg.getMdcTransactionRelayMain}" debugId="relayMainOption"/>
                        <g:RadioButton ui:field="relayRadioAuxOne" name="relayRadioGroup" value="false" enabled="true" text="{msg.getMdcTransactionRelayAuxOne}" debugId="relayAuxOptionOne"/>
                    </ipay:FormElement>
                </ipay:FormRowPanel>
             </ipay:FormGroupPanel> 
         </g:HTMLPanel>
         <g:HorizontalPanel spacing="5" ui:field="mdcMessageButtons">
                 <g:Button ui:field="btnSend" text="{msg.getSendButton}" />
                 <g:Button ui:field="btnCancel" text="{msg.getCancelButton}" />
         </g:HorizontalPanel>
	</g:VerticalPanel>
	</g:FlowPanel>
</ui:UiBinder> 