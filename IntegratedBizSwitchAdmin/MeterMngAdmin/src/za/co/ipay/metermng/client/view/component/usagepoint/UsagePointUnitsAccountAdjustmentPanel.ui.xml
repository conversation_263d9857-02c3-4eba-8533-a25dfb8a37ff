<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form">

	<ui:style>
	.unitsSymbol {
		padding-left: 2px;
		padding-top: 3px;
	}
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<ipay:FormGroupPanel ui:field="usagePointUnitsAccountPanel" debugId="usagePointUnitsAccountPanel" labelText="{msg.getUnitsAccount}">
		<ipay:FormRowPanel>
			<ipay:FormElement debugId="accRefElement" ui:field="accRefElement" labelText="{msg.getCustomerTxnAccRef}">
				<g:TextBox debugId="txtbxAccRef" ui:field="txtbxAccRef" />
			</ipay:FormElement>
			<ipay:FormElement debugId="ourRefElement" ui:field="ourRefElement" labelText="{msg.getCustomerTxnOurRef}" required="true">
				<g:TextBox debugId="txtbxOurRef" ui:field="txtbxOurRef" />
			</ipay:FormElement>
		</ipay:FormRowPanel>
		<ipay:FormRowPanel>
			<ipay:FormElement debugId="amtElement" ui:field="amtElement" labelText="{msg.getUsagePointTxnAmt}" required="true">
				<g:HorizontalPanel>
					<ipay:BigDecimalValueBox debugId="txtbxAmt" ui:field="txtbxAmt" styleName="gwt-TextBox largeNumericInput" />
					<g:Label ui:field="unitsSymbolLabel" styleName="{style.unitsSymbol}" />
				</g:HorizontalPanel>
			</ipay:FormElement>
		</ipay:FormRowPanel>
		<ipay:FormRowPanel>
			<ipay:FormElement debugId="commentElement" ui:field="commentElement" labelText="{msg.getCustomerTxnComment}">
				<g:TextBox debugId="txtbxComment" ui:field="txtbxComment" visibleLength="40" />
			</ipay:FormElement>
		</ipay:FormRowPanel>
	</ipay:FormGroupPanel>
</ui:UiBinder> 
