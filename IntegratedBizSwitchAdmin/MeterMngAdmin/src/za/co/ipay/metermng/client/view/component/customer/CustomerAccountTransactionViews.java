package za.co.ipay.metermng.client.view.component.customer;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.MultipleViewsFormPanel;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;

public class CustomerAccountTransactionViews extends BaseComponent {

    public static final int REPORT_TAB_HEIGHT = 900;
    public static final int GRAPH_TAB_HEIGHT = 550;

    @UiField
    MultipleViewsFormPanel multipleViewsForm;

    private CustomerAccountTransactionChart graphPanel;
    private CustomerAccountTransactionView tablePanel;
    private int tabLayoutPanelWidth;
    private int tabLayoutPanelHeight;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private boolean viewConstructed = false;

    private static Logger logger = Logger.getLogger(CustomerAccountTransactionViews.class.getName());
    private static CustomerAccountTransactionUiBinder uiBinder = GWT.create(CustomerAccountTransactionUiBinder.class);

    interface CustomerAccountTransactionUiBinder extends UiBinder<Widget, CustomerAccountTransactionViews> {
    }

    public CustomerAccountTransactionViews(UsagePointWorkspaceView usagePointWorkspaceView, ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        initWidget(uiBinder.createAndBindUi(this));
        resizeTabPanel();
        initViews();

        logger.info("Created CustomerAccountTransactionViews");
    }

    public void resizeTabPanel() {

        tabLayoutPanelWidth = Window.getClientWidth() - (Window.getClientWidth() / 5);
        tabLayoutPanelHeight = REPORT_TAB_HEIGHT;
        multipleViewsForm.getTabLayoutPanel().setWidth(tabLayoutPanelWidth + "px");
        multipleViewsForm.getTabLayoutPanel().setHeight(tabLayoutPanelHeight + "px");
    }

    private void initViews() {
        initHeader();
        initForm();
        initGraphUi();
        initReportUi();

        initHandlers();
        multipleViewsForm.getTabLayoutPanel().selectTab(1, true);
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initHandlers() {
        multipleViewsForm.getTabLayoutPanel().addSelectionHandler(new SelectionHandler<Integer>() {
            @Override
            public void onSelection(SelectionEvent<Integer> event) {
                if (event.getSelectedItem().equals(0)) {

                    multipleViewsForm.getTabLayoutPanel().setHeight(GRAPH_TAB_HEIGHT + "px");
                } else if (event.getSelectedItem().equals(1)) {

                    multipleViewsForm.getTabLayoutPanel().setHeight(REPORT_TAB_HEIGHT + "px");
                }
            }
        });
    }

    private void initHeader() {
        multipleViewsForm.removeHeader();
    }

    private void initForm() {
        multipleViewsForm.getForm().removeFromParent();
    }

    private void initGraphUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.graph"));
        graphPanel = new CustomerAccountTransactionChart(clientFactory, getTabLayoutPanelWidth(), getTabLayoutPanelHeight());
        ScrollPanel scroll = new ScrollPanel(graphPanel);
        scroll.addStyleName("multipleView");
        multipleViewsForm.getTabLayoutPanel().add(scroll, tab);
    }

    private void initReportUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.table"));
        tablePanel = new CustomerAccountTransactionView(usagePointWorkspaceView, null, clientFactory);
        multipleViewsForm.getTabLayoutPanel().add(tablePanel, tab);
    }

    public CustomerAccountTransactionView getTablePanel() {
        return tablePanel;
    }

    public int getTabLayoutPanelWidth() {
        return tabLayoutPanelWidth;
    }

    public int getTabLayoutPanelHeight() {
        return tabLayoutPanelHeight;
    }

    public CustomerAccountTransactionChart getGraphPanel() {
        return graphPanel;
    }
}
