package za.co.ipay.metermng.client.view.component.tariff;

import com.google.gwt.user.client.ui.IsWidget;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;

public interface ITariffUIClass extends IsWidget {

    public void setCalcContents(String calcContents);
    public String getCalcContents();

    public void setTariffData(ITariffData tariffData);
    public ITariffData getTariffData();
    public boolean tariffDataRequired();

    public void setTariffInitData(ITariffInitData tariffInitData);

    public void clearForm();
    public void clearErrors();

    public void setForm(SimpleForm form);
    public void removeForm();

    public void setFormReadOnly(boolean readOnly);

    void setCalcTemplate(String calcTemplate);
    String getCalcTemplate();
}
