<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>
		.spacing { padding:7px; width: 95%};
		.tokenheading {padding-top: 2px;
    padding-bottom: 1px;
    font-size: 1.4em;
    font-style: normal;
    font-weight: bold;
    text-align: center;}
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
        <g:SimplePanel styleName="{style.spacing}">
	        <g:FlowPanel styleName="breadCrumbFonts">
	            <g:Label ui:field="heading"  />
	        </g:FlowPanel>
        </g:SimplePanel>
        <g:VerticalPanel spacing="7">
	        <ipay:FormElement ui:field="tokenIssueListboxElement" helpMsg="{msg.getMeterSelectTokenTypeHelp}" labelText="{msg.getMeterSelectTokenType}:">
	            <g:ListBox ui:field="tokenIssueListbox"></g:ListBox> 
	        </ipay:FormElement>
	        <ipay:FormElement ui:field="tokenIssueMeterNumberElement" helpMsg="{msg.getMeterNumberOptionalHelp}" labelText="{msg.getMeterNumberOptional}:">
                <g:TextBox debugId="meterNumBox" ui:field="txtbxMeterNumber" styleName="gwt-TextBox" visibleLength="15" />
            </ipay:FormElement>
            <ipay:FormElement ui:field="tokenIssueManufacurercodelengthElement" helpMsg="{msg.getMeterManufacturerCodeLengthHelp}" labelText="{msg.getMeterManufacturerCodeLength}">
                 <g:RadioButton ui:field="tdigitMc" name="manufacturerCode" checked="true" text="{msg.getMeter2DigitManufacturerCode}"></g:RadioButton>
                 <g:RadioButton ui:field="fdigitMc" name="manufacturerCode" text="{msg.getMeter4DigitManufacturerCode}">Option</g:RadioButton>
            </ipay:FormElement>
	        <g:Button ui:field="btnGetToken" text="{msg.getTokenButton}" />      
	        <g:FlowPanel visible="false" ui:field="theTokenPanel">
	            <g:Label ui:field="theTokenName" styleName="{style.tokenheading}"/>
	            <g:Label ui:field="theTokenCode" styleName="subheading"/>
	        </g:FlowPanel>
	       
	    </g:VerticalPanel>
    </g:FlowPanel>
	

</ui:UiBinder> 