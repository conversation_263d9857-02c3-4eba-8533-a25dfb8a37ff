package za.co.ipay.metermng.client.view.component.group;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

public class GroupHierarchyPanel extends SimpleFormPanel {
    
    @UiField FormElement nameElement;
    @UiField TextBox nameBox;
    @UiField FormElement isAccessGroupElement;
    @UiField CheckBox isAccessGroupBox;

    private static GroupHierarchyPanelUiBinder uiBinder = GWT.create(GroupHierarchyPanelUiBinder.class);

    interface GroupHierarchyPanelUiBinder extends UiBinder<Widget, GroupHierarchyPanel> {
    }

    public GroupHierarchyPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    @Override
    public void addFieldHandlers() {
        nameBox.addChangeHandler(new FormDataChangeHandler(form));
        isAccessGroupBox.addClickHandler(new FormDataClickHandler(form));
    }

    @Override
    public void clearFields() {
        form.setDirtyData(false);
        nameBox.setText("");
        isAccessGroupBox.setValue(false);
    }

    @Override
    public void clearErrors() {
        nameElement.clearErrorMsg();
        isAccessGroupElement.clearErrorMsg();
    }
}
