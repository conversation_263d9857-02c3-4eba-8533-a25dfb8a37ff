package za.co.ipay.metermng.client.view.component.tariff.impl.samoasts;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.component.tariff.impl.BaseTariffView;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.tariff.BlockDto;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.block.Block;
import za.co.ipay.metermng.shared.tariff.samoa.SamoaSTSCalcContents;

public class SamoaSTSTariffView extends BaseTariffView implements ITariffUIClass {

    private static SamoaSTSTariffUiBinder uiBinder = GWT.create(SamoaSTSTariffUiBinder.class);
        
    @UiField FormElement minVendAmountElement;
    @UiField Label minVendAmountCurrencyLabel;
    @UiField BigDecimalValueBox minVendAmountBox;
    
    @UiField FormElement debtChargeElement;
    @UiField Label debtChargeCurrencyLabel;
    @UiField BigDecimalValueBox debtChargeTextBox;

    @UiField FormElement energyChargeElement;
    @UiField Label energyChargeCurrencyLabel;
    @UiField BigDecimalValueBox energyChargeTextBox;

    @UiField FormElement blocksElement;
    @UiField CellTable<BlockDto> blocksTable;

    private ListDataProvider<BlockDto> blocksDataProvider;
    private DecimalInputCell unitPriceCell;
    private DecimalInputCell thresholdCell;
    private String unitSymbol = "kWh";

    interface SamoaSTSTariffUiBinder extends UiBinder<Widget, SamoaSTSTariffView> {
    }

    public SamoaSTSTariffView() {
        initWidget(uiBinder.createAndBindUi(this));
        createCurrencyLabels(); 
        createBlocksTable();
        clearForm();
    }
    
    private void createCurrencyLabels() {
        minVendAmountCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        debtChargeCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol()); 
        energyChargeCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol()); 
        if (FormatUtil.getInstance().isRightToLeft()) {
            minVendAmountCurrencyLabel.setStyleName("btCurrency-right");
            debtChargeCurrencyLabel.setStyleName("btCurrency-right"); 
            energyChargeCurrencyLabel.setStyleName("btCurrency-right"); 
        } else {
            minVendAmountCurrencyLabel.setStyleName("btCurrency-left");
            debtChargeCurrencyLabel.setStyleName("btCurrency-left"); 
            energyChargeCurrencyLabel.setStyleName("btCurrency-left");
        }
    }
    
    public void clearForm() {
        minVendAmountBox.setValue(null);
        debtChargeTextBox.setValue(null);
        energyChargeTextBox.setValue(null);
        blocksDataProvider.getList().clear();
        blocksDataProvider.flush();
        initBlocksDefaultTableRows();
        clearErrors();
    }

    public void clearErrors() {
        minVendAmountElement.clearErrorMsg();
        debtChargeElement.clearErrorMsg();
        energyChargeElement.clearErrorMsg();
    }
    
    @Override
    public void setTariffData(ITariffData tariffData) {
        SamoaSTSCalcContents calcContents = (SamoaSTSCalcContents) tariffData;
        minVendAmountBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getMinVendAmount()));
        debtChargeTextBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getDebtCharge()));
        energyChargeTextBox.setText(FormatUtil.getInstance().formatDecimal(calcContents.getEnergyCharge()));

        //blocks
        blocksDataProvider.getList().clear();
        ArrayList<BlockDto> dtos = new ArrayList<BlockDto>(calcContents.getBlocks().size());
        for(int i=0;i<calcContents.getBlocks().size();i++) {
            dtos.add(new BlockDto(calcContents.getBlocks().get(i), (i+1)));
        }
        blocksDataProvider.getList().addAll(dtos);
        initBlocksDefaultTableRows();
    }
    
    @Override
    public void setCalcContents(String contents) {
    }

    @Override
    public boolean tariffDataRequired() {
        return true;
    }

    @Override
    public ITariffData getTariffData() {
        HasError hasError = new HasError();

        HasBigDecimal minVendAmt = new HasBigDecimal();
        validateBigDecimal(hasError, minVendAmt, minVendAmountBox, minVendAmountElement, false);

        HasBigDecimal hasDebtCharge = new HasBigDecimal();
        validateBigDecimal(hasError, hasDebtCharge, debtChargeTextBox, debtChargeElement, true);
        
        HasBigDecimal hasEnergyCharge = new HasBigDecimal();
        validateBigDecimal(hasError, hasEnergyCharge, energyChargeTextBox, energyChargeElement, true);

        List<Block> blocks = getAndValidateBlocks(hasError);
        
        if (hasError.error) {
            return null;
        }

        SamoaSTSCalcContents  cc = new SamoaSTSCalcContents();
        cc.setMinVendAmount(minVendAmt.value);
        cc.setDebtCharge(hasDebtCharge.value);
        cc.setEnergyCharge(hasEnergyCharge.value);
        cc.setBlocks(blocks);
        return cc;
    }
    
    @Override
    public String getCalcContents() {
        // gets past validator will be replaced on server
        return "placeholder";
    }
    
    private static class HasError {
        boolean error;
    }

    private static class HasBigDecimal {
        BigDecimal value;
    }
    
    private List<Block> getAndValidateBlocks(HasError hasError) {
        boolean valid = true;
        //Blocks
        boolean validBlocks = true;
        int count = 0;
        for(BlockDto b : blocksDataProvider.getList()) {
            if ((b.getBlock().getUnitPrice() == null && b.getBlock().getThreshold() != null)) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.incomplete"));
                break;
            } else if (b.getBlock().getUnitPrice() != null) {
                if (b.getBlock().getUnitPrice().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else if (count > 0 && b.getBlock().getUnitPrice().compareTo(BigDecimal.ZERO) == 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.zero.error"));
                    break;
                } else if (b.getBlock().getThreshold() != null && b.getBlock().getThreshold().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else {
                    count++;
                }
            }
        }
        //Repack the blocks
        ArrayList<Block> newBlocks = new ArrayList<Block>();
        for(BlockDto b : blocksDataProvider.getList()) {
            if (b.getBlock().getUnitPrice() != null) {
                newBlocks.add(b.getBlock());
            }
        }
        //Get the last block with no threshold and check all the ones before that have a threshold set
        int lastIndex = newBlocks.size() - 1;
        for(int i=newBlocks.size()-1; i>=0; i--) {
            Block b = newBlocks.get(i);
            if (b.getUnitPrice() != null && b.getThreshold() == null) {
                if (i != lastIndex) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last"));
                    break;
                }    
            }
        }
            
        if (newBlocks.size() > 0) {
            Block lastBlock = newBlocks.get(newBlocks.size() - 1);
            if (lastBlock.getThreshold() != null) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last.none"));
            }
        }
        
        //now check that each succeeding threshold is > than one before
        BigDecimal previousThreshold = BigDecimal.ZERO;
        if (valid && validBlocks) {
            for(int i=0; i < newBlocks.size(); i++) {
                Block b = newBlocks.get(i);
                if (b.getThreshold() != null && b.getThreshold().compareTo(previousThreshold) < 1) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.increasing.thresholds"));
                    break;
                }
                previousThreshold = b.getThreshold();
            }    
        }
    
        //Valid blocks?
        if (count == 0 && validBlocks) {
            valid = false;
            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.none"));
        }
        if(!valid) {
            hasError.error = true;
            return null;
        }
        return newBlocks;
    }

    private void validateBigDecimal(HasError hasError, HasBigDecimal hasBd, BigDecimalValueBox valueBox, FormElement formElement, boolean required) {
        try {
            BigDecimal value = valueBox.getValue();
            if(value == null) {
                if(required) {
                    hasError.error = true;
                    formElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required")); 
                }
                return;
            }
            if(value.compareTo(BigDecimal.ZERO) < 0) {
                hasError.error = true; 
                formElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
            }
            hasBd.value = value;
        } catch (Exception e) {
            hasError.error = true;
            formElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }
    }

    private void createBlocksTable() {    
        if (blocksDataProvider == null) {            
            blocksDataProvider = new ListDataProvider<BlockDto>();               

            //BlockDto count
            TextColumn<BlockDto> countColumn = new TextColumn<BlockDto>() {
                @Override
                public String getValue(BlockDto data) {
                    return data.getPosition()+"";
                }                
            };  

            //Unit price
            if (FormatUtil.getInstance().isRightToLeft()) {
                unitPriceCell = new DecimalInputCell("", FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
            } else {
                unitPriceCell = new DecimalInputCell(FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
            final Column<BlockDto, String> unitPrice = new Column<BlockDto, String>(unitPriceCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getUnitPrice() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getUnitPrice());
                }                
            };            
            unitPrice.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && !value.isEmpty() && unitPriceCell.isNumeric(value)) {
                        getForm().setDirtyData(true);                    
                        block.getBlock().setUnitPrice(FormatUtil.getInstance().parseDecimal(value));
                        unitPriceCell.getViewData(block).setInvalid(false);
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(unitPrice));
                    } else {
                        unitPriceCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(unitPrice));
                    }
                }
            });

            //Threshold
            thresholdCell = new DecimalInputCell("", unitSymbol, MessagesUtil.getInstance().getMessage("error.numeric.value"));
            final Column<BlockDto, String> threshold = new Column<BlockDto, String>(thresholdCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getThreshold() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getThreshold());
                }                
            };            
            threshold.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && !value.isEmpty() && thresholdCell.isNumeric(value)) {
                        getForm().setDirtyData(true);                    
                        block.getBlock().setThreshold(FormatUtil.getInstance().parseDecimal(value));
                        thresholdCell.getViewData(block).setInvalid(false);
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(threshold));
                    } else {
                        thresholdCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(threshold));
                    }
                }
            });

            // Add the columns
            blocksTable.addColumn(countColumn, MessagesUtil.getInstance().getMessage("tariff.field.block.single"));
            blocksTable.addColumn(unitPrice, MessagesUtil.getInstance().getMessage("tariff.field.unitprice"));
            blocksTable.addColumn(threshold, MessagesUtil.getInstance().getMessage("tariff.field.threshold"));
            blocksTable.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);            
            blocksDataProvider.addDataDisplay(blocksTable);

            blocksTable.getColumn(0).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

            initBlocksDefaultTableRows();
        }
    }

    private void initBlocksDefaultTableRows() {
        int count = blocksDataProvider.getList().size();
        if (count < MeterMngStatics.ALLOWED_BLOCKS) {
            for(int i=count;i<MeterMngStatics.ALLOWED_BLOCKS;i++) {
                blocksDataProvider.getList().add(new BlockDto(new Block(), (i+1)));
            }
        }
    }


    @Override
    protected void addFieldHandlers() {
        minVendAmountBox.addChangeHandler(new FormDataChangeHandler(form));
        debtChargeTextBox.addChangeHandler(new FormDataChangeHandler(form));
        energyChargeTextBox.addChangeHandler(new FormDataChangeHandler(form));
    }

    @Override
    public void setFormReadOnly(boolean readOnly) {
    }
}