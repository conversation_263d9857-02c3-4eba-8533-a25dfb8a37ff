package za.co.ipay.metermng.client.i18n;

import java.util.Map;

import com.google.gwt.i18n.client.LocalizableResource.Key;

/**
 * UiMessages provides the i18n messages that are used within the UiBinder's XML files. The expression language in the
 * XML files do not allow methods with arguments so the usual Messages methods that accept the message's key could not
 * be used. Instead this interface allow these specific methods and their corresponding keys to be defined.
 * The actual implementation class is generated by GWT using our UiMessagesGenerator class when the module is compiled.
 * Add any method here that you require in your UiBinder XML files and use the UiFactory method in the BaseWorkspace
 * class to access the singleton instance.
 *
 * <AUTHOR>
 */
public interface UiMessages {

    //This is used to set the messages that are backing the UiMessages
    void setMessages(Map<String, String> messages);

    @Key("dashboard.groups.added.graph.title")
    String getUsagePointGroupsAddedTitle();
    @Key("dashboard.groups.added.graph.description")
    String getUsagePointGroupsAddedDescription();

    @Key("dashboard.key.indicator.title")
    String getDashboardKeyIndicatorTitle();
    @Key("dashboard.key.indicator.description")
    String getDashboardKeyIndicatorDescription();

    @Key("unit.kilowatthour.symbol")
    String getUnitKilowatthourSymbol();
    @Key("unit.watts.symbol")
    String getUnitWattsSymbol();
    @Key("unit.percent")
    String getPercent();
    @Key("menu.about_link")
    String getMenuAboutLink();
    @Key("changes_unsaved")
    String getChangesUnsaved();

    @Key("workspace.usagepoint.information")
    String getWorkspaceUsagepointInformation();
    @Key("workspace.usagepoint.overview")
    String getWorkspaceUsagepointOverview();
    @Key("workspace.usagepoint.actions")
    String getWorkspaceUsagepointActions();
    @Key("user.custom.fields.title")
    String getUserCustomFieldsTitle();

    @Key("menu.search")
    String getMenuSearch();
    @Key("button.save")
    String getSaveButton();
    @Key("button.new")
    String getNewButton();
    @Key("button.edit")
    String getEditButton();
    @Key("button.back")
    String getBackButton();
    @Key("button.cancel")
    String getCancelButton();
    @Key("button.close")
    String getCloseButton();
    @Key("button.select")
    String getSelectButton();
    @Key("button.delete")
    String getDeleteButton();
    @Key("button.viewentity")
    String getViewEntityButton();
    @Key("button.view")
    String getViewButton();
    @Key("button.replacemeter")
    String getReplaceMeterButton();
    @Key("button.removemeter")
    String getRemoveMeterButton();
    @Key("button.gettoken")
    String getTokenButton();
    @Key("button.saveaccount")
    String getSaveAccountButton();
    @Key("button.addnew")
    String getAddNewButton();
    @Key("button.editchargeschedule")
    String getEditChargeScheduleButton();
    @Key("button.search")
    String getSearchButton();
    @Key("button.clear")
    String getClearButton();
    @Key("button.add")
    String getAddButton();
    @Key("button.remove")
    String getRemoveButton();
    @Key("button.update")
    String getUpdateButton();
    @Key("button.login")
    String getLoginButton();
    @Key("button.logout")
    String getLogoutButton();
    @Key("button.set")
    String getSetButton();
    @Key("button.show.inherited")
    String getShowInheritedButton();
    @Key("button.send")
    String getSendButton();
    @Key("button.done")
    String getDoneButton();
    @Key("button.export")
    String getExportButton();
    @Key("button.clear.groups")
    String getClearGroupsButton();
    @Key("button.submit")
    String getSubmitButton();

    @Key("permission.edit.denied")
    String getEditPermissionDenied();

    @Key("supplygroups.header")
    String getSupplyGroupsHeader();
    @Key("supplygroups.title")
    String getSupplyGroupsTitle();
    @Key("supplygroup.title")
    String getSupplyGroupTitle();
    @Key("supplygroup.field.name")
    String getSupplyGroupName();
    @Key("supplygroup.field.code")
    String getSupplyGroupCode();
    @Key("supplygroup.field.code.default")
    String isDefaultSupplyGroup();
    @Key("supplygroup.field.keyrevisionnumber")
    String getSupplyGroupKeyRevisionNumber();
    @Key("supplygroup.field.keyexpirynumber")
    String getSupplyGroupKeyExpiryNumber();
    @Key("supplygroup.field.active")
    String getSupplyGroupActive();
    @Key("supplygroup.field.kmc.expirydate")
    String getSupplyGroupKmcExpiry();
    @Key("supplygroup.panel.kmc.expirydate.help")
    String getSupplyGroupKmcExpiryHelp();
    @Key("supplygroup.base.date.label")
    String getSgcBaseDateLabel();
    @Key("supplygroup.base.date.label.help")
    String getSgcBaseDateLabelHelp();
    @Key("supply.group.target.label")
    String getSupplyGroupTargetLabel();
    @Key("supply.group.target.label.help")
    String getSupplyGroupTargetLabelHelp();
    @Key("supply.group.in.use.error.lbl")
    String getIsInUseByMeterLbl();
    @Key("grouptypeshierarchies.title")
    String getGroupTypesAndHierarchies();
    @Key("grouptypes.header")
    String getGroupTypesHeader();
    @Key("grouptypes.title")
    String getGroupTypesTitle();
    @Key("grouptype.title")
    String getGroupTypeTitle();
    @Key("grouptype.field.name")
    String getGroupTypeName();
    @Key("grouptype.field.description")
    String getGroupTypeDescription();
    @Key("grouptype.field.name.help")
    String getGroupTypeNameHelp();
    @Key("grouptype.field.description.help")
    String getGroupTypeDescriptionHelp();
    @Key("grouptype.field.active")
    String getGroupTypeActive();
    @Key("grouptype.field.active.help")
    String getGroupTypeActiveHelp();
    @Key("grouptype.field.parent")
    String getGroupTypeParent();
    @Key("grouptype.field.required")
    String getGroupTypeRequired();
    @Key("grouptype.field.required.help")
    String getGroupTypeRequiredHelp();
    @Key("grouptype.field.accessgroup")
    String getGroupTypeAccessGroup();
    @Key("grouptype.field.parent.help")
    String getGroupTypeParentHelp();
    @Key("grouptype.button.viewhierarchies")
    String getViewHierarchiesButton();
    @Key("group.new.instructions")
    String getNewGroupInstructions();
    @Key("group.new.for")
    String getNewGroupFor();
    @Key("group.edit")
    String getGroupEdit();
    @Key("grouptype.accessgroup.help")
    String getAccessGroupTypeHelp();
    @Key("grouptype.field.locationgroup")
    String getGroupTypeLocationGroup();
    @Key("grouptype.locationgroup.help")
    String getLocationGroupTypeHelp();
    @Key("grouptype.field.layout.order")
    String getGroupTypeLayoutOrder();
    @Key("grouptype.field.layout.order.help")
    String getGroupTypeLayoutOrderHelp();
    @Key("grouptype.field.feature")
    String getGroupTypeFeature();
    @Key("grouptype.field.available.feature.help")
    String getAvailableGroupFeatureHelp();
    @Key("grouptype.field.available.feature")
    String getAvailableGroupFeatures();
    @Key("grouptype.field.assigned.feature.help")
    String getAssignedGroupFeatureHelp();
    @Key("grouptype.field.assigned.feature")
    String getAssignedGroupFeatures();
    @Key("grouphierarchies.header")
    String getGroupHierarchiesHeader();
    @Key("grouphierarchies.title")
    String getGroupHierarchiesTitle();
    @Key("grouphierarchy.title")
    String getGroupHierarchyTitle();
    @Key("grouphierarchy.field.name")
    String getGroupHierarchyName();
    @Key("grouphierarchy.field.name")
    String getGroupHierarchyNameHelp();
    @Key("grouphierarchy.field.description")
    String getGroupHierarchyDescription();
    @Key("grouphierarchy.field.active")
    String getGroupHierarchyActive();
    @Key("grouphierarchy.field.parent")
    String getGroupHierarchyParentName();
    @Key("grouphierarchy.field.is_access_group")
    String getIsAccessGroup();
    @Key("grouphierarchy.field.is_access_group.help")
    String getIsAccessGroupHelp();
    @Key("groupnode.field.access_group")
    String getGroupNodeAccessGroup();
    @Key("groupnode.field.access_group.help")
    String getGroupNodeAccessGroupHelp();

    @Key("usagepointgroups.header")
    String getUsagePointGroupsHeader();
    @Key("usagepointgroups.title")
    String getUsagePointGroupsTitle();
    @Key("usagepointgroups.instructions")
    String getUsagePointGroupsInstructions();
    @Key("usagepointgroup.title")
    String getUsagePointGroupTitle();
    @Key("usagepointgroup.field.name")
    String getUsagePointGroupName();
    @Key("usagepointgroup.field.active")
    String getUsagePointGroupActive();
    @Key("usagepointgroup.help.grouptype")
    String getGenGroupGroupTypeHelp();
    @Key("usagepointgroup.field.description")
    String getUsagePointGroupDescription();
    @Key("usagepointgroup.field.parent")
    String getUsagePointGroupParent();
    @Key("usagepointgroup.field.name.help")
    String getUsagePointGroupNameHelp();
    @Key("usagepointgroup.field.description.help")
    String getUsagePointGroupDescriptionHelp();
    @Key("usagepointgroup.field.parent.help")
    String getUsagePointGroupParentHelp();
    @Key("usagepointgroup.field.status.help")
    String getUsagePointGroupStatusHelp();

    @Key("usagepoint.groups.title")
    String getUsagePointAllGroupsTitle();
    @Key("usagepoint.info.title")
    String getUsagePointInfoTitle();
    @Key("usagepoint.title")
    String getUsagePointTitle();
    @Key("usagepoint.show.info")
    String getUsagePointShowInfo();
    @Key("usagepoint.showing.info")
    String getUsagePointShowingInfo();
    @Key("usagepoint.field.active.help")
    String getUsagePointActiveHelp();
    @Key("usagepoint.field.active")
    String getUsagePointActive();
    @Key("usagepoint.field.activated_date.help")
    String getUsagePointActivatedDateHelp();
    @Key("usagepoint.field.activated_date")
    String getUsagePointActivatedDate();
    @Key("usagepoint.field.meter.installation_date")
    String getUsagePointMeterInstallationDate();
    @Key("usagepoint.field.meter.installation_date.help")
    String getUsagePointMeterInstallationDateHelp();
    @Key("usagepoint.field.name.help")
    String getUsagePointNameHelp();
    @Key("usagepoint.field.name")
    String getUsagePointName();
    @Key("usagepoint.field.pricingstructure.help")
    String getUsagePointPricingStructureHelp();
    @Key("usagepoint.field.pricingstructure")
    String getUsagePointPricingStructure();
    @Key("usagepoint.field.lastmdcconnectcontrol")
    String getLastMdcConnectControl();
    @Key("usagepoint.field.blocking.help")
    String getUsagePointBlockingHelp();
    @Key("usagepoint.field.blocking.label")
    String getUsagePointBlockingLabel();
    @Key("usagepoint.field.group.help")
    String getUsagePointGroupHelp();
    @Key("usagepoint.field.group")
    String getUsagePointGroup();
    @Key("usagepoint.required.text")
    String getUsagePointRequiredText();
    @Key("usagepoint.required.activation.text")
    String getUsagePointRequiredActivationText();
    @Key("usagepoint.txn.history")
    String getUsagePointTxnHistory();
    @Key("usagepoint.txn.meterreadings")
    String getUsagePointMeterReadings();
    @Key("usagepoint.history")
    String getUsagePointHistory();
    @Key("usagepoint.reports")
    String getUsagePointReports();
    @Key("usagepoint.recharge.history")
    String getUsagePointRechargeHistory();
    @Key("usagepoint.location")
    String getUsagePointLocation();
    @Key("usagepoint.retailers")
    String getUsagePointRetailers();
    @Key("usagepoint.reports.general")
    String getUsagePointGeneralReports();
    @Key("usagepoint.history.filter")
    String getUsagePointHistoryFilter();
    @Key("usagepoint.txn.filter")
    String getUsagePointTxnFilter();
    @Key("usagepoint.calculate.tariff")
    String getCalcTariffButton();
    @Key("usagepoint.fetch")
    String getUsagepointFetch();
    @Key("usagepoint.fetch.help")
    String getUsagepointFetchHelp();
    @Key("usagepoint.name.instr")
    String getUsagePointNameFetch();
    @Key("usagepoint.install.date.required")
    String getUsagePointInstallDateRequired();

//    @Key("usagepoint.charge.view.dialog.info")
//    String getUsagePointChargeViewDialogInfo();
    @Key("usagepoint.last.cyclic.date.info")
    String getUsagePointLastCyclicDateInfo();
    @Key("usagepoint.last.cyclic.vend.date")
    String getUsagePointLastCyclicVendDate();
    @Key("usagepoint.last.cyclic.billing.date")
    String getUsagePointLastCyclicBillingDate();
    @Key("usagepoint.charge.view.dialog.date")
    String getUsagePointChargeViewDialogDateLbl();
    @Key("usagepoint.charge.writeoff.vend.heading")
    String getUsagePointChargeWriteoffVendHeading();
    @Key("usagepoint.charge.writeoff.billing.heading")
    String getUsagePointChargeWriteoffBillingHeading();
    @Key("usagepoint.charge.writeoff.billing.total")
    String getUsagePointChargeWriteoffBillingTotalLbl();
    @Key("usagepoint.charge.writeoff.vend.total")
    String getUsagePointChargeWriteoffVendTotalLbl();
    @Key("usagepoint.charge.writeoff.both.total")
    String getUsagePointChargeWriteoffTotalBothLbl();
    @Key("usagepoint.charge.button.writeoff")
    String getWriteoffButton();
    @Key("usagepoint.charge.button.upchargeview")
    String getUsagePointChargeViewButton();
    @Key("usagepoint.charge.view.writeoff.date.help")
    String getWriteoffLastCyclicDateHelp();
    @Key("usagepoint.charge.view.filter.date.help")
    String getUsagePointChargeFilterDateHelp();
    @Key("usagepoint.charge.writeoff.trans.success")
    String getWriteoffSuccess();
    @Key("usagepoint.meter.inspection.request.btn")
    String getUsagePointMeterInspectionButton();
    @Key("usagepoint.meter.inspection.request.txt.comment")
    String getUsagePointMeterInspectionComment();
    @Key("usagepoint.meter.inspection.request.txt.comment.help")
    String getUsagePointMeterInspectionCommentHelp();
    @Key("usagepoint.device.move.ref.lbl")
    String getUsagePointDeviceMoveRefLbl();
    @Key("usagepoint.ps.start.date.help")
    String getUsagePointPSStartDateHelp();
    @Key("usagepoint.field.future.pricingstructure")
    String getUsagePointFuturePricingStructure();
    @Key("usagepoint.ps.future.list.help")
    String getUsagePointFuturePSListHelp();
    @Key("usagepoint.ps.future.date.help")
    String getUsagePointFuturePSDateHelp();
    @Key("usagepoint.ps.start.date.lbl")
    String getUsagePointPSStartDateLbl();
    @Key("usagepoint.ps.delete.btn")
    String getUsagePointPSDeleteBtn();
    @Key("usagepoint.ps.view.all.btn")
    String getUsagePointPSViewAllBtn();

    @Key("groupentity.header")
    String getGroupEntityHeader();
    @Key("groupentity.title")
    String getEntityTitle();
    @Key("groupentity.contact.title")
    String getContactTitle();
    @Key("groupentity.field.contact.name")
    String getEntityContactName();
    @Key("groupentity.field.contact.number")
    String getEntityContactNumber();
    @Key("groupentity.field.contact.email")
    String getEntityContactEmail();
    @Key("groupentity.field.contact.address")
    String getEntityContactAddress();
    @Key("groupentity.field.contact.taxref")
    String getEntityContactTaxRef();

    @Key("groupthreshold.title")
    String getThresholdTitle();
    @Key("groupthreshold.meter.disconnect.text")
    String getThresholdDisconnectText();
    @Key("groupthreshold.meter.disconnect.help")
    String getThresholdDisconnectHelp();
    @Key("groupthreshold.emergency.credit.text")
    String getThresholdEmergencyCreditText();
    @Key("groupthreshold.emergency.credit.help")
    String getThresholdEmergencyCreditHelp();
    @Key("groupthreshold.meter.reconnect.text")
    String getThresholdReconnectText();
    @Key("groupthreshold.meter.reconnect.help")
    String getThresholdReconnectHelp();
    @Key("groupthreshold.low.balance.text")
    String getThresholdLowBalanceText();
    @Key("groupthreshold.low.balance.help")
    String getThresholdLowBalanceHelp();
    @Key("groupthreshold.override.settings.text")
    String getThresholdOverrideSettingsText();
    @Key("groupthreshold.override.settings.help")
    String getThresholdOverrideSettingsHelp();

    @Key("ndp.schedule.title")
    String getNdpScheduleTitle();
    @Key("ndp.schedule.active")
    String getNdpScheduleActive();
    @Key("ndp.schedule.active.help")
    String getNdpScheduleActiveHelp();
    @Key("ndp.schedule.delete.button")
    String getDeleteNdpScheduleButton();
    @Key("ndp.disclosurePanel.title")
    String getNdpDisclosureTitle();
    @Key("ndp.seasons.title")
    String getNdpSeasonTitle();
    @Key("ndp.season.day.title")
    String getNdpSeasonDayTitle();
    @Key("ndp.season.day.description")
    String getNdpSeasonDayDescription();
    @Key("ndp.assign.season.start")
    String getNdpAssignSeasonStart();
    @Key("ndp.assign.season.start.help")
    String getNdpAssignSeasonStartHelp();
    @Key("ndp.assign.season.end")
    String getNdpAssignSeasonEnd();
    @Key("ndp.assign.season.end.help")
    String getNdpAssignSeasonEndHelp();
    @Key("ndp.per.day.title")
    String getNdpPerDayTitle();
    @Key("ndp.days.of.week")
    String getNdpDaysofWeek();
    @Key("ndp.assign.dayperiod.start")
    String getNdpAssignDayperiodStart();
    @Key("ndp.assign.dayperiod.start.help")
    String getNdpAssignDayperiodStartHelp();
    @Key("ndp.assign.dayperiod.start.hour")
    String getNdpAssignDayperiodStartHour();
    @Key("ndp.assign.dayperiod.start.minute")
    String getNdpAssignDayperiodStartMinute();
    @Key("ndp.assign.dayperiod.end")
    String getNdpAssignDayperiodEnd();
    @Key("ndp.assign.dayperiod.end.help")
    String getNdpAssignDayperiodEndHelp();
    @Key("ndp.assign.dayperiod.end.hour")
    String getNdpAssignDayperiodEndHour();
    @Key("ndp.assign.dayperiod.end.minute")
    String getNdpAssignDayperiodEndMinute();
    @Key("ndp.days.title")
    String getNdpDaysTitle();
    @Key("ndp.add.season.button")
    String getAddNdpSeason();
    @Key("ndp.new.season.button")
    String getAddNewSeasonButton();
    @Key("ndp.new.special.day.button")
    String getAddSpecialDayButton();
    @Key("ndp.special.day.title")
    String getNdpSpecialDayTitle();
    @Key("ndp.special.day.description")
    String getNdpSpecialdayDescription();
    @Key("ndp.special.day.time.title")
    String getNdpSpecialDayTimeTitle();
    @Key("ndp.assign.special.day")
    String getNdpAssignSpecialDay();
    @Key("ndp.assign.special.day.help")
    String getNdpAssignSpecialDayHelp();
    @Key("ndp.active.instruction")
    String getNdpActiveInstruction();

    @Key("pricingstructures.header")
    String getPricingStructuresHeader();
    @Key("pricingstructures.title")
    String getPricingStructuresTitle();
    @Key("tariffs.header")
    String getTariffsHeader();
    @Key("tariffs.title")
    String getTariffsTitle();
    @Key("tariff.field.unitprice")
    String getTariffUnitPrice();
    @Key("tariff.field.unitprice.help")
    String getTariffUnitPriceHelp();
    @Key("tariff.field.tax")
    String getTariffTax();
    @Key("tariff.field.tax.help")
    String getTariffTaxHelp();
    
    @Key("tariff.field.free.units.title")
    String getTariffFreeUnitsTitle();
    @Key("tariff.field.free.units.descrip")
    String getFreeUnitsDescripTitle();
    @Key("tariff.field.free.units")
    String getFreeUnitsTitle();
    @Key("tariff.field.free.units.help")
    String getFreeUnitsHelp();
    @Key("tariff.field.bsst.charge.title")
    String getBsstChargeTitle();
    @Key("tariff.field.bsst.charge.title.help")
    String getBsstChargeHelp();
    @Key("tariff.field.bsst.charge_name.title")
    String getBsstChargeNameTitle();
    @Key("tariff.field.bsst.charge_name.title.help")
    String getBsstChargeNameHelp();
    
    @Key("tariff.field.cyclic_charge.title")
    String getTariffCyclicChargeTitle();
    @Key("tariff.field.cyclic_charge.add")
    String getTariffAddCyclicCharge();
    @Key("tariff.field.cycle.name")
    String getTariffCycle();
    @Key("tariff.field.cycle.name.help")
    String getTariffCycleHelp();
    @Key("tariff.field.cost.name")
    String getTariffCyclicCostName();
    @Key("tariff.field.cost.name.help")
    String getTariffCyclicCostNameHelp();
    @Key("tariff.field.non_accruing_monthly.name")
    String getTariffNonAccruingMonthlyName();
    @Key("tariff.field.non_accruing_monthly.name.help")
    String getTariffNonAccruingMonthlyNameHelp();
    @Key("tariff.field.cost")
    String getTariffCost();
    @Key("tariff.field.cost.help")
    String getTariffCostHelp();
    @Key("cyclic.charge.apply.at.lbl")
    String getCyclicChargeApplyAtLbl();
    @Key("cyclic.charge.apply.at.vend.lbl")
    String getCyclicChargeApplyAtVendLbl();
    @Key("cyclic.charge.apply.at.billing.lbl")
    String getCyclicChargeApplyAtBillingLbl();
    @Key("tariff.field.percent_charge.title")
    String getTariffPercentChargeTitle();
    @Key("tariff.field.percent_charge.add")
    String getTariffAddPercentCharge();
    @Key("tariff.field.percent_charge_name")
    String getTariffPercentChargeName();
    @Key("tariff.field.percent_charge_name.help")
    String getTariffPercentChargeNameHelp();
    @Key("tariff.field.percent_charge")
    String getTariffPercentCharge();
    @Key("tariff.field.percent_charge.help")
    String getTariffPercentChargeHelp();
    @Key("tariff.field.unit_charge.title")
    String getTariffUnitChargeTitle();
    @Key("tariff.field.unit_charge.add")
    String getTariffAddUnitCharge();
    @Key("tariff.field.unit_charge.name")
    String getTariffUnitChargeName();
    @Key("tariff.field.unit_charge.name.help")
    String getTariffUnitChargeNameHelp();
    @Key("tariff.field.unit_charge.is_percent")
    String getTariffUnitChargeIsPercent();
    @Key("tariff.field.unit_charge.is_percent.help")
    String getTariffUnitChargeIsPercentHelp();
    @Key("tariff.field.unit_charge.is_taxable")
    String getTariffUnitChargeIsTaxable();
    @Key("tariff.field.unit_charge.is_taxable.help")
    String getTariffUnitChargeIsTaxableHelp();
    @Key("tariff.field.unit_charge")
    String getTariffUnitCharge();
    @Key("tariff.field.unit_charge.help")
    String getTariffUnitChargeHelp();
    @Key("tariff.field.unit_charge_percent")
    String getTariffUnitChargePercent();
    @Key("tariff.field.unit_charge_percent.help")
    String getTariffUnitChargePercentHelp();
    @Key("tariff.field.groupthreshold")
    String getTariffGroupThreshold();
    @Key("tariff.field.groupthreshold.help")
    String getTariffGroupThresholdHelp();
    @Key("tariff.field.price")
    String getTariffPrice();
    @Key("tariff.field.baseprice")
    String getTariffBasePrice();
    @Key("tariff.field.threshold")
    String getTariffThreshold();
    @Key("tariff.field.threshold.help")
    String getTariffThresholdHelp();
    @Key("tariff.field.step1")
    String getTariffStep1();
    @Key("tariff.field.step2")
    String getTariffStep2();
    @Key("tariff.field.step3")
    String getTariffStep3();
    @Key("tariff.field.discount")
    String getDiscount();
    @Key("tariff.field.discount.help")
    String getDiscountHelp();
    @Key("tariff.field.block")
    String getBlocks();
    @Key("tariff.field.block.help")
    String getBlocksHelp();
    @Key("tarif.adv.settings.header")
    String getAdvancedSettingsHeader();
    @Key("tariff.field.pricesymbol")
    String getPriceSymbol();
    @Key("tariff.field.unitsymbol")
    String getUnitSymbol();
    @Key("tariff.field.amountrounding")
    String getAmountRoundingMode();
    @Key("tariff.field.amountprecision")
    String getAmountPrecision();
    @Key("tariff.field.unitsrounding")
    String getUnitsRoundingMode();
    @Key("tariff.field.unitsprecision")
    String getUnitsPrecision();
    @Key("tariff.field.taxrounding")
    String getTaxRoundingMode();
    @Key("tariff.field.taxprecision")
    String getTaxPrecision();
    @Key("tariff.field.pricesymbol.help")
    String getPriceSymbolHelp();
    @Key("tariff.field.unitsymbol.help")
    String getUnitSymbolHelp();
    @Key("tariff.field.amountrounding.help")
    String getAmountRoundingModeHelp();
    @Key("tariff.field.amountprecision.help")
    String getAmountPrecisionHelp();
    @Key("tariff.field.unitsrounding.help")
    String getUnitsRoundingModeHelp();
    @Key("tariff.field.unitsprecision.help")
    String getUnitsPrecisionHelp();
    @Key("tariff.field.taxrounding.help")
    String getTaxRoundingModeHelp();
    @Key("tariff.field.taxprecision.help")
    String getTaxPrecisionHelp();
    @Key("tariff.readonly")
    String getReadOnlyTariff();
    @Key("tariff.field.minvendamount.lbl")
    String getTariffMinVendAmtLbl();
    @Key("tariff.field.minvendamount.help")
    String getTariffMinVendAmtHelp();

    @Key("tariff.field.meter_debt.title")
    String getMeterDebtTitle();
    @Key("tariff.field.meter_debt.singlephase.label")
    String getMeterDebtSinglePhaseLabel();
    @Key("tariff.field.meter_debt.singlephase.label.help")
    String getMeterDebtSinglePhaseLabelHelp();
    @Key("tariff.field.meter_debt.threephase.label")
    String getMeterDebtThreePhaseLabel();
    @Key("tariff.field.meter_debt.threephase.label.help")
    String getMeterDebtThreePhaseLabelHelp();
    
    @Key("tariff.field.unitprice.namibia.help")
    String getTariffUnitPriceNamibiaHelp();
    @Key("tariff.field.namibia.neflevy")
    String getTariffNamibiaNefLevy();
    @Key("tariff.field.namibia.neflevy.help")
    String getTariffNamibiaNefLevyHelp();
    @Key("tariff.field.namibia.ecblevy")
    String getTariffNamibiaEcbLevy();
    @Key("tariff.field.namibia.ecblevy.help")
    String getTariffNamibiaEcbLevyHelp();

    @Key("auxchargeschedules.header")
    String getAuxChargeScheduleHeader();
    @Key("auxchargeschedules.title")
    String getAuxChargeScheduleTitle();

    @Key("auxilliarytypes.header")
    String getAuxilliaryTypesHeader();
    @Key("auxilliarytypes.title")
    String getAuxilliaryTypesTitle();
    @Key("auxtype.field.name")
    String getAuxTypeName();
    @Key("auxtype.field.description")
    String getAuxTypeDescription();
    @Key("auxtype.field.active")
    String getAuxTypeActive();
    @Key("auxtype.field.name.help")
    String getAuxTypeNameHelp();
    @Key("auxtype.field.description.help")
    String getAuxTypeDescriptionHelp();
    @Key("auxtype.field.active.help")
    String getAuxTypeActiveHelp();

    @Key("auxchargeschedule.field.name")
    String getAuxChargeScheduleName();
    @Key("auxchargeschedule.field.name.help")
    String getAuxChargeScheduleNameHelp();
    @Key("auxchargeschedule.field.minamount")
    String getAuxChargeScheduleMinAmount();
    @Key("auxchargeschedule.field.minamount.help")
    String getAuxChargeScheduleMinAmountHelp();
    @Key("auxchargeschedule.field.maxamount")
    String getAuxChargeScheduleMaxAmount();
    @Key("auxchargeschedule.field.maxamount.help")
    String getAuxChargeScheduleMaxAmountHelp();
    @Key("auxchargeschedule.field.vendportion")
    String getAuxChargeScheduleVendPortion();
    @Key("auxchargeschedule.field.vendportion.help")
    String getAuxChargeScheduleVendPortionHelp();
    @Key("auxchargeschedule.field.currportion")
    String getAuxChargeScheduleCurrPortion();
    @Key("auxchargeschedule.field.currportion.help")
    String getAuxChargeScheduleCurrPortionHelp();
    @Key("aux.charge.sched.cycle.label")
    String getAuxChargeSchedCycleLabel();
    @Key("aux.charge.sched.cycle.label.help")
    String getAuxChargeSchedCycleLabelHelp();
    @Key("aux.charge.sched.cycle.amount.label")
    String getAuxChargeSchedCycleAmountLabel();
    @Key("aux.charge.sched.cycle.amount.label.help")
    String getAuxChargeSchedCycleAmountLabelHelp();
    @Key("auxchargeschedule.field.active")
    String getAuxChargeScheduleActive();
    @Key("auxchargeschedule.field.active.help")
    String getAuxChargeScheduleActiveHelp();

    @Key("pricingstructure.field.name")
    String getPricingStructureName();
    @Key("pricingstructure.field.name.help")
    String getPricingStructureNameHelp();
    @Key("pricingstructure.field.description")
    String getPricingStructureDescription();
    @Key("pricingstructure.field.description.help")
    String getPricingStructureDescriptionHelp();
    @Key("pricingstructure.field.active")
    String getPricingStructureActive();
    @Key("pricingstructure.field.active.help")
    String getPricingStructureActiveHelp();

    @Key("tariff.title")
    String getTariffTitle();
    @Key("tariff.field.name")
    String getTariffName();
    @Key("tariff.field.name.help")
    String getTariffNameHelp();
    @Key("tariff.field.description")
    String getTariffDescription();
    @Key("tariff.field.description.help")
    String getTariffDescriptionHelp();
    @Key("tariff.field.startdate")
    String getTariffStartDate();
    @Key("tariff.field.startdate.help")
    String getTariffStartDateHelp();
    @Key("tariff.title.type")
    String getTariffTypeTitle();
    @Key("tariff.field.type")
    String getTariffType();

    @Key("calendar.settings.header")
    String getCalendarSettingsHeader();
    @Key("calendar.settings.title")
    String getCalendarSettingsTitle();
    @Key("calendar.season.current.title")
    String getCalendarSeasonCurrentTitle();
    @Key("calendar.season.title")
    String getCalendarSeasonTitle();
    @Key("calendar.season.description")
    String getCalendarSeasonDescription();
    @Key("calendar.season.field.name")
    String getCalendarSeasonName();
    @Key("calendar.season.field.name.help")
    String getCalendarSeasonNameHelp();
    @Key("calendar.season.field.active")
    String getCalendarSeasonActive();
    @Key("calendar.season.field.active.help")
    String getCalendarSeasonActiveHelp();

    @Key("calendar.period.current.title")
    String getCalendarPeriodCurrentTitle();
    @Key("calendar.period.title")
    String getCalendarPeriodTitle();
    @Key("calendar.period.description")
    String getCalendarPeriodDescription();
    @Key("calendar.period.field.name")
    String getCalendarPeriodName();
    @Key("calendar.period.field.name.help")
    String getCalendarPeriodNameHelp();
    @Key("calendar.period.field.code")
    String getCalendarPeriodCode();
    @Key("calendar.period.field.code.help")
    String getCalendarPeriodCodeHelp();
    @Key("calendar.period.field.active")
    String getCalendarPeriodActive();
    @Key("calendar.period.field.active.help")
    String getCalendarPeriodActiveHelp();

    @Key("calendars.heading")
    String getCalendarsHeading();
    @Key("calendars.title")
    String getCalendarsTitle();
    @Key("calendars.description")
    String getCalendarsDescription();

    @Key("calendar.field.name")
    String getCalendarName();
    @Key("calendar.field.name.help")
    String getCalendarNameHelp();
    @Key("calendar.field.description")
    String getCalendarDescription();
    @Key("calendar.field.description.help")
    String getCalendarDescriptionHelp();
    @Key("calendar.field.active")
    String getCalendarActive();
    @Key("calendar.field.active.help")
    String getCalendarActiveHelp();

    @Key("calendar.assign.season.title")
    String getCalendarAssignSeasonTitle();
    @Key("calendar.assign.season.heading")
    String getCalendarAssignSeasonHeading();
    @Key("calendar.assign.season.description")
    String getCalendarAssignSeasonDescription();

    @Key("calendar.assign.season")
    String getCalendarAssignSeason();
    @Key("calendar.assign.season.help")
    String getCalendarAssignSeasonHelp();
    @Key("calendar.assign.season.start")
    String getCalendarAssignSeasonStart();
    @Key("calendar.assign.season.start.help")
    String getCalendarAssignSeasonStartHelp();
    @Key("calendar.assign.season.end")
    String getCalendarAssignSeasonEnd();
    @Key("calendar.assign.season.end.help")
    String getCalendarAssignSeasonEndHelp();

    @Key("calendar.assign.period.title")
    String getCalendarAssignPeriodTitle();
    @Key("calendar.assign.period.description")
    String getCalendarAssignPeriodDescription();

    @Key("calendar.assign.period")
    String getCalendarAssignPeriod();
    @Key("calendar.assign.period.help")
    String getCalendarAssignPeriodHelp();
    @Key("calendar.assign.period.start")
    String getCalendarAssignPeriodStart();
    @Key("calendar.assign.period.start.help")
    String getCalendarAssignPeriodStartHelp();
    @Key("calendar.assign.period.start.hour")
    String getCalendarAssignPeriodStartHour();
    @Key("calendar.assign.period.start.minute")
    String getCalendarAssignPeriodStartMinute();
    @Key("calendar.assign.period.end")
    String getCalendarAssignPeriodEnd();
    @Key("calendar.assign.period.end.help")
    String getCalendarAssignPeriodEndHelp();
    @Key("calendar.assign.period.end.hour")
    String getCalendarAssignPeriodEndHour();
    @Key("calendar.assign.period.end.minute")
    String getCalendarAssignPeriodEndMinute();

    @Key("calendar.dayprofiles.title")
    String getCalendarDayProfilesTitle();
    @Key("calendar.dayprofiles.heading")
    String getCalendarDayProfilesHeading();
    @Key("calendar.dayprofiles.description")
    String getCalendarDayProfilesDescription();
    @Key("calendar.dayprofile.field.name")
    String getCalendarDayProfileName();
    @Key("calendar.dayprofile.field.code")
    String getCalendarDayProfileCode();
    @Key("calendar.dayprofile.field.active")
    String getCalendarDayProfileActive();
    @Key("calendar.dayprofile.field.name.help")
    String getCalendarDayProfileNameHelp();
    @Key("calendar.dayprofile.field.code.help")
    String getCalendarDayProfileCodeHelp();
    @Key("calendar.dayprofile.field.active.help")
    String getCalendarDayProfileActiveHelp();

    @Key("calendar.assign.dayprofile.heading")
    String getCalendarAssignDayProfilesHeading();
    @Key("calendar.assign.dayprofile.description")
    String getCalendarAssignDayProfilesDescription();

    @Key("calendar.specialday.current.title")
    String getCalendarSpecialdayCurrentTitle();
    @Key("calendar.specialday.heading")
    String getCalendarSpecialdayHeading();
    @Key("calendar.specialday.title")
    String getCalendarSpecialdayTitle();
    @Key("calendar.specialday.description")
    String getCalendarSpecialdayDescription();
    @Key("calendar.specialday.field.name")
    String getCalendarSpecialdayName();
    @Key("calendar.specialday.field.name.help")
    String getCalendarSpecialdayNameHelp();
    @Key("calendar.specialday.field.code")
    String getCalendarSpecialdayCode();
    @Key("calendar.specialday.field.code.help")
    String getCalendarSpecialdayCodeHelp();
    @Key("calendar.specialday.field.active")
    String getCalendarSpecialdayActive();
    @Key("calendar.specialday.field.active.help")
    String getCalendarSpecialdayActiveHelp();

    @Key("calendar.specialday.field.date")
    String getCalendarSpecialdayDate();
    @Key("calendar.specialday.field.date.help")
    String getCalendarSpecialdayDateHelp();
    @Key("calendar.specialday.field.day")
    String  getCalendarSpecialdayDay();
    @Key("calendar.specialday.field.month")
    String getCalendarSpecialdayMonth();
    @Key("calendar.specialday.field.dayprofile")
    String getCalendarSpecialdayProfile();
    @Key("calendar.specialday.field.dayprofile.help")
    String getCalendarSpecialdayProfileHelp();

    @Key("devicestores.header")
    String getDeviceStoresHeader();
    @Key("devicestores.title")
    String getDeviceStoresTitle();
    @Key("devicestore.field.name")
    String getDeviceStoreName();
    @Key("devicestore.field.description")
    String getDeviceStoreDescription();
    @Key("devicestore.field.name.help")
    String getDeviceStoreNameHelp();
    @Key("devicestore.field.description.help")
    String getDeviceStoreDescriptionHelp();
    @Key("devicestore.field.active")
    String getDeviceStoreActive();
    @Key("devicestore.field.active.help")
    String getDeviceStoreActiveHelp();
    @Key("devicestore.location.title")
    String getDeviceStoreLocationTitle();

    @Key("devicestore.meters.header")
    String getDeviceStoreMetersHeader();
    @Key("devicestore.meters.title")
    String getDeviceStoreMetersTitle();
    @Key("devicestore.field.store_vendors_meter")
    String getDeviceStoreStoresVendorsMeter();
    @Key("devicestore.field.store_vendors_meter_help")
    String getDeviceStoreStoresVendorsMeterHelp();
    @Key("devicestore.field.custom_message")
    String getDeviceStoreCustomMessage();
    @Key("devicestore.field.custom_message_help")
    String getDeviceStoreCustomMessageHelp();

    @Key("blockingtypes.header")
    String getBlockingTypesHeader();
    @Key("blockingtypes.title")
    String getBlockingTypesTitle();
    @Key("blockingtype.form.typename")
    String getBlockingTypeNameLbl();
    @Key("blockingtype.form.typename.help")
    String getBlockingTypeNameHelp();
    @Key("blockingtype.form.units")
    String getBlockingTypeUnitsLbl();
    @Key("blockingtype.form.units.help")
    String getBlockingTypeUnitsHelp();
    @Key("blockingtype.form.dailyamount")
    String getBlockingTypeDailyAmountLbl();
    @Key("blockingtype.form.dailyamount.help")
    String getBlockingTypeDailyAmountHelp();
    @Key("blockingtype.form.complete")
    String getBlockingTypeCompleteLbl();
    @Key("blockingtype.form.complete.help")
    String getBlockingTypeCompleteHelp();
    @Key("blockingtype.form.vends")
    String getBlockingTypeVendsLbl();
    @Key("blockingtype.form.vends.help")
    String getBlockingTypeVendsHelp();
    @Key("blockingtype.form.amount")
    String getBlockingTypeAmountLbl();
    @Key("blockingtype.form.amount.help")
    String getBlockingTypeAmountHelp();
    @Key("blockingtype.form.message")
    String getBlockingTypeMessageLbl();
    @Key("blockingtype.form.message.help")
    String getBlockingTypeMessageHelp();

    @Key("link.logout")
    String getLogoutLink();
    @Key("link.group")
    String getGroupLink();
    @Key("link.meters")
    String getMetersLink();
    @Key("link.customers")
    String getCustomersLink();
    @Key("link.groups")
    String getGroupsLink();
    @Key("link.menu")
    String getMenuLink();
    @Key("link.pricingstructure")
    String getPricingStructureLink();
    @Key("link.calendars")
    String getCalendarsLink();
    @Key("link.calendarsettings")
    String getCalendarSettingsLink();
    @Key("link.auxchargeschedule")
    String getAuxChargeScheduleLink();
    @Key("link.auxilliarytype")
    String getAuxTypeLink();
    @Key("link.supplygroup")
    String getSupplyGroupLink();
    @Key("link.displaytokens")
    String getDisplayTokensLink();
    @Key("link.devicestores")
    String getDeviceStoresLink();
	@Key("link.blockingtype")
	String getBlockingTypeLink();
    @Key("link.usergroup")
    String getUserGroupLink();
    @Key("link.accessgroups")
    String getAccessGroupsHeader();
    @Key("link.search")
    String getSearchLink();
    @Key("link.search.advanced")
    String getAdvancedSearchLink();
    @Key("link.meter.readings")
    String getMeterReadingsLink();
    @Key("link.analytics")
    String getAnalyticsLink();
    @Key("link.configuration")
    String getConfigurationLink();
    @Key("link.tools")
    String getToolsLink();
    @Key("link.taskschedules")
    String getTaskSchedulesLink();
    @Key("link.locationgroups")
    String getLocationGroupsLink();
    @Key("link.appsettings")
    String getAppSettingsLink();
    @Key("link.billingdet")
    String getBillingDetLink();

    @Key("application.title")
    String getApplicationTitle();

    @Key("meter.title")
    String getMeterTitle();
    @Key("meter.number.instructions")
    String getMeterNumberInstructions();
    @Key("usagepoint.name.instructions")
    String getUsagePointNameInstructions();
    @Key("meter.add")
    String getAddMeter();
    @Key("bulk.upload.heading.metercustup")
    String getMeterCustUPBulkUpload();
    @Key("bulk.upload.meterupload.heading")
    String getMeterBulkUpload();
    @Key("bulk.upload.file.button.gentemplate.description")
    String getGentemplateButtonDesc();
    @Key("default.template.bulk.uploads")
    String getDownloadDefaultTempleteText();
    @Key("meter.info.title")
    String getMeterInfoTitle();
    @Key("meter.required.text")
    String getMeterRequiredText();
    @Key("meter.required.activation.text")
    String getMeterRequiredActivationText();
    @Key("meter.show.info")
    String getMeterShowInfo();
    @Key("meter.showing.info")
    String getMeterShowingInfo();
    @Key("meter.active.help")
    String getMeterActiveHelp();
    @Key("meter.active")
    String getMeterActive();
    @Key("meter.replace")
    String getMeterReplace();
    @Key("meter.replace.help")
    String getMeterReplaceHelp();
    @Key("meter.remove")
    String getMeterRemove();
    @Key("meter.remove.help")
    String getMeterRemoveHelp();
    @Key("meter.select.meter.model")
    String getSelectMeterModel();
    @Key("meter.select.meter.model.help")
    String getSelectMeterModelHelp();
    @Key("meter.select.metertype")
    String getSelectMeterType();
    @Key("meter.select.metertype.help")
    String getSelectMeterTypeHelp();
    @Key("meter.number")
    String getMeterNumber();
    @Key("meter.number.help")
    String getMeterNumberHelp();
    @Key("meter.number.optional")
    String getMeterNumberOptional();
    @Key("meter.number.optional.help")
    String getMeterNumberOptionalHelp();
    @Key("meter.iso")
    String getMeterIso();
    @Key("meter.iso.help")
    String getMeterIsoHelp();
    @Key("meter.checksum")
    String getMeterChecksum();
    @Key("meter.checksum.help")
    String getMeterChecksumHelp();
    @Key("meter.serialnumber")
    String getMeterSerialNumber();
    @Key("meter.serialnumber.help")
    String getMeterSerialNumberHelp();
    @Key("meter.breakerid")
    String getMeterBreakerId();
    @Key("meter.breakerid.help")
    String getMeterBreakerIdHelp();
    @Key("meter.encryptionkey")
    String getMeterEncryptionKey();
    @Key("meter.encryptionkey.help")
    String getMeterEncryptionKeyHelp();
    @Key("meter.stsinfo")
    String getMeterStsInfo();
    @Key("meter.algorithmcode")
    String getMeterAlgorithmCode();
    @Key("meter.algorithmcode.help")
    String getMeterAlgorithmCodeHelp();
    @Key("meter.tokentechcode")
    String getMeterTokenTechCode();
    @Key("meter.tokentechcode.help")
    String getMeterTokenTechCodeHelp();
    @Key("meter.supplygroupcode")
    String getMeterSupplyGroupCode();
    @Key("meter.supplygroupcode.help")
    String getMeterSupplyGroupCodeHelp();
    @Key("meter.new.supplygroupcode")
    String getMeterNewSupplyGroupCode();
    @Key("meter.new.supplygroupcode.help")
    String getMeterNewSupplyGroupCodeHelp();
    @Key("meter.tariffindex")
    String getMeterTariffIndex();
    @Key("meter.tariffindex.help")
    String getMeterTariffIndexHelp();
    @Key("meter.new.tariffindex")
    String getMeterNewTariffIndex();
    @Key("meter.new.tariffindex.help")
    String getMeterNewTariffIndexHelp();
    @Key("base.date.label")
    String getBaseDateLabel();
    @Key("base.date.label.help")
    String getBaseDateLabelHelp();
    @Key("meter.three.tokens")
    String getMeterThreeTokens();
    @Key("meter.three.tokens.help")
    String getMeterThreeTokensHelp();

    @Key("meter.open.newtab")
    String getMeterOpenTab();
    @Key("meter.open")
    String getMeterOpen();
    @Key("meter.enter.number")
    String getMeterEnterNumber();
    @Key("meter.or")
    String getMeterOr();
    @Key("meter.then")
    String getMeterThen();
    @Key("meter.add.new")
    String getMeterAddNew();
    @Key("meter.specify.install.date")
    String getMeterSpecifyInstallDate();

    @Key("meter.change.activation.date")
    String getMeterChangeActivationDate();
    @Key("option.positive")
    String getOptionPositive();
    @Key("option.negative")
    String getOptionNegative();

    @Key("meter.install.date.required")
    String  getMeterInstallDateRequired();
    @Key("meter.token.active")
    String getMeterMustBeActive();
    @Key("meter.powerlimit")
    String getMeterPowerLimit();
    @Key("meter.powerlimit.help")
    String getMeterPowerLimitHelp();
    @Key("meter.description")
    String getMeterDescription();
    @Key("meter.description.help")
    String getMeterDescriptionHelp();
    @Key("meter.title.enginerringtokens")
    String getMeterEngineeringTokensTitle();
    @Key("meter.issue.engineeringtoken")
    String getMeterIssueEngineeringToken();
    @Key("meter.select.tokentype")
    String getMeterSelectTokenType();
    @Key("meter.select.tokentype.help")
    String getMeterSelectTokenTypeHelp();
    @Key("meter.changekey.instructions")
    String getMeterChangekeyInstructions();
    @Key("meter.power_limit.instructions")
    String getMeterPowerLimitInstructions();
    @Key("meter.power_limit.container_label")
    String getMeterPowerLimitContainer();
    @Key("meter.power_limit.token.generate")
    String getMeterGeneratePowerLimitToken();
    @Key("meter.power_limit.token.generate.help")
    String getMeterGeneratePowerLimitTokenHelp();
    @Key("meter.txn.history")
    String getMeterTxnHistory();
    @Key("meter.history")
    String getMeterHistory();
    @Key("meter.reports")
    String getMeterReports();
    @Key("meter.reports.general")
    String getMeterGeneralReports();
    @Key("meter.recharge.history")
    String getMeterRechargeHistory();
    @Key("meter.retailers")
    String getMeterRetailers();
    @Key("meter.location")
    String getMeterLocation();
    @Key("meter.powerlimit.units.w")
    String getPowerLimitUnitsW();
    @Key("meter.units.kwh")
    String getMeterUnitsKwh();
    @Key("meter.units.kwh.help")
    String getMeterUnitsKwhHelp();
    @Key("meter.units.watts")
    String getMeterUnitsWatts();
    @Key("meter.units.watts.help")
    String getMeterUnitsWattsHelp();
    @Key("meter.currency")
    String getMeterCurrency();
    @Key("meter.currency.help")
    String getMeterCurrencyHelp();
    @Key("meter.free.description")
    String getMeterFreeDescription();
    @Key("meter.free.description.help")
    String getMeterFreeDescriptionHelp();
    @Key("meter.setphase.description")
    String getMeterSetPhaseDescription();
    @Key("meter.setphase.description.help")
    String getMeterSetPhaseDescriptionHelp();
    @Key("meter.clear.description")
    String getMeterClearDescription();
    @Key("meter.clear.description.help")
    String getMeterClearDescriptionHelp();
    @Key("meter.clearcredit.type.description")
    String getMeterClearcreditTypeDescription();
    @Key("meter.clearcredit.type.help")
    String getMeterClearcreditTypeHelp();
    @Key("meter.txn.filter")
    String getMeterTxnFilter();
    @Key("meter.history.filter")
    String getMeterHistoryFilter();
    @Key("meter.engtoken.filter")
    String getMeterEngTokenFilter();
    @Key("meter.generate.keychange")
    String getMeterGenerateKeyChange();
    @Key("meter.generate.keychange.help")
    String getMeterGenerateKeyChangeHelp();
    @Key("meter.txn.usagepoint")
    String getMeterTxnUsagepoint();
    @Key("meter.txn.token.type")
    String getMeterTxnTokenType();
    @Key("meter.token.code1")
    String getMeterTokenCode1();
    @Key("meter.token.code2")
    String getMeterTokenCode2();
    @Key("meter.token.code3")
    String getMeterTokenCode3();
    @Key("meter.token.code4")
    String getMeterTokenCode4();
    @Key("mdc.txn.show.connect.disconnect.only")
    String getMdcTxnConnectDisconnect();
    @Key("mdc.txn.show.balance.messages.only")
    String getMdcTxnBalanceMessages();

    @Key("meter.new.supplygroupcode")
    String getMeterNewSupplygroupcode();

    @Key("meter.old.supplygroupcode")
    String getMeterOldSupplygroupcode();

    @Key("meter.new.tariffindex")
    String getMeterNewTariffindex();

    @Key("meter.old.tariffindex")
    String getMeterOldTariffindex();

    @Key("meter.new.keyrevisionnum")
    String getMeterNewKeyrevisionnum();

    @Key("meter.old.keyrevisionnum")
    String getMeterOldKeyrevisionnum();

    @Key("meter.clear.tid")
    String getMeterClearTid();

    @Key("meter.txn.date")
    String getMeterTxnDate();
    @Key("meter.txn.user")
    String getMeterTxnUser();
    @Key("meter.txn.user.ref")
    String getMeterTxnUserRef();

    @Key("meter.txn.bulk.import.file.name")
    String getImportFileName();

    @Key("meter.select.store.move")
    String getMeterSelectStoreMove();
    @Key("meter.select.store.add")
    String getMeterSelectStoreAdd();
    @Key("meter.select.store.help")
    String getMeterSelectStoreHelp();
    @Key("meter.message.type")
    String getMeterMessageType();
    @Key("meter.message.type.help")
    String getMeterMessageTypeHelp();
    @Key("meter.assigned.to.usagepoint")
    String getMeterAssignedToUsagePoint();

    @Key("mrid.ui")
    String getMrid();
    @Key("mrid.ui.help")
    String getMridHelp();
    @Key("mrid.ui.external")
    String getMridExternal();
    @Key("mrid.ui.external.help")
    String getMridExternalHelp();

    @Key("meter.centian.header")
    String getMeterCentianHeader();
    @Key("meter.centian.kwh.credit.remaining")
    String getMeterCentianKWhCreditRemaining();
    @Key("meter.centian.currency.credit.remaining")
    String getMeterCentianCurrencyCreditRemaining();
    @Key("meter.centian.number.disconnections")
    String getMeterCentianNumDisconnections();
    @Key("meter.centian.tamper.detected")
    String getMeterCentianTamperDetected();
    @Key("meter.centian.tamper.updated")
    String getMeterCentianInformationDate();
    @Key("meter.centian.current.tamper.status.header")
    String getMeterCentianCurrentTamperStatusHeader();
    @Key("meter.centian.current.tamper.status.description")
    String getMeterCentianCurrentTamperStatusDescription();
    @Key("meter.centian.current.tamper.status.updated")
    String getMeterCentianCurrentTamperStatusUpdated();

    @Key("meter.online.bulk.header")
    String getMeterOnlineBulkHeader();
    @Key("meter.online.bulk.select.meters.button")
    String getSelectMetersButton();
    @Key("online.bulk.panel.up.group.info.title")
    String getUsagePointGroupPanelTitle();
    @Key("online.bulk.panel.customer.info.title")
    String getCustomerInformation();
    @Key("online.bulk.panel.select.store.help")
    String getOnlineBulkSelectStoreHelp();
    @Key("online.bulk.panel.meter.help")
    String getOnlineBulkMeterNumberHelp();
    @Key("online.bulk.panel.suite.no.text")
    String getOnlineBulkSuiteNoText();
    @Key("online.bulk.panel.tenant.text")
    String getOnlineBulkTenantText();
    @Key("online.bulk.panel.surname.help")
    String getOnlineBulkSurnameHelp();
    @Key("meter.number.suggestion.help")
    String getMeterNumberSuggestionHelp();
    @Key("online.bulk.panel.tariffindex.help")
    String getOnlineBulkTariffIndxHelpMeter();

    @Key("meter.online.bulk.customer.phone.help")
    String getOnlineBulkCustomerPhoneHelp();
    @Key("online.bulk.panel.supplygroupcode.help")
    String getOnlineBulkSupplyGroupCodeHelp();
    @Key("online.bulk.panel.encryptionKey.help")
    String getEncryptionKeyHelp();
    @Key("meter.online.bulk.free.issue.title")
    String getOnlineBulkFreeIssueTitle();
    @Key("meter.online.bulk.free.issue.generate")
    String getOnlineBulkFreeIssueGenerate();
    @Key("meter.online.bulk.free.issue.sms.token")
    String getOnlineBulkFreeIssueSmsToken();
    @Key("meter.online.bulk.free.issue.sms.token.help")
    String getOnlineBulkFreeIssueSmsTokenHelp();
    @Key("credit.token.link")
    String getCreditTokenLink();
    @Key("eng.token.link")
    String getEngTokenLink();

    @Key("meter.new.current.pricingstructure.required")
    String getMeterNewCurrentPSRequired();
    @Key("meter.new.current.pricingstructure.select")
    String getMeterNewCurrentSelectPS();
    @Key("usagepoint.ps.future.lbl")
    String getMeterNewFutureSelectPS();

    @Key("meter.manufacturer.code.length")
    String getMeterManufacturerCodeLength();
    @Key("meter.manufacturer.code.length.help")
    String getMeterManufacturerCodeLengthHelp();
    @Key("meter.2digit.manufacturer.code")
    String getMeter2DigitManufacturerCode();
    @Key("meter.4digit.manufacturer.code")
    String getMeter4DigitManufacturerCode();

    @Key("unitsacc.title")
    String getUnitsAccountTitle();
    @Key("unitsacc.name.help")
    String getUnitsAccountNameHelp();
    @Key("unitsacc.name")
    String getUnitsAccountName();
    @Key("unitsacc.balance.help")
    String getUnitsAccountBalanceHelp();
    @Key("unitsacc.balance")
    String getUnitsAccountBalance();
    @Key("unitsacc.sync.help")
    String getUnitsAccountSyncHelp();
    @Key("unitsacc.sync")
    String getUnitsAccountSyncButton();
    @Key("unitsacc.low.balance.threshold.help")
    String getUnitsAccountLowBalanceThresholdHelp();
    @Key("unitsacc.low.balance.threshold")
    String getUnitsAccountLowBalanceThreshold();
    @Key("unitsacc.notification.email.help")
    String getUnitsAccountNotificationEmailHelp();
    @Key("unitsacc.notification.email")
    String getUnitsAccountNotificationEmail();
    @Key("unitsacc.notification.phone.help")
    String getUnitsAccountNotificationPhoneHelp();
    @Key("unitsacc.notification.phone")
    String getUnitsAccountNotificationPhone();
    @Key("unitsacc.note")
    String getUnitsAccountNote();
    @Key("unitsacc.required")
    String getUnitsAccountRequired();

    @Key("customer.search.instructions")
    String getCustomerSearchInstructions();
    @Key("customer.add")
    String getAddCustomer();
    @Key("customer.title")
    String getCustomerTitle();
    @Key("customer.info.title")
    String getCustomerInfoTitle();
    @Key("customer.show.info")
    String getCustomerShowInfo();
    @Key("customer.showing.info")
    String getCustomerShowingInfo();
    @Key("customer.active.help")
    String getCustomerActiveHelp();
    @Key("customer.active")
    String getCustomerActive();
    @Key("customer.unassign")
    String getCustomerUnassign();
    @Key("customer.assign")
    String getCustomerAssign();
    @Key("customer.assign.short")
    String getCustomerAssignShort();
    @Key("customer.assign.help")
    String getCustomerAssignHelp();
    @Key("customer.unassign.help")
    String getCustomerUnassignHelp();
    @Key("customer.open")
    String getCustomerOpen();
    @Key("customer.field.title.help")
    String getCustomerFieldTitleHelp();
    @Key("customer.field.title")
    String getCustomerFieldTitle();
    @Key("customer.initials.help")
    String getCustomerInitialsHelp();
    @Key("customer.initials")
    String getCustomerInitials();
    @Key("customer.firstnames.help")
    String getCustomerFirstNamesHelp();
    @Key("customer.firstnames")
    String getCustomerFirstNames();
    @Key("customer.surname.help")
    String getCustomerSurnameHelp();
    @Key("customer.surname")
    String getCustomerSurname();
    @Key("customer.idnumber.help")
    String getCustomerIdNumberHelp();
    @Key("customer.idnumber")
    String getCustomerIdNumber();
    @Key("customer.company.help")
    String getCustomerCompanyHelp();
    @Key("customer.company")
    String getCustomerCompany();
    @Key("customer.tax.help")
    String getCustomerTaxHelp();
    @Key("customer.tax")
    String getCustomerTax();
    @Key("customer.emails.help")
    String getCustomerEmailsHelp();
    @Key("customer.email")
    String getCustomerEmail();
    @Key("customer.phones.help")
    String getCustomerPhonesHelp();
    @Key("customer.phones")
    String getCustomerPhones();
    @Key("customer.phone")
    String getCustomerPhone();
    @Key("customer.address.physical")
    String getCustomerPhysicalAddress();
    @Key("customer.agreement")
    String getCustomerAgreement();
    @Key("customer.agreementref.help")
    String getCustomerAgreementRefHelp();
    @Key("customer.agreementref")
    String getCustomerAgreementRef();
    @Key("customer.startdate.help")
    String getCustomerStartDateHelp();
    @Key("customer.startdate")
    String getCustomerStartDate();
    @Key("customer.freeissue.help")
    String getCustomerFreeIssueHelp();
    @Key("customer.freeissue")
    String getCustomerFreeIssue();
    @Key("customer.agreement.billable.help")
    String getBillableHelp();
    @Key("customer.agreement.billable")
    String getBillable();
    @Key("customer.ref.label")
    String getCustomerReference();
    @Key("customer.ref.help")
    String getCustomerReferenceHelp();

    @Key("customer.required")
    String getCustomerRequired();
    @Key("customer.required.activation")
    String getCustomerRequiredActivation();
    @Key("customer.surname.instr")
    String getCustomerEnterSurname();
    @Key("customer.search.listbox.label")
    String getCustomerSearchListboxLabel();
    @Key("customer.auxaccount.addedit")
    String getAddEditAuxAccount();
    @Key("customer.auxaccount.active.help")
    String getCustomerAuxAccountActiveHelp();
    @Key("customer.auxaccount.active")
    String getCustomerAuxAccountActive();
    @Key("customer.auxaccount.type.help")
    String getCustomerAuxAccountTypeHelp();
    @Key("customer.auxaccount.type")
    String getCustomerAuxAccountType();
    @Key("customer.auxaccount.name.help")
    String getCustomerAuxAccountNameHelp();
    @Key("customer.auxaccount.name")
    String getCustomerAuxAccountName();
    @Key("customer.auxaccount.balance.help")
    String getCustomerAuxAccountBalanceHelp();
    @Key("customer.auxaccount.balance.pos")
    String getCustomerAuxAccountBalancePos();
    @Key("customer.auxaccount.balance.neg")
    String getCustomerAuxAccountBalanceNeg();
    @Key("customer.auxaccount.balance")
    String getCustomerAuxAccountBalance();
    @Key("customer.auxaccount.priority.help")
    String getCustomerAuxAccountPriorityHelp();
    @Key("customer.auxaccount.priority")
    String getCustomerAuxAccountPriority();
    @Key("customer.auxaccount.chargeschedule.help")
    String getCustomerAuxAccountChargeScheduleHelp();
    @Key("customer.auxaccount.chargeschedule")
    String getCustomerAuxAccountChargeSchedule();
    @Key("customer.title.auxaccounts")
    String getCustomerAuxAccountsTitle();
    @Key("customer.title.auxaccounts.current")
    String getCurrentAuxAccountsTitle();
    @Key("customer.title.auxaccounts.description")
    String getAuxAccountsDescription();
    @Key("customer.auxaccount.balance.type.debt")
    String getAuxAccountBalanceDebtLbl();
    @Key("customer.auxaccount.balance.type.refund")
    String getAuxAccountBalanceRefundLbl();
    @Key("customer.auxaccount.amount.pos")
    String getCustomerAuxAccountAmountPos();
    @Key("customer.auxaccount.amount.neg")
    String getCustomerAuxAccountAmountNeg();
    @Key("customer.auxaccount.start.date.lbl")
    String getCustomerAuxAccountStartDateLbl();
    @Key("customer.auxaccount.start.date.help")
    String getCustomerAuxAccountStartDateHelp();
    @Key("customer.auxaccount.last.charge.date.lbl")
    String getCustomerAuxAccountLastChargeDateLbl();
    @Key("customer.auxaccount.last.charge.date.help")
    String getCustomerAuxAccountLastChargeDateHelp();
    @Key("customer.auxaccount.suspend.until.lbl")
    String getCustomerAuxAccountSuspendLbl();
    @Key("customer.auxaccount.suspend.until.help")
    String getCustomerAuxAccountSuspendHelp();
    @Key("customer.auxaccount.install.suspend.info")
    String getCustomerAuxAccountInstallSuspendInfo();
    @Key("customer.debt.status.lbl")
    String getCustomerDebtStatusLbl();

    @Key("customer.title.txnhistory")
    String getCustomerTxnHistoryTitle();

    @Key("customer.auxaccount.history.title")
    String getCustomerAuxAccHistoryTitle();

    @Key("customer.title.history")
    String getCustomerHistoryTitle();
    @Key("customer.title.reports")
    String getCustomerReportsTitle();
    @Key("customer.title.generalreports")
    String getCustomerGeneralReportsTitle();
    @Key("customer.title.chargescheduledetails")
    String getCustomerChargeScheduleTitleDetails();
    @Key("customer.chargeschedule.startdate")
    String getCustomerChargeScheduleStartDate();
    @Key("customer.chargeschedule.vendpor")
    String getCustomerChargeScheduleVendPortion();
    @Key("customer.chargeschedule.currpor")
    String getCustomerChargeScheduleCurrPortion();
    @Key("customer.chargeschedule.cycle")
    String getCustomerChargeScheduleCycle();
    @Key("customer.chargeschedule.chamt")
    String getCustomerChargeScheduleChargeAmt();
    @Key("customer.chargeschedule.minamt")
    String getCustomerChargeScheduleMinAmt();
    @Key("customer.chargeschedule.maxamt")
    String getCustomerChargeScheduleMaxAmt();
    @Key("customer.chargeschedule.status")
    String getCustomerChargeScheduleStatus();
    @Key("customer.auxaccount.filter")
    String getCustomerAuxAccountFilter();
    @Key("customer.history.filter")
    String getCustomerHistoryFilter();
    @Key("customer.agreement.history.filter")
    String getCustomerAgreementHistoryFilter();

    @Key("customer.account")
    String getCustomerAccount();
    @Key("customer.account.name")
    String getCustomerAccountName();
    @Key("customer.account.name.help")
    String getCustomerAccountNameHelp();
    @Key("customer.account.balance")
    String getCustomerAccountBalance();
    @Key("customer.account.balance.help")
    String getCustomerAccountBalanceHelp();
    @Key("customer.account.sync")
    String getCustomerAccountSyncButton();
    @Key("customer.account.sync.help")
    String getCustomerAccountSyncHelp();
    @Key("customer.account.low.balance.threshold")
    String getCustomerAccountLowBalanceThreshold();
    @Key("customer.account.low.balance.threshold.help")
    String getCustomerAccountLowBalanceThresholdHelp();
    @Key("customer.account.credit.limit")
    String getCustomerAccountCreditLimit();
    @Key("customer.account.credit.limit.help")
    String getCustomerAccountCreditLimitHelp();
    @Key("customer.account.note")
    String getCustomerAccountNote();
    @Key("customer.account.notification.email")
    String getCustomerAccountNotificationEmail();
    @Key("customer.account.notification.email.help")
    String getCustomerAccountNotificationEmailHelp();
    @Key("customer.account.notification.phone")
    String getCustomerAccountNotificationPhone();
    @Key("customer.account.notification.phone.help")
    String getCustomerAccountNotificationPhoneHelp();

    @Key("customer.txn.filter")
    String getCustomerTxnFilter();
    @Key("customer.txn.acc.ref")
    String getCustomerTxnAccRef();
    @Key("customer.txn.our.ref")
    String getCustomerTxnOurRef();
    @Key("customer.txn.comment")
    String getCustomerTxnComment();
    @Key("customer.txn.amt.incl.tax")
    String getCustomerTxnAmtInclTax();
    @Key("customer.txn.tax")
    String getCustomerTxnTax();

    @Key("customer.trans.upload")
    String getTransactionUpload();
    @Key("customer.trans.upload.heading")
    String getTransUploadHeading();
    @Key("customer.trans.upload.data.title")
    String getTransUploadTitle();
    @Key("customer.trans.upload.data.description")
    String getTransUploadDescription();
    @Key("customer.trans.upload.file.help")
    String getTransUploadFileHelp();
    @Key("customer.trans.upload.file")
    String getTransUploadFile();
    @Key("customer.trans.upload.csv.button")
    String getTransUploadCsvButton();
    @Key("customer.process.trans.button")
    String getProcessTransButton();

    @Key("auxaccount.trans.upload")
    String getAuxTransactionUpload();
    @Key("auxaccount.upload")
    String getAuxAccountUpload();

    @Key("location.field.erfnumber")
    String getLocationErfNumber();
    @Key("location.field.erfnumber.help")
    String getLocationErfNumberHelp();

    @Key("location.field.streetnumber")
    String getLocationStreetNumber();
    @Key("location.field.streetnumber.help")
    String getLocationStreetNumberHelp();
    @Key("location.field.buildingname")
    String getLocationBuildingName();
    @Key("location.field.buildingname.help")
    String getLocationBuildingNameHelp();
    @Key("location.field.suitenumber")
    String getLocationSuiteNumberName();
    @Key("location.field.suitenumber.help")
    String getLocationSuiteNumberHelp();

    @Key("location.field.address")
    String getLocationAddress();
    @Key("location.field.address.help")
    String getLocationAddressHelp();
    @Key("location.field.city")
    String getLocationCity();
    @Key("location.field.city.help")
    String getLocationCityHelp();
    @Key("location.field.province")
    String getLocationProvince();
    @Key("location.field.province.help")
    String getLocationProvinceHelp();
    @Key("location.field.country")
    String getLocationCountry();
    @Key("location.field.country.help")
    String getLocationCountryHelp();
    @Key("location.field.postalcode")
    String getLocationPostalCode();
    @Key("location.field.postalcode.help")
    String getLocationPostalCodeHelp();
    @Key("location.field.lat")
    String getLocationLat();
    @Key("location.field.lat.help")
    String getLocationLatHelp();
    @Key("location.field.long")
    String getLocationLong();
    @Key("location.field.long.help")
    String getLocationLongHelp();

    @Key("changegroup.change")
    String getChangeGroup();
    @Key("changegroup.username")
    String getUsername();
    @Key("changegroup.username.help")
    String getUsernameHelp();
    @Key("changegroup.current")
    String getCurrentGroup();
    @Key("changegroup.assigned")
    String getAssignedGroup();
    @Key("changegroup.select")
    String getSelectNewGroup();
    @Key("changegroup.available")
    String getAvailableGroupsHeader();
    @Key("group.current.none")
    String getNoUserGroups();
    @Key("changegroup.error.group.none")
    String getNoGroupSelectedError();
    @Key("changegroup.error.same")
    String getSameGroupSelectedError();
    @Key("changegroup.available")
    String getAvailableGroups();
    @Key("changegroup.warning")
    String getChangeGroupWarning();

    @Key("usergroup.header")
    String getUserGroupHeader();
    @Key("usergroup.title")
    String getUserGroupTitle();
    @Key("usergroup.label")
    String getUserGroup();
    @Key("usergroup.label.help")
    String getUserGroupHelp();
    @Key("usergroup.title.current")
    String getCurrentUserGroupHeader();
    @Key("usergroup.field.usergroup")
    String getAssignedUserGroup();
    @Key("usergroup.accessgroup.none")
    String getNoAccessGroups();

    @Key("accessgroup.access.label")
    String getAccessGroupLabel();
    @Key("accessgroup.access.instructions")
    String getAccessGroupInstructions();

    @Key("search.advanced.header")
    String getAdvancedSearchHeader();
    @Key("search.advanced.results.header")
    String getAdvancedSearchResultsHeader();
    @Key("search.advanced.results.header.current")
    String getCurrentAdvancedSearchResultsHeader();
    @Key("search.meter.number")
    String getSearchMeterNumber();
    @Key("search.meter.model")
    String getMeterModel();
    @Key("search.meter.no.usage.point")
    String getSearchMeterNoUsagePoint();
    @Key("search.meter.no.usage.point.help")
    String getSearchMeterNoUsagePointHelp();
    @Key("search.meter.sgc.label1")
    String getAdvancedSearchSgcLabel();
    @Key("search.meter.sgc.label2")
    String getAdvancedSearchSgcLabel2();
    @Key("search.meter.sgc.help")
    String getAdvancedSearchSgcHelp();
    @Key("search.type")
    String getSearchType();
    @Key("search.type.agreement")
    String getAgreementSearchType();
    @Key("search.startswith")
    String getSearchStartsWith();
    @Key("search.contains")
    String getSearchContains();
    @Key("search.customer")
    String getSearchCustomer();
    @Key("search.customer.name")
    String getSearchCustomerName();
    @Key("search.customer.surname")
    String getSearchCustomerSurname();
    @Key("search.customer.idnumber")
    String getSearchCustomerIdNumber();
    @Key("search.customer.title")
    String getSearchCustomerTitle();
    @Key("search.customer.agreement")
    String getSearchCustomerAgreement();
    @Key("search.customer.agreement.ref")
    String getSearchAgreementReference();
    @Key("search.account")
    String getSearchAccount();

    @Key("search.customer.phone1.number")
    String getSearchPhone1Number();
    @Key("search.customer.phone2.number")
    String getSearchPhone2Number();
    @Key("search.customer.phone.number")
    String getSearchPhoneNumber();
    @Key("search.customer.custom.textfield1")
    String getSearchCustom1();

    @Key("search.location.header")
    String getLocationSearchHeader();
    @Key("search.location.erf.number")
    String getSearchLocationErfNumber();
    @Key("search.location.building.name")
    String getSearchLocationBuildingName();
    @Key("search.location.suite.number")
    String getSearchLocationSuitNumber();
    @Key("search.location.address1")
    String getSearchLocationAddress1();
    @Key("search.location.address2")
    String getSearchLocationAddress2();
    @Key("search.location.address3")
    String getSearchLocationAddress3();
    @Key("search.location.type")
    String getLocationSearchType();
    @Key("search.location.type.label")
    String getLocationSearchTypeHeader();
    @Key("search.location.type.customer")
    String getLocationSearchTypeCustomer();
    @Key("search.location.type.usagepoint")
    String getLocationSearchTypeUsagePoint();

    @Key("search.account.name")
    String getSearchAccountName();
    @Key("search.customer.no.usage.point")
    String getSearchCustNoUsagePoint();
    @Key("search.usagepoint.name")
    String getSearchUsagePointName();
    @Key("search.usage.point.no.customer")
    String getSearchUsagePointNoCustomer();
    @Key("search.usage.point.no.meter")
    String getSearchUsagePointNoMeter();
    @Key("search.meter")
    String getMeterSearchHeader();
    @Key("search.customer")
    String getCustomerSearchHeader();
    @Key("search.usagepoint")
    String getUsagePointSearchHeader();
    @Key("search.no.results")
    String getNoSearchResultsMessage();
    @Key("search.meter.result")
    String getMeterSearchResultHeader();
    @Key("search.meter.model.result")
    String getMeterModelSearchResultHeader();
    @Key("search.customer.result")
    String getCustomerSearchResultHeader();
    @Key("search.usagepoint.result")
    String getUsagePointSearchResultHeader();
    @Key("search.pricingStructure.result")
    String getPricingStructureSearchResultHeader();
    @Key("search.paymentMode.result")
    String getPaymentModeSearchResultHeader();
    @Key("search.name")
    String getSearchName();

    @Key("search.get.total.label")
    String getChckbxGetTotalLabel();
    @Key("search.count.label")
    String getSearchCountLabel();

    @Key("link.search.meters.viewed")
    String getLastViewedMetersLink();
    @Key("link.search.meters.modified")
    String getLastModifiedMetersLink();

    @Key("meterreadings.title")
    String getMeterReadingsHeader();
    @Key("meterreadings.header.graph")
    String getMeterReadingsGraphHeader();
    @Key("meterreadings.title.graph")
    String getMeterReadingsGraphTitle();
    @Key("meterreadings.start")
    String getMeterReadingsStart();
    @Key("meterreadings.end")
    String getMeterReadingsEnd();
    @Key("meterreadings.type")
    String getReadingType();
    @Key("meterreadings.graph.type")
    String getGraphType();
    @Key("meterreadings.balancing")
    String getBalancingMeter();
    @Key("meterreadings.header.table")
    String getMeterReadingsTableHeader();
    @Key("meterreadings.title.table")
    String getMeterReadingsTableTitle();
    @Key("meterreadings.report.results")
    String getMeterReadingReportsResultsHeader();
    @Key("meterreadings.meter.field.super")
    String getSuperMeterName();
    @Key("meterreadings.meter.field.subs")
    String getSubMetersName();

    @Key("link.energybalancing")
    String getEnergyBalancingLink();
    @Key("energybalancing.header")
    String getEnergyBalancingHeader();
    @Key("energybalancing.title")
    String getEnergyBalancingTitle();
    @Key("energybalancing.start")
    String getEnergyBalancingStart();
    @Key("energybalancing.end")
    String getEnergyBalancingEnd();
    @Key("energybalancing.variation")
    String getEnergyBalancingVariation();

    @Key("link.energybalancing.meters")
    String getEnergyBalancingMetersLink();
    @Key("energybalancing.meter.header")
    String getEnergyBalancingMeterHeader();
    @Key("energybalancing.meter.super")
    String getSuperMeterNumber();
    @Key("energybalancing.meter.sub")
    String getSubMeterNumber();
    @Key("energybalancing.meter.subs")
    String getSubMeters();
    @Key("energybalancing.meter.super.help")
    String getSuperMeterNumberHelp();
    @Key("energybalancing.meter.sub.help")
    String getSubMeterNumberHelp();
    @Key("energybalancing.meter.subs.help")
    String getSelectedSubMetersHelp();
    @Key("energybalancing.meter.instructions")
    String getSuperSubMeterInstructions();

    @Key("meter.manufacturers")
    String getManufacturersHeader();
    @Key("meter.manufacturers.title")
    String getManufacturersTitle();
    @Key("meter.manufacturers.field.name")
    String getManufacturerName();
    @Key("meter.manufacturers.field.name.help")
    String getManufacturerNameHelp();
    @Key("meter.manufacturers.field.description")
    String getManufacturerDescription();
    @Key("meter.manufacturers.field.description.help")
    String getManufacturerDescriptionHelp();
    @Key("meter.manufacturers.field.active")
    String getManufacturerActive();
    @Key("meter.manufacturers.field.active.help")
    String getManufacturerActiveHelp();

    @Key("meter.mdc")
    String getMdcHeader();
    @Key("meter.mdc.name")
    String getMdcName();
    @Key("meter.mdc.title")
    String getMdcTitle();
    @Key("meter.mdc.field.name.help")
    String getMdcNameHelp();
    @Key("meter.mdc.field.description")
    String getMdcDescription();
    @Key("meter.mdc.field.description.help")
    String getMdcDescriptionHelp();
    @Key("meter.mdc.field.active")
    String getMdcActive();
    @Key("meter.mdc.field.value")
    String getMdcValue();
    @Key("meter.mdc.field.value.help")
    String getMdcValueHelp();
    @Key("meter.mdc.field.active.help")
    String getMdcActiveHelp();
    @Key("mdc.txn.messages")
    String getMdcTxnLabel();
    @Key("mdc.txn.message")
    String getMdcTxnMessage();
    @Key("mdc.txn.send.message.title")
    String getMdcSendMessageTitle();
    @Key("mdc.txn.override.lbl")
    String getMdcTxnOverrideLbl();
    @Key("mdc.txn.override.help")
    String getMdcTxnOverrideHelp();

    @Key("channel.header")
    String getChannelHeader();
    @Key("channel.config.header")
    String getConfigChannelHeader();
    @Key("channel.title")
    String getChannelTitle();
    @Key("channel.field.value")
    String getChannelValueLabel();
    @Key("channel.field.value.help")
    String getChannelValueHelp();
    @Key("channel.field.name")
    String getChannelNameLabel();
    @Key("channel.field.descrip")
    String getChannelDescripLabel();
    @Key("channel.field.billingdetname")
    String getBillingDetNameLabel();
    @Key("channel.field.billingdetname.channel.help")
    String getBillingDetNameChannelHelp();
    @Key("channel.field.meter.reading.type")
    String getChannelMeterReadingTypeLabel();
    @Key("channel.field.time.interval.help")
    String getChannelTimeIntervalHelp();
    @Key("channel.field.time.interval")
    String getChannelTimeIntervalLabel();
    @Key("channel.field.maxsize")
    String getChannelMaxSizeLabel();
    @Key("channel.field.maxsize.help")
    String getChannelMaxSizeHelp();
    @Key("channel.field.reading_multiplier")
    String getChannelReadingMultiplierLabel();
    @Key("channel.field.reading_multiplier.help")
    String getChannelReadingMultiplierHelp();
    @Key("channel.field.active.help")
    String getChannelActiveHelp();
    @Key("channel.field.active")
    String getChannelActive();
    @Key("channel.readings.header")
    String getChannelReadingsHeader();
    @Key("channel.readings.header.up")
    String getChannelReadingsHeaderUp();
    @Key("channel.readings.meter.model")
    String getChannelReadingsMeterModel();
    @Key("channel.readings.mdc")
    String getChannelReadingsMdc();
    @Key("channel.readings.pricing.structure")
    String getChannelReadingsPricingStructure();
    @Key("channel.readings.installdate")
    String getChannelReadingsInstallDate();
    @Key("channel.readings.import.note")
    String getChannelReadingsImportNote();
    @Key("channel.readings.timestamp.label")
    String getChannelReadingsTimestampLabel();
    @Key("channel.readings.timestamp.help")
    String getChannelReadingsTimestampHelp();
    @Key("file.item.panel.reg.read.reminder")
    String getFileItemPanelRegReadReminder();

    @Key("mdc.channel.field.time.interval")
    String getMdcChannelTimeIntervalLabel();
    @Key("channel.override.field.time.interval")
    String getChannelOverrideTimeIntervalLabel();
    @Key("mdc.channel.field.maxsize")
    String getMdcChannelMaxSizeLabel();
    @Key("channel.override.field.maxsize")
    String getChannelOverrideMaxSizeLabel();
    @Key("mdc.channel.field.reading_multiplier")
    String getMdcChannelReadingMultiplierLabel();
    @Key("channel.override.field.reading_multiplier")
    String getChannelOverrideReadingMultiplierLabel();

    @Key("billingdet.header")
    String getBillingDetHeader();
    @Key("billingdet.title")
    String getBillingDetTitle();
    @Key("billingdet.field.name")
    String getBillingDetName();
    @Key("billingdet.field.name.help")
    String getBillingDetNameHelp();
    @Key("billingdet.field.description")
    String getBillingDetDescription();
    @Key("billingdet.field.description.help")
    String getBillingDetDescriptionHelp();
    @Key("billingdet.active")
    String getBillingDetActive();
    @Key("billingdet.active.help")
    String getBillingDetActiveHelp();
    @Key("billingdet.appliesto.group.label")
    String getBillingDetGroupLabel();
    @Key("billingdet.discount.label")
    String getBillingDetDiscount();
    @Key("billingdet.discount.help")
    String getBillingDetDiscountHelp();
    @Key("billingdet.applies.to.label")
    String getBillingDetAppliesToLabel();
    @Key("billingdet.applies.to.help")
    String getBillingDetAppliesToHelp();
    @Key("billingdet.charge.type.help")
    String getBillingDetChargeTypeHelp();
    @Key("billingdet.taxable")
    String getBillingDetTaxable();
    @Key("billingdet.taxable.help")
    String getBillingDetTaxableHelp();
    
    @Key("unitcharge.choose.charge.type")
    String getChargeType();
    @Key("unitcharge.type.none")
    String getChargeTypeNone();
    @Key("unitcharge.type.percentage")
    String getChargeTypePercentage();
    @Key("unitcharge.type.flatrate")
    String getChargeTypeFlatRate();

    @Key("meter.models")
    String getMeterModelsHeader();
    @Key("meter.models.title")
    String getMeterModelsTitle();
    @Key("meter.models.field.manufacturer")
    String getMeterModelsManufacturer();
    @Key("meter.models.field.manufacturer.help")
    String getMeterModelsManufacturerHelp();
    @Key("meter.models.field.name")
    String getMeterModelName();
    @Key("meter.models.field.name.help")
    String getMeterModelNameHelp();
    @Key("meter.models.field.description")
    String getMeterModelDescription();
    @Key("meter.models.field.description.help")
    String getMeterModelDescriptionHelp();
    @Key("meter.models.field.active")
    String getMeterModelActive();
    @Key("meter.models.field.active.help")
    String getMeterModelActiveHelp();
    @Key("meter.models.field.resource")
    String getMeterModelsServiceResource();
    @Key("meter.models.field.resource.help")
    String getMeterModelsServiceResourceHelp();
    @Key("meter.models.field.metertype")
    String getMeterModelsMeterType();
    @Key("meter.models.field.metertype.help")
    String getMeterModelsMeterTypeHelp();
    @Key("meter.models.field.toa")
    String getMeterModelTOA();
    @Key("meter.models.field.toa.help")
    String getMeterModelTOAHelp();
    @Key("meter.models.field.mdc")
    String getMdc();
    @Key("meter.models.field.mdc.help")
    String getMdcHelp();
    @Key("meter.models.field.paymentmodes")
    String getMeterModelsPaymentModes();
    @Key("meter.models.field.paymentmodes.help")
    String getMeterModelsPaymentModesHelp();
    @Key("meter.models.field.balance.sync")
    String getMeterModelBalanceSync();
    @Key("meter.models.field.balance.sync.help")
    String getMeterModelBalanceSyncHelp();
    @Key("meter.models.field.needs.breaker.id")
    String getMeterModelNeedsBreakerId();
    @Key("meter.models.field.needs.breaker.id.help")
    String getMeterModelNeedsBreakerIdHelp();
    @Key("meter.models.field.message.display")
    String getMeterModelMessageDisplay();
    @Key("meter.models.field.uri.present")
    String getMeterModelUriPresent();
    @Key("meter.models.field.uri.present.help")
    String getMeterModelUriPresentHelp();
    @Key("meter.uri.address")
    String getMeterUriAddress();
    @Key("meter.uri.address.help")
    String getMeterUriAddressHelp();
    @Key("meter.uri.port")
    String getMeterUriPort();
    @Key("meter.uri.port.help")
    String getMeterUriPortHelp();
    @Key("meter.uri.protocol")
    String getMeterUriProtocol();
    @Key("meter.uri.protocol.help")
    String getMeterUriProtocolHelp();
    @Key("meter.uri.params")
    String getMeterUriParams();
    @Key("meter.uri.params.help")
    String getMeterUriParamsHelp();
    @Key("meter.uri.fields")
    String getMeterUriFields();
    @Key("meter.models.field.message.display.help")
    String getMeterModelMessageDisplayHelp();
    @Key("meter.models.field.needs.encryption.key")
    String getMeterModelNeedsEncryptionKey();
    @Key("meter.models.field.needs.encryption.key.help")
    String getMeterModelNeedsEncryptionKeyHelp();
    @Key("meter.models.battery.capacity")
    String getMeterModelBatteryCapacity();
    @Key("meter.models.battery.capacity.help")
    String getMeterModelBatteryCapacityHelp();
    @Key("meter.models.battery.threshold")
    String getMeterModelBatteryThreshold();
    @Key("meter.models.battery.threshold.help")
    String getMeterModelBatteryThresholdHelp();
    @Key("meter.models.battery.event.lbl")
    String getMeterModelBatteryEventLbl();

    @Key("ptr.serviceresource")
    String getServiceResource();
    @Key("ptr.metertype")
    String getMeterType();
    @Key("ptr.paymentmode")
    String getPaymentMode();

    @Key("pricingstructure.field.serviceresource.help")
    String getPricingServiceResourceHelp();
    @Key("pricingstructure.field.metertype.help")
    String getPricingMeterTypeHelp();
    @Key("pricingstructure.field.paymentmode.help")
    String getPricingPaymentModeHelp();

    @Key("tou.thin.field.calendar")
    String getTouThinCalendar();
    @Key("tou.thin.field.calendar.help")
    String getTouThinCalendarHelp();
    @Key("tou.thin.field.monthlydemand")
    String getMonthlyDemandCharge();
    @Key("tou.thin.field.monthlydemand.help")
    String getMonthlyDemandChargeHelp();
    @Key("tou.thin.field.monthlydemandtype")
    String getMonthlyDemandType();
    @Key("tou.thin.field.monthlydemandtype.help")
    String getMonthlyDemandTypeHelp();
    @Key("tou.thin.field.servicecharge")
    String getServiceCharge();
    @Key("tou.thin.field.servicecharge.help")
    String getServiceChargeHelp();
    @Key("tou.thin.field.servicecharge.descrip")
    String getServiceChargeDescrip();
    @Key("tou.thin.field.servicecharge.descrip.help")
    String getServiceChargeDescripHelp();
    @Key("tou.thin.field.servicechargecycle")
    String getServiceChargeCycle();
    @Key("tou.thin.field.servicechargecycle.help")
    String getServiceChargeCycleHelp();
    @Key("tou.thin.field.enablereadingtypes")
    String getEnableReadingTypes();
    @Key("tou.thin.field.enablereadingtypes.help")
    String getEnableReadingTypesHelp();
    @Key("tou.thin.charges.button")
    String getGenerateChargesButton();
    @Key("tou.thin.field.charges")
    String getTouThinCharges();
    @Key("tou.thin.field.charges.help")
    String getTouThinChargesHelp();
    @Key("tou.thin.field.charges.specialday")
    String getSpecialDaysCharges();
    @Key("tou.thin.field.charges.specialday.help")
    String getSpecialDaysChargesHelp();

    @Key("register.reading.billing.determinant.title")
    String getRegisterReadingBillingDeterminantTitle();
    @Key("register.reading.billing.determinant.help")
    String getRegisterReadingBillingDeterminantHelp();
    @Key("register.reading.rates.button")
    String getRegisterReadingRatesButtonText();
    @Key("register.reading.rates.title")
    String getRegisterReadingRatesTitle();
    @Key("register.reading.rates.help")
    String getRegisterReadingRatesHelp();

    @Key("register.reading.txn.label")
    String getRegisterReadingTxnLabel();
    @Key("register.reading.txn.filter")
    String getRegisterReadingTxnFilter();

    @Key("appsetting.header")
    String getAppSettingHeader();
    @Key("appsetting.title")
    String getAppSettingTitle();
    @Key("appsetting.field.name")
    String getAppSettingName();
    @Key("appsetting.field.name.help")
    String getAppSettingNameHelp();
    @Key("appsetting.field.value")
    String getAppSettingValue();
    @Key("appsetting.field.value.help")
    String getAppSettingValueHelp();
    @Key("appsetting.field.description")
    String getAppSettingDescription();
    @Key("appsetting.field.description.help")
    String getAppSettingDescriptionHelp();
    @Key("appsetting.field.datatype")
    String getAppSettingDataType();
    @Key("appsetting.field.datatype.help")
    String getAppSettingDataTypeHelp();
    @Key("appsetting.field.datatype.listitems")
    String getAppSettingDataTypeListItems();
    @Key("appsettings.popup.table.note.dups")
    String getAppSettingDataTypeListItemDups();

    @Key("demo.addmeterreadings.link")
    String getDemoMeterReadingsLink();
    @Key("demo.addmeterreadings.header")
    String getAddMeterReadingsHeader();
    @Key("demo.addmeterreadings.interval")
    String getMeterReadingInterval();
    @Key("demo.addmeterreadings.readingtypes")
    String getMeterReadingTypes();
    @Key("demo.addmeterreadings.delete")
    String getDeleteExistingMeterReadings();
    @Key("Send tariffCalcReq after readings")
    String getTariffCalcAfterMeterReadingsTitle();

    @Key("demo.addsupermeterreadings.link")
    String getDemoSuperMeterReadingsLink();
    @Key("demo.addsupermeterreadings.header")
    String getAddSuperMeterReadingsHeader();
    @Key("demo.addsupermeterreadings.readingtype")
    String getMeterReadingType();
    @Key("demo.addsupermeterreadings.super.delete.readings")
    String getDeleteExistingSuperMeterReadings();
    @Key("demo.addsupermeterreadings.subs.regen.readings")
    String getRegenerateSubMeterReadings();
    @Key("demo.addsupermeterreadings.variation")
    String getMeterReadingVariation();
    @Key("demo.addsupermeterreadings.variations")
    String getMeterReadingVariations();
    @Key("demo.addsupermeterreadings.variations.help")
    String getMeterReadingVariationsHelp();
    @Key("demo.addsupermeterreadings.hour")
    String getHour();
    @Key("demo.addsupermeterreadings.supermeter")
    String getSuperMeter();

    @Key("taskschedule.title")
    String getTaskScheduleHeader();
    @Key("taskschedule.header")
    String getTaskScheduleTitle();
    @Key("taskschedule.name")
    String getTaskScheduleName();
    @Key("taskschedule.name.help")
    String getTaskScheduleNameHelp();
    @Key("taskschedule.active")
    String getTaskScheduleActive();
    @Key("taskschedule.active.help")
    String getTaskScheduleActiveHelp();
    @Key("taskschedule.schedule")
    String getTaskScheduleSchedule();
    @Key("taskschedule.schedule.help")
    String getTaskScheduleScheduleHelp();
    @Key("taskschedule.hour")
    String getTaskScheduleHour();
    @Key("taskschedule.minutes")
    String getTaskScheduleMinutes();
    @Key("taskschedule.seconds")
    String getTaskScheduleSeconds();
    @Key("taskschedule.day")
    String getTaskScheduleDay();
    @Key("taskschedule.daymonth")
    String getTaskScheduleDayOfMonth();
    @Key("taskschedule.users")
    String getTaskScheduleUsers();
    @Key("taskschedule.meter.single")
    String getTaskScheduleSingleMeter();
    @Key("taskschedule.meter.super")
    String getTaskScheduleSuperMeter();
    @Key("taskschedule.meter.readingtype")
    String getTaskScheduleMeterReadingType();
    @Key("taskschedule.timeperiods")
    String getTaskScheduleTimePeriod();
    @Key("taskschedule.every")
    String getTaskScheduleEvery();
    @Key("taskschedule.of")
    String getTaskScheduleOf();
    @Key("taskschedule.time")
    String getTaskScheduleTime();

    @Key("scheduledtask.header")
    String getScheduledTasksHeader();
    @Key("scheduledtask.title")
    String getScheduledTasksTitle();
    @Key("scheduledtask.field.name")
    String getScheduledTaskName();
    @Key("scheduledtask.field.name.help")
    String getScheduledTaskNameHelp();
    @Key("scheduledtask.field.class")
    String getScheduledTaskClass();
    @Key("scheduledtask.field.class.help")
    String getScheduledTaskClassHelp();
    @Key("scheduledtask.previous.hours")
    String getTaskSchedulePreviousHours();
    @Key("scheduledtask.customer.name")
    String getCustomer();
    @Key("scheduledtask.customer.name.help")
    String getCustomerHelp();

    @Key("password.change.header")
    String getChangePasswordHeader();
    @Key("password.old")
    String getOldPassword();
    @Key("password.new")
    String getNewPassword();
    @Key("password.confirm")
    String getConfirmPassword();

    @Key("timer.session.timeout.warning")
    String getSessionTimoutWarning();
    @Key("timer.session.loggedin")
    String getSessionTimoutStayLoggedIn();
    @Key("timer.session.logout")
    String getSessionTimoutLoggedOut();

    @Key("login.title")
    String getLoginHeader();
    @Key("login.form.password")
    String getPassword();

    @Key("application.info")
    String getApplicationInfo();

    @Key("dashboard.meter.count.title")
    String getDashboardMeterCountTitle();
    @Key("dashboard.meter.count.description")
    String getDashboardMeterCountDescription();

    @Key("timezone.header")
    String getTimeZoneHeader();
    @Key("timezone.warning")
    String getTimeZoneWarning();
    @Key("timezone.label")
    String getTimeZone();

    @Key("import.data.header")
    String getImportDataHeader();
    @Key("import.meters.title")
    String getImportMetersTitle();

    @Key("import.data.metermodel.help")
    String getImportDataMeterModelHelp();
    @Key("import.data.metermodel")
    String getImportDataMeterModel();
    @Key("import.data.file.help")
    String getImportDataFileHelp();
    @Key("import.data.file")
    String getImportDataFile();
    @Key("import.data.button")
    String getImportDataButton();
    @Key("import.meters.heading")
    String getImportMetersHeading();
    @Key("import.meters.description")
    String getImportMetersDescription();

    @Key("bulk.upload.download.sample")
    String getBulkUploadSampleDescription();
    @Key("bulk.upload.download.sample.button")
    String getBulkUploadSampleButton();
    @Key("bulk.upload.file.select.labeltext")
    String getBulkUploadFileLabelText();
    @Key("bulk.upload.select.file.help")
    String getBulkUploadFileSelectHelp();
    @Key("bulk.upload.csv.button")
    String getBulkUploadCsvButton();
    @Key("bulk.upload.process.button")
    String getBulkUploadProcessButton();
    @Key("bulk.upload.gencsvtemplate.title")
    String getGenCsvTemplateTitle();
    @Key("bulk.upload.gencsvtemplate.subtitle")
    String getGenCsvTemplateSubTitle();
    @Key("bulk.ignore.dup.meters")
    String getIgnoreMetersCheckBoxLabel();
    @Key("bulk.ignore.dup.meters.help")
    String getIgnoreMetersCheckBoxHelp();

    @Key("import.upload.header")
    String getFileImportHeader();
    @Key("import.upload.file.upload.title")
    String getFileUploadTitle();
    @Key("import.upload.file.select.labeltext")
    String getUploadFileLabelText();
    @Key("import.upload.select.file.help")
    String getUploadFileSelectHelp();
    @Key("import.upload.csv.button")
    String getUploadFileButton();
    @Key("import.filetype.select.file.help")
    String getFileTypeSelectHelp();
    @Key("import.filetype.select.labeltext")
    String getFileTypeLabelText();

    @Key("import.file.items.header")
    String getFileItemsHeader();
    @Key("button.import.selected")
    String getImportSelectedButton();
    @Key("button.import.all")
    String getImportAllButton();
    @Key("button.stop.import.all")
    String getStopImportAllButton();
    @Key("button.import.extract.fail")
    String getExtractFailedBtn();
    @Key("import.upload.file.name.label")
    String getFileNameLabel();
    @Key("import.upload.num.items.label")
    String getNumItemsLabel();
    @Key("import.upload.startdate.label")
    String getUploadStartLabel();
    @Key("import.upload.enddate.label")
    String getUploadEndLabel();
    @Key("import.upload.username.label")
    String getUploadUsernameLabel();
    @Key("import.upload.num.failed.upload.label")
    String getUploadNumFailedLabel();
    @Key("import.last.start.label")
    String getLastImportStartLabel();
    @Key("import.last.end.label")
    String getLastImportEndLabel();
    @Key("import.upload.last.imported.by.label")
    String getLastImportUserNameLabel();
    @Key("import.upload.keychange.bulkref.label")
    String getKeyChangeBulkRefLabel();

    @Key("power.limit.edit.label.prompt")
    String getPowerlimitEditLabelPrompt();
    @Key("power.limit.edit.label.help")
    String getPowerlimitEditLabelHelp();
    @Key("power.limit.edit.value.prompt")
    String getPowerlimitEditValuePrompt();
    @Key("power.limit.edit.value.help")
    String getPowerlimitEditValueHelp();

    @Key("special.actions.header")
    String getSpecialActionsHeader();
    @Key("special.actions.title")
    String getSpecialActionsTitle();
    @Key("special.action.name")
    String getSpecialActionName();
    @Key("special.action.name.help")
    String getSpecialActionNameHelp();
    @Key("special.action.reason.required")
    String getSpecialActionReasonRequired();
    @Key("special.action.reason.required.help")
    String getSpecialActionReasonRequiredHelp();
    @Key("special.action.reason.input.type")
    String getSpecialActionReasonInputType();
    @Key("special.action.reason.input.type.help")
    String getSpecialActionReasonInputTypeHelp();
    @Key("special.action.description")
    String getSpecialActionDescription();
    @Key("special.action.description.help")
    String getSpecialActionDescriptionHelp();
    @Key("special.action.reasons.header")
    String getSpecialActionReasonsHeader();
    @Key("special.action.reasons.title")
    String getSpecialActionReasonsTitle();
    @Key("special.action.reasons.name")
    String getSpecialActionReasonsName();
    @Key("special.action.reasons.name.help")
    String getSpecialActionReasonsNameHelp();
    @Key("special.action.reasons.description")
    String getSpecialActionReasonsDescription();
    @Key("special.action.reasons.description.help")
    String getSpecialActionReasonsDescriptionHelp();
    @Key("special.action.reasons.active")
    String getSpecialActionReasonsActive();
    @Key("special.action.reasons.active.help")
    String getSpecialActionReasonsActiveHelp();
    @Key("special.action.and.or")
    String getSpecialActionAndOr();
    @Key("special.action.enter.reason")
    String getSpecialActionEnterReason();
    @Key("special.action.select.reason")
    String getSpecialActionSelectReason();

    @Key("button.send_reprint")
    String getSendReprint();
    @Key("messaging.recipient")
    String getMessageRecipient();
    @Key("messaging.recipient.help")
    String getMessageRecipientHelp();
    @Key("messaging.message.label")
    String getMessageLabel();
    @Key("messaging.message.type")
    String getMessageType();
    @Key("messaging.token.reprint.email.subject")
    String getTokenReprintRequest();
    @Key("messaging.type.email")
    String getMessagingTypeEmail();
    @Key("messaging.type.sms")
    String getMessagingTypeSms();

    @Key("button.vend.reversal")
    String getVendReversal();
    @Key("vend.reversed")
    String getVendReversed();

    @Key("meter.use.existing.instructions")
    String getUseExistingMeterInstructions();
    @Key("meter.copy.selected.groups")
    String getCopySelectedGroups();

    @Key("tariff.field.kenya.monthly")
    String getTariffKenyaMonthly();
    @Key("tariff.field.kenya.monthly.help")
    String getTariffKenyaMonthlyHelp();
    @Key("tariff.field.kenya.fuel")
    String getTariffKenyaFuel();
    @Key("tariff.field.kenya.fuel.help")
    String getTariffKenyaFuelHelp();
    @Key("tariff.field.kenya.forex")
    String getTariffKenyaForex();
    @Key("tariff.field.kenya.forex.help")
    String getTariffKenyaForexHelp();
    @Key("tariff.field.kenya.inflation")
    String getTariffKenyaInflation();
    @Key("tariff.field.kenya.inflation.help")
    String getTariffKenyaInflationHelp();
    @Key("tariff.field.kenya.erc")
    String getTariffKenyaErc();
    @Key("tariff.field.kenya.erc.help")
    String getTariffKenyaErcHelp();
    @Key("tariff.field.kenya.rep")
    String getTariffKenyaRep();
    @Key("tariff.field.kenya.rep.help")
    String getTariffKenyaRepHelp();
    @Key("tariff.field.kenya.warma")
    String getTariffKenyaWarma();
    @Key("tariff.field.kenya.warma.help")
    String getTariffKenyaWarmaHelp();

    @Key("tariff.field.samoa.debt_charge")
    String getTariffSamoaDebtCharge();
    @Key("tariff.field.samoa.debt_charge.help")
    String getTariffSamoaDebtChargeHelp();
    @Key("tariff.field.samoa.energy_charge")
    String getTariffSamoaEnergyCharge();
    @Key("tariff.field.samoa.energy_charge.help")
    String getTariffSamoaEnergyChargeHelp();

    @Key("print.customer.contract")
    String getPrintContractButton();

    @Key("engineering.token.user.reference.txtbx.label")
    String getEngineeringTokenUserRefTxBxLabel();
    @Key("engineering.token.user.reference.txtbx.label.help")
    String getEngineeringTokenUserRefTxBxLabelHelp();

    @Key("location.field.address.line.1")
    String getLocationFieldAddressLine1();
    @Key("location.field.address.line.2")
    String getLocationFieldAddressLine2();
    @Key("location.field.address.line.3")
    String getLocationFieldAddressLine3();
    @Key("customer.phone.1")
    String getCustomerPhone1();
    @Key("customer.phone.2")
    String getCustomerPhone2();

    @Key("link.metadata.upload")
    String getMetadataUploadLink();
    @Key("metadata.upload.select.file.help")
    String getMetadataUploadFileSelectHelp();
    @Key("metadata.upload.button")
    String getMetadataUploadButton();
    @Key("metadata.lat.help")
    String getMetadataLatHelpMsg();
    @Key("metadata.lat.label")
    String getMetadataLatLabel();
    @Key("metadata.lon.help")
    String getMetadataLonHelpMsg();
    @Key("metadata.lon.label")
    String getMetadataLonLabel();
    @Key("metadata.upload.heading")
    String getMetadataTitle();

    @Key("reprint.warning.line.1")
    String getReprintWarningLine1();
    @Key("reprint.warning.line.3")
    String getReprintWarningLine3();
    @Key("reprint.credit.vend.tax.invoice")
    String getReprintCreditVendTaxInvoice();
    @Key("reprint.util.name")
    String getReprintUtilName();
    @Key("reprint.util.dist.id")
    String getReprintUtilDistId();
    @Key("reprint.util.vat.no")
    String getReprintUtilVatNo();
    @Key("reprint.util.address")
    String getReprintUtilAddress();
    @Key("reprint.issued")
    String getReprintIssued();
    @Key("reprint.token.tech")
    String getReprintTokenTech();
    @Key("reprint.alg")
    String getReprintAlg();
    @Key("reprint.sgc")
    String getReprintSgc();
    @Key("reprint.krn")
    String getReprintKrn();
    @Key("reprint.standard.token")
    String getReprintStandardToken();
    @Key("reprint.receipt.nr")
    String getReprintReceiptNr();
    @Key("reprint.debt.items")
    String getReprintDebtItems();
    @Key("reprint.fixed.items")
    String getReprintFixedItems();
    @Key("reprint.total.vat.excl")
    String getReprintTotalVatExcl();
    @Key("reprint.total.vat.incl")
    String getReprintTotalVatIncl();
    @Key("reprint.print")
    String getReprintPrint();
    @Key("meter.token.code")
    String getMeterTokenCode();
    @Key("meter.tariffindex.column")
    String getMeterTariffIndexColumn();
    @Key("usagepoint.txn.amt")
    String getUsagePointTxnAmt();
    @Key("reprint.deposit")
    String getReprintDeposit();
    @Key("reprint.save.to.pdf")
    String getReprintSaveToPdf();
    @Key("reprint.customer")
    String getReprintCustomer();

    @Key("demo.addmeterreadings.earliest.reading")
    String getDemoAddMeterReadingsEarliestReading();
    @Key("demo.addmeterreadings.latest.reading")
    String getDemoAddMeterReadingsLatestReading();
    @Key("demo.addmeterreadings.zero.checkbox.text")
    String getDemoAddMeterReadingsZeroCheckboxText();
    @Key("demo.addmeterreadings.zero.form.title")
    String getDemoAddMeterReadingsZeroFormTitle();
    @Key("demo.addmeterreadings.consecutive")
    String getDemoAddMeterReadingsConsecutive();
    @Key("demo.addmeterreadings.random")
    String getDemoAddMeterReadingsRandom();
    @Key("demo.addmeterreadings.percentage.instances")
    String getDemoAddMeterReadingsPercentageInstances();
    @Key("demo.addmeterreadings.missing.checkbox.text")
    String getDemoAddMeterReadingsMissingCheckboxText();
    @Key("demo.addmeterreadings.missing.form.title")
    String getDemoAddMeterReadingsMissingFormTitle();
    @Key("demo.addmeterreadings.algorithm.logic")
    String getDemoAddMeterReadingsAlgorithmLogic();
    @Key("demo.addmeterreadings.delete.all")
    String getDemoAddMeterReadingsDeleteAll();
    @Key("demo.addmeterreadings.delete.selected")
    String getDemoAddMeterReadingsDeleteSelected();
    @Key("demo.addmeterreadings.append")
    String getDemoAddMeterReadingsAppend();
    @Key("demo.addmeterreadings.reading.variants")
    String getDemoAddMeterReadingsReadingVariants();
    @Key("channel.field.titlename")
    String getChannelFieldTitlename();
    @Key("channel.field.titlename.help")
    String getChannelFieldTitlenameHelp();
    @Key("channel.config.mdc.titlename")
    String getChannelConfigMdcTitleName();
    @Key("channel.config.field.billingdetnames")
    String getBillingDetNamesLabel();
    @Key("mdc.channel.override.metermodels")
    String getChannelOverrideMeterModelsTitle();

    @Key("customer.auxaccount.functions.as")
    String getCustomerAuxaccountFunctionsAs();

    @Key("sts.tokens.header")
    String getStsTokensHeader();
    @Key("verify.token")
    String getVerifyToken();
    @Key("verify.token.class")
    String getVerifyTokenClass();
    @Key("verify.token.subclass")
    String getVerifyTokenSubclass();
    @Key("verify.token.id")
    String getVerifyTokenId();
    @Key("verify.token.units")
    String getVerifyTokenUnits();
    @Key("verify.token.date")
    String getVerifyTokenDate();

    @Key("meter.select.meter.phase")
    String getMeterSelectMeterPhase();
    @Key("meter.select.meter.phase.help")
    String getMeterSelectMeterPhaseHelp();

    @Key("configure.user.interface")
    String getConfigureUserInterface();

    @Key("meter.models.field.data.decoder")
    String getMeterModelsDataDecoder();
    @Key("meter.models.field.data.decoder.help")
    String getMeterModelsDataDecoderHelp();

    @Key("usagepoint.field.meter.activation.date")
    String getUsagePointFieldMeterActivationDate();

    @Key("customer.unassign.unassign.customer")
    String getCustomerUnassignUnassignCustomer();

    @Key("units.account")
    String getUnitsAccount();
    @Key("units.account.transaction.history")
    String getUnitsAccountTransactionHistory();
    @Key("units.account.transaction.description")
    String getUnitsAccountTransactionDescription();

    @Key("meter.model.in.use")
    String getMeterModelInUse();

    @Key("bulk.upload.metercustup.notice")
    String getBulkUploadMetercustupNotice();

    @Key("action.params.header.label")
    String getActionParamsHeader();
    @Key("bulk.keychange.from.header")
    String getBulkKeyFromHeader();
    @Key("bulk.keychange.from.supplygroupcode")
    String getBulkKeyChangeFromSupplyGroupCode();
    @Key("bulk.keychange.from.supplygroupcode.help")
    String getBulkKeyChangeFromSupplyGroupCodeHelp();
    @Key("bulk.keychange.from.tariffindex")
    String getBulkKeyChangeFromTariffIndex();
    @Key("bulk.keychange.from.tariffindex.help")
    String getBulkKeyChangeFromTariffIndexHelp();
    @Key("bulk.keychange.to.header")
    String getBulkKeyToHeader();
    @Key("bulk.keychange.use.target")
    String getBulkKeyChangeUseTarget();
    @Key("bulk.keychange.use.targetHelp")
    String getBulkKeyChangeUseTargetHelp();
    @Key("bulk.keychange.supplygroupcode")
    String getBulkKeyChangeSupplyGroupCode();
    @Key("bulk.keychange.supplygroupcode.help")
    String getBulkKeyChangeSupplyGroupCodeHelp();
    @Key("bulk.keychange.tariffindex")
    String getBulkKeyChangeTariffIndex();
    @Key("bulk.keychange.tariffindex.help")
    String getBulkKeyChangeTariffIndexHelp();
    @Key("bulk.keychange.instruction.label")
    String getBulkKeyChangeInstructionLabel();
    @Key("bulk.keychange.instruction.help")
    String getBulkKeyChangeInstructionHelp();
    @Key("bulk.keychange.generate.keychanges.next.vend.after.date")
    String getBulkKeyChangeAfterDateLabel();
    @Key("bulk.keychange.generate.keychanges.next.vend.after.date.help")
    String getBulkKeyChangeAfterDateHelp();
    @Key("bulk.keychange.overwrite.existing")
    String getBulkKeyChangeOverWriteExisting();
    @Key("bulk.keychange.overwrite.existing.help")
    String getBulkKeyChangeOverwriteExistingHelp();

    @Key("bill.payments")
    String getBillPayments();
    @Key("bill.payments.description")
    String getBillPaymentsDescription();

    @Key("up_meter_install.header")
    String getUpMeterInstallHeader();
    @Key("up_meter_install.sub.header")
    String getUpMeterInstallSubHeader();

    @Key("access_group.update.button")
    String getUpdateAccessGroupButton();
    @Key("access_group.lbl")
    String getAccessGroupLbl();
    @Key("access_group.update.group.help")
    String getAccessGroupUpdateHelp();
    @Key("access_group.update.pricing.lbl")
    String getAccessGroupUpdatePricingLbl();
    @Key("access_group.update.pricing.help")
    String getAccessGroupUpdatePricingHelp();

    @Key("up_pricing_structure.header")
    String getUpPricingStructureHeader();
    @Key("up_pricing_structure.sub.header")
    String getUpPricingStructureSubHeader();

    @Key("reprint.key.change.notice.line.1")
    String getReprintKeyChangeNoticeLine1();
    @Key("reprint.key.change.notice.line.2")
    String getReprintKeyChangeNoticeLine2();

    @Key("bulk.pricing.structure.change.start.date")
    String getBulkPricingStructureChangeStartDate();
    @Key("bulk.pricing.structure.change.pricing.structure")
    String getBulkPricingStructureChangePricingStructure();
    @Key("bulk.pricing.structure.change.start.date.help")
    String getBulkPricingStructureChangeStartDateHelp();
    @Key("bulk.pricing.structure.change.pricing.structure.help")
    String getBulkPricingStructureChangePricingStructureHelp();

    @Key("bulk.blocking.blocking.type")
    String getBulkBlockingBlockingType();

    @Key("reprint.total.tax")
    String getReprintTotalTax();
    @Key("reprint.total.tax-inclusive")
    String getReprintTotalTaxIncl();
    @Key("register.readings.total.consumption")
    String getRegisterReadingsTotalConsumption();

    @Key("mdc.txn.relay.title")
    String getMdcTransactionRelayTitle();
    @Key("mdc.txn.relay.help")
    String getMdcTransactionRelayHelp();
    @Key("mdc.txn.relay.main")
    String getMdcTransactionRelayMain();
    @Key("mdc.txn.relay.aux.one")
    String getMdcTransactionRelayAuxOne();

    @Key("bulk.device.store.movement.help")
    String getBulkDeviceStoreMovementHelp();
    @Key("bulk.device.store.movement")
    String getBulkDeviceStoreMovement();
    
    @Key("customer.notification.types")
    String getCustomerNotificationTypes();
    @Key("customer.manage.notification.types")
    String getCustomerManageNotificationTypes();
    @Key("customer.adjust.aux.accounts")
    String getCustomerAdjustAuxAccounts();
    @Key("customer.new.aux.accounts")
    String getCustomerNewAuxAccounts();
    String getBulkFreeIssueUnits();
    String getBulkFreeIssueDescription();
    String getBulkFreeIssueUserReference();
}
