package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class AuxTransUploadEvent extends GwtEvent<AuxTransUploadEventHandler> {

	public static Type<AuxTransUploadEventHandler> TYPE = new Type<AuxTransUploadEventHandler>();

	private String name;

	public AuxTransUploadEvent(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	@Override
	public Type<AuxTransUploadEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(AuxTransUploadEventHandler handler) {
		handler.handleEvent(this);
	}

}