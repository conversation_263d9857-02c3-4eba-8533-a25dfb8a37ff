<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">
  <ui:style>
    .checkBox {
        /* Padding so error messages have enough space to be readable */
        padding-right: 12em;
    }
  </ui:style>

  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
            <p1:FormRowPanel>
              <p1:FormElement ui:field="nameElement" labelText="{msg.getGroupHierarchyName}:" required="true"  helpMsg="{msg.getGroupHierarchyNameHelp}" debugId="hierarchyNameElement">
                <g:TextBox ui:field="nameBox" visibleLength="20" debugId="hierarchyNameBox"/>
              </p1:FormElement>                          
              <p1:FormElement ui:field="isAccessGroupElement" debugId="isAccessGroupElement" labelText="{msg.getIsAccessGroup}:" helpMsg="{msg.getIsAccessGroupHelp}" visible="false">
                <g:CheckBox ui:field="isAccessGroupBox" styleName="{style.checkBox}" debugId="isAccessGroupBox"/>
              </p1:FormElement>
            </p1:FormRowPanel> 
    </g:FlowPanel>

</ui:UiBinder> 