package za.co.ipay.metermng.client.view.component.selection;

import za.co.ipay.metermng.client.widget.suggestboxtree.CachedDataSuggestOracle;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.SelectionDataItem.SelectionDataType;

/**
 * An Oracle that caches the children for each node for the suggest boxes.
 * It also adds methods for retrieving the parent.
 * 
 * <AUTHOR>
 *
 */
public class SelectionDataItemSuggestOracle extends CachedDataSuggestOracle<Long, SelectionDataItem> {
    private SelectionDataItem parent;
    private String label = "Unknown";
    
    public SelectionDataItemSuggestOracle(SelectionDataItem parent) {
        super(parent.getChildren());
        this.parent = parent;
        if(parent.getChildren() != null && ! parent.getChildren().isEmpty()) {
            SelectionDataItem selectionDataItem = parent.getChildren().get(0);
            if(selectionDataItem.getType() == SelectionDataType.GROUP_DATA) {
                label = parent.getChildren().get(0).getLabel();
            } else {
                label = parent.getLabel();
            }
        }
        
    }
    
    @Override
    public String getLabel() {
        return label;
    }
    
    @Override
    public void findById(Long id, FindSuggestionCallback callback) {
        for (SelectionDataItem selectionDataItem : sourceData) {
            if(id.equals(selectionDataItem.getActualId())) {
                callback.found(selectionDataItem);
                return;
            }
        }
        callback.found(null);
    }
    
    @Override
    public boolean isEmptyItem(Suggestion suggestion) {
        if(suggestion == null) {
            return true;
        }
        SelectionDataItem selectionDataItem = (SelectionDataItem) suggestion;
        String id = selectionDataItem.getId();
        return id.equals(SelectionDataItem.NO_VALUES_ID_KEY) || id.equals(SelectionDataItem.EMPTY_ITEM_ID_KEY); 
    }
}
