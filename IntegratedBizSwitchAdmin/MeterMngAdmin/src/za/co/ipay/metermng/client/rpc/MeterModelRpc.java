package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelScreenDataDto;

@RemoteServiceRelativePath("secure/metermodel.do")
public interface MeterModelRpc extends RemoteService {

    public MeterModelScreenDataDto getMeterModelsScreenData() throws ServiceException;
    
    public Integer getMeterModelsCount() throws ServiceException;
    
    public ArrayList<MeterModelDto> getMeterModels(int startRow, int pageSize, String sortField, boolean isAscending) 
        throws ServiceException;
    
    public MeterModelDto getMeterModelDtoForMeterModelId(Long meterModelId) throws ServiceException;
    
    public MeterModel saveMeterModel(MeterModel meterModel, ArrayList<IdNameDto> paymentModes) throws ValidationException, ServiceException;

    public ArrayList<Long> getMeterModelIds(Long pricingStructureId);

    public MeterModelData getMeterModelById(Long meterModelId);

}
