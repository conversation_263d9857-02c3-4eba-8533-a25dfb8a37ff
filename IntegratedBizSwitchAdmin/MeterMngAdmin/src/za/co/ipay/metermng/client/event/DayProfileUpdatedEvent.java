package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class DayProfileUpdatedEvent extends GwtEvent<DayProfileUpdatedEventHandler> {

    public static Type<DayProfileUpdatedEventHandler> TYPE = new Type<DayProfileUpdatedEventHandler>();
    
    private Long dayProfileId;
    
    public Long getDayProfileId() {
        return dayProfileId;
    }

    public void setDayProfileId(Long dayProfileId) {
        this.dayProfileId = dayProfileId;
    }

    public DayProfileUpdatedEvent() {
        
    }
    
	@Override
    public Type<DayProfileUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(DayProfileUpdatedEventHandler handler) {
        handler.processDayProfileUpdatedEvent(this);
    }
}
