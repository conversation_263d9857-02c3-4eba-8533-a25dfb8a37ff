package za.co.ipay.metermng.client.view.component.group.entity.ndp;


import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;
import java.util.logging.Logger;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.ImageResourceCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarModel;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.globalndp.GlobalNdpPanel;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeason;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDay;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class SchedulePanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField VerticalPanel seasonsPanel;
    @UiField(provided=true) CellTable<NdpSeason> seasonsTable;
    @UiField TablePager ndpSeasonsPager;
    @UiField Button btnNewSeason;

    @UiField VerticalPanel specialdayPanel;
    @UiField(provided=true) CellTable<NdpSpecialDay> specialdayTable;
    @UiField TablePager specialdayPager;
    @UiField Button btnSpecialDay;

    private ListDataProvider<NdpSeason> dataProviderSeasons;
    private SingleSelectionModel<NdpSeason> selectionModelSeasons;
    private ListHandler<NdpSeason> columnSortHandlerSeasons;
    private ColumnSortList columnSortListSeasons;

    private ListDataProvider<NdpSpecialDay> dataProviderSpecialDay;
    private SingleSelectionModel<NdpSpecialDay> selectionModelSpecialDay;
    private ListHandler<NdpSpecialDay> columnSortHandlerSpecialDay;
    private ColumnSortList columnSortListSpecialDay;

    private NdpScheduleData ndpScheduleData;

    private TextColumn<NdpSeason> startSeasonColumn;
    private TextColumn<NdpSeason> endSeasonColumn;
    private Column<NdpSeason, ImageResource> deleteSeasonColumn;

    private TextColumn<NdpSpecialDay> specialDayColumn;
    private Column<NdpSpecialDay, ImageResource> deleteSpecialDayColumn;

    private Boolean isInherited = false;
    private Boolean isViewOnly = false;
    private CalendarModel calmod;
    private ContainsNdpPanel parentNdpPanel;
    private SchedulePopup inheritedPopup;
    private SeasonDaysDetailPanel seasonDaysDetailPanel;
    private SpecialDayDetailPanel specialDayDetailPanel;

    private static Logger logger = Logger.getLogger(SchedulePanel.class.getName());

    private static SchedulePanelUiBinder uiBinder = GWT.create(SchedulePanelUiBinder.class);

    interface SchedulePanelUiBinder extends UiBinder<Widget, SchedulePanel> {
    }

    public SchedulePanel(ClientFactory clientFactory, ContainsNdpPanel parentNdpPanel, SchedulePopup inheritedPopup) {
        this.clientFactory = clientFactory;
        this.parentNdpPanel = parentNdpPanel;
        this.inheritedPopup = inheritedPopup;
        calmod = new CalendarModel();

        createSeasonsTable();
        createSpecialDayTable();

        initWidget(uiBinder.createAndBindUi(this));
        initSeasonTable();
        initSpecialDayTable();

        if (inheritedPopup != null) {          //for showing inherited values will create a new schedulePanel to go inside popup every time
            this.isInherited = true;
        } else if (parentNdpPanel instanceof GlobalNdpPanel) {
                if (!clientFactory.getUser().hasPermission(MeterMngStatics.GLOBAL_NDP_PERMISSION)) {
                    //then is GlobalNdpPanel and has only clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP))
                    isViewOnly = true;
                }
        } else if (parentNdpPanel instanceof NonDisconnectPeriodPanel){
             if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_NDP_GROUP_ADMIN)) {
                 //then comes from genGroup: NonDisconnectPeriodPanel and has only clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP))
                 isViewOnly = true;
             }
        }

        if (isInherited || isViewOnly) {
            btnNewSeason.removeFromParent();
            btnSpecialDay.removeFromParent();
            seasonsTable.removeColumn(deleteSeasonColumn);
            specialdayTable.removeColumn(deleteSpecialDayColumn);
        }
    }

    protected void createSeasonsTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            seasonsTable = new CellTable<NdpSeason>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            seasonsTable = new CellTable<NdpSeason>(DEFAULT_PAGE_SIZE);
        }
    }

   protected void initSeasonTable() {
        if (dataProviderSeasons == null) {

            startSeasonColumn = new TextColumn<NdpSeason>() {
                @Override
                public String getValue(NdpSeason data) {
                    if (data.getStartMonth() == null || data.getStartDay() == null) {
                        return null;
                    }
                    return (data.getStartDay()+" "+calmod.formatMonth(data.getStartMonth()-1));//we use 1 for jan calmod uses 0
                }


            };
            startSeasonColumn.setSortable(true);

            endSeasonColumn = new TextColumn<NdpSeason>() {
                @Override
                public String getValue(NdpSeason data) {
                    if (data.getEndMonth() == null || data.getEndDay() == null) {
                        return null;
                    }
                    return (data.getEndDay()+" "+calmod.formatMonth(data.getEndMonth()-1));//we use 1 for jan calmod uses 0
                }
            };
            endSeasonColumn.setSortable(true);

            ImageResourceCell deleteCell = new ImageResourceCell() {
                public Set<String> getConsumedEvents() {
                    HashSet<String> events = new HashSet<String>();
                    events.add("click");
                    return events;
                }
            };

            deleteSeasonColumn = new Column<NdpSeason, ImageResource>(deleteCell) {
                @Override
                public ImageResource getValue(NdpSeason dataObj) {
                    return MediaResourceUtil.getInstance().getDeleteImage();
                }

                @Override
                public void onBrowserEvent(Context context, Element elem,
                        final NdpSeason object, NativeEvent event) {
                    super.onBrowserEvent(context, elem, object, event);
                    if ("click".equals(event.getType())) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                clearSeasonTableSelection();
                                deleteSeason(object);
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            };

            // Add the columns.
            seasonsTable.addColumn(startSeasonColumn, MessagesUtil.getInstance().getMessage("ndp.assign.season.start"));
            seasonsTable.addColumn(endSeasonColumn, MessagesUtil.getInstance().getMessage("ndp.assign.season.end"));
            seasonsTable.addColumn(deleteSeasonColumn, "");

            dataProviderSeasons = new ListDataProvider<NdpSeason>();
            dataProviderSeasons.addDataDisplay(seasonsTable);
            ndpSeasonsPager.setDisplay(seasonsTable);
            seasonsTable.setPageSize(getPageSize());

            logger.info("Created Ndp Seasons Table");
        }

        selectionModelSeasons = new SingleSelectionModel<NdpSeason>();
        CellPreviewEvent.Handler<NdpSeason> seasonsHandler = new CellPreviewEvent.Handler<NdpSeason>() {
            final CellPreviewEvent.Handler<NdpSeason> seasonSelectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<NdpSeason> event) {
                if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                    if (!event.isCanceled()) {
                        event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                    }
                } else {
                    seasonSelectionEventManager.onCellPreview(event);
                }
            }
        };
        seasonsTable.setSelectionModel(selectionModelSeasons, seasonsHandler);
        selectionModelSeasons.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {
                final NdpSeason selected = selectionModelSeasons.getSelectedObject();
                if (selected != null) {
                    seasonsTable.getSelectionModel().setSelected(selected, true);
                    displaySeasonsDayDetailPanel(selected);
                }
            }
        });
    }

    public void clearSeasonTableSelection() {
        if (selectionModelSeasons != null) {
            NdpSeason selected = selectionModelSeasons.getSelectedObject();
            if (selected != null) {
                selectionModelSeasons.setSelected(selected, false);
            }
        }
    }

    private void createSpecialDayTable(){
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            specialdayTable = new CellTable<NdpSpecialDay>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            specialdayTable = new CellTable<NdpSpecialDay>(DEFAULT_PAGE_SIZE);
        }

    }

    private void initSpecialDayTable() {
        if (dataProviderSpecialDay == null) {

            specialDayColumn = new TextColumn<NdpSpecialDay>() {
                @Override
                public String getValue(NdpSpecialDay data) {
                    if (data.getNdpDay() == null || data.getNdpMonth() == null) {
                        return null;
                    }
                    return (data.getNdpDay()+" "+calmod.formatMonth(data.getNdpMonth()-1));//we use 1 for jan calmod uses 0
                }


            };
            specialDayColumn.setSortable(true);

            ImageResourceCell deleteCell = new ImageResourceCell() {
                public Set<String> getConsumedEvents() {
                    HashSet<String> events = new HashSet<String>();
                    events.add("click");
                    return events;
                }
            };

            deleteSpecialDayColumn  = new Column<NdpSpecialDay, ImageResource>(deleteCell) {
                @Override
                public ImageResource getValue(NdpSpecialDay dataObj) {
                    return MediaResourceUtil.getInstance().getDeleteImage();
                }

                @Override
                public void onBrowserEvent(Context context, Element elem,
                        final NdpSpecialDay object, NativeEvent event) {
                    super.onBrowserEvent(context, elem, object, event);
                    if ("click".equals(event.getType())) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                clearSpecialDayTableSelection();
                                deleteSpecialDay(object);
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            };

            // Add the columns.
            specialdayTable.addColumn(specialDayColumn, MessagesUtil.getInstance().getMessage("ndp.special.day.column.heading"));
            specialdayTable.addColumn(deleteSpecialDayColumn, "");

            dataProviderSpecialDay = new ListDataProvider<NdpSpecialDay>();
            dataProviderSpecialDay.addDataDisplay(specialdayTable);
            specialdayPager.setDisplay(specialdayTable);
            specialdayTable.setPageSize(getPageSize());

            logger.info("Created Ndp Special Days Table");
        }

        selectionModelSpecialDay = new SingleSelectionModel<NdpSpecialDay>();
        CellPreviewEvent.Handler<NdpSpecialDay> specialDayHandler = new CellPreviewEvent.Handler<NdpSpecialDay>() {
            final CellPreviewEvent.Handler<NdpSpecialDay> specialDaySelectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<NdpSpecialDay> event) {
                if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                    if (!event.isCanceled()) {
                        event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                    }
                } else {
                    specialDaySelectionEventManager.onCellPreview(event);
                }
            }
        };
        specialdayTable.setSelectionModel(selectionModelSpecialDay, specialDayHandler);
        selectionModelSpecialDay.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final NdpSpecialDay selected = selectionModelSpecialDay.getSelectedObject();
                if (selected != null) {
                    specialdayTable.getSelectionModel().setSelected(selected, true);
                    displaySpecialDayDetailPanel(selected);
                }
            }
        });
    }

    public void clearSpecialDayTableSelection() {
        if (selectionModelSpecialDay != null) {
            NdpSpecialDay selected = selectionModelSpecialDay.getSelectedObject();
            if (selected != null) {
                selectionModelSpecialDay.setSelected(selected, false);
            }
        }
    }

    @UiHandler("btnNewSeason")
    void addSeason(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                displaySeasonsDayDetailPanel(null);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }


    @UiHandler("btnSpecialDay")
    void addSpecialDay(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                displaySpecialDayDetailPanel(null);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void disableButtons() {
        btnNewSeason.setEnabled(false);
        btnSpecialDay.setEnabled(false);
    }

    public void enableButtons() {
        btnNewSeason.setEnabled(true);
        btnSpecialDay.setEnabled(true);
    }

    public void display(NdpScheduleData ndpScheduleData) {
        this.ndpScheduleData = ndpScheduleData;

        dataProviderSeasons.getList().clear();
        if (ndpScheduleData.getNdpSeasonList() != null) {
            dataProviderSeasons.getList().addAll(ndpScheduleData.getNdpSeasonList());
        }
        sortSeasonDataProviderList();

        dataProviderSpecialDay.getList().clear();
        if (ndpScheduleData.getNdpSpecialDayList() != null) {
            dataProviderSpecialDay.getList().addAll(ndpScheduleData.getNdpSpecialDayList());
        }
        sortSpecialDayDataProviderList();

        //Already have the SchedulePanel catering for viewing inherited values --> inherited schedulePanel appears inside a popup on the GroupEntity Page.
        //Can use the same facility for viewonly - but if its view only of the values at THIS level - do NOT set the height for the popup!
        if (isInherited && !isViewOnly) {        //i.e. truly an inherited value, so will show popup
            setInheritedPopupHeightForScrollBar(0);
        }
    }

    protected void setInheritedPopupHeightForScrollBar(int height) {
        inheritedPopup.setHeight(this.getOffsetHeight() + height);
    }

    protected ContainsNdpPanel getNdpParentPanel() {
        return parentNdpPanel;
    }


    //************************************ SEASONS ***********************************************************************************
    public void displaySeasonsDayDetailPanel(NdpSeason ndpSeason) {
        if (seasonDaysDetailPanel != null) {
            seasonDaysDetailPanel.removeFromParent();
        }
        seasonDaysDetailPanel = new SeasonDaysDetailPanel(clientFactory,
                                                                    this,
                                                                    ndpScheduleData,
                                                                    isInherited,
                                                                    isViewOnly);
        seasonsPanel.add(seasonDaysDetailPanel);
        btnNewSeason.setVisible(false);
        seasonDaysDetailPanel.display(ndpSeason);
    }


    public void sortSeasonDataProviderList() {
        dataProviderSeasons.refresh();
        if (columnSortHandlerSeasons == null || columnSortHandlerSeasons.getList() == null) {
            columnSortHandlerSeasons = new ListHandler<NdpSeason>(dataProviderSeasons.getList());

            columnSortHandlerSeasons.setComparator(startSeasonColumn, new Comparator<NdpSeason>() {
                public int compare(NdpSeason o1, NdpSeason o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 == null) return -1;
                    if (o2 == null) return 1;

                    int result = o1.getStartMonth().compareTo(o2.getStartMonth());
                    if (result != 0) return result;

                    return o1.getStartDay().compareTo(o2.getStartDay());
                }
            });

            seasonsTable.addColumnSortHandler(columnSortHandlerSeasons);
            columnSortListSeasons = seasonsTable.getColumnSortList();
            columnSortListSeasons.push(startSeasonColumn);
            ColumnSortEvent.fire(seasonsTable, columnSortListSeasons);
        } else {
            columnSortHandlerSeasons.setList(dataProviderSeasons.getList());
            ColumnSortEvent.fire(seasonsTable, columnSortListSeasons);
        }
    }

    protected void addNewSeason(NdpSeason ndpSeason, boolean isDayTimeEntered) {
        //add this season to the arraylist of seasons
        ArrayList<NdpSeason> list = ndpScheduleData.getNdpSeasonList();
        boolean isInList = false;
        for (NdpSeason ss : list) {
            if(ss.getId().equals(ndpSeason.getId())) {
                isInList = true;
                break;
            }
        }
        if (!isInList) {
            list.add(ndpSeason);
            ndpScheduleData.setNdpSeasonList(list);

            dataProviderSeasons.getList().clear();
            dataProviderSeasons.getList().addAll(ndpScheduleData.getNdpSeasonList());
            sortSeasonDataProviderList();
        }

        if (isDayTimeEntered) {
            parentNdpPanel.enableActivate();
        }

        returnSetup();
        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.season.saved"),
                MediaResourceUtil.getInstance().getInformationIcon(),
                btnNewSeason.getAbsoluteLeft() + btnNewSeason.getOffsetWidth(),
                btnNewSeason.getAbsoluteTop() + btnNewSeason.getOffsetHeight(),
                null);
    }

    protected void returnSetup() {
        //null the detailPanels & clear the selections
        if (seasonDaysDetailPanel != null) {
            getHasDirtyDataManager().unregister(seasonDaysDetailPanel.getHasDirtyData());
            seasonDaysDetailPanel.removeFromParent();
            seasonDaysDetailPanel = null;
        }
        clearSeasonTableSelection();
        btnNewSeason.setVisible(true);

        if (specialDayDetailPanel != null) {
            specialDayDetailPanel.removeFromParent();
            specialDayDetailPanel = null;
        }
        clearSpecialDayTableSelection();;
        btnSpecialDay.setVisible(true);
    }


    public Boolean checkOverlapNewSeason(NdpSeason newSeason) {
        boolean overlaps = false;
        int newStartMonth = newSeason.getStartMonth();
        int newStartDay = newSeason.getStartDay();

        int newEndMonth = newSeason.getEndMonth();
        int newEndDay = newSeason.getEndDay();


        for (NdpSeason ss : ndpScheduleData.getNdpSeasonList()) {
           if (newSeason.getId() != null && ss.getId().equals(newSeason.getId())) {
               continue;
           }

           //if new start > old end --> fine; test next
           if (newStartMonth > ss.getEndMonth()) {
               continue;
           }
           if (newStartMonth == ss.getEndMonth()) {
               if (newStartDay > ss.getEndDay()) {
                   continue;
               }
           }

           //new start is smaller than old END, if it is greater than old start --> OVERLAP
           boolean startGreater = false;
           if (newStartMonth > ss.getStartMonth()) {
               startGreater = true;
           } else if (newStartMonth == ss.getStartMonth()) {
               if (newStartDay >= ss.getStartDay()) {
                   startGreater = true;
               }
           }
           if (startGreater) {
               overlaps = true;
               break;
           }

           //new start < old start. if newEnd also < oldStart --> Fine; test next
           if (newEndMonth < ss.getStartMonth()) {
               continue;
           }
           if (newEndMonth == ss.getStartMonth()) {
               if (newEndDay < ss.getStartDay()) {
                   continue;
               }
           }

           //new start < old Start but new End > old Start --> OVERLAP
           overlaps = true;
           break;
        }

        return overlaps;
    }


  //---------------------------------DELETE SEASON ---------------------------------------------------------------------

  public void deleteSeason(final NdpSeason ndpSeason) {
      //when click delete, close DayDetailPanel
      returnSetup();
      //if remove this Season, will schedule still be activatable
      //remove current season from list before go check if others have a day with times
      ArrayList<NdpSeason> checkTheseSeasons = ndpScheduleData.getNdpSeasonList();
      checkTheseSeasons.remove(ndpSeason);
      clientFactory.getNdpRpc().isDayProfilePresentForSchedule(checkTheseSeasons, ndpScheduleData.getNdpSpecialDayList(), null, null, new ClientCallback<Boolean>() {
          @Override
          public void onSuccess(Boolean result) {
              if (result) {
                  confirmDeleteSeason(ndpSeason, false);
              } else {
                  confirmDeleteAndDeactivateSchedule(ndpSeason);
              }
          }
      });
  }

  private String constructSeasonString(NdpSeason ndpSeason) {
      return ndpSeason.getStartDay()+" "+calmod.formatMonth(ndpSeason.getStartMonth()-1)
              + " - " + ndpSeason.getEndDay()+" "+calmod.formatMonth(ndpSeason.getEndMonth()-1);
  }

  private void confirmDeleteSeason(final NdpSeason ndpSeason, final boolean deactivate) {
      String seasonToDelete = constructSeasonString(ndpSeason);
      Dialogs.confirm(
              ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.season.confirm.delete", new String[] {seasonToDelete})),
              ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
              ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
              ResourcesFactoryUtil.getInstance().getQuestionIcon(),
              new ConfirmHandler() {
                  @Override
                  public void confirmed(boolean confirm) {
                      if (confirm) {
                          deleteSeasonNow(ndpSeason);
                          if (deactivate) {
                              parentNdpPanel.disableActivate();
                          }
                      }
                      else {
                          return;
                      }
                  }
              }
       );
  }

  private void confirmDeleteAndDeactivateSchedule(final NdpSeason ndpSeason) {
      String seasonToDelete = constructSeasonString(ndpSeason);
      if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentNdpPanel.isActiveBoxTicked()) {
          Dialogs.confirm(
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.season.confirm.delete.and.deactivate", new String[] {seasonToDelete})),
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                  ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                  new ConfirmHandler() {
                      @Override
                      public void confirmed(boolean confirm) {
                          if (confirm) {
                              deleteSeasonNow(ndpSeason);
                              parentNdpPanel.deactivateSchedule();
                          }
                          else {
                              return;
                          }
                      }
                  }
                  );
      } else {
          confirmDeleteSeason(ndpSeason, true);
      }
  }

  private void deleteSeasonNow(final NdpSeason ndpSeason) {
      //delete season & all its day profiles
      clientFactory.getNdpRpc().deleteSeasonAndDayProfiles(ndpSeason.getId(), new ClientCallback<Void>() {
          @Override
          public void onSuccess(Void result) {
              removeSeason(ndpSeason);
          }
      });
  }

  private void removeSeason(NdpSeason ndpSeason) {
      //remove from scheduleData
      ndpScheduleData.getNdpSeasonList().remove(ndpSeason);

      //remove from table
      dataProviderSeasons.getList().clear();
      if (ndpScheduleData.getNdpSeasonList() != null) {
          dataProviderSeasons.getList().addAll(ndpScheduleData.getNdpSeasonList());
      }
      sortSeasonDataProviderList();

      Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.season.deleted"),
              MediaResourceUtil.getInstance().getInformationIcon(),
              btnNewSeason.getAbsoluteLeft() + btnNewSeason.getOffsetWidth(),
              btnNewSeason.getAbsoluteTop() - btnNewSeason.getOffsetHeight(),
              null);
  }


  //************************************ SPECIAL DAYS ***********************************************************************************
  private void displaySpecialDayDetailPanel(NdpSpecialDay ndpSpecialDay) {
      if (specialDayDetailPanel != null) {
          specialDayDetailPanel.removeFromParent();
      }
      specialDayDetailPanel = new SpecialDayDetailPanel(clientFactory,
                                                                  this,
                                                                  ndpScheduleData,
                                                                  isInherited,
                                                                  isViewOnly);
      specialdayPanel.add(specialDayDetailPanel);
      btnSpecialDay.setVisible(false);
      specialDayDetailPanel.display(ndpSpecialDay);
  }


  public void sortSpecialDayDataProviderList() {
      dataProviderSpecialDay.refresh();
      if (columnSortHandlerSpecialDay == null || columnSortHandlerSpecialDay.getList() == null) {
          columnSortHandlerSpecialDay = new ListHandler<NdpSpecialDay>(dataProviderSpecialDay.getList());

          columnSortHandlerSpecialDay.setComparator(specialDayColumn, new Comparator<NdpSpecialDay>() {
              public int compare(NdpSpecialDay o1, NdpSpecialDay o2) {
                  if (o1 == o2) {
                      return 0;
                  }
                  if (o1 == null) return -1;
                  if (o2 == null) return 1;

                  int result = o1.getNdpMonth().compareTo(o2.getNdpMonth());
                  if (result != 0) return result;

                  return o1.getNdpDay().compareTo(o2.getNdpDay());
              }
          });

          specialdayTable.addColumnSortHandler(columnSortHandlerSpecialDay);
          columnSortListSpecialDay = specialdayTable.getColumnSortList();
          columnSortListSpecialDay.push(specialDayColumn);
          ColumnSortEvent.fire(specialdayTable, columnSortListSpecialDay);
      } else {
          columnSortHandlerSpecialDay.setList(dataProviderSpecialDay.getList());
          ColumnSortEvent.fire(specialdayTable, columnSortListSpecialDay);
      }
  }

  protected void addNewSpecialDay(NdpSpecialDay ndpSpecialDay, boolean isDayTimeEntered) {
      //add this special day to the arraylist of special days
      ArrayList<NdpSpecialDay> list = ndpScheduleData.getNdpSpecialDayList();
      boolean isInList = false;
      for (NdpSpecialDay ss : list) {
          if(ss.getId().equals(ndpSpecialDay.getId())) {
              isInList = true;
              break;
          }
      }
      if (!isInList) {
          list.add(ndpSpecialDay);
          ndpScheduleData.setNdpSpecialDayList(list);

          dataProviderSpecialDay.getList().clear();
          dataProviderSpecialDay.getList().addAll(ndpScheduleData.getNdpSpecialDayList());
          sortSpecialDayDataProviderList();
      }

      if (isDayTimeEntered) {
          parentNdpPanel.enableActivate();
      }

      returnSpecialDaySetup();
      Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.special.day.saved"),
              MediaResourceUtil.getInstance().getInformationIcon(),
              btnSpecialDay.getAbsoluteLeft() + btnSpecialDay.getOffsetWidth(),
              btnSpecialDay.getAbsoluteTop() + btnSpecialDay.getOffsetHeight(),
              null);
  }

  protected void returnSpecialDaySetup() {
      //null the detailPanel & clear the selection
      if (specialDayDetailPanel != null) {
          getHasDirtyDataManager().unregister(specialDayDetailPanel.getHasDirtyData());
          specialDayDetailPanel.removeFromParent();
          specialDayDetailPanel = null;
      }
      clearSpecialDayTableSelection();
      btnSpecialDay.setVisible(true);
  }


  public Boolean checkDuplicate(NdpSpecialDay newSpecialDay) {
      boolean dup = false;

      for (NdpSpecialDay ss : ndpScheduleData.getNdpSpecialDayList()) {
          if (ss.getId().equals(newSpecialDay.getId())) {
              continue;
          }
          if (ss.getNdpMonth().equals(newSpecialDay.getNdpMonth())
                  && ss.getNdpDay().equals(newSpecialDay.getNdpDay()) ) {
              dup = true;
              break;
          }
      }
      return dup;
  }

  //---------------------------------DELETE SPECIAL DAY ---------------------------------------------------------------------

  public void deleteSpecialDay(final NdpSpecialDay ndpSpecialDay) {
      //when click delete, close SpecialDayDetailPanel
      returnSpecialDaySetup();
      //if remove this SpecialDay, will schedule still be activatable
      //remove current Special Day from list before go check if others have a day with times
      ArrayList<NdpSpecialDay> checkTheseSpecialDays = ndpScheduleData.getNdpSpecialDayList();
      checkTheseSpecialDays.remove(ndpSpecialDay);
      if (ndpScheduleData.getNdpSeasonList().size() > 0 || checkTheseSpecialDays.size() > 0) {
          clientFactory.getNdpRpc().isDayProfilePresentForSchedule(ndpScheduleData.getNdpSeasonList(), checkTheseSpecialDays, null, null,  new ClientCallback<Boolean>() {
              @Override
              public void onSuccess(Boolean result) {
                  if (result) {
                      confirmDeleteSpecialDay(ndpSpecialDay, false);
                  } else {
                      confirmDeleteSpecialDayAndDeactivateSchedule(ndpSpecialDay);
                  }
              }
          });
      } else {
          confirmDeleteSpecialDayAndDeactivateSchedule(ndpSpecialDay);
      }
  }

  private String constructSpecialDayString(NdpSpecialDay ndpSpecialDay) {
      return ndpSpecialDay.getNdpDay()+" "+calmod.formatMonth(ndpSpecialDay.getNdpMonth()-1) ;
  }

  private void confirmDeleteSpecialDay(final NdpSpecialDay ndpSpecialDay, final boolean deactivate) {
      String specialDayToDelete = constructSpecialDayString(ndpSpecialDay);
      Dialogs.confirm(
              ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.special.day.confirm.delete", new String[] {specialDayToDelete})),
              ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
              ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
              ResourcesFactoryUtil.getInstance().getQuestionIcon(),
              new ConfirmHandler() {
                  @Override
                  public void confirmed(boolean confirm) {
                      if (confirm) {
                          deleteSpecialDayNow(ndpSpecialDay);
                          if (deactivate) {
                              parentNdpPanel.disableActivate();
                          }
                      }
                      else {
                          return;
                      }
                  }
              }
              );
  }

  private void confirmDeleteSpecialDayAndDeactivateSchedule(final NdpSpecialDay ndpSpecialDay) {
      String specialDayToDelete = constructSpecialDayString(ndpSpecialDay);
      if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentNdpPanel.isActiveBoxTicked()) {
          Dialogs.confirm(
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.special.day.confirm.delete.and.deactivate", new String[] {specialDayToDelete})),
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                  ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                  ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                  new ConfirmHandler() {
                      @Override
                      public void confirmed(boolean confirm) {
                          if (confirm) {
                              deleteSpecialDayNow(ndpSpecialDay);
                              parentNdpPanel.deactivateSchedule();
                          }
                          else {
                              return;
                          }
                      }
                  }
                  );
      } else {
          confirmDeleteSpecialDay(ndpSpecialDay, true);
      }
  }

  private void deleteSpecialDayNow(final NdpSpecialDay ndpSpecialDay) {
      //delete special day & all its day profiles
      clientFactory.getNdpRpc().deleteSpecialDayAndDayProfiles(ndpSpecialDay.getId(), new ClientCallback<Void>() {
          @Override
          public void onSuccess(Void result) {
              removeSpecialDay(ndpSpecialDay);
          }
      });
  }

  private void removeSpecialDay(NdpSpecialDay ndpSpecialDay) {
      //remove from scheduleData
      ndpScheduleData.getNdpSpecialDayList().remove(ndpSpecialDay);

      //remove from table
      dataProviderSpecialDay.getList().clear();
      if (ndpScheduleData.getNdpSpecialDayList() != null) {
          dataProviderSpecialDay.getList().addAll(ndpScheduleData.getNdpSpecialDayList());
      }
      sortSpecialDayDataProviderList();

      Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.special.day.deleted"),
              MediaResourceUtil.getInstance().getInformationIcon(),
              btnSpecialDay.getAbsoluteLeft() + btnSpecialDay.getOffsetWidth(),
              btnSpecialDay.getAbsoluteTop() - btnSpecialDay.getOffsetHeight(),
              null);
  }

  public HasDirtyDataManager getHasDirtyDataManager() {
      return this.parentNdpPanel.getHasDirtyDataManager();
  }

  public SeasonDaysDetailPanel getSeasonDaysDetailPanel() {
    return seasonDaysDetailPanel;
  }

  public SpecialDayDetailPanel getSpecialDayDetailPanel() {
    return specialDayDetailPanel;
  }

}
