<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
    xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client"
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:form="urn:import:za.co.ipay.gwt.common.client.form">
    
    <ui:style>
        .cellTable {
          border-bottom: 1px solid #ccc;
          text-align: left;
          margin-bottom: 4px;
        }
        .ctufileupload {
          margin-left: 5px;
          margin-top:5px;
        }
        .ctuMarginAbove {
            margin-top:10px;
        }
        .ctuLabelBold{
            font-weight: bold;
        }
        .addSpacing {
          margin-right: 5px;
       }      
    </ui:style>
  
   <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    <g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">
    
        <g:north size="30">
            <form:PageHeader heading="" ui:field="pageHeader" />
        </g:north>

        <g:center>
            <g:ScrollPanel>
                <g:FlowPanel ui:field="mainPanel" width="99%" height="100%">
        
                    <g:HTMLPanel>
                    <g:VerticalPanel>
                        <g:HTML ui:field="dataTitle" styleName="dataTitle" />
                        <g:VerticalPanel ui:field="genTemplateButtonPanel" styleName="formElementsPanel" visible="false">
                            <g:HTML styleName="dataDescription" text="{msg.getGentemplateButtonDesc}" />
                        </g:VerticalPanel>
                        
                        <g:VerticalPanel spacing="10" styleName="formElementsPanel" ui:field="uploadCsvPanel">
                        
                            <g:HTML ui:field="uploadCsvDescription" styleName="dataDescription" />
                            <g:FormPanel ui:field="importDataFormPanel">
                                <g:VerticalPanel>
                                    <g:Hidden name="action" value="" ui:field="actionParam" />
                                    <g:Hidden name="ignoreDups" value="false" ui:field="ignoreDupsParam" />
                                    <form:FormRowPanel>
                                        <form:FormElement ui:field="importDataFileElement" helpMsg="{msg.getBulkUploadFileSelectHelp}" labelText="{msg.getBulkUploadFileLabelText}:" debugId="importDataFileElement" >
                                            <g:FileUpload ui:field="importDataFile" name="file" title="importDataFile" styleName="{style.ctufileupload}" width="500px" debugId="importDataFile"></g:FileUpload>
                                        </form:FormElement>
                                    </form:FormRowPanel>
                                </g:VerticalPanel>
                            </g:FormPanel>        
                            
                            <g:FlowPanel>
                                <form:FormRowPanel>
                                    <form:FormElement ui:field="ignoreDupsElement" visible="false" labelText="{msg.getIgnoreMetersCheckBoxLabel}" helpMsg="{msg.getIgnoreMetersCheckBoxHelp}">
                                        <g:CheckBox ui:field="ignoreDupsCheckBox"/>
                                    </form:FormElement>
                                </form:FormRowPanel>    
                                <form:FormRowPanel>
                                    <g:Button ui:field="btnDownloadTemplate" text="{msg.getDownloadDefaultTempleteText}" visible="false" styleName="gwt-Button {style.addSpacing}"/>
                                    <g:Button ui:field="btnUploadCsv" debugId="btnUploadCsv" text="{msg.getBulkUploadCsvButton}"/>
                                </form:FormRowPanel> 
                            </g:FlowPanel>
                        </g:VerticalPanel>
                                
                        <g:VerticalPanel spacing="10" styleName="formElementsPanel" ui:field="processCsvPanel" visible="false">
                            <g:HTML ui:field="processCsvDescription" styleName="dataDescription" />
                            <g:Label ui:field="tableHeading" styleName="{style.ctuLabelBold}" debugId="tableHeading"></g:Label>    
                            <c:CellTable ui:field="clltblTransactions" debugId="transTable"/>
                            <g:Label ui:field="errorMsg" visible="false" styleName="errorInline" debugId="errorMsg"></g:Label>
                            <g:FlowPanel styleName="{style.ctuMarginAbove}">
                                <g:Button ui:field="btnProcessTrans" text="{msg.getBulkUploadProcessButton}" visible="false" enabled="false" debugId="btnProcessTrans"/>
                                <g:Button ui:field="btnCancel" text="{msg.getCancelButton}" visible="false" enabled="false" debugId="btnCancel"/>
                            </g:FlowPanel>
                        </g:VerticalPanel>
                    
                    </g:VerticalPanel>        
                    </g:HTMLPanel>
                    
               </g:FlowPanel>
            </g:ScrollPanel>
        </g:center>

    </g:DockLayoutPanel>
    
</ui:UiBinder> 