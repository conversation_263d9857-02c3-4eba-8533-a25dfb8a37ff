package za.co.ipay.metermng.client.view.component.devicestore;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.component.LocationComponent;

public class DeviceStorePanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField TextBox customMsg;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox chckbxActive;
    @UiField CheckBox storesOtherVendorsMeter;
    
    @UiField FormElement nameElement;
    @UiField FormElement customMsgElement;
    @UiField FormElement storesOtherVendorsMeterElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement activeElement;
    
    @UiField FormRowPanel storesOtherVendorsMeterRow;
    @UiField FormRowPanel customMsgRow;

    @UiField(provided=true) LocationComponent locationComponent;
        

    private String customMsgHelpMsg;
    private String storesOtherVendorsMeterMsgHelpMsg;
    private Messages messages = MessagesUtil.getInstance();

    private static DeviceStorePanelUiBinder uiBinder = GWT.create(DeviceStorePanelUiBinder.class);

    interface DeviceStorePanelUiBinder extends UiBinder<Widget, DeviceStorePanel> {
    }

    {
        customMsgHelpMsg = messages.getMessage("devicestore.field.custom_message_help");
        storesOtherVendorsMeterMsgHelpMsg = messages.getMessage("devicestore.field.store_vendors_meter_help");
    }

    public DeviceStorePanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        locationComponent = new LocationComponent(clientFactory, form);
        locationComponent.setLocation(null);
        initWidget(uiBinder.createAndBindUi(this));
        clearFields();
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        chckbxActive.setValue(true);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        locationComponent.clearFields();
        customMsg.setText("");
        storesOtherVendorsMeter.setValue(false);
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        locationComponent.clearErrors();
        customMsgElement.clearErrorMsg();
        storesOtherVendorsMeterElement.clearErrorMsg();
    }

    public void reset() {
        this.clearFields();
        this.clearErrors();

        customMsgRow.setVisible(true);
        customMsgElement.setHelpMsg(customMsgHelpMsg);

        storesOtherVendorsMeterRow.setVisible(true);
        storesOtherVendorsMeterElement.setHelpMsg(storesOtherVendorsMeterMsgHelpMsg);
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        chckbxActive.addClickHandler(new FormDataClickHandler(form));
        storesOtherVendorsMeter.addClickHandler(new FormDataClickHandler(form) {
            @Override
            public void onClick(ClickEvent event) {
                super.onClick(event);
                customMsgRow.setVisible(!storesOtherVendorsMeter.getValue());
                storesOtherVendorsMeterElement.setHelpMsg(modifiedHelpMessage(storesOtherVendorsMeterMsgHelpMsg, storesOtherVendorsMeter.getValue()));
            }
        });

        customMsg.addChangeHandler(new FormDataChangeHandler(form) {
            @Override
            public void onChange(ChangeEvent event) {
                super.onChange(event);
                String tmpValue = customMsg.getText().trim();
                storesOtherVendorsMeterRow.setVisible(tmpValue.isEmpty());
                customMsgElement.setHelpMsg(modifiedHelpMessage(customMsgHelpMsg, !tmpValue.isEmpty()));
            }
        });

        customMsg.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                storesOtherVendorsMeterRow.setVisible(false);
            }
        });

        customMsg.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                storesOtherVendorsMeterRow.setVisible(customMsg.getText().trim().isEmpty());
            }
        });
    }

    private String modifiedHelpMessage(String mainMessage, boolean applyModification) {
        String modifiedString = mainMessage + "\n<br/>";
        if (applyModification) {
            String extraHelpMsg = messages.getMessage("devicestore.field.store_vendors_meter_help2",
                    new String[]{messages.getMessage("devicestore.field.store_vendors_meter"),
                            messages.getMessage("devicestore.field.custom_message")});
            modifiedString += extraHelpMsg;
        }
        return modifiedString;
    }

    public void setModifiedHelpMessage(FormElement formElement, String defaultMessage) {
        formElement.setHelpMsg(modifiedHelpMessage(defaultMessage, true));
    }
}