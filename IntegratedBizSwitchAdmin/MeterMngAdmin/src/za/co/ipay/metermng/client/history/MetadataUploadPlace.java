package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MetadataUploadPlace extends Place {

    public static MetadataUploadPlace ALL_PLACE = new MetadataUploadPlace("all");

    private String name;

    public MetadataUploadPlace(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getPlaceAsString(MetadataUploadPlace p) {
        return "metadata-upload:"+p.getName();
    }

    @Prefix(value = "metadata-upload")
    public static class Tokenizer implements PlaceTokenizer<MetadataUploadPlace> {

        @Override
        public String getToken(MetadataUploadPlace place) {
            return "all";
        }

        @Override
        public MetadataUploadPlace getPlace(String token) {
            return new MetadataUploadPlace(token);
        }
    }

}
