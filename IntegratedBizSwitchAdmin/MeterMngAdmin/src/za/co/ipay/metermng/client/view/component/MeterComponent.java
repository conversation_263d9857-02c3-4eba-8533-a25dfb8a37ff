package za.co.ipay.metermng.client.view.component;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.AppSettingEvent;
import za.co.ipay.metermng.client.event.AppSettingEventHandler;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEventHandler;
import za.co.ipay.metermng.client.event.MeterModelChangedEvent;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEvent;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.devicestore.DeviceStoreMeters;
import za.co.ipay.metermng.client.view.component.meter.CentianTamperStatesDialogueBox;
import za.co.ipay.metermng.client.view.component.meter.ContainsMeterComponent;
import za.co.ipay.metermng.client.view.component.meter.MeterComponentTokenPanel;
import za.co.ipay.metermng.client.view.component.pricingstructure.MeterComponentValidatePStoMM;
import za.co.ipay.metermng.client.view.component.usercustomfields.ContainsUserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.component.usercustomfields.UserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.MeterPhaseE;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.appsettings.PowerLimitValue;
import za.co.ipay.metermng.shared.dto.CentianTamperStates;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class MeterComponent extends BaseComponent implements ProvidesResize, RequiresResize, ContainsUserCustomFieldsComponent, AssignChannelReadingsComponent {

    private static MeterComponentUiBinder uiBinder = GWT.create(MeterComponentUiBinder.class);

    @UiField DisclosurePanel meterDisclosurePanel;
    @UiField TextBox txtbxMeterNumber;
    @UiField TextBox txtbxSerialNumber;
    @UiField IpayListBox lstbxTtCode;
    @UiField IpayListBox lstbxAlgCode;
    @UiField IpayListBox lstbxSgKrn;
    @UiField TextBox txtbxCurrTI;
    @UiField Label baseDateLabel;
    @UiField CheckBox chckbxThreeTokens;
    @UiField Button btnCancel;
    @UiField Button btnSave;
    @UiField Label lblReplace;
    @UiField Label lblRemove;
    @UiField Image openorclosearrow;
    @UiField Image meterImage;
    @UiField Label headerLabel;
    @UiField IpayListBox lstbxSelectStore;
    @UiField IpayListBox lstbxMeterModel;
    TextBox txtbxMrid;
    CheckBox chckbxMridExternal;
    @UiField TextBox txtbxBreakerId;
    @UiField TextBox txtbxEncryptionKey;
    @UiField TextBox txtbxMeterUriAddress;
    @UiField BigDecimalValueBox txtbxMeterUriPort;
    @UiField TextBox txtbxMeterUriProtocol;
    @UiField TextArea txtbxMeterUriParams;
    @UiField FormElement meterModelElement;
    @UiField FormElement replaceLinkElement;
    @UiField FormElement removeLinkElement;
    @UiField FormElement meterNumberElement;
    @UiField FormElement meterSerialNumElement;
    @UiField FormElement tTCodeElement;
    @UiField FormElement algCodeElement;
    @UiField FormElement currSGCElement;
    @UiField FormElement currTIElement;
    @UiField FormRowPanel chckbxThreeTokensFormRow;
    @UiField FormElement chckbxThreeTokensElement;
    FormElement mridElement;
    @UiField FlowPanel selectStorePanel;
    @UiField FormRowPanel breakerIdPanel;
    @UiField FormElement breakerIdElement;
    @UiField FormRowPanel encryptionKeyPanel;
    @UiField FormElement encryptionKeyElement;
    @UiField FormGroupPanel uriPanel;
    @UiField FormElement meterUriAddressElement;
    @UiField FormElement meterUriPortElement;
    @UiField FormElement meterUriProtocolElement;
    @UiField FormElement meterUriParamsElement;
    @UiField FlowPanel contentPanel;
    @UiField HTMLPanel buttons;
    @UiField FlowPanel requiredKeys;
    @UiField FormRowPanel generateKeyChangeFormRow;
    @UiField FormElement generateKeyChangeElement;
    @UiField IpayListBox lstbxGenerateKeyChange;
    @UiField Label pendingkeychange;
    @UiField Label lblMeterAssigned;
    @UiField Label lblDateMeterInstalled;

    @UiField FormGroupPanel stsContainer;
    @UiField FormGroupPanel centianContainer;
    @UiField Label lblCentianKwhCreditRem;
    @UiField Label lblCentianCurrCredityRem;
    @UiField Label lblCentianNumDisc;
    @UiField Label lblCentianTamperDetected;
    @UiField Label lblCentianTamperStates;
    @UiField Label lblCentianInfoDate;

    @UiField(provided = true) IpayListBox powerLimitListBox;
    @UiField FormGroupPanel powerLimitContainer;
    @UiField FormRowPanel generatePowerLimitFormRow;
    @UiField FormElement powerLimitElement;
    @UiField IpayListBox generatePowerLimitTokenListBox;

    @UiField FormElement deviceStoreElement;
    @UiField(provided=true) UserCustomFieldsComponent userCustomFieldsComponent;
    @UiField FormRowPanel meterPhasePanel;
    @UiField TextBox txtbxMeterPhase;

    @UiField (provided=true)MridComponent mridComponent;

    private ClientFactory clientFactory;
    private AssignMeterDialogueBox assignMeterDialogueBox;
    private RemoveMeterDialogueBox removeMeterDialogueBox;
    private CentianTamperStatesDialogueBox tamperStatesDialogueBox;

    protected MeterData meterData = new MeterData();
    private ContainsMeterComponent containsMeterComponent;
    private Boolean disclosureOpen = null;

    private int transactionCount = 0;
    private Long endDeviceStoreId = null;
    private Long accessGroupId = null;

    private boolean userCustomFieldsComponentVisible;
    private boolean meterModelChanged = false;
    private boolean newMeterModelHasChannels = false;
    private boolean isStsMeter = false;
    private String newMrid;
    private String storeMrid;
    private boolean storeMridExternal = false;
    private HasDirtyData hasDirtyData;

    private static final Integer PRESELECT_STS_DROPDOWN_MAXCOUNT = 2;
    private static Logger logger = Logger.getLogger(MeterComponent.class.getName());

    private ArrayList<LookupListItem> powerLimitLookupListItems = new ArrayList<>();
    private List<AppSetting> customFields;

    private List<PowerLimitValue> powerLimits = new ArrayList<>();

    private Messages messages = MessagesUtil.getInstance();
    private int newPowerLimitIndex = -1;
    private Map<String, FormFields> userInterfaceFields;

    private ClientCallback<MeterData> meterSvcAsyncCallback;
    private UsagePointWorkspaceView usagePointWorkspaceView = null;

    interface MeterComponentUiBinder extends UiBinder<Widget, MeterComponent> {
    }

    public MeterComponent(ClientFactory theClientFactory, ContainsMeterComponent parentWorkspace, List<AppSetting> customFields) {
        this.hasDirtyData = parentWorkspace.getWorkspace().createAndRegisterHasDirtyData();
        this.clientFactory = theClientFactory;
        this.containsMeterComponent = parentWorkspace;
        this.customFields = customFields;
        if (containsMeterComponent.getWorkspace() instanceof UsagePointWorkspaceView) {
            this.usagePointWorkspaceView = (UsagePointWorkspaceView) containsMeterComponent.getWorkspace();
        }
        userCustomFieldsComponent = new UserCustomFieldsComponent(clientFactory, this, hasDirtyData);
        mridComponent = new MridComponent(clientFactory, hasDirtyData, meterData.getMrid(), meterData.isMridExternal());
        txtbxMrid = mridComponent.getTxtbxMrid();
        chckbxMridExternal = mridComponent.getChckbxMridExternal();
        mridElement = mridComponent.getTxtbxMridElement();
        initPowerLimit();
        initWidget(uiBinder.createAndBindUi(this));
        init();
        addFieldHandlers();
    }

    private void init() {
        configureCustomFields();
        openorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
        meterImage.setResource(MediaResourceUtil.getInstance().getDashboardSmallImage());
        actionPermissions(false);    // remove action buttons if no edit permission

        getNewMrid();

        addHandlers();

        if (containsMeterComponent instanceof DeviceStoreMeters) {
            lblReplace.removeFromParent();
            removeLinkElement.removeFromParent();
        }

        disclosureOpen = null;

        generateKeyChangeFormRow.setVisible(false);
        lstbxGenerateKeyChange.addItem(MessagesUtil.getInstance().getMessage("meter.keychange.none"), "none");
        lstbxGenerateKeyChange.addItem(MessagesUtil.getInstance().getMessage("meter.keychange.now"), "now");
        lstbxGenerateKeyChange.addItem(MessagesUtil.getInstance().getMessage("meter.keychange.flag"), "flag");

        generatePowerLimitFormRow.setVisible(false);
        generatePowerLimitTokenListBox.addItem(MessagesUtil.getInstance().getMessage("tokens.power_limit.no_gen"), "no_gen");
        generatePowerLimitTokenListBox.addItem(MessagesUtil.getInstance().getMessage("tokens.power_limit.gen"), "gen");
    }

    void getNewMrid() {
        clientFactory.getMeterRpc().getNewMrid(new ClientCallback<String>() {
            @Override
            public void onSuccess(String result) {
                newMrid = result;
                String mrid = meterData.getMrid();
                if (mrid == null) {
                    txtbxMrid.setText(newMrid);
                } else {
                    txtbxMrid.setText(mrid);
                }
            }
        });
    }

    public void initDialogs() {
        if (assignMeterDialogueBox != null && assignMeterDialogueBox.isShowing()) {
            assignMeterDialogueBox.populateDeviceMoveRef();
        }
        if (removeMeterDialogueBox != null && removeMeterDialogueBox.isShowing()) {
            removeMeterDialogueBox.populateDeviceMoveRef();
        }
    }

    /**
     * The order of execution matters.
     * setVisible vs removeFromParent.
     * setVisible is used to temporary hide/display the element.
     * removeFromParent only works one way.
     */
    private void actionPermissions(boolean isSetDataCall) {

        // We use setVisible and not removeFromParent since Fetch might be used.
        requiredKeys.setVisible(true);
        lblReplace.setVisible(true);
        lblRemove.setVisible(true);
        buttons.setVisible(true);

        UsagePointData usagePointData = containsMeterComponent.getUsagePoint();
        if (clientFactory.isEnableAccessGroups()) {
            boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointData, clientFactory.isGroupGroupUser());
            if (groupHasGlobal) {
                requiredKeys.setVisible(false);
                lblReplace.setVisible(false);
                lblRemove.setVisible(false);
                buttons.setVisible(false);
            }

            if (clientFactory.isGroupGlobalUser()) {
                requiredKeys.removeFromParent();
                lblReplace.removeFromParent();
                lblRemove.removeFromParent();

                if (usagePointData != null) {
                    CustomerAgreementData customerAgreementData = usagePointData.getCustomerAgreementData();
                    if ((customerAgreementData != null && customerAgreementData.getId() != null)
                            || usagePointData.getId() != null) {
                        // Global cannot operate on a meter belong to a group contract.
                        buttons.removeFromParent();
                    }
                }
            }
        }

        if (isSetDataCall && meterData.getId() == null
                && clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_EDIT)
                && !clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_ADD)) {
            // We use setVisible and not removeFromParent since Fetch Meter might be used.
            requiredKeys.setVisible(false);
            buttons.setVisible(false);
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_ADD)
                && !clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_EDIT)) {
            // Has no add or edit
            requiredKeys.removeFromParent();
            buttons.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_UP_REPLACE_METER)) {
            replaceLinkElement.removeFromParent();
            removeLinkElement.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
            mridComponent.removeFromParent();
        }
    }

    private void populateAlgCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxAlgCode.setLookupItems(result);
                if (meterData.getStsMeter() != null && meterData.getStsMeter().getStsAlgorithmCodeId() != null) {
                    lstbxAlgCode.selectItemByValue(meterData.getStsMeter().getStsAlgorithmCodeId().toString());
                } else {
                    setDefaultListItem(result, lstbxAlgCode);
                }
            }

        };
        if (clientFactory != null) {
            lstbxAlgCode.clear();
            clientFactory.getLookupRpc().getAlgCodeLookupList(lookupSvcAsyncCallback);
        }
    }

    private void setDefaultListItem(List<LookupListItem> result, IpayListBox box) {
        for (LookupListItem item : result) {
            if (item.isSelected()) {
                box.selectItemByValue(item.getValue());
                break;
            }
        }
    }

    private void populateTtCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxTtCode.setLookupItems(result);
                if (meterData.getStsMeter() != null && meterData.getStsMeter().getStsTokenTechId() != null) {
                    lstbxTtCode.selectItemByValue(meterData.getStsMeter().getStsTokenTechId().toString());
                } else {
                    setDefaultListItem(result, lstbxTtCode);
                }
            }

        };
        if (clientFactory != null) {
            lstbxTtCode.clear();
            clientFactory.getLookupRpc().getTtCodeLookupList(lookupSvcAsyncCallback);
        }
    }

    private void populateSgKrnCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                if (result != null) {
                    lstbxSgKrn.setLookupItems(result);
                    if (meterData.getStsMeter() != null && meterData.getStsMeter().getStsCurrSupplyGroupId() != null) {
                        lstbxSgKrn.selectItemByValue(meterData.getStsMeter().getStsCurrSupplyGroupId().toString());
                        showStsBaseDate();
                        handleShowKeyChangeQuery();
                    } else {
                        setDefaultListItem(result, lstbxSgKrn);
                    }
                }
            }

        };
        if (clientFactory != null) {
            lstbxSgKrn.clear();
            clientFactory.getLookupRpc().getSgKrnLookupList(lookupSvcAsyncCallback);
        }
    }

    private void presetSTSValues() {
        if (stsContainer.isVisible() && meterData.getStsMeter() == null) {
            if (lstbxAlgCode.getSelectedIndex() == 0 && lstbxAlgCode.getItemCount() == PRESELECT_STS_DROPDOWN_MAXCOUNT) {
                lstbxAlgCode.setSelectedIndex(1);
            }
            if (lstbxTtCode.getSelectedIndex() == 0 && lstbxTtCode.getItemCount() == PRESELECT_STS_DROPDOWN_MAXCOUNT) {
                lstbxTtCode.setSelectedIndex(1);
            }
            if (lstbxSgKrn.getSelectedIndex() == 0 && lstbxSgKrn.getItemCount() == PRESELECT_STS_DROPDOWN_MAXCOUNT) {
                lstbxSgKrn.setSelectedIndex(1);
            }
            showStsBaseDate();
        }
    }

    private void populateStoresListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                if (result == null) {
                    result = new ArrayList<LookupListItem>();
                }
                result.add(0, new LookupListItem(null, ""));
                lstbxSelectStore.setLookupItems(result);
                if (meterData != null && meterData.getEndDeviceStoreId() != null) {
                    lstbxSelectStore.selectItemByValue(meterData.getEndDeviceStoreId().toString());
                }
            }

        };
        if (clientFactory != null) {
            lstbxSelectStore.clear();
            clientFactory.getLookupRpc().getEndDeviceStoresLookupList(lookupSvcAsyncCallback);
        }
    }

    public void populateMeterModelListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxMeterModel.setLookupItems(result);

                logger.info("populateMeterModelListBox() onSuccess: lstbxMeterModel.getItemCount()=" + lstbxMeterModel.getItemCount() + " result.size=" + result.size());
                if (meterData.getMeterModelId() != null) {
                    lstbxMeterModel.selectItemByValue(String.valueOf(meterData.getMeterModelId()));
                }

                //if selected OR first item on list (which is default) has needs needs_breaker_id or needs_enc_key, then enable relevant inputs
                checkBreakerIdEncryptionKeyRequired();
                checkUriIsPresent();
                setIsStsMeter();
                checkPowerLimitRequired();
                updateMeterPhase();
            }

        };
        if (clientFactory != null) {
            lstbxMeterModel.clear();
            UsagePointData uPointData = containsMeterComponent.getUsagePoint();
            if (uPointData != null && uPointData.getUpPricingStructureData() != null && uPointData.getUpPricingStructureData().getPricingStructure() != null) {
                clientFactory.getLookupRpc().getMeterModelByPricingStructureIdLookupList(uPointData.getUpPricingStructureData().getPricingStructure().getId(), lookupSvcAsyncCallback);
            } else {
                clientFactory.getLookupRpc().getMeterModelLookupList(lookupSvcAsyncCallback);
            }
        }
    }

    @SuppressWarnings("deprecation")
    private void setIsStsMeter() {
        LookupListItem item = lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex());
        isStsMeter = (item.getExtraInfo().equals(String.valueOf(MeterTypeE.STS.getId())));
        logger.info("isSts2: " + isStsMeter);
        stsContainer.setVisible(isStsMeter && clientFactory.isEnableSTS());
        presetSTSValues();
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        final UsagePointData usagePointData = containsMeterComponent.getUsagePoint();
        toggleUPWorkspaceButtons(false);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePointData != null) {
                    usagePointData.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointData, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleSaveButton();
                            } else {
                                Place place = ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).getPlace();
                                ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).processInvalidState(place);
                                toggleUPWorkspaceButtons(true);
                            }
                        }
                    });
                } else {
                    continueHandleSaveButton();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void continueHandleSaveButton() {
        // Map form fields to data object
        meterSvcAsyncCallback = new ClientCallback<MeterData>() {

            @Override
            public void onSuccess(MeterData result) {
                hasDirtyData.setDirtyData(false);

                if (containsMeterComponent instanceof DeviceStoreMeters) {
                    clearMeterComponent(false);
                    meterData = new MeterData();
                } else {
                    meterData = result;
                }

                if (containsMeterComponent.getUsagePoint() != null) {
                    containsMeterComponent.getUsagePoint().setMeterData(meterData);
                    if (!meterData.getRecordStatus().equals(RecordStatus.ACT)) {
                        containsMeterComponent.getUsagePoint().setRecordStatus(RecordStatus.DAC);
                    }
                    if (meterModelChanged) {
                        clientFactory.getEventBus().fireEvent(new MeterModelChangedEvent());
                    }
                }

                boolean doKeyChange = false;
                if (generateKeyChangeFormRow.isVisible()
                    && lstbxGenerateKeyChange.getSelectedValues().get(0).equals("now")) {
                    doKeyChange = true;
                }
                boolean doPowerLimit = false;
                if (generatePowerLimitTokenListBox.isVisible()
                    && generatePowerLimitTokenListBox.getSelectedIndex() == 1) {
                    doPowerLimit = true;
                }
                if (doKeyChange || doPowerLimit) {
                    PopupPanel simplePopup = new PopupPanel(false);
                    VerticalPanel verticalPanel = new VerticalPanel();
                    simplePopup.setAnimationEnabled(true);
                    simplePopup.setStylePrimaryName("ipaypopup");
                    Long upid = null;
                    if (containsMeterComponent.getUsagePoint() != null) {
                        upid = containsMeterComponent.getUsagePoint().getId();
                    }
                    if (doKeyChange) {
                        verticalPanel.add(new MeterComponentTokenPanel(clientFactory, meterData.getStsMeter(),
                                meterData, upid, simplePopup, !doPowerLimit, null));
                    }
                    if (doPowerLimit) {
                        verticalPanel.add(
                                new MeterComponentTokenPanel(clientFactory, meterData.getStsMeter(), meterData,
                                        upid, simplePopup, true, powerLimitLookupListItems.get(newPowerLimitIndex)));
                    }
                    simplePopup.setWidget(verticalPanel);
                    simplePopup.setGlassEnabled(true);
                    simplePopup.center();
                    simplePopup.show();
                }

                showHidePendingKeyChangeMessage();
                generateKeyChangeFormRow.setVisible(false);

                // Moved here to fix token generation failure for new meter.
                generatePowerLimitTokenListBox.setItemSelected(0, true);

                if (meterModelChanged) { // Notify MDCTransactionView of change
                    clientFactory.getWorkspaceContainer().notifyWorkspaces(
                            new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.METER_MODEL_MODIFIED));
                    // if meterModel change to a regread meterModel with Channels and is linked to an ACTive Usagepoint, ask for start register Readings
                    if (newMeterModelHasChannels && containsMeterComponent.getUsagePoint() != null
                            && containsMeterComponent.getUsagePoint().getRecordStatus().equals(RecordStatus.ACT)) {
                        checkChannelInfoForInitRegReads();
                    } else {
                        completeSaveMeter();
                    }
                } else {
                    completeSaveMeter();
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                toggleUPWorkspaceButtons(true);
                super.onFailure(caught);
            }
        };
        clearErrorMessages();
        setIsStsMeter();
        if (validate()) {
            //if meterModel changed and meter is connected to usage_point, check compatibility for regReads billing dets
            if (meterModelChanged && containsMeterComponent.getUsagePoint() != null && containsMeterComponent.getUsagePoint().getId() != null) {
                //if meterModel was one with an MDC or is now one with an MDC then go check the PS compatibility
                //Rather than first selecting PS for both current anf future and deciding if RegRead involved, just go run the method, will return the answer
                //if previous MM has channels or new MM has channels, go test
                LookupListItem newMeterModelLli = lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex());
                if (meterData != null && meterData.getMeterModelData() != null && meterData.getMeterModelData().isMdcHasChannels()
                    || ("true".equals(newMeterModelLli.getExtraInfoMap().get("hasChannels")) ? true : false)) {

                    Long currentPsId = containsMeterComponent.getUsagePoint().getUpPricingStructureData().getPricingStructure().getId();
                    UpPricingStructureData futurePsData = containsMeterComponent.getUsagePoint().getUpPricingStructureData().getFutureUpPricingStructureData();

                    List<PSDto> pSDtos = new ArrayList<>();
                    //for currentPS sending in new date and not installDate because on takeOn of data from other systems,
                    //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
                    pSDtos.add(new PSDto(currentPsId, new Date()));
                    if (futurePsData != null) {
                        pSDtos.add(new PSDto(futurePsData.getUpPricingStructure().getPricingStructureId(), futurePsData.getUpPricingStructure().getStartDate()));
                    }

                    String newMdcIdStr = (String) newMeterModelLli.getExtraInfoMap().get("mdcId");

                    new MeterComponentValidatePStoMM(this).isregReadPsSameBillingDetsAsMeterModel(clientFactory, pSDtos,
                            (newMdcIdStr == null ? null : Long.parseLong(newMdcIdStr)),
                            clientFactory.getUser().getUserName(), logger);

                } else {
                    saveContinue(meterSvcAsyncCallback);
                }
            } else {
                saveContinue(meterSvcAsyncCallback);
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.save.errors"), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(), btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(), null);
            toggleUPWorkspaceButtons(true);
        }
    }

    //Start Methods for MeterComponentValidatePStoMM
    public void errorOrNonMatchPsToMm() {
        lstbxMeterModel.selectItemByValue(String.valueOf(Long.valueOf(lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getValue())));
        toggleUPWorkspaceButtons(true);
    }

    public void passToSaveContinue() {
        saveContinue(meterSvcAsyncCallback);
    }
    public void partialMatchDenyContinue() {
        //replace meterModel with previous
        if (meterData != null && meterData.getMeterModelId() != null) {
            lstbxMeterModel.selectItemByValue(String.valueOf(meterData.getMeterModelId()));
        } else {
            lstbxMeterModel.setSelectedIndex(0);
        }
        toggleUPWorkspaceButtons(true);
        return;
    }
    //End Methods for MeterComponentValidatePStoMM

    private void saveContinue(final ClientCallback<MeterData> meterSvcAsyncCallback) {
        if (selectStorePanel.isVisible()) {
            LookupListItem item = lstbxSelectStore.getItem(lstbxSelectStore.getSelectedIndex());
            Long selectedStore = Long.valueOf(item.getValue());
            if (!selectedStore.equals(endDeviceStoreId)) {
                saveWithDeviceStore(selectedStore, meterSvcAsyncCallback);
            } else { // No need to show confirmation dialog if no change in store
                saveOrUpdateMeter(meterSvcAsyncCallback);
            }
        } else if (endDeviceStoreId != null) {
            saveWithDeviceStore(endDeviceStoreId, meterSvcAsyncCallback);
        } else {
            saveOrUpdateMeter(meterSvcAsyncCallback);
        }
    }

    private void saveWithDeviceStore(final Long deviceStoreId, final ClientCallback<MeterData> meterSvcAsyncCallback) {
        clientFactory.getDeviceStoreRpc().getDeviceStore(deviceStoreId, new ClientCallback<EndDeviceStoreData>() {
            @Override
            public void onSuccess(EndDeviceStoreData result) {
                if (result.isStoresOtherVendorsMeter()) {
                    Dialogs.confirm(messages.getMessage("devicestore.meters.save.dialog", new String[]{result.getName()}),
                        messages.getMessage("button.yes"), messages.getMessage("button.no"),
                        MediaResourceUtil.getInstance().getInformationIcon(), new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    saveOrUpdateMeter(meterSvcAsyncCallback);
                                } else {
                                    toggleUPWorkspaceButtons(true);
                                }

                                if (!confirm && selectStorePanel.isVisible()) {
                                    lstbxSelectStore.selectItemByValue(endDeviceStoreId.toString());
                                }
                            }
                        });
                } else {
                    saveOrUpdateMeter(meterSvcAsyncCallback);
                }
            }
        });
    }


    private void saveOrUpdateMeter(ClientCallback<MeterData> meterSvcAsyncCallback) {

        MeterData newMeterData = new MeterData();
        if (meterData != null) {
            newMeterData.setId(meterData.getId());
            newMeterData.setStsMeter(meterData.getStsMeter());
            newMeterData.setPowerLimit(meterData.getPowerLimit());
            newMeterData.setPowerLimitLabel(meterData.getPowerLimitLabel());
            newMeterData.setAggregatorNotified(meterData.isAggregatorNotified());
        }
        mapFormToData(newMeterData);
        if (clientFactory != null) {
            clientFactory.getMeterRpc().updateMeter(newMeterData, meterSvcAsyncCallback);
        }
    }

    private void completeSaveMeter() {
        meterModelChanged = false;
        containsMeterComponent.onSaveMeter(meterData, btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
            btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth());
    }

    private void checkChannelInfoForInitRegReads() {
        final MeterComponent parent = this;
        final UsagePointData usagePoint = containsMeterComponent.getUsagePoint();
        MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(meterData.getId(),
            meterData.getMeterModelId(),
            usagePoint.getId(),
            usagePoint.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId(),
            usagePoint.getUpMeterInstall().getId(),
            usagePoint.getInstallationDate());
        clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo, new ClientCallback<MeterUpMdcChannelInfo>() {
            @Override
            public void onSuccess(MeterUpMdcChannelInfo result) {
                if (result.getChannelList() == null || result.getChannelList().isEmpty()) {
                    logger.info("getMeterUpMdcChannelInfo: result is null");
                    fireUpdateEvent(null);
                } else {
                    logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size=" + result.getChannelList().size());

                    //get initial readings for the channels
                    final AssignChannelReadingsDialogueBox assignChannelReadings =
                            new AssignChannelReadingsDialogueBox(parent, result, meterData.getMeterNum(),
                                    usagePoint.getName(),
                                    meterData.getMeterModelData().getName(),
                                    meterData.getMeterModelData().getMdcName(),
                                    usagePoint.getUpPricingStructureData().getPricingStructure().getName(),
                                    meterData.getMeterModelData().getServiceResourceId());
                            Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                @Override
                                public void execute() {
                                    assignChannelReadings.center();
                                    assignChannelReadings.show();
                                }
                            });
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                toggleUPWorkspaceButtons(true);
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void fireUpdateEvent(final MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if (meterUpMdcChannelInfo != null
                        && meterUpMdcChannelInfo.getChannelList() != null
                        && !meterUpMdcChannelInfo.getChannelList().isEmpty()
                        && meterUpMdcChannelInfo.getUsagePointId() != null) {
                    clientFactory.getMeterRpc().saveInitRegReadings(meterUpMdcChannelInfo, new ClientCallback<Void>() {
                        @Override
                        public void onSuccess(Void result) {
                            logger.info("MeterComponent: Successful save of init reg readings");
                            completeSaveMeter();
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            logger.info("MeterComponent: saveInitRegReadings failed. Throwable.getMessage()= " + caught.getMessage());
                            toggleUPWorkspaceButtons(true);
                            super.onFailure(caught);
                        }
                    });
                } else {
                    completeSaveMeter();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    // clear custom fields
                    userCustomFieldsComponent.clearCustomFields();
                    //Map form fields to data object
                    if (clientFactory != null) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution sessionCheckResolution) {
                                containsMeterComponent.onCancelMeter();
                                mapDataToForm();
                                checkBreakerIdEncryptionKeyRequired();
                                checkUriIsPresent();
                                checkPowerLimitRequired();
                                meterModelChanged = false;
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getMessage("meter.changes.cleared"),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnCancel.getAbsoluteLeft() + btnCancel.getOffsetWidth(),
                                        btnCancel.getAbsoluteTop() - btnCancel.getOffsetHeight(), null);
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            }
        });
    }

    @UiHandler("lblReplace")
    void handleReplaceLink(ClickEvent event) {
        final UsagePointData usagePointData = containsMeterComponent.getUsagePoint();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePointData != null) {
                    usagePointData.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointData, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleReplaceLink();
                            } else {
                                Place place = ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).getPlace();
                                ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).processInvalidState(place);
                            }
                        }
                    });
                } else {
                    continueHandleReplaceLink();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void continueHandleReplaceLink() {
        clearErrorMessages();
        this.assignMeterDialogueBox = new AssignMeterDialogueBox(clientFactory, containsMeterComponent);
        containsMeterComponent.onAssignMeter(assignMeterDialogueBox);
        assignMeterDialogueBox.clear();
        assignMeterDialogueBox.setGlassEnabled(true);
        assignMeterDialogueBox.clearErrorMessages();
        assignMeterDialogueBox.setAutoHideEnabled(false);
        assignMeterDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
        assignMeterDialogueBox.setAnimationEnabled(true);
        String popupHeading = "meter.replace";
        if (lblReplace.getText().equals(MessagesUtil.getInstance().getMessage("meter.assign.from.store"))) {
            popupHeading = "meter.fetch.number";
        }
        assignMeterDialogueBox.setText(MessagesUtil.getInstance().getMessage(popupHeading) + ":");
        int left = lblReplace.getAbsoluteLeft() + lblReplace.getOffsetWidth() - 250;
        int top = lblReplace.getAbsoluteTop() + lblReplace.getOffsetHeight();
        assignMeterDialogueBox.setPopupPosition(left, top);
        assignMeterDialogueBox.setReplacingOldMeter(containsMeterComponent.getUsagePoint().getId() != null && meterData.getId() != null);
        assignMeterDialogueBox.setInstallationDate(new Date());
        assignMeterDialogueBox.show();
    }

    @UiHandler("lblRemove")
    void handleRemoveLink(ClickEvent event) {
        final UsagePointData usagePointData = containsMeterComponent.getUsagePoint();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePointData != null) {
                    usagePointData.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointData, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleRemoveLink();
                            } else {
                                Place place = ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).getPlace();
                                ((UsagePointWorkspaceView) containsMeterComponent.getWorkspace()).processInvalidState(place);
                            }
                        }
                    });
                } else {
                    continueHandleRemoveLink();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void continueHandleRemoveLink() {
        clearErrorMessages();

        this.removeMeterDialogueBox = new RemoveMeterDialogueBox(clientFactory, containsMeterComponent);
        //reset position because if new page might still be constructing the DOM
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {
            @Override
            public void execute() {
                removeMeterDialogueBox.center();
            }
        });

        containsMeterComponent.onRemoveMeter(removeMeterDialogueBox);
        if (containsMeterComponent.getUsagePoint().getMeterData() == null) {
            containsMeterComponent.getUsagePoint().setMeterData(meterData);
        }
        removeMeterDialogueBox.clear();
        removeMeterDialogueBox.displayClearMeterBalanceMsg();
        removeMeterDialogueBox.setGlassEnabled(true);
        removeMeterDialogueBox.clearErrorMessages();
        removeMeterDialogueBox.setAutoHideEnabled(false);
        removeMeterDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
        removeMeterDialogueBox.setAnimationEnabled(true);
        removeMeterDialogueBox.setText(MessagesUtil.getInstance().getMessage("meter.remove") + ":");
        removeMeterDialogueBox.center();
        removeMeterDialogueBox.show();
    }

    @UiHandler("lblCentianTamperDetected")
    void handlelblCentianTamperDetectedLink(ClickEvent event) {
        if (tamperStatesDialogueBox == null) {
            tamperStatesDialogueBox = new CentianTamperStatesDialogueBox(clientFactory, meterData.getCentianTamperStates(), meterData.getStsMeter().getTamperUpdate());
        }
        tamperStatesDialogueBox.setAutoHideEnabled(true);
        tamperStatesDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
        tamperStatesDialogueBox.setAnimationEnabled(true);
        tamperStatesDialogueBox.center();
        tamperStatesDialogueBox.show();
    }

    public void showRemoveMeterPopup() {
        if (!lblRemove.isAttached()) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                MediaResourceUtil.getInstance().getLockedIcon(),
                MessagesUtil.getInstance().getMessage("button.close"));
            return;
        }

        lblRemove.fireEvent(new ClickEvent() {
        });
    }

    private void checkUriIsPresent() {
        boolean uriIsPresent = meterModelUriIsPresent();
        logger.info("checkUriIsPresent: uriIsPresent=" + uriIsPresent);

        if (uriIsPresent) {
            uriPanel.setVisible(true);
            if (meterData != null) {
                if (meterData.getMeterUriAddress() != null) {
                    txtbxMeterUriAddress.setText(meterData.getMeterUriAddress());
                }
                if (meterData.getMeterUriPort() != null) {
                    txtbxMeterUriPort.setText(meterData.getMeterUriPort().toString());
                }
                if (meterData.getMeterUriProtocol() != null) {
                    txtbxMeterUriProtocol.setText(meterData.getMeterUriProtocol());
                }
                if(meterData.getMeterUriParams() != null) {
                    txtbxMeterUriParams.setText(meterData.getMeterUriParams());
                }

            }
        } else {
            uriPanel.setVisible(false);
            txtbxMeterUriAddress.setText("");
            txtbxMeterUriPort.setValue(null);
            txtbxMeterUriProtocol.setText("");
            txtbxMeterUriParams.setText("");
        }
    }

    private void checkBreakerIdEncryptionKeyRequired() {
        boolean needsBreakerId = meterModelNeedsBreakerId();
        boolean needsEncryptionKey = meterModelNeedsEncryptionKey();
        logger.info("checkBreakerIdEncryptionKeyRequired(): needsBreakerId=" + needsBreakerId + ", needsEncryptionKey=" + needsEncryptionKey);

        if (needsBreakerId) {
            breakerIdPanel.setVisible(true);
            if (meterData != null && meterData.getBreakerId() != null) {
                txtbxBreakerId.setText(meterData.getBreakerId());
            }
        } else {
            breakerIdPanel.setVisible(false);
            txtbxBreakerId.setText("");
        }
        if (needsEncryptionKey) {
            encryptionKeyPanel.setVisible(true);
            txtbxEncryptionKey.setText("");
            if (meterData != null && meterData.getEncKey() != null) {
                txtbxEncryptionKey.setText(meterData.getEncKey());
            }
        } else {
            encryptionKeyPanel.setVisible(false);
            txtbxEncryptionKey.setText("");
        }
    }

    private void checkPowerLimitRequired() {
        boolean needsPowerLimit = meterModelNeedsPowerLimit();
        logger.info("checkPowerLimitRequired(): needsPowerLimit=" + needsPowerLimit);

        if (needsPowerLimit) {
            if (meterData.getPowerLimit() != null) {
                for (int x = 0; x < powerLimits.size(); x++) {
                    if (meterData.getPowerLimit().toString().equals(powerLimitListBox.getListBox().getValue(x))) {
                        powerLimitListBox.setItemSelected(x, true);
                        generatePowerLimitTokenListBox.setItemSelected(0, true);
                        generatePowerLimitFormRow.setVisible(isStsMeter && !(powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()).trim().equals("")));
                        break;
                    }
                }
            } else {
                updatePowerLimitUI();
            }
            powerLimitContainer.setVisible(true);
            generatePowerLimitFormRow.setVisible(clientFactory.getUser().hasPermission("mm_gen_power_limit") && isStsMeter && !(powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()).trim().equals("")));
        } else {
            updatePowerLimitUI();
        }
    }

    private void updatePowerLimitUI() {
        powerLimitListBox.setSelectedIndex(0);
        generatePowerLimitTokenListBox.setItemSelected(0, true);
        generatePowerLimitFormRow.setVisible(false);
        powerLimitContainer.setVisible(false);
    }

    @SuppressWarnings("deprecation")
    private boolean meterModelNeedsEncryptionKey() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex < 0) {
            // some async scenarios causing issues, needs revamp of list box population to chain calls
            return false;
        }
        boolean hasEncryptionKey = lstbxMeterModel.getItem(selectedIndex).getExtraInfo3().equals("true");
        return hasEncryptionKey;
    }

    @SuppressWarnings("deprecation")
    private boolean meterModelNeedsBreakerId() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex < 0) {
            // some async scenarios causing issues, needs revamp of list box population to chain calls
            return false;
        }
        return lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getExtraInfo2().equals("true");
    }

    private boolean meterModelNeedsPowerLimit() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex < 0) {
            // some async scenarios causing issues, needs revamp of list box population to chain calls
            return false;
        }
        if (!isStsMeter) {
            return false;
        }
        return "true".equals(lstbxMeterModel.getItem(selectedIndex).getExtraInfoMap().get("hasPowerLimit"));
    }

    private boolean meterModelUriIsPresent() {
        int selectedIndex = lstbxMeterModel.getSelectedIndex();
        if (selectedIndex < 0) {
            // some async scenarios causing issues, needs revamp of list box population to chain calls
            return false;
        }
        return "true".equals(lstbxMeterModel.getItem(selectedIndex).getExtraInfoMap().get("uriPresent"));
    }

    @SuppressWarnings("unlikely-arg-type")
    @UiHandler("lstbxMeterModel")
    void handleMeterModelChange(ChangeEvent event) {
        setIsStsMeter();
        if (meterData != null && meterData.getMeterModelId() != null) {
            meterModelChanged = !(lstbxMeterModel.getValue(lstbxMeterModel.getSelectedIndex()).equals(meterData.getMeterModelId()));
        } else {
            meterModelChanged = true;
        }

        if (meterModelChanged) {
            String hasChannels = (String)lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex()).getExtraInfoMap().get("hasChannels");
            newMeterModelHasChannels = (hasChannels != null) ? Boolean.parseBoolean(hasChannels) : false;
        }
        logger.info("meterModelChanged=" + meterModelChanged + "  newMeterModelHasChannels=" + newMeterModelHasChannels);

        boolean needsBreakerId = meterModelNeedsBreakerId();
        boolean needsEncryptionKey = meterModelNeedsEncryptionKey();
        boolean needsPowerLimit = meterModelNeedsPowerLimit();
        boolean uriIsPresent = meterModelUriIsPresent();
        logger.info("handleMeterModelChange(): needsBreakerId=" + needsBreakerId + ", needsEncryptionKey=" + needsEncryptionKey + ", needsPowerLimit=" + needsPowerLimit + ", uriIsPresent=" + uriIsPresent);
        breakerIdPanel.setVisible(needsBreakerId);
        encryptionKeyPanel.setVisible(needsEncryptionKey);
        uriPanel.setVisible(uriIsPresent);
        if (meterData == null || meterData.getId() == null) {
            // not keeping track of previous meter model for new meters, so just clear fields
            // (not clear if meterData is ever null so checking it anyway)
            if (!needsBreakerId) {
                txtbxBreakerId.setText("");
            }
            if(!uriIsPresent) {
                txtbxMeterUriAddress.setText("");
                txtbxMeterUriPort.setText("");
                txtbxMeterUriProtocol.setText("");
                txtbxMeterUriParams.setText("");
            }
            if (!needsEncryptionKey) {
                txtbxEncryptionKey.setText("");
            }
            if (!needsPowerLimit) {
                updatePowerLimitUI();
            }
        }
        powerLimitContainer.setVisible(needsPowerLimit);
        generatePowerLimitFormRow.setVisible(clientFactory.getUser().hasPermission("mm_gen_power_limit") && clientFactory.getUser().hasPermission("mm_gen_power_limit") && needsPowerLimit && isStsMeter && !(powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()).trim().equals("")));
        final boolean removeBreakerId = !needsBreakerId && (meterData.getBreakerId() != null || !ValidateUtil.isNullOrBlank(txtbxBreakerId.getText()));
        final boolean removeEncryptionKey = !needsEncryptionKey && (meterData.getEncKey() != null || !ValidateUtil.isNullOrBlank(txtbxEncryptionKey.getText()));
        final boolean removePowerLimit = !needsPowerLimit && (meterData.getPowerLimit() != null || !(powerLimitListBox.getSelectedIndex() == 0));
        final boolean removeUri = !uriIsPresent && (meterData.getMeterUriAddress() != null || meterData.getMeterUriPort() != null || meterData.getMeterUriProtocol() != null || meterData.getMeterUriParams() != null ||
                !ValidateUtil.isNullOrBlank(txtbxMeterUriAddress.getText()) || !ValidateUtil.isNullOrBlank(txtbxMeterUriPort.getText()) || !ValidateUtil.isNullOrBlank(txtbxMeterUriProtocol.getText()) || !ValidateUtil.isNullOrBlank(txtbxMeterUriParams.getText()));
        logger.info("handleMeterModelChange(): removeBreakerId=" + removeBreakerId + ", removeEncryptionKey=" + removeEncryptionKey + ", removePowerLimit=" + removePowerLimit + ", removeUri=" + removeUri);
        if ((meterData != null && meterData.getId() != null) && (removeBreakerId || removeEncryptionKey || removePowerLimit || removeUri)) {
            StringBuilder fields = new StringBuilder("[");
            if (removeBreakerId) {
                fields.append(ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.breakerid")).append(",");
            }
            if(removeUri) {
                fields.append(ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.uri.fields.list")).append(",");
            }
            if (removePowerLimit) {
                fields.append(ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.powerlimit")).append(",");
            }
            if (removeEncryptionKey) {
                fields.append(ResourcesFactoryUtil.getInstance().getMessages().getMessage("meter.encryptionkey"));
            }
            if (fields.charAt(fields.length() - 1) == ',') {
                fields.deleteCharAt(fields.length() - 1);
            }
            fields.append("]");

            Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("meter.metermodelchange.remove_fields.question", new String[]{fields.toString()})),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (removeBreakerId) {
                                breakerIdPanel.setVisible(false);
                                meterData.setBreakerId(null);
                                txtbxBreakerId.setText("");
                            }
                            if(removeUri) {
                                uriPanel.setVisible(false);
                                meterData.setMeterUriAddress(null);
                                meterData.setMeterUriPort(null);
                                meterData.setMeterUriProtocol(null);
                                meterData.setMeterUriParams(null);
                                txtbxMeterUriAddress.setText("");
                                txtbxMeterUriPort.setText("");
                                txtbxMeterUriProtocol.setText("");
                                txtbxMeterUriParams.setText("");
                            }
                            if (removeEncryptionKey) {
                                encryptionKeyPanel.setVisible(false);
                                meterData.setEncKey(null);
                                txtbxEncryptionKey.setText("");
                            }
                            if (removePowerLimit) {
                                updatePowerLimitUI();
                            }
                        } else {
                            lstbxMeterModel.selectItemByValue(String.valueOf(meterData.getMeterModelId()));
                            checkBreakerIdEncryptionKeyRequired();
                            setIsStsMeter();
                            checkPowerLimitRequired();
                            return;
                        }
                    }
                },
                lstbxMeterModel.getAbsoluteLeft() + lstbxMeterModel.getOffsetWidth(),
                lstbxMeterModel.getAbsoluteTop()
            );
        }
        updateMeterPhase();
    }

    private void updateMeterPhase() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                clientFactory.getMeterModelRpc().getMeterModelById(Long.valueOf(lstbxMeterModel.getSelectedValues().get(0)),
                        new ClientCallback<MeterModelData>() {
                            @Override
                            public void onSuccess(MeterModelData meterModelData) {
                                Long meterPhaseId = meterModelData.getMeterPhaseId();
                                if (meterPhaseId == null) {
                                    meterPhasePanel.setVisible(false);
                                } else {
                                    txtbxMeterPhase.setText(MeterPhaseE.fromId(meterPhaseId).getName());
                                    meterPhasePanel.setVisible(true);
                                }
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @SuppressWarnings("deprecation")
    @UiHandler("lstbxAlgCode")
    void handleAlgCodeChange(ChangeEvent event) {

        if (isStsMeter) {
            String algorithmCode = lstbxAlgCode.getItem(lstbxAlgCode.getSelectedIndex()).getExtraInfo();
            if (algorithmCode.equals("07")) {
                chckbxThreeTokensFormRow.setVisible(true);
            } else {
                chckbxThreeTokensFormRow.setVisible(false);
            }

            if (meterData != null && meterData.getStsMeter() != null && meterData.getStsMeter().getStsAlgorithmCode() != null) {
                chckbxThreeTokens.setValue(meterData.getStsMeter().isThreeTokens());
            } else {
                chckbxThreeTokens.setValue(false);
            }
        }
    }

    @UiHandler("txtbxCurrTI")
    void handleCurrTIChange(ChangeEvent event) {
        if (txtbxCurrTI.getText().trim().length() == 1) {
            txtbxCurrTI.setText("0" + txtbxCurrTI.getText().trim());
        }
    }

    private void addHandlers() {
        this.meterDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.TRUE;
            }
        });

        this.meterDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.FALSE;
            }
        });

        this.txtbxCurrTI.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleShowKeyChangeQuery();
            }
        });

        this.lstbxSgKrn.addChangeHandler(new ChangeHandler() {

            @Override
            public void onChange(ChangeEvent event) {
                if (containsMeterComponent instanceof DeviceStoreMeters) {
                    return;
                }

                showStsBaseDate();

                handleShowKeyChangeQuery();
                if (meterData.getId() != null && transactionCount <= 3) {
                    clientFactory.getMeterRpc().getTransactionCount(meterData.getId(), new ClientCallback<Integer>() {
                        @Override
                        public void onSuccess(Integer result) {
                            transactionCount = result;
                            if (transactionCount > 3) {
                                showTransactionWarning();
                            }
                        }
                    });
                } else if (transactionCount > 3
                    && !(lstbxSgKrn.getSelectedValues().get(0).equals(String.valueOf(meterData.getStsMeter().getStsCurrSupplyGroupId())))) {
                    showTransactionWarning();
                } else {
                    currSGCElement.clearErrorMsg();
                }

            }
        });

        clientFactory.getEventBus().addHandler(SupplyGroupAddedEvent.TYPE, new SupplyGroupAddedEventHandler() {

            @Override
            public void processSupplyGroupAddedEvent(SupplyGroupAddedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution sessionCheckResolution) {
                        populateSgKrnCodeListBox();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });


        clientFactory.getEventBus().addHandler(EndDeviceStoreUpdatedEvent.TYPE, new EndDeviceStoreUpdatedEventHandler() {

            @Override
            public void processEndDeviceStoreUpdatedEvent(EndDeviceStoreUpdatedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution sessionCheckResolution) {
                        populateStoresListBox();
                    }
                };
               clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        //Note - if the meterModel itself is updated - UsagePointWorkspaceView will catch the notification & do metercomponent.populateMeterModelListBox()

    }

    private void showStsBaseDate() {
        LookupListItem baseDateLstItm = lstbxSgKrn.getItem(lstbxSgKrn.getListBox().getSelectedIndex());
        if (baseDateLstItm != null && baseDateLstItm.getExtraInfoMap() != null) {
            String selectedSgcBaseDate = (String) baseDateLstItm.getExtraInfoMap().get("baseDate");
            if (selectedSgcBaseDate != null) {
                baseDateLabel.setText(selectedSgcBaseDate);
            } else {
                baseDateLabel.setText(null);
            }
        } else {
            baseDateLabel.setText(null);
        }
    }

    protected void showTransactionWarning() {
        currSGCElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.warning.sg.transactions"));
    }

    protected void handleShowKeyChangeQuery() {
        if (meterData != null && meterData.getStsMeter() != null) {
            try {
                if (txtbxCurrTI.getText().equals(meterData.getStsMeter().getStsCurrTariffIndex())
                    && lstbxSgKrn.getSelectedValues().get(0).equals(String.valueOf(meterData.getStsMeter().getStsCurrSupplyGroupId()))) {
                    generateKeyChangeFormRow.setVisible(false);
                } else {
                    generateKeyChangeFormRow.setVisible(true);
                }
            } catch (NullPointerException npe) {
                generateKeyChangeFormRow.setVisible(false);
            }
        } else {
            generateKeyChangeFormRow.setVisible(false);
        }
    }

    public MeterData getMeterData() {
        return meterData;
    }

    public void setMeterData(MeterData theMeterData) {
        if (theMeterData == null) {
            this.meterData = new MeterData();
        } else {
            this.meterData = theMeterData;
        }
        actionPermissions(true);
        mapDataToForm();
    }

    public ClientFactory getClientFactory() {
        return clientFactory;
    }

    public void setClientFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
    }

    public void mapDataToForm() {
        clearErrorMessages();
        lblMeterAssigned.setVisible(false);
        populateTtCodeListBox();
        populateAlgCodeListBox();
        populateSgKrnCodeListBox();
        populateStoresListBox();
        populateMeterModelListBox();
        if (containsMeterComponent instanceof UsagePointWorkspaceView) {
            replaceLinkElement.setVisible(true);
            if (containsMeterComponent.getUsagePoint() != null && containsMeterComponent.getUsagePoint().getId() != null && containsMeterComponent.getUsagePoint().getMeterId() == null) {
                //adding meter to existing Usage POint
                removeLinkElement.setVisible(false);
                lblReplace.setText(MessagesUtil.getInstance().getMessage("meter.assign.from.store"));
                replaceLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("meter.assign.from.store.help"));
                selectStorePanel.setVisible(true);
            } else if (containsMeterComponent.getUsagePoint() != null && containsMeterComponent.getUsagePoint().getId() != null) {
                selectStorePanel.setVisible(false);
                lblReplace.setText(MessagesUtil.getInstance().getMessage("meter.replace"));
                replaceLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("meter.replace.help"));
                lblMeterAssigned.setText(MessagesUtil.getInstance().getMessage("meter.assigned.to.usagepoint") + " " + containsMeterComponent.getUsagePoint().getName());
                if (containsMeterComponent.getUsagePoint().getInstallationDate() != null) {
                    lblMeterAssigned.setVisible(true);
                }
                if (containsMeterComponent.getUsagePoint().getUpMeterInstall() != null) {
                    Date installdate = containsMeterComponent.getUsagePoint().getUpMeterInstall().getInstallDate();
                    lblDateMeterInstalled.setText(MessagesUtil.getInstance().getMessage("meter.installed.at", new String[]{FormatUtil.getInstance().formatDate(installdate)
                        , FormatUtil.getInstance().formatTime(installdate)}));
                }
                if (meterData.getId() != null) {
                    removeLinkElement.setVisible(true);
                } else {
                    removeLinkElement.setVisible(false);
                }
            } else {
                removeLinkElement.setVisible(false);
                if ((containsMeterComponent.getUsagePoint() == null || containsMeterComponent.getUsagePoint().getId() == null) && meterData.getId() != null) {
                    replaceLinkElement.setVisible(false);
                } else {
                    lblReplace.setText(MessagesUtil.getInstance().getMessage("meter.assign.from.store"));
                    replaceLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("meter.assign.from.store.help"));
                    selectStorePanel.setVisible(true);
                }
            }
            lblDateMeterInstalled.setVisible(containsMeterComponent.getUsagePoint() != null && containsMeterComponent.getUsagePoint().getUpMeterInstall() != null
                && containsMeterComponent.getUsagePoint().getInstallationDate() != null);    //installationDate is set to null when remove meter. Only want this message when successfully save usage point as well
        } else {
            selectStorePanel.setVisible(false);
        }

        if (meterData.getId() != null) {
            if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
                txtbxMrid.setText(meterData.getMrid());
                chckbxMridExternal.setValue(meterData.isMridExternal());
            }
            storeMrid = meterData.getMrid();
            storeMridExternal = meterData.isMridExternal();

            txtbxMeterNumber.setEnabled(false);

            txtbxMeterNumber.setText(meterData.getMeterNum());
            txtbxSerialNumber.setText(meterData.getSerialNum());
            setEndDeviceStoreId(meterData.getEndDeviceStoreId(), meterData.getAccessGroupId());

            if (meterData.getStsMeter() != null) {
                if (meterData.getStsMeter().getStsCurrTariffIndex() == null) {
                    txtbxCurrTI.setText("01");
                } else {
                    txtbxCurrTI.setText(meterData.getStsMeter().getStsCurrTariffIndex());
                }
                if (meterData.getStsMeter().getStsAlgorithmCode().equals("07")) {
                    chckbxThreeTokensFormRow.setVisible(true);
                    chckbxThreeTokens.setValue(meterData.getStsMeter().isThreeTokens());
                } else {
                    chckbxThreeTokensFormRow.setVisible(false);
                }

                if (clientFactory.isEnableCentianSTS() && meterData.displayCentianData()) {
                    mapCentianDataToForm();
                } else {
                    centianContainer.removeFromParent();
                }
            }
            if (userCustomFieldsComponentVisible) {
                mapUserCustomFields(meterData);
            }
            headerLabel.setText(MessagesUtil.getInstance().getMessage("meter.title") + ": " + meterData.getMeterNum());
            showHidePendingKeyChangeMessage();
            if (chckbxMridExternal.getValue()) {
                txtbxMrid.setEnabled(true);
            }
        } else {
            //if any change here, also check clearMeterComponent()
            clearMeterComponent(true);
        }
        updatePowerLimits();
    }

    private void mapCentianDataToForm() {
        centianContainer.setVisible(true);
        lblCentianInfoDate.setText(MessagesUtil.getInstance().getMessage("meter.centian.tamper.updated") + " "
            + FormatUtil.getInstance().formatDate(meterData.getStsMeter().getTamperUpdate()) + " "
            + FormatUtil.getInstance().formatTime(meterData.getStsMeter().getTamperUpdate()));
        lblCentianKwhCreditRem.setText(MessagesUtil.getInstance().getMessage("meter.centian.kwh.credit.remaining") + " " + meterData.getStsMeter().getKwhCreditRemaining());
        lblCentianCurrCredityRem.setText(MessagesUtil.getInstance().getMessage("meter.centian.currency.credit.remaining") + " " + meterData.getStsMeter().getCurrCreditRemaining());
        lblCentianNumDisc.setText(MessagesUtil.getInstance().getMessage("meter.centian.number.disconnections") + " " + meterData.getStsMeter().getNumberDisconnections());
        StringBuilder tamperStates = new StringBuilder();
        if (meterData.getStsMeter().getTamperDetect() != null) {
            if (meterData.getCentianTamperStates().isOverPower()) {
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.OVER_POWER));
            }
            if (meterData.getCentianTamperStates().isOverVoltage()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.OVER_VOLTAGE));
            }
            if (meterData.getCentianTamperStates().isLowVoltage()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.LOW_VOLTAGE));
            }
            if (meterData.getCentianTamperStates().isOverFrequency()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.OVER_FREQUENCY));
            }
            if (meterData.getCentianTamperStates().isLowFrequency()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.LOW_FREQUENCY));
            }
            if (meterData.getCentianTamperStates().isReverseEnergy()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.REVERSE_ENERGY));
            }
            if (meterData.getCentianTamperStates().isOpenCover()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.OPEN_COVER));
            }
            if (meterData.getCentianTamperStates().isMagnetTamper()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.MAGNET_TAMPER));
            }
            if (meterData.getCentianTamperStates().isBypassEarthTamper()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.BYPASS_EARTH));
            }
            if (meterData.getCentianTamperStates().isSequenceError()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.SEQUENCE_ERROR));
            }
            if (meterData.getCentianTamperStates().isOverTemperature()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.OVER_TEMPERATURE));
            }
            if (meterData.getCentianTamperStates().isLowTemperature()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.LOW_TEMPERATURE));
            }
            if (meterData.getCentianTamperStates().isPhaseUnbalance()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.PHASE_UNBALANCE));
            }
            if (meterData.getCentianTamperStates().isPhaseVoltageLoss()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.PHASE_VOLTAGE_LOSS));
            }
            if (meterData.getCentianTamperStates().isTariffConfigError()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.TARIFF_CONFIG_ERROR));
            }
            if (meterData.getCentianTamperStates().isMetrologyFail()) {
                if (tamperStates.length() > 0) {
                    tamperStates.append(", ");
                }
                tamperStates.append(MessagesUtil.getInstance().getMessage(CentianTamperStates.METROLOGY_FAIL));
            }
            String labelstring = tamperStates.toString();

            lblCentianTamperStates.setText(labelstring);
        } else {
            lblCentianTamperStates.removeFromParent();
            lblCentianTamperDetected.setText(MessagesUtil.getInstance().getMessage("meter.centian.tamper.none"));
        }
    }

    public void clearMeterComponent(boolean isMapData) {
        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
            if (txtbxMrid.getText().equals(newMrid)) {
                getNewMrid();
            }
            if (meterData.getMrid() == null) {
                txtbxMrid.setText(newMrid);
                chckbxMridExternal.setValue(false);
                storeMrid = newMrid;
                storeMridExternal = false;
            } else {
                txtbxMrid.setText(meterData.getMrid());
                chckbxMridExternal.setValue(meterData.isMridExternal());
                storeMrid = meterData.getMrid();
                storeMridExternal = meterData.isMridExternal();
            }
        }

        if (!isMapData) {
            lstbxAlgCode.setSelectedIndex(0);
            lstbxTtCode.setSelectedIndex(0);
            lstbxSgKrn.setSelectedIndex(0);
            lstbxSelectStore.setSelectedIndex(0);
        }
        txtbxMeterNumber.setEnabled(true);
        txtbxMeterNumber.setText("");
        txtbxSerialNumber.setText("");
        txtbxBreakerId.setText("");
        breakerIdPanel.setVisible(false);
        baseDateLabel.setText(null);
        txtbxCurrTI.setText("01");
        txtbxCurrTI.setEnabled(true);
        headerLabel.setText(MessagesUtil.getInstance().getMessage("meter.add"));
        if (chckbxMridExternal.getValue()) {
            txtbxMrid.setEnabled(true);
        }
        lstbxMeterModel.setSelectedIndex(0);
        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearCustomFields();
        }
    }

    @SuppressWarnings("deprecation")
    public void mapFormToData(MeterData meterData) {
        LookupListItem item = lstbxMeterModel.getItem(lstbxMeterModel.getSelectedIndex());
        Long metermodelid = Long.valueOf(item.getValue());
        StsMeter stsMeter = null;
        if (isStsMeter && (stsMeter = meterData.getStsMeter()) == null) {
            stsMeter = new StsMeter();
            meterData.setStsMeter(stsMeter);
        }
        meterData.setMeterModelId(metermodelid);
        meterData.setMeterTypeId(Long.valueOf(item.getExtraInfo()));

        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
            meterData.setMrid(txtbxMrid.getText());
            meterData.setMridExternal(chckbxMridExternal.getValue());
        } else {
            meterData.setMrid(storeMrid);
            meterData.setMridExternal(storeMridExternal);
        }

        if (txtbxMeterNumber.getText().isEmpty()) {
            meterData.setMeterNum(null);
        } else {
            meterData.setMeterNum(txtbxMeterNumber.getText());
        }

        if (txtbxSerialNumber.getText().isEmpty()) {
            meterData.setSerialNum(null);
        } else {
            meterData.setSerialNum(txtbxSerialNumber.getText());
        }

        if (breakerIdPanel.isVisible() && !txtbxBreakerId.getText().isEmpty()) {
            meterData.setBreakerId(txtbxBreakerId.getText());
        } else {
            meterData.setBreakerId(null);
        }
        if(uriPanel.isVisible()) {
            if(!txtbxMeterUriAddress.getText().isEmpty()) {
                meterData.setMeterUriAddress(txtbxMeterUriAddress.getText());
            } else {
                meterData.setMeterUriAddress(null);
            }
            if(txtbxMeterUriPort.getValue() != null) {
                meterData.setMeterUriPort(txtbxMeterUriPort.getValue());
            } else {
                meterData.setMeterUriPort(null);
            }
            if(!txtbxMeterUriProtocol.getText().isEmpty()) {
                meterData.setMeterUriProtocol(txtbxMeterUriProtocol.getText());
            } else {
                meterData.setMeterUriProtocol(null);
            }
            if(!txtbxMeterUriParams.getText().isEmpty()) {
                meterData.setMeterUriParams(txtbxMeterUriParams.getText());
            } else {
                meterData.setMeterUriParams(null);
            }
        } else {
            meterData.setMeterUriAddress(null);
            meterData.setMeterUriPort(null);
            meterData.setMeterUriProtocol(null);
            meterData.setMeterUriParams(null);
        }

        if (encryptionKeyPanel.isVisible() && !txtbxEncryptionKey.getText().isEmpty()) {
            meterData.setEncKey(txtbxEncryptionKey.getText());
        } else {
            meterData.setEncKey(null);
        }

        if (powerLimitContainer.isVisible()) {
            int powerLimitIndex = powerLimitListBox.getSelectedIndex();
            String powerLimitValue = powerLimitListBox.getItem(powerLimitIndex).getValue();
            if (powerLimitValue != null && !powerLimitValue.trim().isEmpty()) {
                if (generatePowerLimitTokenListBox.getSelectedIndex() == 0) {
                    BigDecimal powerLimit = new BigDecimal(powerLimitValue);
                    if (powerLimit.compareTo(BigDecimal.valueOf(-1)) != 0) {
                        meterData.setPowerLimit(powerLimit);
                    } else {
                        meterData.setPowerLimit(null);
                    }
                    meterData.setPowerLimitLabel(powerLimitListBox.getItem(powerLimitIndex).getDisplayString());
                    if (isStsMeter) {
                        stsMeter.setPowerLimit(powerLimit);
                    }
                } else {
                    newPowerLimitIndex = powerLimitIndex;
                    int powerLimitSavedIndex = 0;
                    for (int i = 0; i < powerLimitLookupListItems.size(); i++) {
                        if (powerLimitLookupListItems.get(i).getValue()
                            .equals(String.valueOf(meterData.getPowerLimit()))) {
                            powerLimitSavedIndex = i;
                            break;
                        }
                    }
                    powerLimitListBox.setSelectedIndex(powerLimitSavedIndex);
                }
            }
        } else {
            meterData.setPowerLimit(null);
            meterData.setPowerLimitLabel(null);
            if (isStsMeter) {
                stsMeter.setPowerLimit(null);
            }
        }

        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.mapFormToData(meterData);
        }

        if (isStsMeter) {
            if (lstbxAlgCode.getSelectedIndex() == 0) {
                stsMeter.setStsAlgorithmCodeId(null);
            } else {
                LookupListItem lli = lstbxAlgCode.getItem(lstbxAlgCode.getListBox().getSelectedIndex());
                stsMeter.setStsAlgorithmCodeId(Long.valueOf(lli.getValue()));
                stsMeter.setStsAlgorithmCode(lli.getExtraInfo());
                lstbxAlgCode.selectItemByValue(stsMeter.getStsAlgorithmCodeId().toString());
            }

            if (lstbxTtCode.getSelectedIndex() == 0) {
                stsMeter.setStsTokenTechId(null);
            } else {
                LookupListItem lli = lstbxTtCode.getItem(lstbxTtCode.getListBox().getSelectedIndex());
                stsMeter.setStsTokenTechId(Long.valueOf(lli.getValue()));
                stsMeter.setStsTokenTechCode(lli.getExtraInfo());
                lstbxTtCode.selectItemByValue(stsMeter.getStsTokenTechId().toString());
            }

            stsMeter.setThreeTokens(chckbxThreeTokens.getValue());

            String selecteditem = lstbxGenerateKeyChange.getSelectedValues().get(0);
            if (selecteditem.equals("none")) {
                meterData.setGenKeychangeTokenChoiceNone(true);
            }
            boolean isKeyChangeListBoxVisible = generateKeyChangeFormRow.isVisible();
            if (isKeyChangeListBoxVisible && !selecteditem.equals("none") && meterData != null && stsMeter != null) {

                String tich = txtbxCurrTI.getText().trim();
                if (tich.length() == 1) {
                    tich = ("0" + tich);
                }
                stsMeter.setStsNewTariffIndex(tich);

                if (lstbxSgKrn.getSelectedIndex() > 0) {
                    String extraInfo = lstbxSgKrn.getItem(lstbxSgKrn.getListBox().getSelectedIndex()).getExtraInfo();
                    if (extraInfo != null && (!extraInfo.equals("SGC:" + stsMeter.getStsCurrSupplyGroupCode() + ":KRN:"
                            + stsMeter.getStsCurrKeyRevisionNum()) || !tich.equals(stsMeter.getStsCurrTariffIndex()))) {
                        stsMeter.setStsNewSupplyGroupId(Long.valueOf(lstbxSgKrn.getSelectedValues().get(0)));
                        String[] lliA = extraInfo.split(":");
                        for (int i = 0; i < lliA.length; i++) {
                            if (lliA[i].equals("SGC")) {
                                stsMeter.setStsNewSupplyGroupCode(lliA[i + 1]);
                            } else if (lliA[i].equals("KRN")) {
                                stsMeter.setStsNewKeyRevisionNum(Integer.parseInt(lliA[i + 1]));
                            }
                        }
                        lstbxSgKrn.selectItemByValue(stsMeter.getStsNewSupplyGroupId().toString());
                    }
                    showStsBaseDate();
                }

                if (selecteditem.equals("flag")) {
                    stsMeter.setStsForceKeychangeAfter(new Date(0));
                    stsMeter.setUserForcedKeychange(clientFactory.getUser().getUserName());
                }

            } else {
                if (isKeyChangeListBoxVisible && selecteditem.equals("none") && meterData != null
                        && stsMeter != null) {
                    stsMeter.setStsNewSupplyGroupId(null);
                    stsMeter.setStsNewSupplyGroupCode(null);
                    stsMeter.setStsNewKeyRevisionNum(null);
                    stsMeter.setStsNewTariffIndex(null);
                    stsMeter.setStsForceKeychangeAfter(null);
                }
                if (lstbxSgKrn.getSelectedIndex() == 0) {
                    stsMeter.setStsCurrSupplyGroupId(null);
                    stsMeter.setStsCurrSupplyGroupCode(null);
                    stsMeter.setStsCurrKeyRevisionNum(null);
                } else {
                    stsMeter.setStsCurrSupplyGroupId(Long.valueOf(lstbxSgKrn.getSelectedValues().get(0)));
                    LookupListItem lli = lstbxSgKrn.getItem(lstbxSgKrn.getListBox().getSelectedIndex());
                    if (lli.getExtraInfo() != null) {
                        String[] lliA = lli.getExtraInfo().split(":");
                        for (int i = 0; i < lliA.length; i++) {
                            if (lliA[i].equals("SGC")) {
                                stsMeter.setStsCurrSupplyGroupCode(lliA[i + 1]);
                            } else if (lliA[i].equals("KRN")) {
                                stsMeter.setStsCurrKeyRevisionNum(Integer.parseInt(lliA[i + 1]));
                            }
                        }
                    }
                    lstbxSgKrn.selectItemByValue(stsMeter.getStsCurrSupplyGroupId().toString());
                    showStsBaseDate();
                }

                if (txtbxCurrTI.getText().isEmpty()) {
                    if (txtbxCurrTI.isEnabled()) {
                        stsMeter.setStsCurrTariffIndex(null);
                    }
                } else {
                    String tich = txtbxCurrTI.getText().trim();
                    if (tich.length() == 1) {
                        tich = ("0" + tich);
                    }
                    stsMeter.setStsCurrTariffIndex(tich);
                }
            }
        }

        if (selectStorePanel.isVisible() && (containsMeterComponent instanceof UsagePointWorkspaceView)) {
            LookupListItem lli = lstbxSelectStore.getItem(lstbxSelectStore.getListBox().getSelectedIndex());
            meterData.setEndDeviceStoreId(Long.valueOf(lli.getValue()));
            if (lli.getExtraInfoMap().get("accessGroupId") != null) {
                meterData.setAccessGroupId(Long.valueOf(lli.getExtraInfoMap().get("accessGroupId").toString()));
            } else {
                meterData.setAccessGroupId(null);
            }
        } else {
            meterData.setEndDeviceStoreId(endDeviceStoreId);  //DeviceStoreMeters
            meterData.setAccessGroupId(accessGroupId);
        }

        meterData.setRecordStatus(RecordStatus.ACT);
    }

    private void showHidePendingKeyChangeMessage() {
        pendingkeychange.setVisible(false);
        if (meterData != null && meterData.getStsMeter() != null) {
            StsMeter themeter = meterData.getStsMeter();
            if (themeter.getStsForceKeychangeAfter() != null) {
                pendingkeychange.setVisible(true);
                pendingkeychange.setText(MessagesUtil.getInstance().getMessage("meter.pending.keychange")
                        + " SGC: " + (themeter.getStsNewSupplyGroupCode()==null
                        ?themeter.getStsCurrSupplyGroupCode()
                        :themeter.getStsNewSupplyGroupCode())
                        + " - KRN: " + (themeter.getStsNewKeyRevisionNum()==null
                        ?themeter.getStsCurrKeyRevisionNum()
                        :themeter.getStsNewKeyRevisionNum())
                        + " - TI: " + (themeter.getStsNewTariffIndex()==null
                        ?themeter.getStsCurrTariffIndex():themeter.getStsNewTariffIndex()));
                // Displaying the current sts_meter value when the new sts_meter value is null to accommodate externally
                // generated and imported key change tokens (planio 17735) where the new values are null but a keychange
                // is due
            } else {
                pendingkeychange.setVisible(false);
            }
        }
    }

    @SuppressWarnings("deprecation")
    private boolean validate() {
        boolean validated = true;

        Meter meter = new Meter();
        if (!txtbxMeterNumber.getText().isEmpty()) {
            meter.setMeterNum(txtbxMeterNumber.getText());
        }
        if (!txtbxSerialNumber.getText().isEmpty()) {
            meter.setSerialNum(txtbxSerialNumber.getText());
        }
        if (!txtbxBreakerId.getText().isEmpty()) {
            meter.setBreakerId(txtbxBreakerId.getText());
        }
        if (!txtbxEncryptionKey.getText().isEmpty()) {
            meter.setEncKey(txtbxEncryptionKey.getText());
        }
        if (!txtbxMeterUriAddress.getText().isEmpty()) {
            meter.setMeterUriAddress(txtbxMeterUriAddress.getText());
        }
        if (txtbxMeterUriPort.getValue() != null) {
            meter.setMeterUriPort(txtbxMeterUriPort.getValue());
        }
        if (!txtbxMeterUriProtocol.getText().isEmpty()) {
            meter.setMeterUriProtocol(txtbxMeterUriProtocol.getText());
        }
        if (!txtbxMeterUriParams.getText().isEmpty()) {
            meter.setMeterUriParams(txtbxMeterUriParams.getText());
        }

        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
            if (!txtbxMrid.getText().isEmpty()) {
                meter.setMrid(txtbxMrid.getText());
            }
        } else {
            meter.setMrid(storeMrid);
        }

        if (meterSerialNumElement.isVisible()
            && (!ClientValidatorUtil.getInstance().validateField(meter, "serialNum", meterSerialNumElement)
            || !MeterMngClientUtils.validateUserInterfaceComponent(meterSerialNumElement,
            userInterfaceFields.get(UserInterfaceFormFields.SERIAL_NUMBER), txtbxSerialNumber))) {
            validated = false;
        }

        if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_UNIQUEID_EDIT)) {
            if (!ClientValidatorUtil.getInstance().validateField(meter, "mrid", mridElement)) {
                validated = false;
            }
            if (!mridComponent.validate()) {
                validated = false;
            }
        }

        if (meterModelNeedsBreakerId()) {
            if (meter.getBreakerId() == null || meter.getBreakerId().isEmpty()) {
                breakerIdElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.breakerid.error"));
                validated = false;
            } else if (!ClientValidatorUtil.getInstance().validateField(meter, "breakerId", breakerIdElement)) {
                validated = false;
            }
        }

        if (meterModelNeedsEncryptionKey()) {
            if (meter.getEncKey() == null || meter.getEncKey().isEmpty()) {
                encryptionKeyElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.encryptionkey.error"));
                validated = false;
            } else if (!ClientValidatorUtil.getInstance().validateField(meter, "encKey", encryptionKeyElement)) {
                validated = false;
            }
        }

        if(meterModelUriIsPresent()) {
            if (txtbxMeterUriPort.getValue() != null) {
                if(!MeterMngCommonUtil.isValidPort(String.valueOf(txtbxMeterUriPort.getValue()))) {
                    meterUriPortElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.uri.port.error"));
                    validated = false;
                } else if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriPort", meterUriPortElement)) {
                    validated = false;
                }
            }
            if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriAddress", meterUriAddressElement)) {
                validated = false;
            }
            if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriProtocol", meterUriProtocolElement)) {
                validated = false;
            }
            if (!ClientValidatorUtil.getInstance().validateField(meter, "meterUriParams", meterUriParamsElement)) {
                validated = false;
            }
        }

        boolean isProprietaryMeterAlgCode = false;
        if (isStsMeter) {
            StsMeter stsmeter = new StsMeter();
            stsmeter.setStsCurrTariffIndex(txtbxCurrTI.getText());
            if (!ClientValidatorUtil.getInstance().validateField(stsmeter, "stsCurrTariffIndex", currTIElement)) {
                validated = false;
            }
            if (lstbxTtCode.getSelectedIndex() == 0) {
                tTCodeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required", new String[]{tTCodeElement.getLabelText()}));
                validated = false;
            }
            if (lstbxAlgCode.getSelectedIndex() == 0) {
                algCodeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required", new String[]{algCodeElement.getLabelText()}));
                validated = false;
            }
            if (lstbxSgKrn.getSelectedIndex() <= 0) {
                currSGCElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required",
                    new String[]{currSGCElement.getLabelText()}));
                validated = false;
            }
            if (txtbxCurrTI.getText().trim().isEmpty()) {
                currTIElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required", new String[]{currTIElement.getLabelText()}));
                validated = false;
            }

            String algorithmCode = lstbxAlgCode.getItem(lstbxAlgCode.getSelectedIndex()).getExtraInfo();
            if (!algorithmCode.equals("07")) {
                if (chckbxThreeTokens.getValue()) {
                    chckbxThreeTokensElement.setErrorMsg(MessagesUtil.getInstance().getMessage("meter.three.tokens.error"));
                }
            }
            if (algorithmCode.equals("89")) {
                isProprietaryMeterAlgCode = true;
            }
        }

        if (!ClientValidatorUtil.getInstance().validateField(meter, "meterNum", meterNumberElement)) {
            validated = false;
        } else {
            if (isStsMeter && !isProprietaryMeterAlgCode) {
                if (!MeterMngCommonUtil.stsLengthCheck(meter.getMeterNum())) {
                    validated = false;
                    meterNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.sts.length"));
                } else if (!MeterMngCommonUtil.luhnCheck(meter.getMeterNum())) {
                    validated = false;
                    meterNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meter.luhncheck.failed"));
                }
            }
        }

        if (selectStorePanel.isVisible() && lstbxSelectStore.getSelectedIndex() == 0) {
            deviceStoreElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required",
                new String[]{deviceStoreElement.getLabelText()}));
            validated = false;
        }

        if (userCustomFieldsComponentVisible) {
            if (!userCustomFieldsComponent.validateCustomFields()) {
                validated = false;
            }
        }

        logger.info("Validated fields: " + validated);
        return validated;
    }

    public boolean fieldsRequiredForActivationComplete() {
        boolean allRequiredFieldsPopulated = (!txtbxMeterNumber.getText().trim().isEmpty());
        if (isStsMeter) {


            if (allRequiredFieldsPopulated) {
                allRequiredFieldsPopulated = (!txtbxCurrTI.getText().trim().isEmpty());
            }
        }

        return allRequiredFieldsPopulated;
    }

    public void setEndDeviceStoreId(Long endDeviceStoreId, Long accessGroupId) {
        this.endDeviceStoreId = endDeviceStoreId;
        this.accessGroupId = accessGroupId;
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                meterDisclosurePanel.setWidth("100%");

            }
        }.schedule(100);
    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            meterDisclosurePanel.setOpen(isOpen);
        } else {
            meterDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    public void clearErrorMessages() {
        replaceLinkElement.clearErrorMsg();
        removeLinkElement.clearErrorMsg();
        meterNumberElement.clearErrorMsg();
        meterSerialNumElement.clearErrorMsg();
        tTCodeElement.clearErrorMsg();
        algCodeElement.clearErrorMsg();
        currSGCElement.clearErrorMsg();
        currTIElement.clearErrorMsg();
        mridElement.clearErrorMsg();
        breakerIdElement.clearErrorMsg();
        encryptionKeyElement.clearErrorMsg();
        chckbxThreeTokensElement.clearErrorMsg();
        deviceStoreElement.clearErrorMsg();

        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearErrorMessages();
        }

        if(uriPanel.isVisible()){
            meterUriPortElement.clearErrorMsg();
            meterUriAddressElement.clearErrorMsg();
            meterUriProtocolElement.clearErrorMsg();
            meterUriParamsElement.clearErrorMsg();
        }
    }

    public void initPowerLimit() {
        initPowerLimitListBox();
        updatePowerLimits();
    }

    public void updatePowerLimits() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.POWER_LIMIT_SETTINGS, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                populatePowerLimitDropDown(result);
            }
        });
    }

    private void populatePowerLimitDropDown(final AppSetting powerLimitAppSetting) {
        Integer selectedPowerLimit = meterData.getPowerLimit() == null ? null
                : meterData.getPowerLimit().intValue();
        powerLimitLookupListItems.clear();
        // this is the only way to clear the items list on powerLimitListBox. for some
        // reason, the setLookupItems method does not like it when being called for a
        // second time such as when app settings are updated, hence the clearing and
        // adding approach rather than setting it afterwards
        powerLimitListBox.setLookupItems(powerLimitLookupListItems);
        boolean generatePowerLimitVisible = clientFactory.getUser().hasPermission("mm_gen_power_limit");
        if (selectedPowerLimit == null) {
            powerLimitListBox.addItem("", "");
            generatePowerLimitVisible = false;
        }
        int indexSelected = 0;
        List<PowerLimitValue> powerLimitValues = PowerLimitValue.fromAppSetting(powerLimitAppSetting);
        PowerLimitValue limit;
        for (int i = 0; i<powerLimitValues.size(); i++) {
            limit = powerLimitValues.get(i);
            if (selectedPowerLimit != null && selectedPowerLimit.equals(limit.getValue())) {
                indexSelected = i;
            }
            powerLimitListBox.addItem(limit.getLabel(), String.valueOf(limit.getValue()));
        }
        powerLimitListBox.setItemSelected(indexSelected, true);
        generatePowerLimitFormRow.setVisible(generatePowerLimitVisible);
    }

    private void initPowerLimitListBox() {
        powerLimitListBox = new IpayListBox(false);
        clientFactory.getEventBus().addHandler(AppSettingEvent.TYPE, new AppSettingEventHandler() {
            @Override
            public void handleEvent(AppSettingEvent event) {
                if (event.getUpdatedSetting() != null
                        && event.getUpdatedSetting().getKey().equals(AppSettings.POWER_LIMIT_SETTINGS)) {
                    populatePowerLimitDropDown(event.getUpdatedSetting());
                }
            }
        });
        if (clientFactory.getUser().hasPermission("mm_gen_power_limit")) {
            powerLimitListBox.getListBox().addChangeHandler(new ChangeHandler() {
                @Override
                public void onChange(ChangeEvent event) {
                    LookupListItem selectedLookupListItem = powerLimitListBox.getItem(powerLimitListBox.getSelectedIndex());
                    if (selectedLookupListItem.getValue() != null
                        && !selectedLookupListItem.getDisplayString().equals("")
                        && !selectedLookupListItem.getValue().equals("")) {

                        setIsStsMeter();
                        generatePowerLimitFormRow.setVisible(clientFactory.getUser().hasPermission("mm_gen_power_limit") && isStsMeter && !(powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()).trim().equals("")));
                    } else {
                        generatePowerLimitTokenListBox.setItemSelected(0, true);
                        generatePowerLimitFormRow.setVisible(false);
                    }
                }
            });
        } else {
            powerLimitListBox.setEnabled(false);
        }
    }

    @Override
    public void addUserCustomFieldsComponent() {
        userCustomFieldsComponent.setVisible(true);
        userCustomFieldsComponentVisible = true;
        mapUserCustomFields(meterData);
    }

    public UserCustomFieldsComponent getUserCustomFieldsComponent() {
        return userCustomFieldsComponent;
    }

    @Override
    public void setHistoryVisibilityMap(Map<String, CustomFieldDto> result) {
    }

    public void removeUserCustomFieldsComponent() {
        logger.info("Custom AppSettings are NULL or empty!!");
        userCustomFieldsComponent.setVisible(false);
        userCustomFieldsComponentVisible = false;
    }

    private void configureCustomFields() {
        boolean hasCustomFields = false;
        if (customFields != null && !customFields.isEmpty()) {
            hasCustomFields = userCustomFieldsComponent.configureCustomFields(customFields, "meter");
        }

        if (customFields == null || customFields.isEmpty() || !hasCustomFields) {
            removeUserCustomFieldsComponent();
            return;
        }

        userCustomFieldsComponent.setVisible(true);
        userCustomFieldsComponentVisible = true;
    }

    private void mapUserCustomFields(MeterData meterData) {
        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearCustomFields();
        }
        if (meterData != null) {
            userCustomFieldsComponent.mapDataToForm(
                meterData.getCustomVarchar1(),
                meterData.getCustomVarchar2(),
                meterData.getCustomNumeric1(),
                meterData.getCustomNumeric2(),
                meterData.getCustomTimestamp1(),
                meterData.getCustomTimestamp2());
        }
    }

    public void addFieldHandlers() {
        lstbxSelectStore.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxMeterModel.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxMeterNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxSerialNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxMrid.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxBreakerId.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEncryptionKey.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxAlgCode.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxSgKrn.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxTtCode.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxCurrTI.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        chckbxMridExternal.addClickHandler(new FormDataClickHandler(hasDirtyData));
        powerLimitListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        chckbxThreeTokens.addClickHandler(new FormDataClickHandler(hasDirtyData));
    }

    public HasDirtyData getHasDirtyData() {
        return hasDirtyData;
    }

    public void updateUserInterfaceComponentSettings(Map<String, FormFields> userInterfaceFields) {
        this.userInterfaceFields = userInterfaceFields;
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
            userInterfaceFields.get(UserInterfaceFormFields.SERIAL_NUMBER), meterSerialNumElement);
    }

    public Button getSaveBtn() {
        return btnSave;
    }

    private void toggleUPWorkspaceButtons(boolean enable) {
        if (usagePointWorkspaceView != null)
            usagePointWorkspaceView.toggleSaveBtns(enable);
    }
}
