package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.dto.FilterCriteria;

@RemoteServiceRelativePath("secure/appSetting.do")
public interface AppSettingRpc extends RemoteService {

    public Integer getAppSettingCount(FilterCriteria filterCriteria, String searchText);
    
    ArrayList<AppSetting> getAppSettingsForUsagePointGroupCustomFields() throws ValidationException, ServiceException;
    
    public ArrayList<AppSetting> getAppSettingsForUPAndMeterAndCustomerCustomFields() throws ServiceException;
    
    Map<String, CustomFieldDto> getAppSettingsCustomFieldsHistoryVisibility(Map<String, CustomFieldDto> customFieldsHistoryRequiredMap) throws ServiceException;
    
    void saveAppSetting(AppSetting appSetting) throws ValidationException, ServiceException, AccessControlException;

    AppSetting getAppSettingByKey(String key) throws ValidationException, ServiceException;

    ArrayList<AppSetting> getAppSettings(FilterCriteria filterCriteria, String searchText);

    public Map<String, AppSetting> getAppSettingsByKeys(List<String> keys);
}
