package za.co.ipay.metermng.client.view.workspace.schedule.taskclass;

import java.util.ArrayList;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskData;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskUtil;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

public class MeterReadingsExportTaskPanel extends SimpleFormPanel implements TaskClassUi {
    
    private ClientFactory clientFactory;
    
    @UiField FormElement singleMeterElement;
    @UiField FormElement meterReadingTypeElement;
    @UiField FormElement timePeriodElement;
    
    @UiField(provided=true) SuggestBox singleMeterBox;
    MeterSuggestOracle singleMeterSuggest;
    MeterDto selectedSingleMeter;
    @UiField ListBox meterReadingTypeBox;
    @UiField ListBox timePeriodBox;
    
    HandlerRegistration meterReadingTypeBoxReg;
    HandlerRegistration timePeriodBoxReg;
    HandlerRegistration singleMeterBoxReg;

    private static MeterReadingsExportTaskPanelUiBinder uiBinder = GWT.create(MeterReadingsExportTaskPanelUiBinder.class);

    interface MeterReadingsExportTaskPanelUiBinder extends UiBinder<Widget, MeterReadingsExportTaskPanel> {
    }

    public MeterReadingsExportTaskPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        createMeterBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();        
    }
    
    private void createMeterBox(ClientFactory clientFactory) {
        singleMeterSuggest = new MeterSuggestOracle(clientFactory);
        singleMeterBox = new SuggestBox(singleMeterSuggest);
        singleMeterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    selectedSingleMeter = ((MeterSuggestion) event.getSelectedItem()).getMeter();
                    form.setDirtyData(true);
                }                
            }
        });
        singleMeterBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                singleMeterBox.showSuggestionList();
            }
        });
    }
    
    public void setMeterReadingTypes(ArrayList<MeterReadingType> types) {
        meterReadingTypeBox.clear();
        meterReadingTypeBox.addItem("");
        for(MeterReadingType type : types) {
            meterReadingTypeBox.addItem(
                    type.getName() + " (" + MeterMngCommonUtil.getCorrectedUnitOfMeasure(type.getUnitOfMeasure()) + ")",
                    type.getValue());
        }
    }
    
    public void setTimePeriods() {
        timePeriodBox.clear();
        timePeriodBox.addItem("");
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.day"), MeterMngStatics.PREVIOUS_DAY_VALUE);
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.week"), MeterMngStatics.PREVIOUS_WEEK_VALUE);
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.month"), MeterMngStatics.PREVIOUS_MONTH_VALUE);
    }

    @Override
    public void setTaskClassContents(String contents) {
        clearFields();        
        if (contents != null && !contents.trim().equals("")) {
            MeterReadingsExportTaskData data = MeterReadingsExportTaskUtil.getTaskClassContents(contents);
            setSingleMeter(data.getMeterNumber());
            setValue(meterReadingTypeBox, data.getMeterReadingType());
            setValue(timePeriodBox, data.getTimePeriod());
        }
    }
    
    private void setSingleMeter(String meterNumber) {
        if (meterNumber != null) {
            clientFactory.getMeterRpc().getMeterByMeterNumber(meterNumber, new ClientCallback<MeterDto>(){
                @Override
                public void onSuccess(MeterDto result) {
                    if (result != null) {
                        singleMeterBox.setText(result.getNumber());
                        selectedSingleMeter = result;
                    }                    
                }});
        }
    }
    
    private void setValue(ListBox box, String value) {
        if (value == null || value.trim().equals("")) {
            box.setSelectedIndex(0);
        } else {
            for(int i=0;i<box.getItemCount();i++) {
                if (box.getValue(i).equals(value)) {
                    box.setSelectedIndex(i);
                    return;
                }
            }
        }
    }

    @Override
    public String getTaskClassContents() {
        return MeterReadingsExportTaskUtil.getTaskClassContents(
                   new MeterReadingsExportTaskData(selectedSingleMeter.getNumber(), 
                                                   meterReadingTypeBox.getValue(meterReadingTypeBox.getSelectedIndex()), 
                                                   timePeriodBox.getValue(timePeriodBox.getSelectedIndex())));
    }

    @Override
    public boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        if (selectedSingleMeter == null) {
            valid = false;
            if (singleMeterBox.getText() != null && singleMeterBox.getText().equals("")) {            
                singleMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.singlemeter.select"));
            } else {
                singleMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.singlemeter.none"));
            }
        }
        if (meterReadingTypeBox.getSelectedIndex() < 1) {
            valid = false;
            meterReadingTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.type"));
        }
        if (timePeriodBox.getSelectedIndex() < 1) {
            valid = false;
            timePeriodElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.time"));
        }
        return valid;
    }

    @Override
    public void addFieldHandlers() {
        meterReadingTypeBoxReg = meterReadingTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        timePeriodBoxReg = timePeriodBox.addChangeHandler(new FormDataChangeHandler(form));
        singleMeterBoxReg = singleMeterBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
    }

    @Override
    public void clearFields() {
        selectedSingleMeter = null;
        singleMeterBox.setText("");
        meterReadingTypeBox.setSelectedIndex(0);
        timePeriodBox.setSelectedIndex(0);
    }

    @Override
    public void clearErrors() {
        singleMeterElement.setErrorMsg(null);
        meterReadingTypeElement.setErrorMsg(null);
        timePeriodElement.setErrorMsg(null);
    }

    @Override
    public void removeFieldHandlers() {
        meterReadingTypeBoxReg.removeHandler();
        timePeriodBoxReg.removeHandler();
        singleMeterBoxReg.removeHandler();
    }
}