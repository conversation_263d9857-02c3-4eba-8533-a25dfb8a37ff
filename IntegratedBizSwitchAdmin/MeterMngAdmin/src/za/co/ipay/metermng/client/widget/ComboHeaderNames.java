package za.co.ipay.metermng.client.widget;

import java.io.Serializable;

/**
 * ComboHeaderNames is used to wrap a main column header's name and a number of sub-header names.
 * <AUTHOR>
 */
public class ComboHeaderNames implements Serializable {

    private static final long serialVersionUID = 1L;

    private String mainHeader;
    private String[] subHeaders;
    
    public ComboHeaderNames(String mainHeaders, String... subHeader) {
        this.mainHeader = mainHeaders;
        this.subHeaders = subHeader;
    }

    public String getMainHeader() {
        return mainHeader;
    }

    public String[] getSubHeaders() {
        return subHeaders;
    }    
}
