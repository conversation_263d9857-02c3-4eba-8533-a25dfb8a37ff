package za.co.ipay.metermng.client.view.component.tariff.impl.tou;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.cellview.client.SafeHtmlHeader;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.metermng.client.event.CalendarUpdatedEvent;
import za.co.ipay.metermng.client.event.CalendarUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.tariff.ContainsPayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.component.tariff.PayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.impl.BaseTariffView;
import za.co.ipay.metermng.client.view.component.tariff.impl.block.CyclicChargePanel;
import za.co.ipay.metermng.client.view.component.tariff.impl.block.PercentChargePanel;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouTariffCalendar;
import za.co.ipay.metermng.shared.dto.pricing.TouCalendarSeasonsPeriodsSpecialDaysData;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.cyclic.CyclicChargeData;
import za.co.ipay.metermng.shared.tariff.demand.DemandChargeData;
import za.co.ipay.metermng.shared.tariff.paytype.PayTypeDiscount;
import za.co.ipay.metermng.shared.tariff.percent.PercentChargeData;
import za.co.ipay.metermng.shared.tariff.tou.TouSeasonChgData;
import za.co.ipay.metermng.shared.tariff.tou.TouThinCalcContents;
import za.co.ipay.metermng.shared.tariff.tou.TouThinCalcInitData;
import za.co.ipay.metermng.shared.tariff.tou.TouThinChargeData;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

public class TouThinSmartTariffView extends BaseTariffView implements ITariffUIClass, ContainsPayTypeDiscountPanel {

    @UiField FormElement calendarElement;
    @UiField ListBox calendarBox;

    @UiField FormElement monthlyDemandChargeElement;
    @UiField Label monthlyDemandChargeCurrencyLabel;
    @UiField BigDecimalValueBox monthlyDemandChargeBox;
    @UiField FormElement monthlyDemandTypeElement;
    @UiField ListBox monthlyDemandTypeBox;

    @UiField FormElement taxElement;
    @UiField PercentageTextBox taxBox;

    @UiField FormElement enableReadingTypesElement;
    @UiField ListBox enableReadingTypesBox;
    @UiField Button chargesButton;

    @UiField FormElement chargesElement;
    @UiField CellTable<TouSeasonChgData> chargesTable;
    private ListDataProvider<TouSeasonChgData> chargesDataProvider;
    private DecimalInputCell rateCell;

    private TouThinChargeData touThinChargeData;
    private TouThinCalcInitData touThinCalcInitData;
    private boolean noSpecialDays = true;
    private PayTypeDiscountPanel payTypeDiscountPanel;
    @UiField FormRowPanel paytypeDiscountFormRowPanel;
    @UiField FlowPanel cyclicChargesPanel;
    @UiField FlowPanel percentChargesPanel;

    private static Logger logger = Logger.getLogger(TouThinSmartTariffView.class.getName());

    private static TouTariffUiBinder uiBinder = GWT.create(TouTariffUiBinder.class);

    interface TouTariffUiBinder extends UiBinder<Widget, TouThinSmartTariffView> {
    }

    public TouThinSmartTariffView(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        createCurrencyLabels();
        initUi();
    }

    private void createCurrencyLabels() {
        monthlyDemandChargeCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        if (FormatUtil.getInstance().isRightToLeft()) {
            monthlyDemandChargeCurrencyLabel.setStyleName("btCurrency-right");
        } else {
            monthlyDemandChargeCurrencyLabel.setStyleName("btCurrency-left");
        }
    }

    private void initUi() {
        initChargesTable();
        clientFactory.getEventBus().addHandler(CalendarUpdatedEvent.TYPE, new CalendarUpdatedEventHandler() {

            @Override
            public void processCalendarUpdatedEvent(CalendarUpdatedEvent event) {
                Long calendarId = null;
                if (touThinChargeData.getSeasonCharges().size() > 0) {
                    calendarId = touThinChargeData.getSeasonCharges().get(0).getTouCalendarId();
                }
                clientFactory.getCalendarRpc().getCalendars(calendarId, new ClientCallback<ArrayList<TouCalendar>>() {

                    @Override
                    public void onSuccess(ArrayList<TouCalendar> result) {
                        touThinCalcInitData.setCalendars(result);
                        populateCalendars(false);
                    }
                });
            }
        });
        payTypeDiscountPanel = new PayTypeDiscountPanel(this);
        paytypeDiscountFormRowPanel.add(payTypeDiscountPanel);
        payTypeDiscountPanel.setVisible(false);
    }

    @UiHandler("chargesButton")
    public void onClickChargesButton(ClickEvent e) {
        clearErrors();

        //Check calendar is selected
        Long calendarId = null;
        int index = calendarBox.getSelectedIndex();
        if (index > 0) {
            calendarId = Long.valueOf(calendarBox.getValue(index));
        }
        if (calendarId == null) {
            calendarElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.calendar"));
        }

        //Check types are selected
        final ArrayList<Long> ids = new ArrayList<Long>();
        for(int i=0;i<enableReadingTypesBox.getItemCount();i++) {
            if (enableReadingTypesBox.isItemSelected(i)) {
                ids.add(Long.valueOf(enableReadingTypesBox.getValue(i)));
            }
        }
        if (ids.size() == 0) {
            enableReadingTypesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.types"));
        }

        if (calendarId != null && ids.size() > 0) {
            if (touThinChargeData != null && touThinChargeData.getTariffCalendar() != null
                    && touThinChargeData.getTariffCalendar().getTouCalendarId() != null) {
                //Tariff has an existing calendar
                if (calendarId.equals(touThinChargeData.getTariffCalendar().getTouCalendarId())) {
                    // Current calendar is the same calendar as tou's data so redisplay its row with any extra selected types
                    logger.info("Current calendar reload...");
                    loadCalendarData(calendarId, getMeterReadingTypes(ids), true);
                } else {
                    logger.info("Charges: "+touThinChargeData.getSeasonCharges().isEmpty());
                    if (touThinChargeData.getSeasonCharges().isEmpty()) {
                        loadCalendarData(calendarId, getMeterReadingTypes(ids), true);
                    } else {
                        final Long cId = calendarId;
                        logger.info("Changing from existing calendar: " + touThinChargeData.getTariffCalendar().getTouCalendarId() + " to " + calendarId);
                        Dialogs.confirm(MessagesUtil.getInstance().getMessage("tou.thin.change.calendar"),
                                        MessagesUtil.getInstance().getMessage("option.positive"),
                                        MessagesUtil.getInstance().getMessage("option.negative"),
                                        MediaResourceUtil.getInstance().getQuestionIcon(),
                                        new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    loadCalendarData(cId, getMeterReadingTypes(ids), true);
                                                }
                                            }
                                });
                    }
                }
            } else {
                loadCalendarData(calendarId, getMeterReadingTypes(ids), false);
            }
        }
    }

    private ArrayList<MeterReadingType> getMeterReadingTypes(ArrayList<Long> ids) {
        ArrayList<MeterReadingType> types = new ArrayList<MeterReadingType>();
        for(MeterReadingType type : touThinCalcInitData.getEnableReadingTypes()) {
            if (ids.contains(type.getId())) {
                types.add(type);
            }
        }
        return types;
    }

    private void loadCalendarData(Long calendarId, final ArrayList<MeterReadingType> selectedTypes, final boolean forCurrentTariff) {
        logger.info("Loading calendar data: " + calendarId);
        clientFactory.getCalendarRpc().getTouCalendarData(calendarId,
                new ClientCallback<TouCalendarSeasonsPeriodsSpecialDaysData>() {
                    @Override
                    public void onSuccess(TouCalendarSeasonsPeriodsSpecialDaysData result) {
                        noSpecialDays = result.getSpecialDays().isEmpty();
                        displayCalendarData(result, selectedTypes, forCurrentTariff);
                    }
                });
    }

    private void displayCalendarData(TouCalendarSeasonsPeriodsSpecialDaysData data, ArrayList<MeterReadingType> selectedTypes, boolean forCurrentTariff) {
        if (forCurrentTariff) {
            checkExistingTariffCalendarData(data, selectedTypes);
        } else {
            displayCalendarDataForNewTariff(data, selectedTypes);
        }
    }

    private void checkExistingTariffCalendarData(TouCalendarSeasonsPeriodsSpecialDaysData data, ArrayList<MeterReadingType> selectedTypes) {
        //Display the current charges
        chargesDataProvider.getList().clear();
        chargesDataProvider.getList().addAll(touThinChargeData.getSeasonCharges());
        logger.info("Current charges: "+chargesDataProvider.getList().size());

        //Check what rows are already there and add missing ones
        List<TouSeasonChgData> newCharges = new ArrayList<TouSeasonChgData>();
        TouSeasonChgData charge = null;
        for(MeterReadingType type : selectedTypes) {
            for(TouSeason season : data.getSeasons()) {
                for(TouPeriod period : data.getPeriods()) {
                    charge = findExistingSeasonCharge(season.getId(), period.getId(), type.getId());
                    if (charge == null) {
                        charge = new TouSeasonChgData();
                        charge.setMeterReadingTypeId(type.getId());
                        charge.setMeterReadingTypeName(type.getName());
                        charge.setMeterReadingTypeUnits(type.getUnitOfMeasure());
                        charge.setPeriodId(period.getId());
                        charge.setPeriodName(period.getName());
                        charge.setSeasonId(season.getId());
                        charge.setSeasonName(season.getName());
                        charge.setTouCalendarId(data.getTouCalendarId());
                    }
                    newCharges.add(charge);
                }
            }
        }
        chargesDataProvider.getList().clear();
        chargesDataProvider.getList().addAll(newCharges);
        logger.info("SeasonCharges count: " + newCharges.size());
    }

    private TouSeasonChgData findExistingSeasonCharge(Long season, Long period, Long type) {
        for(TouSeasonChgData chg : chargesDataProvider.getList()) {
            if (chg.getSeasonId().equals(season)
                    && chg.getPeriodId().equals(period)
                    && chg.getMeterReadingTypeId().equals(type)) {
                return chg;
            }
        }
        return null;
    }

    private void displayCalendarDataForNewTariff(TouCalendarSeasonsPeriodsSpecialDaysData data, ArrayList<MeterReadingType> selectedTypes) {
        chargesDataProvider.getList().clear();
        ArrayList<TouSeasonChgData> seasonCharges = new ArrayList<TouSeasonChgData>();
        TouSeasonChgData charge = null;
        for(MeterReadingType type : selectedTypes) {
            for(TouSeason season : data.getSeasons()) {
                for(TouPeriod period : data.getPeriods()) {
                    logger.info("Creating chg for season:"+season.getName()+" period:"+period.getName()+" type:"+type.getName());
                    charge = new TouSeasonChgData();
                    charge.setMeterReadingTypeId(type.getId());
                    charge.setMeterReadingTypeName(type.getName());
                    charge.setMeterReadingTypeUnits(type.getUnitOfMeasure());
                    charge.setPeriodId(period.getId());
                    charge.setPeriodName(period.getName());
                    charge.setSeasonId(season.getId());
                    charge.setSeasonName(season.getName());
                    charge.setTouCalendarId(data.getTouCalendarId());
                    seasonCharges.add(charge);
                }
            }
        }
        chargesDataProvider.getList().addAll(seasonCharges);
        logger.info("SeasonCharges row count: "+seasonCharges.size());
    }

    public void clearForm() {
        calendarBox.setSelectedIndex(0);
        taxBox.getBigDecimalValueBox().setValue(BigDecimal.ZERO);
        monthlyDemandChargeBox.setValue(null);
        monthlyDemandTypeBox.setSelectedIndex(0);
        for(int i=0;i<enableReadingTypesBox.getItemCount();i++) {
            enableReadingTypesBox.setItemSelected(i, false);
        }
        chargesDataProvider.getList().clear();
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(i);
            cyclicChargePanel.clearForm();
        }
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(i);
            percentChargePanel.clearForm();
        }
        if (payTypeDiscountPanel.isVisible()) {
            payTypeDiscountPanel.clear();
        }
        clearErrors();
    }

    public void clearErrors() {
        calendarElement.setErrorMsg(null);
        monthlyDemandChargeElement.setErrorMsg(null);
        monthlyDemandTypeElement.setErrorMsg(null);
        enableReadingTypesElement.setErrorMsg(null);
        chargesElement.setErrorMsg(null);
        taxElement.setErrorMsg(null);
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(0);
            cyclicChargePanel.clearErrors();
        }
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(0);
            percentChargePanel.clearErrors();
        }
        if (payTypeDiscountPanel.isVisible()) {
            payTypeDiscountPanel.clearErrors();
        }
    }

    @Override
    public void setCalcContents(String contents) {
    }

    private void setCalcContentsFields(boolean init) {
        TouThinCalcContents calcContents;
        if(init) {
            calcContents = touThinCalcInitData.getTemplate().getCalcContents();
        } else {
            calcContents = touThinChargeData.getCalcContents();
        }
        logger.info("calcContents: " + calcContents);
        DemandChargeData demandCharge = null;
        if(calcContents.getDemandCharges() != null && ! calcContents.getDemandCharges().isEmpty()) {
            demandCharge = calcContents.getDemandCharges().get(0);
        }
        if(demandCharge != null) {
            monthlyDemandChargeBox.setText(FormatUtil.getInstance().formatDecimal(demandCharge.getCharge()));
            for(int i=0;i<monthlyDemandTypeBox.getItemCount();i++) {
                if (monthlyDemandTypeBox.getValue(i).equals(demandCharge.getReadingType())) {
                    monthlyDemandTypeBox.setSelectedIndex(i);
                }
            }
        }
        logger.info("cyclicCharges: " + calcContents.getCyclicCharges());
        List<CyclicChargeData> cyclicCharges = calcContents.getCyclicCharges();
        if(cyclicCharges != null && ! cyclicCharges.isEmpty()) {
            cyclicChargesPanel.clear();
            int numCyclicChargePanels = 0;
            for (CyclicChargeData cyclicChargeData : cyclicCharges) {
                boolean required = false;
                if(init && cyclicChargeData.getName() != null) {
                    required = true;
                }
                String applyAtEventName =  "applyAtEvent" + numCyclicChargePanels++;
                CyclicChargePanel cyclicChargePanel = new CyclicChargePanel(clientFactory, required, touThinCalcInitData.getTemplate().isUseNameAsLabel(), touThinCalcInitData.getTemplate().isEnableNonAccruingMonthly(), true);
                //if more than one CyclicChargePanel, each must have its own "event" Name otherwise all the radioButtons with the same name will be mutually exclusive.
                cyclicChargePanel.getApplyAtVend().setName(applyAtEventName);
                cyclicChargePanel.getApplyAtBilling().setName(applyAtEventName);
                cyclicChargesPanel.add(cyclicChargePanel);
                cyclicChargePanel.setCyclicCharge(cyclicChargeData);
            }
        }

        List<PercentChargeData> percentCharges = calcContents.getPercentCharges();
        logger.info("cyclicCharges: " + calcContents.getCyclicCharges());
        if(percentCharges != null && ! percentCharges.isEmpty()) {
            percentChargesPanel.clear();
            for (PercentChargeData percentChargeData : percentCharges) {
                boolean required = false;
                // only make the field required if this data is from template
                if(init && percentChargeData.getName() != null) {
                    required = true;
                }
                PercentChargePanel percentChargePanel = new PercentChargePanel(clientFactory, required, touThinCalcInitData.getTemplate().isUseNameAsLabel());
                percentChargesPanel.add(percentChargePanel);
                percentChargePanel.setPercentCharge(percentChargeData);
            }
        }

        BigDecimal taxMultiplier = calcContents.getTax();
        if (taxMultiplier != null) {
            taxMultiplier = taxMultiplier.subtract(BigDecimal.ONE).multiply(BigDecimal.valueOf(100L));
        }
        taxBox.setAmount(taxMultiplier);

        //discounts
        List<PayTypeDiscount> payTypeDiscounts = calcContents.getPayTypeDiscounts();
        if (payTypeDiscounts != null) {
            payTypeDiscountPanel.setCalcContentsPayTypeDiscount(payTypeDiscounts);
            payTypeDiscountPanel.setVisible(true);
        } else {
            payTypeDiscountPanel.clear();
            payTypeDiscountPanel.setVisible(false);
        }
    }

    @Override
    public String getCalcContents() {
        // gets past validator will be replaced on server
        return "placeholder";
    }

    private boolean isChargesValid() {
        if (chargesDataProvider.getList().isEmpty()) {
            chargesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.charges.none"));
            return false;
        } else {
            for(TouSeasonChgData chg : chargesDataProvider.getList()) {
                if (chg.getTouRate() == null || chg.getTouRate().doubleValue() < 0.0) {
                    logger.info("Invalid chg rate: "+chg.getTouRate()+" "+chg.getMeterReadingTypeName()+" "+chg.getPeriodName()+" "+chg.getSeasonName());
                    chargesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.charges.invalid"));
                    return false;
                }
            }
        }
        logger.info("Valid charges");
        return true;
    }

    @Override
    public void setFormReadOnly(boolean readOnly) {
        chargesButton.setEnabled(!readOnly);
    }

    @Override
    public void setTariffInitData(ITariffInitData tariffInitData) {
        logger.info("Set tariffInitData");
        touThinCalcInitData = (TouThinCalcInitData) tariffInitData;
        touThinChargeData = new TouThinChargeData();
        applyTariffData(true);
    }

    @Override
    public void setTariffData(ITariffData tariffData) {
        logger.info("Set tariffData");
        touThinChargeData = (TouThinChargeData) tariffData;
        applyTariffData(false);
    }

    /**
     * When called from setTariffInitData it tells other methods that it is in the initialization process
     * and therefore does not have a existing tariff data retrieved and doesn't do things like try to set
     * the selected item that would have been had it been from a saved tariff.
     *
     * @param init
     */
    public void applyTariffData(boolean init) {
        populateCalendars(init); //this populates the charges tables too
        populateMonthlyDemandReadingTypes();
        populateEnableReadingTypes(init);
        setCalcContentsFields(init);
    }

    private void populateCalendars(boolean init) {
        calendarBox.clear();
        calendarBox.addItem("");

        boolean selected = false;
        Long calendarId = (!init && touThinChargeData.getTariffCalendar() != null ? touThinChargeData.getTariffCalendar().getTouCalendarId() : null);
        TouCalendar tc = null;
        for (int i=0;i<touThinCalcInitData.getCalendars().size();i++) {
            tc = touThinCalcInitData.getCalendars().get(i);
            calendarBox.addItem(tc.getName(), tc.getId().toString());
            if (tc.getId().equals(calendarId)) {
                calendarBox.setSelectedIndex(i+1);
                selected = true;
            }
        }

        //If the tariff already has a calendar, then load the calendar's structure from the back end and display the charges/special day charges
        if (! init && selected) {
            ArrayList<Long> typeIds = new ArrayList<Long>();
            for(TouSeasonChgData chg : touThinChargeData.getSeasonCharges()) {
                typeIds.add(chg.getMeterReadingTypeId());
            }
            loadCalendarData(calendarId, getMeterReadingTypes(typeIds), true);
        }
    }

    private void populateMonthlyDemandReadingTypes() {
        monthlyDemandTypeBox.clear();
        monthlyDemandTypeBox.addItem("");
        for (MeterReadingType t : touThinCalcInitData.getMonthlyDemandReadingTypes()) {
            monthlyDemandTypeBox.addItem(
                        t.getName()+" / " + MeterMngCommonUtil.getCorrectedUnitOfMeasure(t.getUnitOfMeasure()),
                    t.getValue());
        }
    }

    private void populateEnableReadingTypes(boolean init) {
        enableReadingTypesBox.clear();

        ArrayList<String> readingTypes = new ArrayList<String>();
        if(!init) {
            // during init touThinChargeData is not yet populated
            for(TouSeasonChgData chg : touThinChargeData.getSeasonCharges()) {
                readingTypes.add(chg.getMeterReadingTypeName());
            }
        }
        for (int i=0;i<touThinCalcInitData.getEnableReadingTypes().size();i++) {
            MeterReadingType t = touThinCalcInitData.getEnableReadingTypes().get(i);
            enableReadingTypesBox.addItem(t.getName()+" ("+t.getUnitOfMeasure()+")", t.getId().toString());
            if (! init && readingTypes.contains(t.getName())) {
                enableReadingTypesBox.setItemSelected(i, true);
            } else {
                enableReadingTypesBox.setItemSelected(i, false);
            }
        }
    }

    @Override
    public ITariffData getTariffData() {
        if (touThinChargeData != null) {
            boolean valid = true;
            clearErrors();
            //Monthly demand charge
            BigDecimal monthlyDemandCharge = monthlyDemandChargeBox.getValue();
            String monthlyDemandReadingTypeValue = "";
            int index = monthlyDemandTypeBox.getSelectedIndex();
            if (index > 0) {
                monthlyDemandReadingTypeValue = monthlyDemandTypeBox.getValue(index);
            }
            if (monthlyDemandCharge != null && monthlyDemandReadingTypeValue.equals("")) {
                valid = false;
                monthlyDemandTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.monthlydemandtype.required"));
            } else if (monthlyDemandCharge == null && !monthlyDemandReadingTypeValue.equals("")) {
                valid = false;
                monthlyDemandChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.monthlydemand.required"));
            }
            if (monthlyDemandCharge != null && monthlyDemandCharge.compareTo(BigDecimal.ZERO) <= 0) {
                valid = false;
                monthlyDemandChargeElement
                        .setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.monthlydemand.positive"));
            }
            List<CyclicChargeData> cyclicCharges = new ArrayList<>(cyclicChargesPanel.getWidgetCount());
            for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
                CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(i);
                try {
                    CyclicChargeData cyclicCharge = cyclicChargePanel.getCyclicCharge();
                    // Charges not populated will be null
                    if(cyclicCharge != null) {
                        cyclicCharges.add(cyclicCharge);
                    }
                } catch (Exception e) {
                    valid = false;
                }
            }
            List<PercentChargeData> percentCharges = new ArrayList<>(percentChargesPanel.getWidgetCount());
            for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
                PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(i);
                try {
                    PercentChargeData percentCharge = percentChargePanel.getPercentCharge();
                    // Charges not populated will be null
                    if(percentCharge != null) {
                        percentCharges.add(percentCharge);
                    }
                } catch (Exception e) {
                    valid = false;
                }
            }

            //Tax
            BigDecimal tax = taxBox.getAmount();
            logger.info("tax: "+tax);
            BigDecimal taxMultiplier = null;
            if (tax == null) {
                valid = false;
                taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.tax"));
            } else if (tax.doubleValue() < 0.0) {
                valid = false;
                taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.tax.positive"));
            } else {
                try {
                    // taxMultiplier is 1 + (percent / 100) eg. 14% = 1.14
                    // more efficient to do this calculation here than to store 14% and have the tariff calculator do this calculation on every vend
                    taxMultiplier = BigDecimal.ONE.add(tax.divide(BigDecimal.valueOf(100L)));
                } catch (Exception e) {
                    valid = false;
                    taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.numeric.value"));
                }
            }

            //Also validate the other input as can prevent the tariff data being saved if there is an error
            //Check calendar is selected
            Long calendarId = null;
            index = calendarBox.getSelectedIndex();
            if (index > 0) {
                calendarId = Long.valueOf(calendarBox.getValue(index));
            }
            if (calendarId == null) {
                valid = false;
                calendarElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.calendar"));
            }
            //Check enabled reading types are selected
            final ArrayList<Long> ids = new ArrayList<Long>();
            for(int i=0;i<enableReadingTypesBox.getItemCount();i++) {
                if (enableReadingTypesBox.isItemSelected(i)) {
                    ids.add(Long.valueOf(enableReadingTypesBox.getValue(i)));
                }
            }
            if (ids.size() == 0) {
                valid = false;
                enableReadingTypesElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.types"));
            }
            //Check charges
            if (!isChargesValid()) {
                valid = false;
            }
            if (payTypeDiscountPanel.isVisible()) {
                if (!payTypeDiscountPanel.isValid()) {
                    valid = false;
                }
            }
            logger.info("valid:" + valid);
            //Valid input?
            if (!valid) {
                return null;
            }
            TouThinCalcContents calcContents = new TouThinCalcContents();
            touThinChargeData.setCalcContents(calcContents);
            if(monthlyDemandCharge != null) {
                ArrayList<DemandChargeData> demandCharges = new ArrayList<DemandChargeData>();
                DemandChargeData demandCharge = new DemandChargeData();
                demandCharge.setCharge(monthlyDemandCharge);
                demandCharge.setReadingType(monthlyDemandReadingTypeValue);
                demandCharges.add(demandCharge);
                calcContents.setDemandCharges(demandCharges);
            }
            calcContents.setPercentCharges(percentCharges);
            calcContents.setCyclicCharges(cyclicCharges);
            calcContents.setTax(taxMultiplier);
            if (payTypeDiscountPanel.isVisible()) {
                touThinChargeData.getCalcContents().setPayTypeDiscounts(payTypeDiscountPanel.getThisPayTypeDiscountsList());
            }

            //Calendar
            Long touCalendarId = null;
            index = calendarBox.getSelectedIndex();
            if (index > 0) {
                touCalendarId = Long.valueOf(calendarBox.getValue(index));
            }
            if (touThinChargeData.getTariffCalendar() == null) {
                touThinChargeData.setTariffCalendar(new TouTariffCalendar());
            }
            touThinChargeData.getTariffCalendar().setTouCalendarId(touCalendarId);

            //Charges
            touThinChargeData.setSeasonCharges(new ArrayList<TouSeasonChgData>(chargesDataProvider.getList()));

            // copy template only fields
            calcContents.setLimitReverseBillingToTotalConsumption(touThinCalcInitData.getTemplate().getCalcContents().isLimitReverseBillingToTotalConsumption());
            calcContents.setFirstReadingMaxGap(touThinCalcInitData.getTemplate().getCalcContents().getFirstReadingMaxGap());
            calcContents.setReverseBillingTaxable(touThinCalcInitData.getTemplate().getCalcContents().isReverseBillingTaxable());
            logger.info("Got valid tariff data");
            return touThinChargeData;
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("tou.thin.no.tariff.data.available"),
                                        MediaResourceUtil.getInstance().getErrorIcon());
            return null;
        }
    }

    @Override
    protected void addFieldHandlers() {
        taxBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        monthlyDemandChargeBox.addChangeHandler(new FormDataChangeHandler(form));
        monthlyDemandTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        enableReadingTypesBox.addChangeHandler(new FormDataChangeHandler(form));
        calendarBox.addChangeHandler(new FormDataChangeHandler(form));
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(i);
            cyclicChargePanel.addFieldHandlers(form);
        }
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(i);
            percentChargePanel.addFieldHandlers(form);
        }
    }

    private void initChargesTable() {
        if (chargesDataProvider == null) {
            // no paging
            chargesTable.setPageSize(Integer.MAX_VALUE);
            chargesDataProvider = new ListDataProvider<TouSeasonChgData>();
            TextColumn<TouSeasonChgData> season = new TextColumn<TouSeasonChgData>() {
                @Override
                public String getValue(TouSeasonChgData data) {
                    return data.getSeasonName();
                }
            };

            TextColumn<TouSeasonChgData> period = new TextColumn<TouSeasonChgData>() {
                @Override
                public String getValue(TouSeasonChgData data) {
                    return data.getPeriodName();
                }
            };

            TextColumn<TouSeasonChgData> readingType = new TextColumn<TouSeasonChgData>() {
                @Override
                public String getValue(TouSeasonChgData data) {
                    return data.getMeterReadingTypeName();
                }
            };

            if (FormatUtil.getInstance().isRightToLeft()) {
                rateCell = new DecimalInputCell("", FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
            } else {
                rateCell = new DecimalInputCell(FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
            final Column<TouSeasonChgData, String> rate = new Column<TouSeasonChgData, String>(rateCell) {
                @Override
                public String getValue(TouSeasonChgData data) {
                    if (data.getTouRate() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getTouRate());
                }
            };

            rate.setFieldUpdater(new FieldUpdater<TouSeasonChgData, String>() {
                @Override
                public void update(int index, TouSeasonChgData touChargeWithDetail, String value) {
                    if (value != null && !value.isEmpty() && rateCell.isNumeric(value)) {
                        getForm().setDirtyData(true);
                        touChargeWithDetail.setTouRate(FormatUtil.getInstance().parseDecimal(value));
                        chargesTable.redraw();
                        MeterMngClientUtils.focusOnNext(chargesTable, index, chargesTable.getColumnIndex(rate));
                    } else {
                        rateCell.getViewData(touChargeWithDetail).setInvalid(true);     // Mark as invalid.
                        chargesTable.redraw();
                        MeterMngClientUtils.focusOnNext(chargesTable, index, chargesTable.getColumnIndex(rate));
                    }
                }
            });

            TextColumn<TouSeasonChgData> chargeUnits = new TextColumn<TouSeasonChgData>() {
                @Override
                public String getValue(TouSeasonChgData data) {
                    return MeterMngCommonUtil.getCorrectedUnitOfMeasure(data.getMeterReadingTypeUnits());
                }
            };

            // Add the columns.
            chargesTable.addColumn(readingType, MessagesUtil.getInstance().getMessage("tariff.tou.thinsmart.readingtype"));
            chargesTable.addColumn(season, MessagesUtil.getInstance().getMessage("tariff.tou.thinsmart.season"));
            chargesTable.addColumn(period, MessagesUtil.getInstance().getMessage("tariff.tou.thinsmart.period"));
            chargesTable.addColumn(chargeUnits, MessagesUtil.getInstance().getMessage("tariff.tou.thinsmart.chargeunits"));
            SafeHtmlHeader header = new SafeHtmlHeader(new SafeHtml() {
                private static final long serialVersionUID = 1L;
                @Override
                public String asString() {
                        return "<div align=\"center\">"+MessagesUtil.getInstance().getMessage("tariff.tou.thinsmart.rate")+"</div>";
                }
            });
            chargesTable.addColumn(rate, header);
            chargesTable.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
            chargesTable.setStyleDependentName(DEBUG_ID_PREFIX, noSpecialDays);
            chargesTable.setRowStyles(new RowStyles<TouSeasonChgData>() {
                @Override
                public String getStyleNames(TouSeasonChgData row, int rowIndex) {
                    List<TouSeasonChgData> data = chargesDataProvider.getList();
                    if(rowIndex > 1) {
                        TouSeasonChgData prevRow = data.get(rowIndex - 1);
                        if(!row.getMeterReadingTypeId().equals(prevRow.getMeterReadingTypeId())) {
                            return "touReadingTypePadding";
                        }
                    }
                    return null;
                }
            });
            chargesDataProvider.addDataDisplay(chargesTable);
        }
    }

}
