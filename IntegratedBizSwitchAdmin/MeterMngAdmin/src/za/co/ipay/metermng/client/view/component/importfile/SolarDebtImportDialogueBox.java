package za.co.ipay.metermng.client.view.component.importfile;

import java.math.BigDecimal;
import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.solar.SolarDebtImportRecord;

public class SolarDebtImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private SolarDebtImportRecord recordIn;

    public SolarDebtImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        SolarDebtImportRecord record = recordIn = itemDto.getSolarDebtImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Account Number", record.getAccountNumber()));
        dataList.add(new ImportRecordField("Debtor Balance", record.getArrearsBalance().toPlainString()));
        dataList.add(new ImportRecordField("30 Days Balance", record.getDaysBalance30().toPlainString()));
        dataList.add(new ImportRecordField("60 Days Balance", record.getDaysBalance60().toPlainString()));
        dataList.add(new ImportRecordField("90 Days Balance", record.getDaysBalance90().toPlainString()));
        dataList.add(new ImportRecordField("120 Days Balance", record.getDaysBalance120().toPlainString()));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        SolarDebtImportRecord chgRec = createRecordFromList();
        isDirtyData = false;
        if (!chgRec.getAccountNumber().equals(recordIn.getAccountNumber())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getArrearsBalance().equals(recordIn.getArrearsBalance())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getDaysBalance30().equals(recordIn.getDaysBalance30())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getDaysBalance60().equals(recordIn.getDaysBalance60())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getDaysBalance90().equals(recordIn.getDaysBalance90())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getDaysBalance120().equals(recordIn.getDaysBalance120())) {
            isDirtyData = true;
            return;
        }
    }

    private SolarDebtImportRecord createRecordFromList() {
        SolarDebtImportRecord chgRec = new SolarDebtImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Account Number")) {
                chgRec.setAccountNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("Debtor Balance")) {
                chgRec.setArrearsBalance(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("30 Days Balance")) {
                chgRec.setDaysBalance30(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("60 Days Balance")) {
                chgRec.setDaysBalance60(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("90 Days Balance")) {
                chgRec.setDaysBalance90(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("120 Days Balance")) {
                chgRec.setDaysBalance120(new BigDecimal(field.getFieldValue()));
            }
        }
        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }

    @Override
    protected void displayUpdateMessage() {
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.account.number.update.success",
                        new String[] { createRecordFromList().getAccountNumber() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItem) {
        importFileItem.setGenericImportRecord(createRecordFromList());
    }
}