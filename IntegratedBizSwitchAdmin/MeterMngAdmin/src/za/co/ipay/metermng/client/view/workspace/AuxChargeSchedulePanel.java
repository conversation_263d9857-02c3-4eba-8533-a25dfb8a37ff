package za.co.ipay.metermng.client.view.workspace;

import java.math.BigDecimal;
import java.util.Date;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;


public class AuxChargeSchedulePanel extends SimpleFormPanel {
    
    @UiField FormElement acsNameElement;
    @UiField FormElement minAmtElement;
    @UiField FormElement maxAmtElement;
    @UiField FormElement vendPortionElement;
    @UiField FormElement currentPortionElement;
    @UiField FormElement chargeCycleElement;
    @UiField FormElement chargeAmtElement;
    @UiField FormElement activeElement;
    
    @UiField TextBox acsNameTextBox;
    @UiField CurrencyTextBox minAmtTextBox;
    @UiField CurrencyTextBox maxAmtTextBox;
    @UiField PercentageTextBox vendPortionTextBox;
    @UiField PercentageTextBox currentPortionTextBox;
    @UiField HTML instalmentLabel;
    @UiField ListBox chargeCycleBox;
    @UiField CurrencyTextBox chargeAmtTextBox;
    @UiField CheckBox activeCheckBox;
    @UiField Label error;
    
    private AuxChargeSchedule auxChargeSchedule;
    private Logger logger = Logger.getLogger("AuxChargeSchedulePanel");
    
    private static AuxChargeStructurePanelUiBinder uiBinder = GWT.create(AuxChargeStructurePanelUiBinder.class);

    interface AuxChargeStructurePanelUiBinder extends UiBinder<Widget, AuxChargeSchedulePanel> {
    }

    public AuxChargeSchedulePanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    @Override
    public void addFieldHandlers() {
        
        acsNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        minAmtTextBox.addChangeHandler(new FormDataChangeHandler(form));
        maxAmtTextBox.addChangeHandler(new FormDataChangeHandler(form));
        vendPortionTextBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        currentPortionTextBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        chargeAmtTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeCheckBox.addClickHandler(new FormDataClickHandler(form));   
        chargeCycleBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                setFieldsAccordingToCycle();
            }
        });
    } 
    
    public void setFieldsAccordingToCycle() {
        String cycleStr = chargeCycleBox.getItemText(chargeCycleBox.getSelectedIndex());
        if (cycleStr != null && !cycleStr.isEmpty() && !cycleStr.equals(CycleE.AD_HOC.name())) {
            clearNonCycleFields();
            disableNonCycleEntries();
            instalmentLabel.setText(MessagesUtil.getInstance().getMessage("aux.charge.sched.cycle.instalment.label"));
            instalmentLabel.setVisible(true);
        } else {
            enableNonCycleEntries();
        }
    }
    
    private void disableNonCycleEntries() {
        minAmtTextBox.setEnabled(false);
        maxAmtTextBox.setEnabled(false);
        vendPortionTextBox.setEnabled(false);
        currentPortionTextBox.setEnabled(false);
    }
    
    private void enableNonCycleEntries() {
        minAmtTextBox.setEnabled(true);
        maxAmtTextBox.setEnabled(true);
        vendPortionTextBox.setEnabled(true);
        currentPortionTextBox.setEnabled(true);
        instalmentLabel.setText("");
        instalmentLabel.setVisible(false);
    }

    public void clearErrors() {
        acsNameElement.clearErrorMsg();
        minAmtElement.clearErrorMsg();
        maxAmtElement.clearErrorMsg();
        vendPortionElement.clearErrorMsg();
        currentPortionElement.clearErrorMsg();
        chargeCycleElement.clearErrorMsg();
        chargeAmtElement.clearErrorMsg();
        activeElement.clearErrorMsg();
        error.setText("");
        error.setVisible(false);
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        acsNameTextBox.setText("");
        enableNonCycleEntries();
        clearNonCycleFields();
        chargeCycleBox.setSelectedIndex(0);
        chargeAmtTextBox.setAmount(null);
        activeCheckBox.setValue(false);
    }
    
    private void clearNonCycleFields() {
        minAmtTextBox.setAmount(null);
        maxAmtTextBox.setAmount(null);
        vendPortionTextBox.setAmount(null);
        currentPortionTextBox.setAmount(null);
    }
    
    public void populate() {
        chargeCycleBox.clear();
        chargeCycleBox.addItem("");
        chargeCycleBox.addItem(CycleE.AD_HOC.name());
        chargeCycleBox.addItem(CycleE.DAILY.name());
        chargeCycleBox.addItem(CycleE.MONTHLY.name());
    }
    
    public boolean isChargesValid() {
        boolean valid = false;
        valid = vendPortionTextBox.getAmount()!=null && vendPortionTextBox.getAmount().compareTo(BigDecimal.ZERO) != 0;
        if (!valid) {    
            valid = currentPortionTextBox.getAmount()!=null && currentPortionTextBox.getAmount().compareTo(BigDecimal.ZERO) != 0;
            if (!valid) {
                valid = chargeAmtTextBox.getAmount() !=null && chargeAmtTextBox.getAmount().compareTo(BigDecimal.ZERO) != 0;
            }
        }
        if (!valid) {
            error.setText(MessagesUtil.getInstance().getMessage("auxchargeschedule.nocharge.error"));
            error.setVisible(true);
        }
        BigDecimal minAmt = minAmtTextBox.getAmount();
        if (minAmt != null && minAmt.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            minAmtElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        BigDecimal maxAmt = maxAmtTextBox.getAmount();
        if (maxAmt != null && maxAmt.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            maxAmtElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        BigDecimal vendPortion = vendPortionTextBox.getAmount();
        if (vendPortion != null && vendPortion.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            vendPortionElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        BigDecimal currentPortion = currentPortionTextBox.getAmount();
        if (currentPortion != null && currentPortion.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            currentPortionElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        
        BigDecimal chargeAmt = chargeAmtTextBox.getAmount();
        if (chargeAmt != null && chargeAmt.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            chargeAmtElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
        }
        int index = chargeCycleBox.getSelectedIndex();
        if ((index == 0 && chargeAmt != null)
                || (index > 0 && chargeAmt == null)) {
            valid = false;
            chargeCycleElement.showErrorMsg(MessagesUtil.getInstance().getMessage("aux.charge.sched.cycle.select.error"));
        }
        
        return valid;
    }
    
    public void selectChargeSchedule(Long chargeCycleId) {
        CycleE cycle = CycleE.fromId(chargeCycleId);
        for (int i = 0; i < chargeCycleBox.getItemCount();i++) {
            if (chargeCycleBox.getItemText(i).equals(cycle.name())) {
                chargeCycleBox.setSelectedIndex(i);
                break;
            }
        }
    }
    
    public boolean setAuxChargeSchedule(AuxChargeSchedule acs) {
        boolean isNew = true;
        this.auxChargeSchedule = acs;
        if (auxChargeSchedule == null) {
            auxChargeSchedule = new AuxChargeSchedule();
            auxChargeSchedule.setRecordStatus(RecordStatus.DAC);
            auxChargeSchedule.setIsArrears(false);           
            isNew = true;
        } else {
            isNew = false;
            if(auxChargeSchedule.isAccountSpecific()) {
                enableAndHideActiveCheckbox();
            }
        }
        // set inputs
        acsNameTextBox.setText(auxChargeSchedule.getScheduleName());
        minAmtTextBox.setAmount(auxChargeSchedule.getMinAmt());
        maxAmtTextBox.setAmount(auxChargeSchedule.getMaxAmt());
        vendPortionTextBox.setAmount(auxChargeSchedule.getVendPortion());
        currentPortionTextBox.setAmount(auxChargeSchedule.getCurrentPortion());
        if (auxChargeSchedule.getChargeCycleId() != null) {
            selectChargeSchedule(auxChargeSchedule.getChargeCycleId());
        } else {
            chargeCycleBox.setSelectedIndex(0);
        }
        chargeAmtTextBox.setAmount(auxChargeSchedule.getChargeAmt());
        activeCheckBox.setValue(auxChargeSchedule.getRecordStatus().equals(RecordStatus.ACT));
        setFieldsAccordingToCycle();
        return isNew;
    }
    
    public void clear() {
        logger.info("clearing aux charge schedule");
        clearFields();
        clearErrors();
        setAuxChargeSchedule(null);
    }
    
    public void enableAndHideActiveCheckbox() {
        activeCheckBox.setValue(true);
        activeCheckBox.setVisible(false);
        activeElement.setVisible(false);
        activeElement.getParent().getParent().getParent().removeFromParent();
    }
    
    public void setAcsName(String name) {
        acsNameTextBox.setText(name);
    }
    
    public AuxChargeSchedule populateFromForm(AuxChargeSchedule auxChargeSchedule) {
        // refresh objects from inputs
        if (auxChargeSchedule == null) {
            auxChargeSchedule = new AuxChargeSchedule();
        }
        auxChargeSchedule.setScheduleName(acsNameTextBox.getText());
        auxChargeSchedule.setChargeAmt(chargeAmtTextBox.getAmount());
        auxChargeSchedule.setCurrentPortion(currentPortionTextBox.getAmount());
        auxChargeSchedule.setMinAmt(minAmtTextBox.getAmount());
        auxChargeSchedule.setMaxAmt(maxAmtTextBox.getAmount());
        auxChargeSchedule.setVendPortion(vendPortionTextBox.getAmount());
        
        int chargeCycleIndx = chargeCycleBox.getSelectedIndex();
        if (chargeCycleIndx > 0) {
            CycleE cycle = CycleE.valueOf(chargeCycleBox.getItemText(chargeCycleIndx)); 
            auxChargeSchedule.setChargeCycleId(cycle.getId());
        }

        auxChargeSchedule.setRecordStatus(activeCheckBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        //default values set for these fields
        auxChargeSchedule.setIsArrears(false);
        auxChargeSchedule.setStartDate(new Date());
        return auxChargeSchedule;
    }
    
    public boolean isValidInput() {
        //create a temporary object
        AuxChargeSchedule auxChargeSchedule = new AuxChargeSchedule();
        populateFromForm(auxChargeSchedule);
        
        boolean valid = true;
        clearErrors();       
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "scheduleName", acsNameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "minAmt", minAmtElement)) {
            valid = false;
        }        
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "maxAmt", maxAmtElement)) {
            valid = false;
        } 
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "vendPortion", vendPortionElement)) {
            valid = false;
        } 
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "currentPortion", currentPortionElement)) {
            valid = false;
        } 
        if (!ClientValidatorUtil.getInstance().validateField(auxChargeSchedule, "chargeAmt", chargeAmtElement)) {
            valid = false;
        } 
        if (!isChargesValid()) {
            valid = false;
        }
        return valid;
    }
    
}
