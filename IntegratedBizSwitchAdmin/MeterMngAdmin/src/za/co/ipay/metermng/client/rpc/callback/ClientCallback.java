package za.co.ipay.metermng.client.rpc.callback;

import java.util.Collection;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.rpc.InvocationException;
import com.google.gwt.user.client.rpc.StatusCodeException;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.NoCurrentUserException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.MeterMngAdmin;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.login.LoginView.LoginViewUtil;

/**
 * ClientCallback is a base RPC callback that handles exceptions that could be thrown by a service call. 
 * This class is here and not in GWTCommon as it uses the application-specific MediaResourceUtil to get the icon images.
 * 
 * <AUTHOR>
 */
public abstract class ClientCallback<T> implements AsyncCallback<T> {

    public static final String SPRING_SECURITY_ACTION = "/j_spring_security_check";
    public static final String CURRENT_WINDOW = "_self";
    private static final String ACCESS_DENIED_ERROR_CODE = "403";

    private static Logger logger = Logger.getLogger(ClientCallback.class.getName());

    private Integer left;
    private Integer top;
    private SessionCheckCallback sessionCheckCallback;

    public ClientCallback() {
        //Default GWT constructor
    }

    public ClientCallback(SessionCheckCallback sessionCheckCallback) {
        //Default GWT constructor
        this.sessionCheckCallback = sessionCheckCallback;
    }

    public ClientCallback(Integer left, Integer top) {
        this.left = left;
        this.top = top;
    }

    /**
     * @see com.google.gwt.user.client.rpc.AsyncCallback#onFailure(Throwable)
     */
    public void onFailure(Throwable caught) {
        hideWaitingDialog();
        onFailureClient(caught);
        onFailureDisplayMessage(caught);
    }
    
    public void onFailureDisplayMessage(Throwable caught) {
        // Show a message to the user to inform them of the error
        if (caught instanceof ValidationException) {            
            displayValidationErrors((ValidationException) caught);
        } else {
            if (caught instanceof ServiceException) {
                displayServiceException((ServiceException) caught);
            } else if (caught instanceof InvocationException) {
                displayInvocationException((InvocationException) caught);
            } else if (caught instanceof AccessControlException) {
                displayAccessControlException();
            } else {
                displayGeneralException(caught);
            }
        }
    }
    
    public void hideWaitingDialog() {
        WaitingDialog waitingDialog = WaitingDialogUtil.getCurrentInstance();
        if (waitingDialog != null) {
            waitingDialog.hide();
        }
    }
    
    private void displayServiceException(ServiceException e) {        
        if (e instanceof NoCurrentUserException) {
            handleNoSessionUserException("from ServiceException: NoCurrentUserException");
        } else {
            logger.log(Level.SEVERE, "Caught ServiceException:", e);
            
            if (e.isTranslateMessage()) {
                String msg = MessagesUtil.getInstance().getMessage(e.getMessage());
                if (e.getArgs() != null) {
                    msg = MessagesUtil.getInstance().getMessage(e.getMessage(), e.getArgs());
                }
                Dialogs.displayErrorMessage(msg, 
                        MediaResourceUtil.getInstance().getErrorIcon(), 
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.general"), 
                        MediaResourceUtil.getInstance().getErrorIcon(),                     
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
        }
    }
    
    private void displayInvocationException(InvocationException ie) {
        if (ie instanceof StatusCodeException) {
            //Couldn't connect to the server?
            StatusCodeException sce = (StatusCodeException) ie;
            if (sce.getStatusCode() == 0) {
                Dialogs.displayErrorMessage(
                        getI18nMessage("error.server.connection", false)+" "+sce.getStatusText(), 
                        MediaResourceUtil.getInstance().getErrorIcon(), 
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                Dialogs.displayErrorMessage(
                        getI18nMessage("error.server", false)+" "+sce.getStatusText(), 
                        MediaResourceUtil.getInstance().getErrorIcon(), 
                        MessagesUtil.getInstance().getMessage("button.close")); 
            }
        } else if (ie.getCause() instanceof NoCurrentUserException) {
            handleNoSessionUserException("from InvocationException: NoCurrentUserException");
        } else if (ie.getMessage().contains(SPRING_SECURITY_ACTION)) {
            //AuthenticationException thrown by Spring Security filter
            handleNoSessionUserException("from InvocationException: Spring Security exception");
        } else if (ie.getMessage().contains(ACCESS_DENIED_ERROR_CODE)) {          
            //By Spring Security permissions annotations
            displayAccessControlException();
        } else {
            //Other
            logger.log(Level.SEVERE, "Caught InvocationException:", ie);
            Dialogs.displayErrorMessage(getI18nMessage("error.general", false), 
                                        MediaResourceUtil.getInstance().getErrorIcon(), 
                                        MessagesUtil.getInstance().getMessage("button.close"));
        }
    }
    
    private void displayAccessControlException() {        
         // Spring Security - access denied
        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                                    MediaResourceUtil.getInstance().getLockedIcon(), 
                                    MessagesUtil.getInstance().getMessage("button.close"));
    }
    
    private void displayGeneralException(Throwable caught) {
        logger.log(Level.SEVERE, "Caught Throwable:", caught);
        Dialogs.displayErrorMessage(getI18nMessage("error.general", false), 
                                    MediaResourceUtil.getInstance().getErrorIcon(), 
                                    MessagesUtil.getInstance().getMessage("button.close"));
    }
    
    private String getI18nMessage(String key, boolean returnOriginal) {
        return getI18nMessage(key, null, returnOriginal);
    }

    private String getI18nMessage(String key, String[] args, boolean returnOriginal) {
        String msg = "";
        if (args != null) {
            msg = MessagesUtil.getInstance().getMessage(key, args);
        } else {
            msg = MessagesUtil.getInstance().getMessage(key);
        }
        
        // messages not loaded yet or unknown key
        if (key.equals(msg)) {
            if (!returnOriginal) {                
                return "An error occurred and was logged.";
            } else {
                return key;
            }
        } else {
            return msg;
        }
    }

    /**
     * Method to do any clean up or changes on the client after a failure. Override this method if the specific async 
     * instance need to do clean up for their client.
     */
    protected void onFailureClient(Throwable caught) {
        // default is to do nothing but override by client implementations where necessary, this method is when need info from message
        onFailureClient();
    }
    protected void onFailureClient() {
        // default is to do nothing but override by client implementations where necessary
    }

    /**
     * Method to display the validation error messages. Override this to get custom client behavior.
     * @param e The validation exception.
     */
    protected void displayValidationErrors(ValidationException e) {
        //Translation the validationMessages if necessary - can contain message key or actual message 
        translateValidationMessages(e);
        
        //Decide where to place the error dialog and if it needs a close button
        Integer l = left;
        Integer t = top;
        String close = MessagesUtil.getInstance().getMessage("button.close");
        
        //Display the validation and field errors in the errors dialog
        if (e.getErrorMessages().size() > 0 && e.getFieldErrorMessages().size() > 0) {
            Dialogs.displayValidationAndFieldValidationMessages(MessagesUtil.getInstance().getMessage("error.validation.header"), 
                                                                e.getErrorMessages(), 
                                                                MessagesUtil.getInstance().getMessage("error.validation.fields.header"), 
                                                                e.getFieldErrorMessages(), 
                                                                MediaResourceUtil.getInstance().getErrorIcon(),
                                                                l, t, close);
        } else {
            if (e.getErrorMessages().size() == 1) {
                Dialogs.displayErrorMessage(e.getErrorMessages().get(0), 
                                            MediaResourceUtil.getInstance().getErrorIcon(), 
                                            l, t, close);
            } else if (e.getErrorMessages().size() > 1) {
                Dialogs.displayValidationMessages(MessagesUtil.getInstance().getMessage("error.validation.header"), 
                                                  e.getErrorMessages(), 
                                                  MediaResourceUtil.getInstance().getErrorIcon(),
                                                  l, t, close);
            }
            if (e.getFieldErrorMessages().size() > 0) {
                Dialogs.displayFieldValidationMessages(MessagesUtil.getInstance().getMessage("error.validation.fields.header"), 
                                                       e.getFieldErrorMessages(), 
                                                       MediaResourceUtil.getInstance().getErrorIcon(),
                                                       l, t, close);
            }
        }
    }
    
    private void translateValidationMessages(ValidationException e) {
        if (e.getErrorMessages().size() > 0) {
            for(ValidationMessage message : e.getErrorMessages()) {
                if (message.isTranslateMessage()) {
                    message.setMessage( getI18nMessage(message.getMessage(), message.getArgs(), true) );
                }
            }
        }
        if (e.getFieldErrorMessages().size() > 0) {
            Collection<ValidationMessage> fieldMessages = e.getFieldErrorMessages().values();
            for(ValidationMessage message : fieldMessages) {
                if (message.isTranslateMessage()) {
                    message.setMessage( getI18nMessage(message.getMessage(), message.getArgs(), true) );
                }
            }
        }
    }
    
    protected void handleNoSessionUserException(String source) {
        logger.info("Allowing user to login again: "+source);
        LoginViewUtil.getInstance(MeterMngAdmin.getClientFactory(), sessionCheckCallback).displayLoginView();
    }

   
}