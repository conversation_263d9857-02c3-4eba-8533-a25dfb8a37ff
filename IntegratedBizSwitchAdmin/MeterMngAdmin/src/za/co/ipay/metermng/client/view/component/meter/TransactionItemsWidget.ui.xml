<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:c="urn:import:com.google.gwt.user.cellview.client">
    <ui:style>
        .btnMarginBottom {
            margin-bottom: 10px;
        }
        .waitMargin {
            margin-top: 10px;
            margin-left: 10px;
        }
    </ui:style>
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:HTMLPanel styleName='ipaypopup'>
        <table>
            <tr>
                <td>
                    <g:ScrollPanel>
                        <c:CellTable pageSize='20'  ui:field="clltbltransitems"/>
                    </g:ScrollPanel>    
                </td>
                <td>
                    <g:SimplePanel ui:field="sendReprintPanel">
                        <g:VerticalPanel>
                            <g:Button ui:field="sendReprintBtn" text="{msg.getSendReprint}" addStyleNames="{style.btnMarginBottom}"/>
                            <g:Button ui:field="vendReversalBtn" text="{msg.getVendReversal}"/>
            	<g:VerticalPanel ui:field="panelReversed" visible="false">
	            	<g:Label text="{msg.getVendReversed}"/>
	            	<g:Label ui:field="reversalCommentLabel"/>
            	</g:VerticalPanel>
                        </g:VerticalPanel>
                    </g:SimplePanel>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <g:VerticalPanel ui:field="stsTokensPanel" visible="false" width="100%">
                        <g:HTML ui:field="stsTokensHeader" text="{msg.getStsTokensHeader}" styleName="dataTitle" addStyleNames="container" horizontalAlignment="ALIGN_CENTER"/>
                        <g:HorizontalPanel>
                            <c:CellTable pageSize='10' ui:field="clltbltranstokens"/>
                            <g:Image styleName="{style.waitMargin}" ui:field="imgValidating" altText="Validating..."  width="24px" height="24px" visible="false" />
                        </g:HorizontalPanel>
                    </g:VerticalPanel>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <g:VerticalPanel ui:field="stsTokenVerifiedPanel" visible="false" >
                        <g:HTML ui:field="verifiedHeader" styleName="dataTitle" addStyleNames="container" horizontalAlignment="ALIGN_LEFT"/>
                        <g:HorizontalPanel spacing="5">
                            <g:Label text="{msg.getVerifyTokenClass}:" styleName="gwt-Label-bold" ui:field="lblTokenClass"/>
                            <g:Label ui:field="lblTokenClassValue"/>
                        </g:HorizontalPanel>
                        <g:HorizontalPanel spacing="5">
                            <g:Label text="{msg.getVerifyTokenSubclass}:" styleName="gwt-Label-bold" ui:field="lblTokenSubclass"/>
                            <g:Label ui:field="lblTokenSubclassValue"/>
                        </g:HorizontalPanel>
                        <g:HorizontalPanel spacing="5">
                            <g:Label text="{msg.getVerifyTokenId}:" styleName="gwt-Label-bold" ui:field="lblTokenIdclass"/>
                            <g:Label ui:field="lblTokenIdValue"/>
                        </g:HorizontalPanel>
                        <g:HorizontalPanel spacing="5">
                            <g:Label text="{msg.getVerifyTokenDate}:" styleName="gwt-Label-bold" ui:field="lblTokenDateclass"/>
                            <g:Label ui:field="lblTokenDateValue"/>
                        </g:HorizontalPanel>
                        <g:HorizontalPanel spacing="5">
                            <g:Label text="{msg.getVerifyTokenUnits}:" styleName="gwt-Label-bold" ui:field="lblUnits"/>
                            <g:Label ui:field="lblUnitsValue"/>
                        </g:HorizontalPanel>
                    </g:VerticalPanel>
                </td>
            </tr>
        </table>
    </g:HTMLPanel>


</ui:UiBinder>