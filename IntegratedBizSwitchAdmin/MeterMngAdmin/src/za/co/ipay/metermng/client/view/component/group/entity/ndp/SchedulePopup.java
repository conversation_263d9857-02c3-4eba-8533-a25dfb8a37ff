package za.co.ipay.metermng.client.view.component.group.entity.ndp;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class SchedulePopup extends BaseComponent {
    
    @UiField Label scheduleActiveLabel;
    @UiField(provided=true) SchedulePanel schedulePanel;
    
    private DialogBox simplePopup;
    
    private static SchedulePopupPanelUiBinder uiBinder = GWT.create(SchedulePopupPanelUiBinder.class);

    interface SchedulePopupPanelUiBinder extends UiBinder<Widget, SchedulePopup> {
    }
    
    public SchedulePopup(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        schedulePanel = new SchedulePanel(clientFactory, null, this);
        
        initWidget(uiBinder.createAndBindUi(this));
    }
    
    protected void populate(NdpScheduleData ndpScheduleData) {
        if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT)) {
            scheduleActiveLabel.setText("Schedule IS Active");
        } else {
            scheduleActiveLabel.setText("Schedule NOT Active");
            scheduleActiveLabel.addStyleName("error");
        }
        schedulePanel.display(ndpScheduleData);
    }
    
    protected void setHeight(int height) {
        if (height + 350 > Window.getClientHeight() ) {
            this.setHeight(Window.getClientHeight() - 350 + "px");
        }
    }
    
    public void show(int left, int top, String source, String parentname) {
        simplePopup = new DialogBox(true);
        String caption = MessagesUtil.getInstance().getMessage("groupthreshold.global.source.label");
        if (source.equals("parent")) {
            caption = MessagesUtil.getInstance().getMessage("groupthreshold.parent.source.label", new String[] {parentname});
        }

        simplePopup.setText(caption);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(this);
        simplePopup.setPopupPosition(left, top);
        simplePopup.show();
    }
}
