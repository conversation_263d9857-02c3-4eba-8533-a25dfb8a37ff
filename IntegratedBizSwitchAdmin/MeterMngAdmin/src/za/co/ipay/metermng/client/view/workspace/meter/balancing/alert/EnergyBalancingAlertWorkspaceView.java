package za.co.ipay.metermng.client.view.workspace.meter.balancing.alert;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormDisplayView;
import za.co.ipay.metermng.client.event.OpenMeterReadingsEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.EnergyBalancingAlertPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import com.google.gwt.cell.client.ClickableTextCell;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

/**
 * EnergyBalancingAlertWorkspaceView allows the user to specify a date range and a variation percent for super/sub meter
 * readings. Any readings within the date range above the variation percent are displayed.
 *
 * <AUTHOR>
 */
public class EnergyBalancingAlertWorkspaceView extends BaseWorkspace {

    @UiField SimpleFormDisplayView view;
    EnergyBalancingAlertPanel panel;
    CellTable<EnergyBalancingDto> table;
    TablePager pager;
    ListDataProvider<EnergyBalancingDto> dataProvider;
    private ArrayList<MeterReadingType> meterReadingTypes;

    private static Logger logger = Logger.getLogger(EnergyBalancingAlertWorkspaceView.class.getName());

    private static EnergyBalancingWorkspaceViewUiBinder uiBinder = GWT.create(EnergyBalancingWorkspaceViewUiBinder.class);

    interface EnergyBalancingWorkspaceViewUiBinder extends UiBinder<Widget, EnergyBalancingAlertWorkspaceView> {
    }

    public EnergyBalancingAlertWorkspaceView(ClientFactory clientFactory, EnergyBalancingAlertPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(EnergyBalancingAlertPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("energybalancing.title"));
        initUi();
    }

    private void initUi() {
        initForm();
        createTable();
    }

    private void initForm() {
        panel = new EnergyBalancingAlertPanel(clientFactory, view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.view"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onView();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            onClear();
                        }
                    }
                });
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("energybalancing.title"), "pageSectionTitle");

        loadMeterReadingTypes();
    }

    private void loadMeterReadingTypes() {
        clientFactory.getMeterRpc().getMeterReadingTypes(new ClientCallback<ArrayList<MeterReadingType>>() {
            @Override
            public void onSuccess(ArrayList<MeterReadingType> result) {
                meterReadingTypes = result;
                panel.setMeterReadingTypes(result);
            }
        });
    }

    private void onClear() {
        panel.clearErrors();
        panel.clearFields();
        dataProvider.getList().clear();
    }

    private void createTable() {
        if (dataProvider == null) {
            if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
                table = new CellTable<EnergyBalancingDto>(MeterMngStatics.DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
            } else {
                table = new CellTable<EnergyBalancingDto>();
            }
            table.addColumn(createSuperMeterColumn(), MessagesUtil.getInstance().getMessage("energybalancing.supermeter"));
            table.addColumn(createSuperMeterReadingColumn(), MessagesUtil.getInstance().getMessage("energybalancing.supermeter.reading"));
            table.addColumn(createSubMetersReadingColumn(), MessagesUtil.getInstance().getMessage("energybalancing.submeters.total"));
            table.addColumn(createVariationColumn(), MessagesUtil.getInstance().getMessage("energybalancing.variation"));
            Column<EnergyBalancingDto, String> buttonColumn = createViewGraphColumn();
            buttonColumn.setFieldUpdater(new FieldUpdater<EnergyBalancingDto, String>() {
                public void update(int index, EnergyBalancingDto object, String value) {
                    logger.info("Viewing graph for "+object.getSuperMeterNumber()+" "+panel.getStartDate()+" "+panel.getEndDate());
                    clientFactory.getEventBus().fireEvent(
                            new OpenMeterReadingsEvent(object.getSuperMeterNumber(),
                                                        MeterMngStatics.ENERGY_BALANCING_GRAPH_TYPE,
                                                        MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE,
                                                        panel.getStartDate(),
                                                        panel.getEndDate()));
                }
              });
            table.addColumn(buttonColumn, "");

            table.getColumn(1).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
            table.getColumn(2).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
            table.getColumn(3).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);

            table.setVisibleRange(0, getPageSize());

            dataProvider = new ListDataProvider<EnergyBalancingDto>();
            dataProvider.addDataDisplay(table);

            pager = new TablePager();
            pager.setDisplay(table);

            VerticalPanel tablePanel = new VerticalPanel();
            tablePanel.add(table);
            tablePanel.add(pager);
            view.getDisplayPanel().add(tablePanel);
        }
    }

    private Column<EnergyBalancingDto, ?> createSuperMeterColumn() {
        return new TextColumn<EnergyBalancingDto>() {
            @Override
            public String getValue(EnergyBalancingDto dto) {
                return dto.getSuperMeterNumber();
            }
        };
    }

    private Column<EnergyBalancingDto, ?> createSuperMeterReadingColumn() {
        return new TextColumn<EnergyBalancingDto>() {
            @Override
            public String getValue(EnergyBalancingDto dto) {
                return FormatUtil.getInstance().formatDecimal(dto.getSuperMeterReading().doubleValue() / 1000)+getUnits();
            }
        };
    }

    private Column<EnergyBalancingDto, ?> createSubMetersReadingColumn() {
        return new TextColumn<EnergyBalancingDto>() {
            @Override
            public String getValue(EnergyBalancingDto dto) {
                return FormatUtil.getInstance().formatDecimal(dto.getTotalSubMeters().doubleValue() / 1000)+getUnits();
            }
        };
    }

    private Column<EnergyBalancingDto, ?> createVariationColumn() {
        return new TextColumn<EnergyBalancingDto>() {
            @Override
            public String getValue(EnergyBalancingDto dto) {
                return FormatUtil.getInstance().formatDecimal(dto.getVariation())+MeterMngStatics.PERCENT_SIGN;
            }
        };
    }

    private String getUnits() {
        int index = panel.readingTypeBox.getSelectedIndex();
        if (index > 0) {
            String id = panel.readingTypeBox.getValue(index);
            if (meterReadingTypes != null) {
                for(MeterReadingType type : meterReadingTypes) {
                  if (type.getId().toString().equals(id)) {
                        return " " + MeterMngCommonUtil.getCorrectedUnitOfMeasure(type.getUnitOfMeasure());
                  }
                }
            }
        }
        return "";
    }

    private Column<EnergyBalancingDto, String> createViewGraphColumn() {
        ClickableTextCell cell = new ClickableTextCell() {
            @Override
            public void render(Context context, SafeHtml data, SafeHtmlBuilder sb) {
              sb.appendHtmlConstant("<a href=\"#\" class=\"gwt-Anchor\">");
              if (data != null) {
                sb.append(data);
              }
              sb.appendHtmlConstant("</a>");
            }
        };
        return new Column<EnergyBalancingDto, String>(cell) {
          @Override
          public String getValue(EnergyBalancingDto object) {
            return MessagesUtil.getInstance().getMessage("energybalancing.view.graph");
          }
        };
    }

    private void onView() {
        if (panel.isValidInput()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getMeterRpc().checkEnergyBalancingMeters(panel.getMeterReadingTypeId(),
                            panel.getStartDate(),
                            panel.getEndDate(),
                            panel.getPercent().doubleValue(),
                            new ClientCallback<ArrayList<EnergyBalancingDto>>() {
                                @Override
                                public void onSuccess(ArrayList<EnergyBalancingDto> result) {
                                    displayEnergyBalancing(result);
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void displayEnergyBalancing(List<EnergyBalancingDto> data) {
        dataProvider.getList().clear();
        if (data.isEmpty()) {
          Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("energybalancing.none"), MediaResourceUtil.getInstance().getInformationIcon());
        } else {
            dataProvider.getList().addAll(data);
        }
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onArrival(Place place) {
    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof EnergyBalancingAlertPlace);
    }
}
