package za.co.ipay.metermng.client.view.workspace.dashboard;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.DashboardPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.dashboard.DashPanel;
import za.co.ipay.metermng.client.view.component.dashboard.DashPanelFactory;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;

public class DashboardWorkspaceView extends BaseWorkspace {

	@UiField 
	PageHeader pageHeader;
	@UiField
	FlowPanel dpanel1;
	@UiField
	FlowPanel dpanel2;
	@UiField
	FlowPanel dpanel3;
	@UiField
	FlowPanel dpanel4;
	@UiField
	FlowPanel dpanel5;
	@UiField
	FlowPanel dpanel6;
	
    private ArrayList<DashPanel> dashPanels = new ArrayList<>();
	private  ArrayList<FlowPanel> dPanels = new ArrayList<>();
    
    private Logger logger = Logger.getLogger("DashboardWorkspaceView");
        
    private static DashboardUiBinder uiBinder = GWT.create(DashboardUiBinder.class);

    interface DashboardUiBinder extends UiBinder<Widget, DashboardWorkspaceView> {
    }

    public DashboardWorkspaceView() {
        initWidget(uiBinder.createAndBindUi(this));
    }
    
    public static final class DashboardWorkspaceFactory implements WorkspaceFactory {
        private ClientFactory clientFactory;

        public DashboardWorkspaceFactory(ClientFactory clientFactory) {
            this.clientFactory = clientFactory;
            clientFactory.getWorkspaceContainer().register(this);
        }

        @Override
        public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        	
			if (!clientFactory.getUser().hasPermission(
					MeterMngStatics.ACCESS_PERMISSION_MM_DASHBOARD_VIEW)) {
				Dialogs.displayErrorMessage(MessagesUtil.getInstance()
						.getMessage("error.accessdenied"), MediaResourceUtil
						.getInstance().getLockedIcon(), MessagesUtil
						.getInstance().getMessage("button.close"));
				workspaceCreateCallback
						.onWorkspaceCreationFailed(new AccessControlException(
								"Access is Denied"));
				return;
			}
            try {
                DashboardWorkspaceView dashboardWorkspaceView = new DashboardWorkspaceView(clientFactory, (DashboardPlace) place);
                workspaceCreateCallback.onWorkspaceCreated(dashboardWorkspaceView);
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        }

        @Override
        public boolean handles(Place place) {
            return place instanceof DashboardPlace;
        }

    }
    
    public DashboardWorkspaceView(ClientFactory clientFactory, DashboardPlace place) {
        this.clientFactory = clientFactory;  
        
        initWidget(uiBinder.createAndBindUi(this));
        
		dPanels.add(dpanel1);
		dPanels.add(dpanel2);
		dPanels.add(dpanel3);
		dPanels.add(dpanel4);
		dPanels.add(dpanel5);
		dPanels.add(dpanel6);
		
		createPanels();
		
        setPlaceString("dashboard:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("dashboard.title"));
		pageHeader.setHeading(MessagesUtil.getInstance().getMessage(
				"dashboard.title"));
    }
    
	@Override
    public void onLeaving() {
    }

    @Override
    public void onSelect() {
    	refreshDashPanels();
    }

	@Override
    public void onArrival(Place place) {
    	refreshDashPanels();
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof DashboardPlace;
    }
    
    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("DashBoardWorkSpaceView Received notification: "+notification);
        
        if (MeterMngStatics.USER_CURRENT_GROUP.equals(notification.getDataType())
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            logger.info("The user's current group has changed - reloading this DashBoard workspace... ");
        	refreshDashPanels();
        	
        } else if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(notification.getDataType())
				&& NotificationType.DATA_UPDATED == notification.getNotificationType()) {
						// Redraw all panels if a related appsetting is changed.
			String notificationObjAsString = (String) notification.getObject();
			
			if (notificationObjAsString.toLowerCase().contains("dashboard.panel.list") 
					||	notificationObjAsString.toLowerCase().contains("dashboard.upgroup.added")) {
				redrawAllPanels();
			}
        }
    }
    
	private void createPanels() {
		
		clientFactory.getAppSettingRpc().getAppSettingByKey(MeterMngStatics.DASHBOARD_PANEL_LIST, new ClientCallback<AppSetting>() {
					@Override
					public void onSuccess(AppSetting result) {
						String[] temp = result.getValue().split(",");
						int count = 0;
						for (FlowPanel fPan : dPanels) {
							boolean panelAdded = false;
							while (panelAdded == false && count < temp.length) {
								String panelName = temp[count++];
								DashPanel dashPanel = DashPanelFactory.getDashPanel(panelName, clientFactory);
								if (dashPanel != null) {
									fPan.add(dashPanel);
									dashPanels.add(dashPanel);
									panelAdded = true;
								}
							}
						}
					}
				});
	}
    
    private void refreshDashPanels() {
		for (DashPanel dPan : dashPanels) {
			if (dPan != null) {
				dPan.refresh();
			}
		}
	}
   
	private void redrawAllPanels(){
		for (FlowPanel fPan : dPanels) {
			fPan.clear();
		}

		dashPanels.clear();
		createPanels();
	}
}
