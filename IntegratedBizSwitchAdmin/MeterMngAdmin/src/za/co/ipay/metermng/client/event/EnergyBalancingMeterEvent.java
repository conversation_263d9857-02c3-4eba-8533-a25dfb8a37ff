package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class EnergyBalancingMeterEvent extends GwtEvent<EnergyBalancingMeterEventHandler> {

    public static Type<EnergyBalancingMeterEventHandler> TYPE = new Type<EnergyBalancingMeterEventHandler>();
    
    private String meter;
    
    public EnergyBalancingMeterEvent(String meter) {
        this.meter = meter;
    }

    public String getMeter() {
        return meter;
    }

    @Override
    public Type<EnergyBalancingMeterEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(EnergyBalancingMeterEventHandler handler) {
        handler.handleEvent(this);
    }
}
