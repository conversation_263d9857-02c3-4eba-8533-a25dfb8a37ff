<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="superMeterElement" labelText="{msg.getTaskScheduleSuperMeter}:" required="true">
                <g:ListBox styleName="gwt-TextBox" ui:field="superMeterBox" title="{msg.getTaskScheduleSuperMeter}" />
            </p1:FormElement> 

            <p1:FormElement ui:field="meterReadingTypeElement" labelText="{msg.getTaskScheduleMeterReadingType}:" required="true">
                <g:ListBox ui:field="meterReadingTypeBox" title="{msg.getTaskScheduleMeterReadingType}" />
            </p1:FormElement> 

            <p1:FormElement ui:field="numberElement" labelText="{msg.getTaskScheduleEvery}" required="true">
                <p1:IntegerValueBox ui:field="numberBox" title="{msg.getTaskScheduleEvery}" styleName="gwt-TextBox" width="40px" />
            </p1:FormElement>
            <p1:FormElement ui:field="unitsElement" labelText="{msg.getTaskScheduleOf}" required="true">
                <g:ListBox ui:field="unitsBox" title="" />
            </p1:FormElement>

            <p1:FormElement ui:field="varianceElement" labelText="{msg.getEnergyBalancingVariation}:" required="true">
                <g:HorizontalPanel>
                    <w:PercentageTextBox ui:field="varianceBox" visibleLength="2" />
                </g:HorizontalPanel>
            </p1:FormElement>   
        </p1:FormRowPanel>
        
    </g:FlowPanel>    

</ui:UiBinder> 