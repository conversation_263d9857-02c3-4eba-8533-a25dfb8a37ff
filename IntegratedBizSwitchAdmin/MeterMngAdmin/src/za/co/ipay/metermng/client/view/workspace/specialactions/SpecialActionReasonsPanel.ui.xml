<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
xmlns:g="urn:import:com.google.gwt.user.client.ui"
xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component">
  <ui:style>
    
  </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

  <g:FlowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getSpecialActionReasonsName}:" helpMsg="{msg.getSpecialActionReasonsNameHelp}"
        required="true">
        <g:TextBox text="" ui:field="nameTextBox" debugId="nameTextBox" title="{msg.getSpecialActionReasonsName}" />
      </p1:FormElement>
      <p1:FormElement ui:field="descriptionElement" debugId="descriptionElement" labelText="{msg.getSpecialActionReasonsDescription}:" helpMsg="{msg.getSpecialActionReasonsDescriptionHelp}">
        <g:TextBox ui:field="descriptionTextBox" debugId="descriptionTextBox" title="{msg.getSpecialActionReasonsDescription}" />
      </p1:FormElement>
      <ipaycomp:MridComponent ui:field="mridComponent"></ipaycomp:MridComponent>
      <p1:FormElement ui:field="activeElement" labelText="{msg.getSpecialActionReasonsActive}:" helpMsg="{msg.getSpecialActionReasonsActiveHelp}">
        <g:CheckBox ui:field="activeBox" debugId="activeBox" />
      </p1:FormElement>
    </p1:FormRowPanel>
  </g:FlowPanel>

</ui:UiBinder> 