package za.co.ipay.metermng.client.view.component.importfile;

import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.client.ui.Button;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public interface IFileTypeHelper {

    void setClientFactory(ClientFactory clientFactory);
    void setExtractBtn(Button extractBtn, boolean isExtractAvailable);
    void setSpareBtn(Button spareBtn);
    void generateCsvDownload(Logger logger, ImportFileDto importFileDto);
    void handleSpareBtn(Logger logger, ImportFileDto importFileDto);
    void setParent(ImportFileItemView importFileItemView);
    ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox();
    void setRegReadingsMsgVisible(FileDetailPanel fileDetailPanel);
    
    //ImportFileItemView table columns
    String getMeterColumnValue(ImportFileItemDto object);
    String getUsagePointColumnValue(ImportFileItemDto object);
    String getAgrRefColumnValue(ImportFileItemDto object);
    String getChannelValueColumnValue(ImportFileItemDto object);
    String getReadingTimeStampColumnValue(ImportFileItemDto object);
    String getPricingStructureNameColumnValue(ImportFileItemDto object);
    String getTariffNameColumnValue(ImportFileItemDto object);
    void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap);
    
    String getWaitingText();
}
