package za.co.ipay.metermng.client.view.component.usagepoint;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.MultipleViewsFormPanel;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.meter.TransactionChart;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;

import java.util.logging.Logger;

public class UsagePointTransactionViews extends BaseComponent {

    @UiField
    MultipleViewsFormPanel multipleViewsForm;

    private TransactionChart graphPanel;
    private UsagePointTransactionView tablePanel;
    private int tabLayoutPanelWidth;
    private int tabLayoutPanelHeight;

    private UsagePointWorkspaceView usagePointWorkspaceView;
    private boolean viewConstructed = false;

    private static Logger logger = Logger.getLogger(UsagePointTransactionViews.class.getName());

    private static UsagePointTransactionHistoryViewsUiBinder uiBinder = GWT.create(UsagePointTransactionHistoryViewsUiBinder.class);

    interface UsagePointTransactionHistoryViewsUiBinder extends UiBinder<Widget, UsagePointTransactionViews> {
    }

    public UsagePointTransactionViews(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        initWidget(uiBinder.createAndBindUi(this));
        resizeTabPanel();
        initViews();
        logger.info("Created UsagePointTransactionViews");
    }

    public void resizeTabPanel() {
        tabLayoutPanelWidth = Window.getClientWidth() - (Window.getClientWidth() / 5);
        tabLayoutPanelHeight = 650;
        multipleViewsForm.getTabLayoutPanel().setWidth(tabLayoutPanelWidth + "px");
        multipleViewsForm.getTabLayoutPanel().setHeight(tabLayoutPanelHeight + "px");
    }

    private void initViews() {
        initHeader();
        initForm();
        initGraphUi();
        initReportUi();
        multipleViewsForm.getTabLayoutPanel().selectTab(1, true);
        
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initHeader() {
        multipleViewsForm.removeHeader();
    }

    private void initForm() {
        multipleViewsForm.getForm().removeFromParent();
    }

    private void initGraphUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.graph"));
        graphPanel = new TransactionChart(clientFactory, getTabLayoutPanelWidth(), getTabLayoutPanelHeight());
        ScrollPanel scroll = new ScrollPanel(graphPanel);
        scroll.addStyleName("multipleView");
        multipleViewsForm.getTabLayoutPanel().add(scroll, tab);
    }

    private void initReportUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.table"));
        tablePanel = new UsagePointTransactionView(clientFactory, usagePointWorkspaceView);
        multipleViewsForm.getTabLayoutPanel().add(tablePanel, tab);
    }

    public UsagePointTransactionView getTablePanel() {
        return tablePanel;
    }

    public int getTabLayoutPanelWidth() {
        return tabLayoutPanelWidth;
    }

    public int getTabLayoutPanelHeight() {
        return tabLayoutPanelHeight;
    }

    public TransactionChart getGraphPanel() {
        return graphPanel;
    }
}
