package za.co.ipay.metermng.client.view.component.mdc;

import java.util.ArrayList;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MdcTransData;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

public class MdcTransactionItemsWidget extends BaseComponent{

    @UiField CellTable<LabelValue> clltbltransitems;

    private DialogBox simplePopup;
    private ListDataProvider<LabelValue> dataProvider = new ListDataProvider<LabelValue>();
    
    private static MdcTransactionItemsWidgetUiBinder uiBinder = GWT.create(MdcTransactionItemsWidgetUiBinder.class);
    
    interface MdcTransactionItemsWidgetUiBinder extends UiBinder<Widget, MdcTransactionItemsWidget> {
    }

    public MdcTransactionItemsWidget(Widget parent) {
	initWidget(uiBinder.createAndBindUi(this));
	setupTable();
    }
    
    protected void setupTable() {
         AbstractCell<String> labelCell = new AbstractCell<String>() { 
            @Override
            public void render(Context context, String value, SafeHtmlBuilder sb) {
                sb.appendHtmlConstant("<span class=\"formElement_label\">" + value + "</span>");
            }
        }; 
        Column<LabelValue, String> labelColumn = new Column<LabelValue, String>(labelCell) {
            @Override
            public String getValue(LabelValue data) {
                return data.getLbl();         
            }
        };
        
        TextColumn<LabelValue> valueColumn = new TextColumn<LabelValue>() {
            @Override
            public String getValue(LabelValue data) {
                return data.getValue();
            }
        };

        
        clltbltransitems.addColumn(labelColumn, MessagesUtil.getInstance().getMessage("mdc.txn.popup.label"));
        clltbltransitems.addColumn(valueColumn, MessagesUtil.getInstance().getMessage("mdc.txn.popup.value"));
         
        dataProvider.addDataDisplay(clltbltransitems);
    }
    
    public void setMdcTransactionList(MdcTransData thedata) {
        dataProvider.setList(buildFieldList(thedata));
    }
    
    private ArrayList<LabelValue> buildFieldList(MdcTransData data) {
        ArrayList<LabelValue> list = new ArrayList<LabelValue>();
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.client"), data.getClient()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.term"), data.getTerminal()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.meter"), data.getMeterNum()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.usagepoint"), data.getUsagePointName()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.customer"), data.getCustName()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.identifier"), data.getIdentifier()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.identifiertype"), data.getIdentifierType()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.reqtype"), data.getReqType()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.override"), data.getOverride()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.controltype"), data.getControltype()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.params"), data.getParams()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.scheduledate"), FormatUtil.getInstance().formatDateTime(data.getScheduleDate())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.cmdaccrec"), FormatUtil.getInstance().formatDateTime(data.getCmdAcceptedReceived())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.repcount"), Integer.toString(data.getRepCount())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.reqsent"), FormatUtil.getInstance().formatDateTime(data.getReqSent())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.refreceived"), data.getRefReceived()));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.reqreceived"), FormatUtil.getInstance().formatDateTime(data.getReqReceived())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.recdate"), FormatUtil.getInstance().formatDateTime(data.getResReceived())));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.rescodereceived"), data.getResCodeReceived()));
        
        String str = "";
        if (data.isSuccessful()) {
            str = MessagesUtil.getInstance().getMessage("option.positive");
        } else {
            str = MessagesUtil.getInstance().getMessage("option.negative");
        }
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.success"), str));
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.timecompld"), FormatUtil.getInstance().formatDateTime(data.getTimeCompleted())));
        
        String status = "";
        if (data.getTimeCompleted() == null) {
            status = MessagesUtil.getInstance().getMessage("mdc.txn.pending");
        } else if (data.getReqSent() == null) {
            status = MessagesUtil.getInstance().getMessage("mdc.txn.discarded");
        } else if (data.isSuccessful()) {
            status = MessagesUtil.getInstance().getMessage("mdc.txn.successful");
        } else {
            status = MessagesUtil.getInstance().getMessage("mdc.txn.failed");
        }
        list.add(new LabelValue(MessagesUtil.getInstance().getMessage("mdc.txn.status"), status));

        return list;
    }
    
    public void show(int left, int top) {
    	simplePopup = new DialogBox(true);
    	simplePopup.setText(MessagesUtil.getInstance().getMessage("mdc.txn.message"));
        simplePopup.setAnimationEnabled(true);
		simplePopup.setWidget(this);
    	simplePopup.setPopupPosition(left, top);
		simplePopup.show();
    }
    
    private class LabelValue {
        private String lbl;
        private String value; 
        
        public LabelValue(String lbl, String value) {
            this.lbl = lbl;
            this.value = value;
        }
        
        public String getLbl() {
            return lbl;
        }

        public String getValue() {
            return value;
        }

    }
}
