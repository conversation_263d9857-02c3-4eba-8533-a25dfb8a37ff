package za.co.ipay.metermng.client.view.workspace.meter.readings.add.singlemeter;

import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;

import com.google.gwt.place.shared.Place;

public class AddMeterReadingsWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;

    public AddMeterReadingsWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        try {
            AddMeterReadingsWorkspaceView view = new AddMeterReadingsWorkspaceView(clientFactory, (AddMeterReadingsPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof AddMeterReadingsPlace) {
            AddMeterReadingsPlace p = (AddMeterReadingsPlace) place;
            if (AddMeterReadingsPlace.SINGLE_METER_TYPE.equals(p.getMeterType())) {
                return true;
            }
        }
        return false;
    }
}
