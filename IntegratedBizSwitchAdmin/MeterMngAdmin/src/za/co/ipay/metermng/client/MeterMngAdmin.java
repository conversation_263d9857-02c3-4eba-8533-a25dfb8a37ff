package za.co.ipay.metermng.client;

import com.google.gwt.activity.shared.ActivityManager;
import com.google.gwt.ajaxloader.client.AjaxLoader;
import com.google.gwt.ajaxloader.client.AjaxLoader.AjaxLoaderOptions;
import com.google.gwt.core.client.EntryPoint;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.GWT.UncaughtExceptionHandler;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.debug.client.DebugInfo;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.maps.client.LoadApi.LoadLibrary;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.place.shared.PlaceHistoryHandler;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.RootLayoutPanel;
import org.moxieapps.gwt.highcharts.client.Global;
import org.moxieapps.gwt.highcharts.client.Highcharts;
import org.moxieapps.gwt.highcharts.client.Lang;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.workspace.SimpleWorkspaceOpenCallbackImpl;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.client.workspace.WorkspaceOpenCallback;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.WorkspaceContainerException;
import za.co.ipay.metermng.client.event.AddMeterReadingsEvent;
import za.co.ipay.metermng.client.event.AddMeterReadingsEventHandler;
import za.co.ipay.metermng.client.event.AppSettingEvent;
import za.co.ipay.metermng.client.event.AppSettingEventHandler;
import za.co.ipay.metermng.client.event.AuxAccountUploadEvent;
import za.co.ipay.metermng.client.event.AuxAccountUploadEventHandler;
import za.co.ipay.metermng.client.event.AuxTransUploadEvent;
import za.co.ipay.metermng.client.event.AuxTransUploadEventHandler;
import za.co.ipay.metermng.client.event.BillingDetEvent;
import za.co.ipay.metermng.client.event.BillingDetEventHandler;
import za.co.ipay.metermng.client.event.ChangePasswordEvent;
import za.co.ipay.metermng.client.event.ChangePasswordEventHandler;
import za.co.ipay.metermng.client.event.CustomerSearchEvent;
import za.co.ipay.metermng.client.event.CustomerSearchEventHandler;
import za.co.ipay.metermng.client.event.CustomerTransUploadEvent;
import za.co.ipay.metermng.client.event.CustomerTransUploadEventHandler;
import za.co.ipay.metermng.client.event.EnergyBalancingMeterEvent;
import za.co.ipay.metermng.client.event.EnergyBalancingMeterEventHandler;
import za.co.ipay.metermng.client.event.FeedbackMessageEvent;
import za.co.ipay.metermng.client.event.FeedbackMessageEventHandler;
import za.co.ipay.metermng.client.event.GlobalNdpEvent;
import za.co.ipay.metermng.client.event.GlobalNdpEventHandler;
import za.co.ipay.metermng.client.event.ImportFileEvent;
import za.co.ipay.metermng.client.event.ImportFileEventHandler;
import za.co.ipay.metermng.client.event.ManufacturerEvent;
import za.co.ipay.metermng.client.event.ManufacturerEventHandler;
import za.co.ipay.metermng.client.event.MdcEvent;
import za.co.ipay.metermng.client.event.MdcEventHandler;
import za.co.ipay.metermng.client.event.MetadataUploadEvent;
import za.co.ipay.metermng.client.event.MetadataUploadEventHandler;
import za.co.ipay.metermng.client.event.MeterBulkUploadEvent;
import za.co.ipay.metermng.client.event.MeterBulkUploadEventHandler;
import za.co.ipay.metermng.client.event.MeterCustUPUploadEvent;
import za.co.ipay.metermng.client.event.MeterCustUPUploadEventHandler;
import za.co.ipay.metermng.client.event.MeterModelEvent;
import za.co.ipay.metermng.client.event.MeterModelEventHandler;
import za.co.ipay.metermng.client.event.MeterOnlineBulkEvent;
import za.co.ipay.metermng.client.event.MeterOnlineBulkEventHandler;
import za.co.ipay.metermng.client.event.MeterSearchEvent;
import za.co.ipay.metermng.client.event.MeterSearchEventHandler;
import za.co.ipay.metermng.client.event.OpenAdminDashboardEvent;
import za.co.ipay.metermng.client.event.OpenAdminDashboardEventHandler;
import za.co.ipay.metermng.client.event.OpenAuxChargeStructureEvent;
import za.co.ipay.metermng.client.event.OpenAuxChargeStructureEventHandler;
import za.co.ipay.metermng.client.event.OpenAuxTypeEvent;
import za.co.ipay.metermng.client.event.OpenAuxTypeEventHandler;
import za.co.ipay.metermng.client.event.OpenBlockingTypeEvent;
import za.co.ipay.metermng.client.event.OpenBlockingTypeEventHandler;
import za.co.ipay.metermng.client.event.OpenCalendarEvent;
import za.co.ipay.metermng.client.event.OpenCalendarEventHandler;
import za.co.ipay.metermng.client.event.OpenDashboardEvent;
import za.co.ipay.metermng.client.event.OpenDashboardEventHandler;
import za.co.ipay.metermng.client.event.OpenDeviceStoreEvent;
import za.co.ipay.metermng.client.event.OpenDeviceStoreEventHandler;
import za.co.ipay.metermng.client.event.OpenDisplayTokensEvent;
import za.co.ipay.metermng.client.event.OpenDisplayTokensEventHandler;
import za.co.ipay.metermng.client.event.OpenEnergyBalancingEvent;
import za.co.ipay.metermng.client.event.OpenEnergyBalancingEventHandler;
import za.co.ipay.metermng.client.event.OpenGroupEvent;
import za.co.ipay.metermng.client.event.OpenGroupEventHandler;
import za.co.ipay.metermng.client.event.OpenMeterReadingsEvent;
import za.co.ipay.metermng.client.event.OpenMeterReadingsEventHandler;
import za.co.ipay.metermng.client.event.OpenPricingStructureEvent;
import za.co.ipay.metermng.client.event.OpenPricingStructureEventHandler;
import za.co.ipay.metermng.client.event.OpenSupplyGroupEvent;
import za.co.ipay.metermng.client.event.OpenSupplyGroupEventHandler;
import za.co.ipay.metermng.client.event.OpenUsagePointEvent;
import za.co.ipay.metermng.client.event.OpenUsagePointEventHandler;
import za.co.ipay.metermng.client.event.OpenUserGroupEvent;
import za.co.ipay.metermng.client.event.OpenUserGroupEventHandler;
import za.co.ipay.metermng.client.event.SearchEvent;
import za.co.ipay.metermng.client.event.SearchEventHandler;
import za.co.ipay.metermng.client.event.SelectAccessGroupEvent;
import za.co.ipay.metermng.client.event.SelectAccessGroupEventHandler;
import za.co.ipay.metermng.client.event.SpecialActionsEvent;
import za.co.ipay.metermng.client.event.SpecialActionsEventHandler;
import za.co.ipay.metermng.client.event.TaskScheduleEvent;
import za.co.ipay.metermng.client.event.TaskScheduleEventHandler;
import za.co.ipay.metermng.client.event.UsagePointSearchEvent;
import za.co.ipay.metermng.client.event.UsagePointSearchEventHandler;
import za.co.ipay.metermng.client.event.UserInterfaceEvent;
import za.co.ipay.metermng.client.event.UserInterfaceEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.factory.ClientFactoryImpl;
import za.co.ipay.metermng.client.history.AbstractUsagePointPlace;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;
import za.co.ipay.metermng.client.history.AdminDashboardPlace;
import za.co.ipay.metermng.client.history.AppSettingPlace;
import za.co.ipay.metermng.client.history.AuxAccountUploadPlace;
import za.co.ipay.metermng.client.history.AuxChargeSchedulePlace;
import za.co.ipay.metermng.client.history.AuxTransUploadPlace;
import za.co.ipay.metermng.client.history.AuxTypePlace;
import za.co.ipay.metermng.client.history.BillingDetPlace;
import za.co.ipay.metermng.client.history.BlockingTypePlace;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.history.ChangePasswordPlace;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.CustomerTransUploadPlace;
import za.co.ipay.metermng.client.history.DashboardPlace;
import za.co.ipay.metermng.client.history.DeviceStorePlace;
import za.co.ipay.metermng.client.history.DisplayTokensPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingAlertPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingMeterPlace;
import za.co.ipay.metermng.client.history.GlobalNdpPlace;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.history.ImportFilePlace;
import za.co.ipay.metermng.client.history.ManufacturerPlace;
import za.co.ipay.metermng.client.history.MdcPlace;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.history.MeterBulkUploadPlace;
import za.co.ipay.metermng.client.history.MeterCustUPUploadPlace;
import za.co.ipay.metermng.client.history.MeterModelPlace;
import za.co.ipay.metermng.client.history.MeterOnlineBulkPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.history.SelectAccessGroupPlace;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.history.SupplyGroupPlace;
import za.co.ipay.metermng.client.history.TaskSchedulePlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.history.UserGroupPlace;
import za.co.ipay.metermng.client.history.UserInterfacePlace;
import za.co.ipay.metermng.client.history.mapper.PlaceToActivityMapper;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.exception.CustomerNotFoundException;
import za.co.ipay.metermng.shared.exception.MeterNotFoundException;
import za.co.ipay.metermng.shared.exception.UsagePointNotFoundException;
import za.co.ipay.metermng.shared.util.MeterMngConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * MeterMngAdmin is the main client-side class for the meter mng application. It sets up the application for use.
 */
public class MeterMngAdmin implements EntryPoint {
    
    private Logger logger = Logger.getLogger(MeterMngAdmin.class.getName());
    
    public static  String RELEASE = "Development";
    public static final String GOOGLE_MAPS_VERSION = "3";

    private static ClientFactory clientFactory;
    private static String mancoLogo;
    
    public interface LoadedHandler {
        public void loaded(MeterMngConfig config);
    }
    
    @Override
    public void onModuleLoad() {
        DebugInfo.setDebugIdPrefix(""); //no prefix used for debug ids 
        GWT.setUncaughtExceptionHandler(new UncaughtExceptionHandler() {
            @Override
            public void onUncaughtException(Throwable e) {
                if (!(e instanceof ValidationException)) {
                    logger.log(Level.SEVERE, "MeterMngAdmin onUncaughtException:", e);
                } else {
                    logger.warning("Caught validation exception: " + e.toString());
                }
            }
        });
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {
            @Override
            public void execute() {
                loadUi();
            }
        });
    }
    
    private void loadUi() {
        final PopupPanel waitDialog = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon());

        //Create the client factory
        clientFactory = GWT.create(ClientFactoryImpl.class);
        
        //Set any locale from the hidden field on the JSP - this allows the browser's Locale to be used, not GWT Locale
        setClientFactoryLocale();

        //Load the config and messages from the server and then create the UI
        clientFactory.loadConfigAndMessages(
                new LoadedHandler() {
                    @Override
                    public void loaded(final MeterMngConfig config) {
                        // TODO retrieve user with the MeterMngAppData to reduce the number 
                        // of calls
                        clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
                            @Override
                            public void onSuccess(final MeterMngUser user) {                                
                                Runnable runnable = new Runnable() {
                                    @Override
                                    public void run() {
                                        mancoLogo = config.getLogoUrl();
                                        createUi(clientFactory, config, user);
                                        waitDialog.hide();
                                    }
                                };
                                runnable.run();
                            }
                        });
                    }
                });
    }
    
    private void setClientFactoryLocale() {
        Element element = DOM.getElementById("reqLocale");
        if (element != null) {
            String localeString = element.getPropertyString("value");
            if (localeString != null && !localeString.trim().equals("")) {
                clientFactory.setLocaleName(localeString);
                logger.info("Got locale from hidden JSP field: "+localeString);
            }
        }
    }
    
    private void setCss(String cssUrl) {
        Element element = DOM.getElementById("cssLink");
        if (element != null) {
            element.setAttribute("href", cssUrl);
            logger.info("Set cssUrl: "+cssUrl);
        }
    }
    
    private void createUi(final ClientFactory clientFactory, MeterMngConfig config, MeterMngUser user) {
        //Set the current user
        clientFactory.setUser(user);
        
        //TODO We should probably be calling all app settings in one go as part of the 
        // MeterMngAppData call and caching them. The below list will grow and we have 
        // too many places in app doing these calls. It could have an expiry as well 
        // to refresh it periodically, but this needs thought because just changing the 
        // UI while a user is busy is not great usability. Obviously settings changes 
        // by the user themselves should propagate. Ideally a central place.
        // The idea with MeterMngConfig was to only start the UI once essential config 
        // is retrieved, now we have essential app settings that need to be incorporated 
        // because the following call to app settings might fail
        // Ideally we don't have individual methods on client factory but a settings object
        // retrievable from it.
        List<String> keys = Arrays.asList(AppSettings.MULTI_USAGE_POINT_ENABLE, AppSettings.MULTI_CUST_AGR_ENABLE, AppSettings.GROUPS_TREE_DISPLAY_SIZE, AppSettings.GROUPS_SUGGEST_BOX_DISPLAY_SIZE);
        clientFactory.getAppSettingRpc().getAppSettingsByKeys(keys, new ClientCallback<Map<String,AppSetting>>() {
            @Override
            public void onSuccess(Map<String, AppSetting> result) {
                AppSetting appSetting = result.get(AppSettings.MULTI_USAGE_POINT_ENABLE);
                if (appSetting != null && appSetting.getValue().equalsIgnoreCase("true")) {
                    clientFactory.setEnableMultiUp(true);         //default is false
                }
                appSetting = result.get(AppSettings.MULTI_CUST_AGR_ENABLE);
                if (appSetting != null && appSetting.getValue().equalsIgnoreCase("true")) {
                    clientFactory.setEnableMultiCustAgr(true);         //default is false
                }
                appSetting = result.get(AppSettings.GROUPS_TREE_DISPLAY_SIZE);
                String value = null;
                if(appSetting != null) {
                    value = appSetting.getValue();
                }
                clientFactory.setGroupsTreeDisplaySize(AppSettings.toRequiredInteger(value, AppSettings.GROUPS_TREE_DISPLAY_SIZE_DEFAULT));
                
                appSetting = result.get(AppSettings.GROUPS_SUGGEST_BOX_DISPLAY_SIZE);
                value = null;
                if(appSetting != null) {
                    value = appSetting.getValue();
                }
                clientFactory.setGroupsSuggestBoxDisplaySize(AppSettings.toRequiredInteger(value, AppSettings.GROUPS_SUGGEST_BOX_DISPLAY_SIZE_DEFAULT));
            }
            
        });

        //Create the UI        
        setCss(config.getCssUrl());
        clientFactory.setEnableSTS(config.isEnableSTS());
        clientFactory.setLogoUrl(config.getLogoUrl());
        clientFactory.setUseMancoLogo(config.isUseMancoLogo());
        clientFactory.setDemoMode(config.isDemoMode());
        clientFactory.setEnableCentianSTS(config.isEnableCentianSTS());
        clientFactory.setAllowReversalsLastTrans(config.isAllowReversalsLastTrans());
        clientFactory.setAllowReversalsOlderTrans(config.isAllowReversalsOlderTrans());
        clientFactory.setEnableNonBillable(config.isEnableNonBillable());
        clientFactory.setEnableAccessGroups(config.isEnableAccessGroups());
        clientFactory.createViews();     
        Window.setTitle(MessagesUtil.getInstance().getMessage("application.default.title"));  
        
        //Place Controller
        PlaceController placeController = clientFactory.getPlaceController();
        
        // Start ActivityManager
        PlaceToActivityMapper activityMapper = new PlaceToActivityMapper(clientFactory);
        ActivityManager activityManager = new ActivityManager(activityMapper, clientFactory.getEventBus());
        activityManager.setDisplay(clientFactory.getWorkspaceContainer().asAcceptsOneWidget());

        // Start PlaceHistoryHandler
        PlaceHistoryMapper historyMapper = clientFactory.getPlaceHistoryMapper();
        PlaceHistoryHandler historyHandler = new PlaceHistoryHandler(historyMapper);
        
        Place startPlace;
        if (clientFactory.getUser().hasPermission(
        		MeterMngStatics.ACCESS_PERMISSION_MM_DASHBOARD_VIEW)){
        	startPlace = new DashboardPlace();
        } else if (clientFactory.getUser().hasPermission(
        		MeterMngStatics.ACCESS_PERMISSION_MM_ADMIN_DASHBOARD_VIEW)){
        	 startPlace = new AdminDashboardPlace();
        } else {
        	 startPlace = SearchPlace.ADVANCED_SEARCH_PLACE;
        }
        
        //Does the user need to update their password?
        if (clientFactory.getUser().isPasswordExpired() || clientFactory.getUser().isPasswordRequiresReset()) {
            startPlace = new ChangePasswordPlace(ChangePasswordPlace.LOGGED_IN_USER);
        } else if (clientFactory.getUser().isChooseCurrentGroupleafNode()) {
            startPlace = new SelectAccessGroupPlace();
        }
        
        historyHandler.register(placeController, clientFactory.getEventBus(), startPlace);                
        addHandlers(clientFactory);

        RootLayoutPanel rp = RootLayoutPanel.get();
        
        rp.add(clientFactory.getPrimaryLayoutView());
        rp.forceLayout();

        // Load Googlemaps API
        loadGoogleMapsApi(config.getGoogleMapsKey());
        
        //High charts global options
        Highcharts.Options options = new Highcharts.Options();
        options.setGlobal(new Global().setUseUTC(false));
        String abbrevMonthsString = MessagesUtil.getInstance().getMessage("moxiechart.abbrev.month.categories");
        String[] abbrevMonthArray = abbrevMonthsString.split(",");
        options.setLang(new Lang().setShortMonths(abbrevMonthArray));
        Highcharts.setOptions(options);
        
        //Go to the first place
        historyHandler.handleCurrentHistory();
        
        //Display warning for expiring password
        if (clientFactory.getUser().getPasswordWarningMsg() != null) {
            String warning = MessagesUtil.getInstance().getMessage(
                                                    clientFactory.getUser().getPasswordWarningMsg().getMessage(), 
                                                    clientFactory.getUser().getPasswordWarningMsg().getArgs());
            Dialogs.displayWarningMessage(warning, MediaResourceUtil.getInstance().getWarningIcon());
        }
        
        // TODO retrieve this as part of MeterMngAppData
        //set timezone
        clientFactory.getMeterMngAppRpc().getServerTimeZoneJson(new ClientCallback<String>() {
            @Override
            public void onSuccess(String result) {
                if (result != null) {
                    FormatUtil.getInstance().setTimeZone("default", result);
                    logger.info("Set Server Timezone: "+result);
                }
            }
        });
    }

    // Initialize the map features
    public static void loadGoogleMapsApi(String googleMapsKey) {
        ArrayList<LoadLibrary> loadLibraries = new ArrayList<>();
        loadLibraries.add(LoadLibrary.DRAWING);
        loadLibraries.add(LoadLibrary.GEOMETRY);
        loadLibraries.add(LoadLibrary.PLACES);
        loadLibraries.add(LoadLibrary.VISUALIZATION);

        Runnable onLoad = new Runnable() {
            @Override
            public void run() {
                clientFactory.setGoogleMapsReady(true);
                clientFactory.getWorkspaceContainer().notifyWorkspaces(
                        new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.MAP_READY)
                );
            }
        };

        String params = "key=" + googleMapsKey + "&" + getLibraries(loadLibraries);
        AjaxLoaderOptions settings = AjaxLoaderOptions.newInstance();
        settings.setOtherParms(params);
        AjaxLoader.loadApi("maps", GOOGLE_MAPS_VERSION, onLoad, settings);
    }

    private static String getLibraries(List<LoadLibrary> loadLibraries) {
        if (loadLibraries == null) {
            return "";
        }
        StringBuilder s = new StringBuilder();
        for (LoadLibrary lib : loadLibraries) {
            if (lib != null) {
                s.append(",");
                s.append(lib.value());
            }
        }
        return s.toString().isEmpty() ? "" : "libraries=" + s.toString().substring(1);
    }
    
    //Method to add the event handlers that drive the application's actions.
    private void addHandlers(final ClientFactory clientFactory) {
        
        //Handle Open Usage Point
        clientFactory.getEventBus().addHandler(OpenUsagePointEvent.TYPE, new OpenUsagePointEventHandler() {                
            @Override
            public void openUsagePoint(final OpenUsagePointEvent event) {
                final PopupPanel waitDialog = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon());
                clientFactory.getWorkspaceContainer().openWorkspace(event.getPlace(), new WorkspaceOpenCallback() {                    
                    @Override
                    public void onWorkspaceOpened(Workspace workspace) {
                        waitDialog.hide();
                    }
                    
                    @Override
                    public void onWorkspaceNotOpened(Exception exception) {
                        waitDialog.hide();                         
                        
                        //Workspace was not opened due to session time out - other code handles required actions
                        if (exception instanceof ServiceException) {
                            ServiceException se = (ServiceException) exception;
                            logger.info("Checking serviceException: "+se.getMessage());
                            if (se.getMessage().equals("error.meter.workspace.error") 
                                    || se.getMessage().equals("error.customer.workspace.error")) {
                                logger.severe("Error opening the workspace, probably session timed out");
                                String previousHistoryToken = null;
                                if (event.getPlace() instanceof MeterPlace) {                            
                                    MeterPlace meterPlace = (MeterPlace) event.getPlace();
                                    previousHistoryToken = meterPlace.getPreviousHistoryToken();
                                } else {
                                    CustomerPlace customerPlace = (CustomerPlace) event.getPlace();
                                    previousHistoryToken = customerPlace.getPreviousHistoryToken();
                                }
                                resetHistoryToken(previousHistoryToken, event.getPlace());
                                return;
                            }
                        }
                        
                        //Display the error if necessary
                        if (exception instanceof WorkspaceContainerException) {
                            WorkspaceContainerException wce = (WorkspaceContainerException) exception;
                            String msg = exception.getMessage();
                            if (wce.isTranslateMessage()) {
                                if (wce.getArgs() != null) {
                                    msg = MessagesUtil.getInstance().getMessage(wce.getMessage(), wce.getArgs());
                                } else {
                                    msg = MessagesUtil.getInstance().getMessage(wce.getMessage()); 
                                }
                            }
                            Dialogs.displayErrorMessage(msg, 
                                                        MediaResourceUtil.getInstance().getErrorIcon(), 
                                                        MessagesUtil.getInstance().getMessage("button.close"), 
                                                        new ClickHandler() {                
                                                            @Override
                                                            public void onClick(ClickEvent event) {
                                                                History.back();                    
                                                            }
                                                        });    
                        } else if (exception instanceof AccessControlException) {
                            //have already displayed error message 
                        } else {    
                            //Display the error
                            AbstractUsagePointPlace place = event.getPlace();
                            String previousHistoryToken = null;    
                            if (place instanceof MeterPlace) {                            
                                MeterPlace meterPlace = (MeterPlace) place;
                                previousHistoryToken = meterPlace.getPreviousHistoryToken();
                                String message = exception.getMessage();                            
                                if (message == null || !(exception instanceof MeterNotFoundException)) {
                                    message = MessagesUtil.getInstance().getMessage("error.meter.load");
                                }                            
                                //If the meter was not found, go to the advanced search screen if there is a valid meter number to search for
                                if (exception instanceof MeterNotFoundException 
                                        && (meterPlace.getMeterNumber() != null && !meterPlace.getMeterNumber().trim().equals(""))) {
                                    clientFactory.getEventBus().fireEvent(
                                            new SearchEvent(SearchPlace.ADVANCED_SEARCH_TYPE, 
                                                            SearchResultType.METER, 
                                                            meterPlace.getMeterNumber()));
                                    
                                    displayMeterSearchMessage(meterPlace.getMeterNumber());
                                    
                                } else {
                                    Dialogs.displayErrorMessage(message,
                                            MediaResourceUtil.getInstance().getErrorIcon(), 
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                }
                            } else if (place instanceof CustomerPlace) {  
                                CustomerPlace customerPlace = (CustomerPlace) place;
                                previousHistoryToken = customerPlace.getPreviousHistoryToken();
                                String message = exception.getMessage();                            
                                if (message == null || !(exception instanceof CustomerNotFoundException)) {
                                    message = MessagesUtil.getInstance().getMessage("error.customer.load");
                                }                            
                                //If the customer was not found, go to the advanced search screen if there is valid customer info to search for
                                if (exception instanceof CustomerNotFoundException 
                                        && (customerPlace.getCustomerId() != null && !customerPlace.getCustomerId().trim().equals(""))) {
                                    SearchResultType searchResultType = SearchResultType.CUSTOMER;
                                    switch (customerPlace.getSearchType()) {
                                        case 1:          //SEARCH_BY_AGREEMENT_REF
                                            searchResultType = SearchResultType.CUSTOMER_AGREEMENT;
                                            break;
                                        case 2:         //SEARCH_BY_ACCOUNT_NAME
                                            searchResultType = SearchResultType.ACCOUNT_NAME;
                                            break;
                                        case 3:          //SEARCH_BY_ID_NUMBER
                                            searchResultType = SearchResultType.CUSTOMER_ID_NUMBER;
                                    }

                                    clientFactory.getEventBus().fireEvent(
                                            new SearchEvent(SearchPlace.ADVANCED_SEARCH_TYPE, 
                                                            searchResultType, 
                                                            customerPlace.getCustomerId().trim()));
                                    
                                    displayCustomerSearchMessage(customerPlace.getCustomerId().trim(), customerPlace.getSearchType());
                                    
                                } else {
                                    Dialogs.displayErrorMessage(message,
                                            MediaResourceUtil.getInstance().getErrorIcon(), 
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                }
                            } else {
                                //must be UsagePointPlace
                                UsagePointPlace usagePointPlace = (UsagePointPlace) place;
                                previousHistoryToken = usagePointPlace.getPreviousHistoryToken();
                                String message = exception.getMessage();                            
                                if (message == null || !(exception instanceof UsagePointNotFoundException)) {
                                    message = MessagesUtil.getInstance().getMessage("error.usagepoint.load");
                                }                            
                                //If the usagePoint was not found, go to the advanced search screen if there is a valid usagePointName to search for
                                if (exception instanceof UsagePointNotFoundException 
                                        && (usagePointPlace.getUsagePointName() != null && !usagePointPlace.getUsagePointName().trim().equals(""))) {
                                    clientFactory.getEventBus().fireEvent(
                                            new SearchEvent(SearchPlace.ADVANCED_SEARCH_TYPE, 
                                                            SearchResultType.USAGE_POINT, 
                                                            usagePointPlace.getUsagePointName().trim()));
                                    
                                    displayUsagePointSearchMessage(usagePointPlace.getUsagePointName());
                                    
                                } else {
                                    Dialogs.displayErrorMessage(message,
                                            MediaResourceUtil.getInstance().getErrorIcon(), 
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                }
                            }
                            resetHistoryToken(previousHistoryToken, place);
                        }
                    }
                    
                    private void resetHistoryToken(String previousHistoryToken, Place place) {
                        //Reset the current URL back to where you came from...
                        if (previousHistoryToken != null && previousHistoryToken.equals(MeterOnlineBulkPlace.getPlaceAsString())) {
                            History.newItem(previousHistoryToken, true);
                        } else if (previousHistoryToken != null) {
                            History.newItem(previousHistoryToken, false);
                        } else {
                            logger.warning("No previousHistoryToken to return to:"+previousHistoryToken+" "+place);
                        }
                    }
                    
                    @Override
                    public void onWorkspaceAlreadyOpen(Workspace workspace) {
                        waitDialog.hide();
                    }
                    
                    @Override
                    public void onWorkspaceOpenPending(Place place) {
                        waitDialog.hide();
                    }
                    
                });
            }
                
        });  
        
        
        //Handle Feedback
        clientFactory.getEventBus().addHandler(FeedbackMessageEvent.TYPE, new FeedbackMessageEventHandler() {            
            @Override
            public void setMessage(FeedbackMessageEvent event) {
                if (event.getType() == Message.MESSAGE_TYPE_SUCCESS) {
                    Dialogs.displayInformationMessage(event.getFeedbackMessage(), MediaResourceUtil.getInstance().getInformationIcon());
                } else if (event.getType() == Message.MESSAGE_TYPE_ERROR) {
                    Dialogs.displayErrorMessage(event.getFeedbackMessage(), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                } else if (event.getType() == Message.MESSAGE_TYPE_WARNING) {
                    Dialogs.displayInformationMessage(event.getFeedbackMessage(), MediaResourceUtil.getInstance().getWarningIcon());
                }
            }
            @Override
            public void clearMessage() {
                clientFactory.getMeterSearchView().clearMessage();
                clientFactory.getCustomerSearchView().clearMessage();
            }
        });
        
        //Handle Meter Search
        clientFactory.getEventBus().addHandler(MeterSearchEvent.TYPE, new MeterSearchEventHandler() {            
            @Override
            public void searchByMeterNumber(final MeterSearchEvent event) {
                clientFactory.getMeterSearchView().clearMessage();
                clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(event.getMeterPlace()));
            }
        });
    
      //Handle Customer Search
        clientFactory.getEventBus().addHandler(CustomerSearchEvent.TYPE, new CustomerSearchEventHandler() {            
            @Override
            public void searchByCustomer(final CustomerSearchEvent event) {
                clientFactory.getCustomerSearchView().clearMessage();
                clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(event.getCustomerPlace()));
            }
        });
        
        //Handle UsagePoint 
          clientFactory.getEventBus().addHandler(UsagePointSearchEvent.TYPE, new UsagePointSearchEventHandler() {            
            @Override
            public void searchByUsagePoint(UsagePointSearchEvent event) {
                clientFactory.getMeterSearchView().clearUsagePointMessage();
                clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(event.getUsagePointPlace()));
            }
          });
        
        //Handle Customer Transaction Upload
          clientFactory.getEventBus().addHandler(CustomerTransUploadEvent.TYPE, new CustomerTransUploadEventHandler() {            
              @Override
              public void handleEvent(final CustomerTransUploadEvent event) {
                  clientFactory.getWorkspaceContainer().openWorkspace(new CustomerTransUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
              }
          });

		// Handle Aux Transaction Upload
		clientFactory.getEventBus().addHandler(AuxTransUploadEvent.TYPE, new AuxTransUploadEventHandler() {
			@Override
			public void handleEvent(final AuxTransUploadEvent event) {
				clientFactory.getWorkspaceContainer().openWorkspace(new AuxTransUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
			}
		});

          //Handle Meter Bulk Upload
            clientFactory.getEventBus().addHandler(MeterCustUPUploadEvent.TYPE, new MeterCustUPUploadEventHandler() {            
                @Override
                public void handleEvent(final MeterCustUPUploadEvent event) {
                    clientFactory.getWorkspaceContainer().openWorkspace(new MeterCustUPUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
                }
            });
            
        //Handle Meter Bulk Upload
        clientFactory.getEventBus().addHandler(MeterBulkUploadEvent.TYPE, new MeterBulkUploadEventHandler() {            
        	@Override
            public void handleEvent(final MeterBulkUploadEvent event) {
        		clientFactory.getWorkspaceContainer().openWorkspace(new MeterBulkUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });

        //Handle Metadata Upload
        clientFactory.getEventBus().addHandler(MetadataUploadEvent.TYPE, new MetadataUploadEventHandler() {
        	@Override
            public void handleEvent(final MetadataUploadEvent event) {
        		clientFactory.getWorkspaceContainer().openWorkspace(new MetadataUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
		// Handle AuxAccount Bulk Upload
		clientFactory.getEventBus().addHandler(AuxAccountUploadEvent.TYPE, new AuxAccountUploadEventHandler() {
			@Override
			public void handleEvent(AuxAccountUploadEvent event) {
				clientFactory.getWorkspaceContainer().openWorkspace(new AuxAccountUploadPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
			}
		});
        
        //Handle open group
        clientFactory.getEventBus().addHandler(OpenGroupEvent.TYPE, new OpenGroupEventHandler() {
            @Override
            public void openGroup(OpenGroupEvent event) {
                logger.info("Opening group for event: "+event.getGrouptype());
                if (event.getGrouptype().equals(GroupPlace.GROUP_TYPE_PLACE.getGroupType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(GroupPlace.GROUP_TYPE_PLACE, new SimpleWorkspaceOpenCallbackImpl());
                } else if (event.getGrouptype().equals(GroupPlace.USAGE_POINT_GROUPS_PLACE.getGroupType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(GroupPlace.USAGE_POINT_GROUPS_PLACE, new SimpleWorkspaceOpenCallbackImpl());
                } else if (event.getGrouptype().equals(GroupPlace.ACCESS_GROUPS_PLACE.getGroupType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(GroupPlace.ACCESS_GROUPS_PLACE, new SimpleWorkspaceOpenCallbackImpl());
                } else if (event.getGrouptype().equals(GroupPlace.LOCATION_GROUPS_PLACE.getGroupType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(GroupPlace.LOCATION_GROUPS_PLACE, new SimpleWorkspaceOpenCallbackImpl());
                } else {
                    logger.warning("Unknown grouptype for event: "+event.getGrouptype());
                }
            }
        });
        
        //Handle Meter Bulk Upload
        clientFactory.getEventBus().addHandler(MeterOnlineBulkEvent.TYPE, new MeterOnlineBulkEventHandler() {            
            @Override
            public void handleEvent(final MeterOnlineBulkEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new MeterOnlineBulkPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle File Upload and Import
        clientFactory.getEventBus().addHandler(ImportFileEvent.TYPE, new ImportFileEventHandler() {            
            @Override
            public void handleEvent(final ImportFileEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new ImportFilePlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle pricing structure
        clientFactory.getEventBus().addHandler(OpenPricingStructureEvent.TYPE, new OpenPricingStructureEventHandler() {
            @Override
            public void openPricingStructure(OpenPricingStructureEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(event.getPricingStructurePlace(), new SimpleWorkspaceOpenCallbackImpl());
            }
        });

        //Handle aux charge structure
        clientFactory.getEventBus().addHandler(OpenAuxChargeStructureEvent.TYPE, new OpenAuxChargeStructureEventHandler() {
            @Override
            public void openAuxChargeStructure(OpenAuxChargeStructureEvent event) {
                AuxChargeSchedulePlace place = new AuxChargeSchedulePlace(event.getAuxChargeStructureId());
                clientFactory.getWorkspaceContainer().openWorkspace(place, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle aux type
        clientFactory.getEventBus().addHandler(OpenAuxTypeEvent.TYPE, new OpenAuxTypeEventHandler() {
            @Override
            public void openAuxType(OpenAuxTypeEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(AuxTypePlace.ALL_AUX_TYPE_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Supply Group
        clientFactory.getEventBus().addHandler(OpenSupplyGroupEvent.TYPE, new OpenSupplyGroupEventHandler() {
			@Override
			public void openSupplyGroup(OpenSupplyGroupEvent event) {
		       clientFactory.getWorkspaceContainer().openWorkspace(SupplyGroupPlace.ALL_SUPPLY_GROUP_PLACE, new SimpleWorkspaceOpenCallbackImpl());
			}
		});
        
        //Display Tokens
        clientFactory.getEventBus().addHandler(OpenDisplayTokensEvent.TYPE, new OpenDisplayTokensEventHandler() {
            @Override
            public void openDisplayTokens(OpenDisplayTokensEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(DisplayTokensPlace.ALL_DISPLAY_TOKENS_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle device store
        clientFactory.getEventBus().addHandler(OpenDeviceStoreEvent.TYPE, new OpenDeviceStoreEventHandler() {            
            @Override
            public void openDeviceStore(OpenDeviceStoreEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(DeviceStorePlace.ALL_DEVICE_STORE_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
		// Handle blocking type
		clientFactory.getEventBus().addHandler(OpenBlockingTypeEvent.TYPE, new OpenBlockingTypeEventHandler() {
			@Override
			public void openBlockingType(OpenBlockingTypeEvent event) {
				BlockingTypePlace place = new BlockingTypePlace(event.getBlockingTypeId());
				clientFactory.getWorkspaceContainer().openWorkspace(place, new SimpleWorkspaceOpenCallbackImpl());
			}
		});
		
        //User Group workspace
        clientFactory.getEventBus().addHandler(OpenUserGroupEvent.TYPE, new OpenUserGroupEventHandler() {            
            @Override
            public void openUserGroup(OpenUserGroupEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(UserGroupPlace.ALL_USER_GROUP_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Search workspace
        clientFactory.getEventBus().addHandler(SearchEvent.TYPE, new SearchEventHandler() {            
            @Override
            public void processSearchEvent(SearchEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(
                        new SearchPlace(event.getSearchType(), event.getDataType(), event.getSearchText()), 
                        new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Meter Readings workspace
        clientFactory.getEventBus().addHandler(OpenMeterReadingsEvent.TYPE, new OpenMeterReadingsEventHandler() {            
            @Override
            public void openMeterReadings(OpenMeterReadingsEvent event) {
                MeterReadingsPlace place = new MeterReadingsPlace(event.getSuperMeter(), 
                                                                   event.getGraphType(), 
                                                                   event.getReadingType(), 
                                                                   event.getStartDate(), 
                                                                   event.getEndDate());
                clientFactory.getWorkspaceContainer().openWorkspace(place, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle calendar
        clientFactory.getEventBus().addHandler(OpenCalendarEvent.TYPE, new OpenCalendarEventHandler() {
            
            @Override
            public void openCalendar(OpenCalendarEvent event) {
                if (event.getCalendarPlaceType().equals(CalendarPlace.CALENDARS_PLACE_SETTINGS.getCalendarPlaceType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(CalendarPlace.CALENDARS_PLACE_SETTINGS, new SimpleWorkspaceOpenCallbackImpl());
                } else if (event.getCalendarPlaceType().equals(CalendarPlace.CALENDARS_PLACE.getCalendarPlaceType())) {
                    clientFactory.getWorkspaceContainer().openWorkspace(CalendarPlace.CALENDARS_PLACE, new SimpleWorkspaceOpenCallbackImpl());
                }
            }
        }); 
        
        //Energy Balancing Alerts
        clientFactory.getEventBus().addHandler(OpenEnergyBalancingEvent.TYPE, new OpenEnergyBalancingEventHandler() {            
            @Override
            public void openEnergyBalaning(OpenEnergyBalancingEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new EnergyBalancingAlertPlace(event.getMeter()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Energy Balancing Meters
        clientFactory.getEventBus().addHandler(EnergyBalancingMeterEvent.TYPE, new EnergyBalancingMeterEventHandler() {            
            @Override
            public void handleEvent(EnergyBalancingMeterEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new EnergyBalancingMeterPlace(event.getMeter()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Manufacturer
        clientFactory.getEventBus().addHandler(ManufacturerEvent.TYPE, new ManufacturerEventHandler() {            
            @Override
            public void handleEvent(ManufacturerEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new ManufacturerPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Meter Data Controller
        clientFactory.getEventBus().addHandler(MdcEvent.TYPE, new MdcEventHandler() {            
            @Override
            public void handleEvent(MdcEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new MdcPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });        
        
        //Billing Determinant
        clientFactory.getEventBus().addHandler(BillingDetEvent.TYPE, new BillingDetEventHandler() {            
            @Override
            public void handleEvent(BillingDetEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new BillingDetPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        }); 
        
        //Meter Model
        clientFactory.getEventBus().addHandler(MeterModelEvent.TYPE, new MeterModelEventHandler() {            
            @Override
            public void handleEvent(MeterModelEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new MeterModelPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Add Meter Readings
        clientFactory.getEventBus().addHandler(AddMeterReadingsEvent.TYPE, new AddMeterReadingsEventHandler() {            
            @Override
            public void handleEvent(AddMeterReadingsEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(
                        new AddMeterReadingsPlace(event.getMeterType(), event.getPaymentMode()), 
                        new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Application Settings
        clientFactory.getEventBus().addHandler(AppSettingEvent.TYPE, new AppSettingEventHandler() {            
            @Override
            public void handleEvent(AppSettingEvent event) {
                if (event.getName() != null && !event.getName().trim().isEmpty()) {
                    clientFactory.getWorkspaceContainer().openWorkspace(new AppSettingPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
                }
            }
        });  
        
        //GlobalNdp Settings
        clientFactory.getEventBus().addHandler(GlobalNdpEvent.TYPE, new GlobalNdpEventHandler() {            
            @Override
            public void handleEvent(GlobalNdpEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new GlobalNdpPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });  
        
        //Scheduling
        clientFactory.getEventBus().addHandler(TaskScheduleEvent.TYPE, new TaskScheduleEventHandler() {            
            @Override
            public void handleEvent(TaskScheduleEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new TaskSchedulePlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        // Change Password workspace
        clientFactory.getEventBus().addHandler(ChangePasswordEvent.TYPE, new ChangePasswordEventHandler() {
            @Override
            public void processChangePasswordEvent(ChangePasswordEvent event) {
                if (ChangePasswordPlace.LOGGED_IN_USER.equals(event.getUser())) {
                    clientFactory.getPrimaryLayoutView().displayChangePasswordScreen(clientFactory.getUser());
                } else {
                    clientFactory.getPrimaryLayoutView().displayChangePasswordScreen();
                }
            }
        });
        
        // Select User Access Group Popup
        clientFactory.getEventBus().addHandler(SelectAccessGroupEvent.TYPE, new SelectAccessGroupEventHandler() {
            @Override
            public void processSelectAccessGroupEvent(SelectAccessGroupEvent event) {
                // Group change happens outside the GWT app when groups from ipay_access_control are in play
                if(! clientFactory.isEnableAccessGroups()) {
                    clientFactory.getPrimaryLayoutView().displayChangeGroupScreen(new SelectAccessGroupPlace());
                }
            }
        });
        
        //Handle dashboard
        clientFactory.getEventBus().addHandler(OpenDashboardEvent.TYPE, new OpenDashboardEventHandler() {
            @Override
            public void openDashboard(OpenDashboardEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(DashboardPlace.ALL_DASHBOARD_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle admin dashboard
        clientFactory.getEventBus().addHandler(OpenAdminDashboardEvent.TYPE, new OpenAdminDashboardEventHandler() {
            @Override
            public void openAdminDashboard(OpenAdminDashboardEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(AdminDashboardPlace.ALL_ADMIN_DASHBOARD_PLACE, new SimpleWorkspaceOpenCallbackImpl());
            }
        });
        
        //Handle special actions
        clientFactory.getEventBus().addHandler(SpecialActionsEvent.TYPE, new SpecialActionsEventHandler() {
			@Override
			public void handleEvent(SpecialActionsEvent event) {
				clientFactory.getWorkspaceContainer().openWorkspace(new SpecialActionsPlace(event.getName()), new SimpleWorkspaceOpenCallbackImpl());
			}
		});  
        
        // Configure user interface
        clientFactory.getEventBus().addHandler(UserInterfaceEvent.TYPE, new UserInterfaceEventHandler() {
            @Override
            public void handleEvent(UserInterfaceEvent event) {
                clientFactory.getWorkspaceContainer().openWorkspace(new UserInterfacePlace(event.getName()),
                        new SimpleWorkspaceOpenCallbackImpl());
            }
        });
    }

    private void displayMeterSearchMessage(String meterNum) {
        if (meterNum != null && !meterNum.trim().equals("")) {
            clientFactory.getMeterSearchView().showMessage(MessagesUtil.getInstance().getMessage("meter.partial.search", new String[]{meterNum}));
        }
    }

    private void displayCustomerSearchMessage(String customerField, int searchType) {
        String messageKey = "customer.partial.search";
        switch (searchType) {
            case 1:          //SEARCH_BY_AGREEMENT_REF
                messageKey = "customer.agreement.partial.search";
                break;
            case 2:         //SEARCH_BY_ACCOUNT_NAME
                messageKey = "customer.account.partial.search";
                break;
            case 3:          //SEARCH_BY_ID_NUMBER
                messageKey = "customer.id.partial.search";
        }

        if (customerField != null && !customerField.trim().equals("")) {
            clientFactory.getCustomerSearchView().showMessage(MessagesUtil.getInstance().getMessage(messageKey));
        }
    }
    
    private void displayUsagePointSearchMessage(String usagePointName) {
        if (usagePointName != null && !usagePointName.trim().equals("")) {
            clientFactory.getMeterSearchView().showUsagePointMessage(MessagesUtil.getInstance().getMessage("usagepoint.partial.search", new String[]{usagePointName}));
        }
    }

    public static String getLogoUrl() {
        return mancoLogo;
    }

    public static ClientFactory getClientFactory() {
        return clientFactory;
    }
}
