package za.co.ipay.metermng.client.view.component.onlinebulk;

import za.co.ipay.metermng.client.widget.suggestboxtree.SuggestBoxTree.TreeInfo;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;

public class GroupTypeTreeInfo implements TreeInfo {
    private long groupTypeId;
    private String groupTypeName;
    private boolean required;
    private boolean mustSelectLastlevel;
    
    public GroupTypeTreeInfo(SelectionDataItem item, boolean mustSelectLastLevel) {
        this.groupTypeId = item.getActualId();
        this.groupTypeName = item.getName();
        this.required = item.isRequired();
        this.mustSelectLastlevel = mustSelectLastLevel;
    }
    
    public long getGroupTypeId() {
        return groupTypeId;
    }
    
    public String getGroupTypeName() {
        return groupTypeName;
    }
    
    @Override
    public String getCaptionLabel() {
        return groupTypeName;
    }

    @Override
    public boolean isRequired() {
        return required;
    }
    
    @Override
    public boolean mustSelectLastLevel() {
        return mustSelectLastlevel;
    }

}
