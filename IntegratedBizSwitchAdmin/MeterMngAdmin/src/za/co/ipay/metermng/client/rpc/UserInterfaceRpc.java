package za.co.ipay.metermng.client.rpc;

import java.util.List;
import java.util.Map;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.metermng.mybatis.generated.model.Form;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;

@RemoteServiceRelativePath("secure/userInterface.do")
public interface UserInterfaceRpc extends RemoteService {
    public List<Form> getForms();

    public List<FormFields> getFormFieldsList();

    public void saveFormFields(Map<Long, FormFields> formFields);

    public Map<String, FormFields> getFormFields();
}
