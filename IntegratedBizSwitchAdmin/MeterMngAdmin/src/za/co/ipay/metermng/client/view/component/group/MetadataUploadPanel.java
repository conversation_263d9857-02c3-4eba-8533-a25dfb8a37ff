package za.co.ipay.metermng.client.view.component.group;

import java.util.ArrayList;
import java.util.Collections;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;

public class MetadataUploadPanel extends BaseWorkspace  {

    @UiField PageHeader pageHeader;
    @UiField HTML dataTitle;

    @UiField FlowPanel mainPanel;
    @UiField HorizontalPanel groupTypesPanel;
    private HasDirtyData hasDirtyData;

    private static MetadataUploadPanelUiBinder uiBinder = GWT.create(MetadataUploadPanelUiBinder.class);
    private static Logger logger = Logger.getLogger(MetadataUploadPanel.class.getName());

    interface MetadataUploadPanelUiBinder extends UiBinder<Widget, MetadataUploadPanel> {
    }

    public MetadataUploadPanel(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = createAndRegisterHasDirtyData();
        initWidget(uiBinder.createAndBindUi(this));
        setHeaderText(Messages.MessagesUtil.getInstance().getMessage("metadata.upload.heading"));
        loadGroupTypes();
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof MetadataUploadPlace;
    }

    private void loadGroupTypes() {
        clientFactory.getGroupRpc().getGroupTypes(false, false, new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> results) {
                //Display each group type in a selection widget
                logger.info("Got selectionData: " + results.size());
                SelectionDataItem item;
                SelectionDataWidget sdw;
                boolean addit = true;

                Collections.sort(results, SelectionDataItem.layoutOrderComparator());

                for (SelectionDataItem result : results) {
                    item = result;
                    for (int j = 0; j < groupTypesPanel.getWidgetCount(); j++) {
                        addit = true;
                        if (groupTypesPanel.getWidget(j) instanceof SelectionDataWidget) {
                            sdw = (SelectionDataWidget) groupTypesPanel.getWidget(j);
                            sdw.setPlace(MetadataUploadPlace.ALL_PLACE);
                            if (sdw.getGroupTypeId().equals(item.getActualId())) {
                                addit = false;
                                logger.info("Selected group in widget: " + sdw.getSelectedGroup() + "(grouplinkid = " + sdw.getUpGenGroupLnkId() + ")");
                                break;
                            }
                        }
                    }
                    if (addit) {
                        sdw = new SelectionDataWidget(clientFactory, clientFactory.getGroupRpc(), item, false, true, MeterMngStatics.SELECTION_DATA_WIDGET_CONTEXT_USAGE_POINT_GROUP, hasDirtyData);
                        sdw.setPlace(MetadataUploadPlace.ALL_PLACE);
                        groupTypesPanel.add(sdw);
                    }
                }
                logger.info("Groups complete");
            }
        });
    }
}