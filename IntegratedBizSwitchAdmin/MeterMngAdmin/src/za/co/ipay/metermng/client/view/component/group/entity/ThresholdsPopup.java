package za.co.ipay.metermng.client.view.component.group.entity;

import java.math.BigDecimal;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;

public class ThresholdsPopup extends BaseComponent {

    @UiField FormElement meterDisconnectElement;
    @UiField CurrencyTextBox meterDisconnectBox;

    @UiField FormElement emergencyCreditElement;
    @UiField CurrencyTextBox emergencyCreditBox;

    @UiField FormElement meterReconnectElement;
    @UiField CurrencyTextBox meterReconnectBox;

    @UiField FormElement lowBalanceElement;
    @UiField CurrencyTextBox lowBalanceBox;
    
    private DialogBox simplePopup;
    private HasDirtyData hasDirtyData;
    
    private static ThresholdsPopupUiBinder uiBinder = GWT.create(ThresholdsPopupUiBinder.class);

    interface ThresholdsPopupUiBinder extends UiBinder<Widget, ThresholdsPopup> {
    }
    

    public ThresholdsPopup() {
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    public ThresholdsPopup(CustomerAccThresholds thresholds) {
        initWidget(uiBinder.createAndBindUi(this));
        display(thresholds);
        // display only
        meterDisconnectBox.setEnabled(false);
        emergencyCreditBox.setEnabled(false);
        meterReconnectBox.setEnabled(false);
        lowBalanceBox.setEnabled(false);
    }
    
    protected void display(CustomerAccThresholds thresholds) {
        if (thresholds.getDisconnect() == null) {
            meterDisconnectBox.setAmount(null);
        } else {
            meterDisconnectBox.setAmount(thresholds.getDisconnect());
        }
        
        if (thresholds.getEmergencyCredit() == null) {
            emergencyCreditBox.setAmount(null);
        } else { 
            emergencyCreditBox.setAmount(thresholds.getEmergencyCredit());
        }
        
        if (thresholds.getReconnect() == null) {
            meterReconnectBox.setAmount(null);
        } else { 
            meterReconnectBox.setAmount(thresholds.getReconnect());
        }
        
        if (thresholds.getLowBalance() == null) {
            lowBalanceBox.setAmount(null);
        } else { 
            lowBalanceBox.setAmount(thresholds.getLowBalance());
        }
    }

    protected CustomerAccThresholds mapFormToData() {
        CustomerAccThresholds thresholdsToSave = new CustomerAccThresholds();
        thresholdsToSave.setId(null);
        
        BigDecimal value = meterDisconnectBox.getAmount();
        thresholdsToSave.setDisconnect(value);
        
        value = emergencyCreditBox.getAmount();
        thresholdsToSave.setEmergencyCredit(value);
        
        value = meterReconnectBox.getAmount();
        thresholdsToSave.setReconnect(value);
        
        value = lowBalanceBox.getAmount();
        thresholdsToSave.setLowBalance(value);
        
        return thresholdsToSave;
    }
    
    protected void clearErrors() {
        meterDisconnectElement.clearErrorMsg();
        emergencyCreditElement.clearErrorMsg();
        meterReconnectElement.clearErrorMsg();
        lowBalanceElement.clearErrorMsg();      
    }
    
    public void addFieldHandlers() {
        // handlers for custom fields are added in the createXXX() methods
        meterDisconnectBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        emergencyCreditBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        meterReconnectBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lowBalanceBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    
    public void show(int left, int top, String source) {
        simplePopup = new DialogBox(true);
        String caption = MessagesUtil.getInstance().getMessage("groupthreshold.parent.source.label");
        if (source.equals("global")) {
            caption = MessagesUtil.getInstance().getMessage("groupthreshold.global.source.label");
        } 
        simplePopup.setText(caption);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(this);
        simplePopup.setPopupPosition(left, top);
        simplePopup.show();
    }

    public HasDirtyData getHasDirtyData() {
        return hasDirtyData;
    }
    
    public void setHasDirtyDataManager(HasDirtyDataManager hasDirtyDataManager) {
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
    }
}
