package za.co.ipay.metermng.client.view.component.dashboard;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Widget;

public abstract class DashPanel extends BaseComponent {
    @UiField FlowPanel dashPanel;
    @UiField HTMLPanel tablePanel;
    @UiField HTML dashTitle;
    @UiField HTML dashDescription;
    @UiField FlowPanel tablePlaceHolder;

    private static DashPanelUiBinder uiBinder = GWT.create(DashPanelUiBinder.class);
    
    interface DashPanelUiBinder extends UiBinder<Widget, DashPanel> {}

    public DashPanel() {}

    public DashPanel(ClientFactory clientFactory) {
        // TODO Auto-generated constructor stub
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
    }

    public abstract  void refresh();

    public void setDashTitle(String title) {
        if (title == null || title.length() == 0) {
            dashTitle.setText("");
            if (dashTitle.isVisible()) {
                dashTitle.setVisible(false);
            }
          } else {
            dashTitle.setText(title);
            if (!dashTitle.isVisible()) {
                dashTitle.setVisible(true);
            }
          }
    }
    
    public void clearDashTitle() {
        dashTitle.setText("");
        if (dashTitle.isVisible()) {
            dashTitle.setVisible(false);
        }
    }
    
    public void setDashDescription(String description) {
        if (description == null || description.length() == 0) {
            dashDescription.setText("");
            if (dashDescription.isVisible()) {
                dashDescription.setVisible(false);
            }
          } else {
              dashDescription.setText(description);
            if (!dashDescription.isVisible()) {
                dashDescription.setVisible(true);
            }
          }

    }
    public void clearDashDescription() {
        dashDescription.setText("");
        if (dashDescription.isVisible()) {
            dashDescription.setVisible(false);
        }
    }
    
    public void removeTablePanel() {
            tablePanel.removeFromParent();
    }
}