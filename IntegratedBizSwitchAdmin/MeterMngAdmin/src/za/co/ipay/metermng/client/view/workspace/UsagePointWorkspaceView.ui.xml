<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.metermng.client.view.component"
	xmlns:meter="urn:import:za.co.ipay.metermng.client.view.component.meter"
	xmlns:customer="urn:import:za.co.ipay.metermng.client.view.component.customer"
	xmlns:up="urn:import:za.co.ipay.metermng.client.view.component.usagepoint"
    xmlns:f="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets">
    <ui:style>
        .contractButtonSpacing {
            margin: 12px;
        }
        .noBorder {
			border-style: dotted dashed solid double;        
		}
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  <g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">
    
    <g:north ui:field="northPanel" size="30">
      <f:PageHeader heading="{msg.getWorkspaceUsagepointOverview}" ui:field="pageHeader" />
    </g:north>
    
    <g:center>
	<g:ScrollPanel>
		<g:HTMLPanel>
			<g:FlowPanel ui:field="overViewPanel" styleName="formElementsPanel">
					<f:FormGroupPanel labelText="{msg.getAccessGroupLbl}" ui:field="accessGroupUpdate" debugId="accessGroupUpdate" visible="false">
						<g:HorizontalPanel ui:field="accessGroupInner" spacing="5">
							<p1:IpayListBox visibleItemCount="1" ui:field="accessGroupLstbx" debugId="accessGroupLstbx" multipleSelect="false"/>
							<g:Button debugId="accessGroupUpdateButton" ui:field="accessGroupUpdateButton" text="{msg.getUpdateAccessGroupButton}"/>
						</g:HorizontalPanel>
					</f:FormGroupPanel>
			</g:FlowPanel>
            <g:FlowPanel ui:field="meterpanel" width="100%">
                <ipay:MeterComponent ui:field="metercomponent" width="100%" debugId="metercomponent"></ipay:MeterComponent>
            </g:FlowPanel>
			<g:FlowPanel ui:field="customerpanel" width="100%">
				<ipay:CustomerComponent ui:field="customercomponent" width="100%" debugId="customercomponent"></ipay:CustomerComponent>
			</g:FlowPanel>
            <g:FlowPanel ui:field="usagepointpanel" width="100%">
                <ipay:UsagePointComponent ui:field="usagepointcomponent" width="100%" debugId="usagepointcomponent"></ipay:UsagePointComponent>
            </g:FlowPanel>
            <g:FlowPanel styleName="breadCrumbFonts">
              <g:Image ui:field="reload" styleName="imagelink" />
              <g:HTML ui:field="infoPanelHeading" styleName="infoPanelHeading">
                <ui:text from="{msg.getWorkspaceUsagepointInformation}" />
              </g:HTML>
            </g:FlowPanel>
            <g:TabPanel debugId="infoTabPanel" ui:field="informationTabs" width="100%" />
            <g:HorizontalPanel width="100%" horizontalAlignment="ALIGN_RIGHT">
            	<g:Button debugId="printContractButton" ui:field="printContractButton" text="{msg.getPrintContractButton}" styleName="{style.contractButtonSpacing}"/>
            </g:HorizontalPanel>
		</g:HTMLPanel>
	</g:ScrollPanel>
  </g:center>
  </g:DockLayoutPanel>
</ui:UiBinder> 