<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace">
	<ui:style>
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <t:FormGroupPanel labelText="{msg.getTariffPercentChargeTitle}">
        <t:FormRowPanel>
            <t:FormElement ui:field="percentChargeNameElement" debugId="percentChargeNameElement" labelText="{msg.getTariffPercentChargeName}:" helpMsg="{msg.getTariffPercentChargeNameHelp}" required="false">
                <g:TextBox ui:field="percentChargeNameBox" debugId="percentChargeNameBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="percentChargeElement" debugId="cyclicChargeElement" labelText="{msg.getTariffPercentCharge}:" helpMsg="{msg.getTariffPercentChargeHelp}" required="false">
                <w:PercentageTextBox ui:field="percentChargeBox" debugId="percentChargeBox" />
            </t:FormElement>
        </t:FormRowPanel>
    </t:FormGroupPanel>
	
</ui:UiBinder> 