package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEventHandler;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.MeterRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.meter.ContainsMeterComponent;
import za.co.ipay.metermng.client.view.component.pricingstructure.AssignMeterDialogueValidatePStoMM;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.widget.pricingstructure.PricingStructureLookup;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.MeterInDeviceStoreSuggestOracle;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

/*
 * NOTES: This popup has four "states" of action:
 * 1. REPLACE Meter (replacingOldMeter is true)
 * 2. FETCH after a previous REMOVE - UP is on the page and had previous install/s and remove date
 * 3. FETCH with clean UP on the page (i.e no previous remove)
 * 4. FETCH with no UP on the page (installation Date Box will not be visible)
 */

public class AssignMeterDialogueBox extends DialogBox implements AssignChannelReadingsComponent{

    @UiField HTML clearMeterBalanceMsg;
    @UiField FlowPanel preamble;
    @UiField Message feedBack;
    @UiField Message meterfeedBack;
    @UiField Message datefeedBack;
    @UiField Message changeActivationFeedBack;
    @UiField Label lblEnterMeterNumber;
    @UiField Button btnAssignReplaceMeter;
    @UiField Label lblSelectMeterStore;
    @UiField IpayListBox lstbxStores;
    @UiField DateBox dtbxInstallDate;
    @UiField FlowPanel installDatePanel;
    @UiField Label installdateRequired;
    @UiField FlowPanel changeActivationDatePanel;
    @UiField RadioButton yesChangeActivationDate;
    @UiField RadioButton noChangeActivationDate;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;
    @UiField Button btnCancel;
    @UiField Button btnCancelDefault;
    @UiField(provided=true) SuggestBox lstbxAssignMeter;
    @UiField FlowPanel deviceMoveRefPanel;
    @UiField FormElement deviceMoveRefElement;
    @UiField TextBox deviceMoveRefTxtbx;

    @UiField Label newCurrentPSRequired;
    @UiField FlowPanel currentPricingStructureFlowPanel;
    @UiField(provided = true)
    PricingStructureLookup currentPricingStructureLookup;

    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    protected ContainsMeterComponent parentWorkspace;
    private boolean replacingOldMeter = false;

    private UpMeterInstall upMeterInstall;

    private MeterData newMeterData;
    MeterInDeviceStoreSuggestOracle meterInDeviceStoreSuggestOracle;
    private Messages messages = MessagesUtil.getInstance();
    private HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();
    private boolean upAllowFutureInstallationDates;
    private boolean isUnitsPricingStructure = false;

    private static Logger logger = Logger.getLogger(AssignMeterDialogueBox.class.getName());

    private static AssignMeterDialogueBoxUiBinder uiBinder = GWT.create(AssignMeterDialogueBoxUiBinder.class);
    interface AssignMeterDialogueBoxUiBinder extends UiBinder<Widget, AssignMeterDialogueBox> {
    }

    public AssignMeterDialogueBox(ClientFactory clientFactory, ContainsMeterComponent parentWorkspace) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.meterInDeviceStoreSuggestOracle = new MeterInDeviceStoreSuggestOracle(clientFactory);
        specialactionreasons = new SpecialActionsReasonComponent(clientFactory, null, SpecialActionsData.REASSIGN_METER) {
            @Override
            void finishComponentSetup() {
                MeterMngClientUtils.ensurePopupIsOnScreen(AssignMeterDialogueBox.this, getPopupLeft(), getPopupTop(), 0);
            }
        };
        lstbxAssignMeter = new SuggestBox(meterInDeviceStoreSuggestOracle);
        currentPricingStructureLookup = new PricingStructureLookup(true, hasDirtyData, clientFactory);
        currentPricingStructureLookup.addValueChangeHandler(new ValueChangeHandler<String>() {
            @Override
            public void onValueChange(ValueChangeEvent<String> valueChangeEvent) {
                handleCurrentPricingStructureChange();
            }
        });
        setWidget(uiBinder.createAndBindUi(this));
        init();
        addHandlers();
    }

    protected void init() {
        this.ensureDebugId("assignMeterDialogBox");
        populateDeviceMoveRef();
        populateStoresListBox();
        populateAssignMeterList();
        feedBack.setVisible(false);
        meterfeedBack.setVisible(false);
        datefeedBack.setVisible(false);
        changeActivationFeedBack.setVisible(false);
        dtbxInstallDate.setFormat(new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                upAllowFutureInstallationDates = "true".equalsIgnoreCase(result.getValue().trim());
            }
        });
    }

    protected void addHandlers() {
        clientFactory.getEventBus().addHandler(EndDeviceStoreUpdatedEvent.TYPE, new EndDeviceStoreUpdatedEventHandler() {
            @Override
            public void processEndDeviceStoreUpdatedEvent(EndDeviceStoreUpdatedEvent event) {
                populateStoresListBox();
            }
        });
        addFieldHandler();
    }

    private void addFieldHandler() {
        // Only really makes sense to check dirty data on fields that are cleared on close.
        lstbxAssignMeter.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
        specialactionreasons.txtbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        specialactionreasons.lstbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxStores.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        dtbxInstallDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        yesChangeActivationDate.addClickHandler(new FormDataClickHandler(hasDirtyData));
        noChangeActivationDate.addClickHandler(new FormDataClickHandler(hasDirtyData));
    }

    public void setReplacingOldMeter(boolean replacingOldMeter) {
        //defaults for changeActivation date
        noChangeActivationDate.setValue(false);
        yesChangeActivationDate.setValue(false);
        changeActivationDatePanel.setVisible(false);

        this.replacingOldMeter = replacingOldMeter;
        logger.info("AssignMeterDialogueBox: setReplacingOldMeter: replacingOldMeter=" + replacingOldMeter);
        if (replacingOldMeter) {
            //containsMeterComponent.getUsagePoint().getId() != null && meterData.getId() != null
            btnAssignReplaceMeter.setText(MessagesUtil.getInstance().getMessage("meter.replace"));
            installdateRequired.setText(MessagesUtil.getInstance().getMessage("meter.install.date.required", new String[]{parentWorkspace.getUsagePoint().getName()}));
            installDatePanel.setVisible(true);
            displayClearMeterBalanceMsg();
            if (PaymentModeE.fromId(usagePointData.getUpPricingStructureData().getPricingStructure().getPaymentModeId()).getId() == (PaymentModeE.UNITS.getId()) &&
                    usagePointData.hasNoTransactions() && usagePointData.getActivationDate() != null) {
                changeActivationDatePanel.setVisible(true);
            }
        } else {
            //no usagepoint on page OR UP but no meter
            btnAssignReplaceMeter.setText(MessagesUtil.getInstance().getMessage("meter.assign"));
            if (usagePointData != null && usagePointData.getId() != null ) {
                installDatePanel.setVisible(true);
                //now deal with if a fetch after a remove (lastRemoveDate not null), if no previous removal date no need to change activation date
                if (usagePointData.getLastRemoveDate() != null && usagePointData.getUpPricingStructureData().getPricingStructure() != null &&
                        PaymentModeE.fromId(usagePointData.getUpPricingStructureData().getPricingStructure().getPaymentModeId()).getId() == (PaymentModeE.UNITS.getId()) &&
                        usagePointData.hasNoTransactions() && usagePointData.getActivationDate() != null) {
                    changeActivationDatePanel.setVisible(true);
                }
            } else {
                installDatePanel.setVisible(false);
            }
        }
        specialactionreasons.setVisible(replacingOldMeter);
        lstbxStores.setVisible(replacingOldMeter);
        lblSelectMeterStore.setVisible(replacingOldMeter);
    }

    public void setMeterNum(String meterNum) {
        //this method called from UsagePointWorkspaceFactory which makes a new AssignMeterDialogueBox ... hence can removeFrom Parent.
        //In MeterComponent the AssignMeterDialogueBox is made once in constructor & reused.... so be careful! The removeFromPArent() will casue issue if this method used elsewhere.
        //preamble.setVisible(false);
        //lblEnterMeterNumber.setVisible(false);
        preamble.removeFromParent();
        lblEnterMeterNumber.removeFromParent();

        lstbxAssignMeter.setValue(meterNum);
        lstbxAssignMeter.setEnabled(false);
        btnCancelDefault.setVisible(false);
        btnCancel.setVisible(true);
        btnCancel.setText(MessagesUtil.getInstance().getMessage("meter.attach.cancel.button"));
    }

    public void setButtonTextAttach() {
        btnAssignReplaceMeter.setText(MessagesUtil.getInstance().getMessage("meter.attach"));
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
        setReplacingOldMeter(usagePointData.getInstallationDate() != null);    //for ASSIGN_METER or SAVE_METER events, handleReplaceLink does its own
    }

    private void displayClearMeterBalanceMsg() {
        if (usagePointData != null
                && usagePointData.getRecordStatus() == RecordStatus.ACT
                && usagePointData.getMeterData() != null
                && usagePointData.getMeterData().getMeterModelData() != null
                && usagePointData.getMeterData().getMeterModelData().getMdcId() != null
                && usagePointData.getMeterData().getMeterModelData().isDisplayMessage()) {
            clearMeterBalanceMsg.setText(MessagesUtil.getInstance().getMessage("remove.meter.pandisplay"));
            clearMeterBalanceMsg.setVisible(true);

        }
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("btnCancel")
    void handleCancelAttachMeterToUsagePoint(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        cleanUpAndHide();
                    }
                }
            });
        } else {
            cleanUpAndHide();
        }
    }

    @UiHandler("btnCancelDefault")
    void handleCancelButton(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            this.hide();
        }
    }

    private void cleanUpAndHide() {
        ((UsagePointWorkspaceView) parentWorkspace).setAddNewMeterToExistingUsagePoint(false);
        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meter.attach.cancelled", new String[]{usagePointData.getMeterData().getMeterNum()}),
                MediaResourceUtil.getInstance().getInformationIcon());
        usagePointData.setMeterData(null);
        usagePointData.setMeterId(null);
        usagePointData.getHistoryData().setLatestMeterHistId(null);
        usagePointData.getHistoryData().setLatestStsMeterHistId(null);
        UsagePointWorkspaceView usagePointWorkspaceView = (UsagePointWorkspaceView) parentWorkspace;
        usagePointWorkspaceView.getMeterComponent().getNewMrid();
        usagePointWorkspaceView.setUsagePointData(usagePointData);
        hide();

    }

    @UiHandler("btnAssignReplaceMeter")
    void handleAssignMeter(ClickEvent event) {
        clearErrorMessages();
        final Long selectedPm = getSelectedPaymentMode();
        if (selectedPm != null) {
            Long usagePointPm = PaymentModeE
                    .fromId(usagePointData.getUpPricingStructureData().getPricingStructure().getPaymentModeId()).getId();
            if (usagePointPm != selectedPm) {
                Messages messages = MessagesUtil.getInstance();
                Dialogs.confirm(messages.getMessage("ps.paymentmode.change.warn"), messages.getMessage("button.yes"),
                        messages.getMessage("button.no"), MediaResourceUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    continueHandleAssignMeter(selectedPm);
                                }
                            }
                        });
            } else {
                continueHandleAssignMeter(selectedPm);
            }
        } else {
            continueHandleAssignMeter(selectedPm);
        }
    }

    private void continueHandleAssignMeter(Long selectedPm) {
        Messages messages = MessagesUtil.getInstance();
        if (migratingFromThinUnits(selectedPm)) {
            String message = MessagesUtil.getInstance().getMessage("meter.assign.from.units.warn");
            Dialogs.confirm(message, messages.getMessage("button.yes"), messages.getMessage("button.no"),
                    MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                continueAssignMeter();
                            }
                        }
                    });
        } else {
            continueAssignMeter();
        }
    }

    private void continueAssignMeter() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getSearchRpc().getUsagePointDataByMeterNum(lstbxAssignMeter.getText().trim(),
                        new ClientCallback<UsagePointData>() {
                            @Override
                            public void onSuccess(UsagePointData result) {
                                if (((UsagePointWorkspaceView) parentWorkspace).checkDuplicateDataOnOtherTabs(result)) {
                                    return;
                                }
                                checkMeterModelSupportsUnitsMode();
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private boolean migratingFromThinUnits(Long selectedPm) {
        if (selectedPm != null) {
            Long usagePointPm = PaymentModeE
                    .fromId(usagePointData.getUpPricingStructureData().getPricingStructure().getPaymentModeId())
                    .getId();
            if (usagePointPm != null) {
                if (PaymentModeE.THIN_UNITS == PaymentModeE.fromId(usagePointPm)) {
                    return PaymentModeE.THIN_UNITS != PaymentModeE.fromId(selectedPm);
                } else {
                    return false;
                }
            }
        }
        return false;
    }


    private Long getSelectedPaymentMode() {
        Long selectedPm = null;
        if (currentPricingStructureFlowPanel.isVisible() &&
                currentPricingStructureLookup.getSelectedPricingStructureItem() != null) {
            selectedPm = Long.valueOf(currentPricingStructureLookup.getSelectedPricingStructureItem()
                    .getExtraInfoMap().get("paymentModeId").toString());
        }
        return selectedPm;
    }

    private void checkMeterModelSupportsUnitsMode() {
        if (isUnitsPricingStructure) {
            handleAssignMeter(isUnitsPricingStructure);
        } else {
            final MeterRpcAsync meterRpcAsync = clientFactory.getMeterRpc();
            meterRpcAsync.getMeterByMeterNumber(lstbxAssignMeter.getText().trim(), new ClientCallback<MeterDto>() {
                @Override
                public void onSuccess(MeterDto meterDto) {
                    if (meterDto == null) {
                        handleAssignMeter(false);
                    } else {
                        meterRpcAsync.getMeter(meterDto.getId(), new ClientCallback<MeterData>() {
                            @Override
                            public void onSuccess(MeterData meterData) {
                                if (meterData != null) {
                                    MeterModelData meterModelData = meterData.getMeterModelData();
                                    if (meterModelData != null) {
                                        ArrayList<Long> paymentModeIds = meterModelData.getPaymentModeIds();
                                        handleAssignMeter(paymentModeIds != null
                                                && paymentModeIds.contains(Long.valueOf(PaymentModeE.UNITS.getId())));
                                    }
                                }
                            }
                        });
                    }
                }
            });
        }
    }

    private void handleAssignMeter(boolean meterModelSupportsUnitsMode) {
        final String meterNumber = lstbxAssignMeter.getText().trim();
        final Date installDate = dtbxInstallDate.getValue();

        boolean assignIt = true;
        if (meterNumber.isEmpty()) {
            assignIt = false;
            meterfeedBack.setText(MessagesUtil.getInstance().getMessage("error.search.meter"));
            meterfeedBack.setType(Message.MESSAGE_TYPE_ERROR);
            meterfeedBack.setVisible(true);

        }
        if (installDate == null) {
            assignIt = false;
            displayDateErrorMsg("meter.date.install.missing");
        } else if (installDate.after(new Date())) {
            if  (!meterModelSupportsUnitsMode || !upAllowFutureInstallationDates || isUnitsPricingStructure) {
                // isUnitsPricingStructure by default is false.
                assignIt = false;
                displayDateErrorMsg("error.field.installdate.future");
            } else if (changeActivationDatePanel.isVisible() && yesChangeActivationDate.getValue()) {
                //panel visible means no trans and have ticked to make activation date the same as installDate - ok to have future installDate
            } else if (!changeActivationDatePanel.isVisible() && usagePointData != null && usagePointData.getActivationDate() == null) {
                //only meter, no UP OR UP has never been activated - ok to have future installDate
            } else {
                assignIt = false;
                displayDateErrorMsg("error.field.installdate.future.trans.or.possible.gap");
            }
        }

        if (changeActivationDatePanel.isVisible()) {
            if (!yesChangeActivationDate.getValue() && !noChangeActivationDate.getValue()) {
                changeActivationFeedBack.setText(MessagesUtil.getInstance().getMessage("meter.change.activation.date.required"));
                changeActivationFeedBack.setType(Message.MESSAGE_TYPE_ERROR);
                changeActivationFeedBack.setVisible(true);
                assignIt = false;
            }
        }

        if (deviceMoveRefPanel.isVisible()) {
            String deviceMoveRef = deviceMoveRefTxtbx.getText();
            if (deviceMoveRefElement.isRequired() && (deviceMoveRef == null || deviceMoveRef.trim().isEmpty())) {
                deviceMoveRefElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required",
                        new String[] { deviceMoveRefElement.getLabelText() }));
                assignIt = false;
            }

            if (deviceMoveRef != null && !deviceMoveRef.trim().isEmpty()) {
                UpMeterInstall upMeterInstall = new UpMeterInstall();
                upMeterInstall.setInstallRef(deviceMoveRef);
                if (!ClientValidatorUtil.getInstance().validateField(upMeterInstall, "installRef",
                        deviceMoveRefElement)) {
                    assignIt = false;
                }
            }
        }

        if (specialactionreasons.isAttached() && specialactionreasons.isVisible()) {
            boolean spv = specialactionreasons.validate();
            if (assignIt)
                assignIt = spv;
        }

        if (assignIt) {
            checkDeviceStoresOtherVendorsMeter(meterNumber, installDate);
        }
    }

    private void displayDateErrorMsg(String code) {
        if (code == null || code.isEmpty()) {
            return;
        }
        datefeedBack.setText(MessagesUtil.getInstance().getMessage(code));
        datefeedBack.setType(Message.MESSAGE_TYPE_ERROR);
        datefeedBack.setVisible(true);
    }

    private void checkDeviceStoresOtherVendorsMeter(final String meterNumber, final Date installDate) {
        Long movedToStoreId = null;
        if (usagePointData.getMeterId() != null) {
            movedToStoreId = Long.valueOf(lstbxStores.getValue(lstbxStores.getSelectedIndex()));
        }
        final Long deviceStoreId = movedToStoreId;
        //>>RC this code is inefficient, don't always need to do TWO trips to backend.
        //>>RC Put second call getDeviceStoreByMeter in a seperate method and pass message after if-then-else
        clientFactory.getDeviceStoreRpc().getDeviceStore(deviceStoreId, new ClientCallback<EndDeviceStoreData>() {
            @Override
            public void onSuccess(EndDeviceStoreData result) {
                if(result.isStoresOtherVendorsMeter()){
                    Dialogs.confirm(messages.getMessage("devicestore.meters.move.dialog", new String[]{result.getName()}),
                            messages.getMessage("button.yes"),
                            messages.getMessage("button.no"),
                            MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if(confirm) {
                                        assign(deviceStoreId, meterNumber, installDate);
                                    }
                                }
                            });
                } else {
                    clientFactory.getDeviceStoreRpc().getDeviceStoreByMeter(meterNumber, new ClientCallback<EndDeviceStoreData>() {
                        @Override
                        public void onSuccess(EndDeviceStoreData result) {
                            if(result.isStoresOtherVendorsMeter()){
                                Dialogs.confirm(messages.getMessage("devicestore.meters.fetch.dialog"),
                                        messages.getMessage("button.yes"),
                                        messages.getMessage("button.no"),
                                        MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if(confirm) {
                                                    assign(deviceStoreId, meterNumber, installDate);
                                                }
                                            }
                                        });
                            } else {
                                assign(deviceStoreId, meterNumber, installDate);
                            }
                        }
                    });
                }
            }
        });
    }

    private void assign(final Long deviceStoreId, final String meterNumber, final Date installDate) {
        if (specialactionreasons.isAttached() && specialactionreasons.isVisible()) {
            usagePointData.setReplaceMeterReasonsLog(specialactionreasons.getLogEntry());
        }

        if (installDatePanel.isVisible()) {
            Dialogs.confirm(
                    new String[]{
                            messages.getMessage("question.confirm.installation.date.1",
                                    new String[]{meterNumber,
                                            FormatUtil.getInstance().formatDate(installDate),
                                            FormatUtil.getInstance().formatTime(installDate)}),
                            messages.getMessage("question.confirm.installation.date.2")},
                    messages.getMessage("option.confirm"),
                    messages.getMessage("option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                if (clientFactory != null) {
                                    validateInstallDate(meterNumber, installDate, deviceStoreId);
                                }
                            } else {
                                dtbxInstallDate.setFocus(true);
                            }
                        }
                    });
        } else {
            Long moveToStoreId = null;
            if (usagePointData.getMeterId() != null) {
                moveToStoreId = Long.valueOf(lstbxStores.getValue(lstbxStores.getSelectedIndex()));
            }
            validatePricingStructureSelection(meterNumber, moveToStoreId);
        }
    }

    private void validateInstallDate(final String meterNumber, final Date installDate, final Long moveToStoreId) {
        clientFactory.getUsagePointRpc().validateInstallationDate(usagePointData.getId(), usagePointData.getMeterId(),
                meterNumber, installDate, new ClientCallback<ServiceException>() {
            @Override
            public void onSuccess(ServiceException result) {
                //Using the ServiceException here as an object since it already contains the error messageKey as well as the string[] args
                if(result == null){
                    validatePricingStructureSelection(meterNumber, moveToStoreId);

                } else {
                    String message = result.getMessage();
                    if(replacingOldMeter && changeActivationDatePanel.isVisible() && message.equals("usagepoint.error.new.installdate.before.last.sts.vend.date")){
                        changeActivationDatePanel.setVisible(false);
                        usagePointData.setNoTransactions(false);
                    }
                    displayDateErrorMsg(MessagesUtil.getInstance().getMessage(message, result.getArgs()));
                }
            }
        });
    }

    private void validatePricingStructureSelection(final String meterNumber, final Long moveToStoreId) {
        //check if pricingStructure selection box is visible but no selection made yet
        if (usagePointData.getId() != null) {
            boolean valid = true;
            if (currentPricingStructureFlowPanel.isVisible() &&
                currentPricingStructureLookup.getSelectedPricingStructureItem() == null) {
                newCurrentPSRequired.setText(MessagesUtil.getInstance().getMessage("usagepoint.ps.required"));
                valid = false;
            }
            if (valid) {
                assignMeter(meterNumber, moveToStoreId);
            }
        } else {
            assignMeter(meterNumber, moveToStoreId);
        }
    }

    protected void assignMeter(final String newMeterNumber, final Long moveToStoreId) {
        if (newMeterNumber != null && !newMeterNumber.trim().isEmpty()) {
            clientFactory.getSearchRpc().getUsagePointDataByMeterNum(newMeterNumber, new ClientCallback<UsagePointData>() {
                @Override
                public void onSuccess(UsagePointData result) {
                    // check if there is a usage point already - if so, error as we can't assign to a customer already assigned
                    if (result == null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.error.meter.not.found"), MediaResourceUtil.getInstance().getErrorIcon(), null);
                    } else if (result.getId() != null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.error.meter.already.assigned", new String[]{newMeterNumber, result.getName()}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        final MeterData newMeterData = result.getMeterData();
                        final UpPricingStructureData upPricingStructureData = usagePointData.getUpPricingStructureData();
                        if (upPricingStructureData.getPricingStructure() != null) {
                            if (currentPricingStructureFlowPanel.isVisible()
                                    && currentPricingStructureLookup.getSelectedPricingStructureItem() !=  null) {
                                Long psId = Long.valueOf(currentPricingStructureLookup.getSelectedPricingStructureItem()
                                        .getValue());
                                UpPricingStructure currentUPPricingStructure = new UpPricingStructure();
                                currentUPPricingStructure.setPricingStructureId(psId);
                                upPricingStructureData.setUpPricingStructure(currentUPPricingStructure);
                                upPricingStructureData.setFutureUpPricingStructureData(null);
                                assignMeter(newMeterData, moveToStoreId);
                            } else {
                                //check meter is the correct model by the usage point pricing structure
                                Long futureId = null;
                                Date futurePsStartDate = null;
                                if (upPricingStructureData.getFutureUpPricingStructureData() != null) {
                                    futureId = upPricingStructureData.getFutureUpPricingStructureData().getUpPricingStructure().getPricingStructureId();
                                    futurePsStartDate = upPricingStructureData.getUpPricingStructure().getStartDate();
                                }
                                final Long currentPricingStructureId = upPricingStructureData.getUpPricingStructure().getPricingStructureId();
                                final Long futurePricingStructureId = futureId;
                                final Date futurePricingStructureStartDate = futurePsStartDate;
                                clientFactory.getMeterModelRpc().getMeterModelIds(currentPricingStructureId, new ClientCallback<ArrayList<Long>>() {
                                    boolean proceed = true;
                                    @Override
                                    public void onSuccess(ArrayList<Long> meterModelResult) {
                                        if (!(meterModelResult.contains(newMeterData.getMeterModelId()))) {
                                            populatePricingStructureLookup(newMeterData);
                                            newCurrentPSRequired.setText(MessagesUtil.getInstance().getMessage("meter.new.current.pricingstructure.required", new String[] { newMeterData.getMeterNum(),usagePointData.getUpPricingStructureData().getPricingStructure().getName() }));
                                            currentPricingStructureFlowPanel.setVisible(true);
                                            proceed = false;
                                        }
                                        if (proceed) {
                                            List<PSDto> pSDtos = new ArrayList<>();
                                            pSDtos.add(new PSDto(currentPricingStructureId, new Date()));
                                            if (futurePricingStructureId != null) {
                                                pSDtos.add(new PSDto(futurePricingStructureId, futurePricingStructureStartDate));
                                            }
                                            clientFactory.getPricingStructureRpc().isregReadPsSameBillingDetsAsMeterModel(pSDtos, newMeterData.getMeterModelData().getMdcId(),
                                                    clientFactory.getUser().getUserName(), new ClientCallback<List<ChannelCompatibilityE>>() {
                                                        @Override
                                                        public void onSuccess(List<ChannelCompatibilityE> results) {
                                                            if (results.contains(ChannelCompatibilityE.NONE_MATCH)) {
                                                                populatePricingStructureLookup(newMeterData);
                                                                currentPricingStructureFlowPanel.setVisible(true);
                                                                newCurrentPSRequired.setText(MessagesUtil.getInstance().getMessage("meter.new.current.pricingstructure.required",
                                                                        new String[]{newMeterData.getMeterNum(), usagePointData.getUpPricingStructureData().getPricingStructure().getName()}));
                                                                proceed = false;
                                                                Dialogs.centreErrorMessage(messages.getMessage("error.pricingStructure.billingDets.notsame.asmetermodel.mdc"),
                                                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                                                        MessagesUtil.getInstance().getMessage("button.close"));
                                                            }
                                                            if (proceed && futurePricingStructureId != null) {
                                                                clientFactory.getMeterModelRpc().getMeterModelIds(futurePricingStructureId, new ClientCallback<ArrayList<Long>>() {
                                                                    @Override
                                                                    public void onSuccess(ArrayList<Long> meterModelResult) {
                                                                        if (!(meterModelResult.contains(newMeterData.getMeterModelId()))) {
                                                                            upPricingStructureData.setFutureUpPricingStructureData(null);
                                                                        }
                                                                        assignMeter(newMeterData, moveToStoreId);
                                                                    }
                                                                });
                                                            } else if (proceed) {
                                                                assignMeter(newMeterData, moveToStoreId);
                                                            }
                                                        }
                                                    });
                                        }
                                    }
                                });
                            }
                        } else {
                            assignMeter(newMeterData, moveToStoreId);
                        }
                    }
                }
            });
        }
    }

    private void assignMeter(final MeterData newMeterData, final Long moveToStoreId) {
        if (!replacingOldMeter) {
            if (installDatePanel.isVisible()) {                  //Fetch but have a UP on page
                //using last remove date from previous meter ... will be null if none there
                assignMeterContinue(newMeterData, moveToStoreId, usagePointData.getLastRemoveDate());
            } else {
                assignMeterContinue(newMeterData, moveToStoreId, null);
            }
        } else {
            Date lastUpInstallDate = null;
            if (usagePointData.getUpMeterInstall() != null) {
                lastUpInstallDate = usagePointData.getUpMeterInstall().getInstallDate();
            }
            assignMeterContinue(newMeterData, moveToStoreId, lastUpInstallDate);
        }
    }

    private void assignMeterContinue(final MeterData newMeterData, Long moveToStoreId, Date lastInstallOrRemoveDate) {
        if (usagePointData.getMeterData() == null) {
            usagePointData.setMeterData(new MeterData());
        }
        usagePointData.getMeterData().setEndDeviceStoreId(moveToStoreId);

        upMeterInstall = new UpMeterInstall();
        upMeterInstall.setInstallDate(dtbxInstallDate.getValue());

        //check that new installation date is AFTER last installation date if there is one
        if (lastInstallOrRemoveDate != null && !upMeterInstall.getInstallDate().after(lastInstallOrRemoveDate)) {
            DateTimeFormat fmt = DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat());
            String installErrorMessage = "usagepointworkspace.error.meter.installdate.before.previous";
            if (!replacingOldMeter) {
                installErrorMessage = "usagepointworkspace.error.meter.installdate.before.last.remove";
            }
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage(installErrorMessage,
                    new String[]{fmt.format(upMeterInstall.getInstallDate()), fmt.format(lastInstallOrRemoveDate)}),
                    MediaResourceUtil.getInstance().getErrorIcon(), null);
            return;
        }

        //check that new installation date is not before the PS startdate
        Date firstTariffStartDate = usagePointData.getFirstTariffStartDate();
        if (firstTariffStartDate != null && dtbxInstallDate.getValue().before(firstTariffStartDate)) {
            Date lastCyclicChargeDate = null;
            if (usagePointData != null) {
                lastCyclicChargeDate = usagePointData.getLastCyclicChargeDate();
            }
            String message = UsagePointWorkspaceView.constructMessageInstallDtVsTariffStart(firstTariffStartDate, lastCyclicChargeDate);
            Messages messages = MessagesUtil.getInstance();
            Dialogs.confirm(message,
                    messages.getMessage("button.yes"),
                    messages.getMessage("button.no"),
                    MediaResourceUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if(confirm) {
                        assignMeterContinue2(newMeterData);
                    }
                }
            });
        } else {
            assignMeterContinue2(newMeterData);
        }
    }

    private void assignMeterContinue2(final MeterData newMeterData) {
        //this is the UpMeterInstall currently on usagePointData - will be null if Up NOT currently active
        //puts upMeterInstall with null removeDate in object) see SearchRpcImpl: setCommonUsagePointFields(..)
        String deviceMoveRef = deviceMoveRefTxtbx.getText();
        UpMeterInstall previousUpMeterInstall = usagePointData.getUpMeterInstall();
        if (previousUpMeterInstall != null) {    // && usagePointData.getUpMeterInstall().getRemoveDate() != null) {
            previousUpMeterInstall.setRemoveDate(upMeterInstall.getInstallDate());
            if (deviceMoveRef != null && !deviceMoveRef.trim().isEmpty()) {
                previousUpMeterInstall.setRemoveRef(deviceMoveRef);
            }

        }
        upMeterInstall.setMeterId(newMeterData.getId());
        upMeterInstall.setUsagePointId(usagePointData.getId());

        if (deviceMoveRef != null && !deviceMoveRef.trim().isEmpty()) {
            upMeterInstall.setInstallRef(deviceMoveRef);
        }

        if (parentWorkspace instanceof UsagePointWorkspaceView) {
            this.newMeterData = newMeterData;

            if (usagePointData != null && usagePointData.getId() != null) {
                //for register reading PS or Model with MDC with mdcChannels: check correlation between billingDets
                UpPricingStructureData upPricingStructureData = usagePointData.getUpPricingStructureData();
                PricingStructure pricingStructure = upPricingStructureData.getPricingStructure();
                Long currentPsId = pricingStructure.getId();
                if (currentPricingStructureFlowPanel.isVisible()) {
                    LookupListItem item = currentPricingStructureLookup.getSelectedPricingStructureItem();
                    currentPsId = item == null ? null : Long.valueOf(item.getValue());
                }
                UpPricingStructureData futurePsData = upPricingStructureData.getFutureUpPricingStructureData();
                List<PSDto> pSDtos = new ArrayList<>();
                //for currentPS sending in new date and not installDate because on takeOn of data from other systems,
                //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
                pSDtos.add(new PSDto(currentPsId, new Date()));
                if (futurePsData != null) {
                    pSDtos.add(new PSDto(futurePsData.getUpPricingStructure().getPricingStructureId(),
                            futurePsData.getUpPricingStructure().getStartDate()));
                }
                new AssignMeterDialogueValidatePStoMM(this).isregReadPsSameBillingDetsAsMeterModel(clientFactory,
                        pSDtos, newMeterData.getMeterModelData().getMdcId(), clientFactory.getUser().getUserName(), logger);
                // when this is needed when activating the UP immediately and the PS has been
                // changed. the page only updates the usagePointData object after this point
                // which causes it to think the PS is outdated
                pricingStructure.setId(currentPsId);
            } else {
                logger.info("AssignMeterDialogueBox: assignMeterContinue:  usagePointData.getId() == null");
                fireUpdateEvent(null);
            }
        }
    }

    public void assignMeterCheckChannels() {
        if (usagePointData.getId() == null || !usagePointData.getRecordStatus().equals(RecordStatus.ACT)
                || !newMeterData.getMeterModelData().isMdcHasChannels()) {
            logger.info("AssignMeterDialogueBox: assignMeterCheckChannels:  usagePointData.getId() == null");
            fireUpdateEvent(null);
            return;
        }

        final AssignMeterDialogueBox parent = this;
        logger.info("AssignMeterDialogueBox: getChannels:  metermodelID= " + newMeterData.getMeterModelId());
        MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(newMeterData.getId(),
                newMeterData.getMeterModelId(),
                usagePointData.getId(),
                usagePointData.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId(),
                null,
                upMeterInstall.getInstallDate());
        clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo, new ClientCallback<MeterUpMdcChannelInfo>() {
            @Override
            public void onSuccess(MeterUpMdcChannelInfo result) {
                if (result == null || result.getChannelList() == null || result.getChannelList().isEmpty()) {
                    logger.info("getMeterUpMdcChannelInfo: result is null");
                    fireUpdateEvent(null);
                    return;
                } else {
                    logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size=" + result.getChannelList().size());

                    //get initial readings for the channels
                    final AssignChannelReadingsDialogueBox assignChannelReadings =
                            new AssignChannelReadingsDialogueBox(parent, result, newMeterData.getMeterNum(),
                                    usagePointData.getName(),
                                    newMeterData.getMeterModelData().getName(),
                                    newMeterData.getMeterModelData().getMdcName(),
                                    usagePointData.getUpPricingStructureData().getPricingStructure().getName(),
                                    newMeterData.getMeterModelData().getServiceResourceId());
                            Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                @Override
                                public void execute() {
                                    assignChannelReadings.center();
                                    assignChannelReadings.show();
                                }
                            });
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void fireUpdateEvent(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        //fireUsagePointUpdatedEvent
        usagePointData.setUpMeterInstallAndPrevious(upMeterInstall);
        usagePointData.setChangeActivationDate(yesChangeActivationDate.getValue());

        UsagePointUpdatedEvent updateevent = new UsagePointUpdatedEvent(((UsagePointWorkspaceView) parentWorkspace),
                usagePointData, UsagePointUpdatedEvent.ASSIGN_METER);
        updateevent.setAssignToMeterNumber(newMeterData.getMeterNum());
        if (meterUpMdcChannelInfo != null) {
            updateevent.setChannelReadingsList(meterUpMdcChannelInfo.getChannelList());
        }
        clientFactory.getEventBus().fireEvent(updateevent);
        clear();
        this.hide();
    }

    public void populateDeviceMoveRef() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.USAGEPOINT_DEVICE_MOVEMENT_REFERENCE,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        setDeviceMoveRefValue(result.getValue());
                    }
                });
    }

    private void populateStoresListBox() {
        final ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxStores.setLookupItems(result);
                if (usagePointData != null && usagePointData.getMeterData() != null && usagePointData.getMeterData().getEndDeviceStoreId() != null) {
                    lstbxStores.selectItemByValue(usagePointData.getMeterData().getEndDeviceStoreId().toString());
                }
            }

        };
        if (clientFactory != null) {
            lstbxStores.clear();
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getLookupRpc().getEndDeviceStoresLookupList(lookupSvcAsyncCallback);
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void populatePricingStructureLookup(MeterData meterData) {
        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT)) {
            currentPricingStructureLookup.updateLookupList(meterData.getMeterModelData().getServiceResourceId(),
                    meterData.getMeterTypeId(), meterData.getMeterModelData().getPaymentModeIds());
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.pricing.structure.accessdenied"),
                    MediaResourceUtil.getInstance().getErrorIcon(), null);
        }
    }

    public void setInstallationDate(Date installationDate) {
        dtbxInstallDate.setValue(installationDate);
    }

    protected void populateAssignMeterList() {
        lstbxAssignMeter.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    lstbxAssignMeter.setText( ((MeterSuggestion) event.getSelectedItem()).getMeter().getNumber());
                }
            }
        });
        lstbxAssignMeter.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                if (!((DefaultSuggestionDisplay) lstbxAssignMeter.getSuggestionDisplay()).isSuggestionListShowing()) {
                    lstbxAssignMeter.showSuggestionList();
                }
            }
        });
    }

    public void setDeviceMoveRefValue(String deviceMoveRefValue) {
        if (deviceMoveRefValue != null && usagePointData != null && usagePointData.getId() != null) {
            Messages messages = MessagesUtil.getInstance();
            if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.required"))) {
                deviceMoveRefPanel.setVisible(true);
                deviceMoveRefElement.setRequired(true);
            } else if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.optional"))) {
                deviceMoveRefPanel.setVisible(true);
                deviceMoveRefElement.setRequired(false);
            } else if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.unavailable"))) {
                deviceMoveRefPanel.removeFromParent();
            }
        }
    }

    public void clear() {
        clearErrorMessages();
        lstbxAssignMeter.setText("");
        deviceMoveRefTxtbx.setText("");
        currentPricingStructureLookup.clearSelection();
        currentPricingStructureFlowPanel.setVisible(false);
        specialactionreasons.clearFields();
    }

    public void clearErrorMessages() {
        feedBack.setText("");
        feedBack.setVisible(false);
        meterfeedBack.setText("");
        meterfeedBack.setVisible(false);
        datefeedBack.setText("");
        datefeedBack.setVisible(false);
        newCurrentPSRequired.setText("");
        changeActivationFeedBack.setText("");
        changeActivationFeedBack.setVisible(false);
        specialactionreasons.clearErrorMessages();
        deviceMoveRefElement.clearErrorMsg();
    }

    void handleCurrentPricingStructureChange() {
        Long currentPaymentMode = null;
        if (currentPricingStructureFlowPanel.isVisible()
                && currentPricingStructureLookup.getSelectedPricingStructureItem() != null) {
            LookupListItem pricingStructureItem = currentPricingStructureLookup.getSelectedPricingStructureItem();
            currentPaymentMode = Long.valueOf(pricingStructureItem.getExtraInfoMap().get("paymentModeId").toString());
        }
        isUnitsPricingStructure = currentPaymentMode != null
                && PaymentModeE.UNITS == PaymentModeE.fromId(currentPaymentMode);
    }
}
