package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import za.co.ipay.metermng.shared.MeterMngStatics;

public class CustomerPlace extends AbstractUsagePointPlace {

    public static final CustomerPlace NEW_CUSTOMER_PLACE = new CustomerPlace("new");

    public static final int SEARCH_BY_ID = 0;
    public static final int SEARCH_BY_AGREEMENT_REF = 1;
    public static final int SEARCH_BY_ACCOUNT_NAME = 2;
    public static final int SEARCH_BY_ID_NUMBER = 3;

    private final String customerId;
    private boolean isFromURL = true;
    private int searchType = 0;

    private ContractType contractType = ContractType.EXISTING;

    public CustomerPlace(String customerId) {
        this.customerId = customerId;
        if (isNew()) {
            contractType = ContractType.NEW;
        }
    }

    public CustomerPlace(String customerId, ContractType contractType) {
        this.customerId = customerId;
        if (contractType != null) {
            this.contractType = contractType;
        }
    }

    public CustomerPlace(String customerId, String previousHistoryToken) {
        this.customerId = customerId;
        this.previousHistoryToken = previousHistoryToken;
    }

    public CustomerPlace(String customerId, int searchType, String previousHistoryToken) {
        this.customerId = customerId;
        this.searchType = searchType;
        this.previousHistoryToken = previousHistoryToken;
    }

    public CustomerPlace(String customerId, boolean isFromURL) {
        super();
        this.customerId = customerId;
        this.isFromURL = isFromURL;
    }

    public static String createPlaceString(CustomerPlace place, String customerId) {
        if (place == null || customerId == null) {
            return "customer:" + customerId;
        }
        if (place.customerId == null || !place.customerId.equals(customerId)) {
            place = new CustomerPlace(customerId, place.contractType);
        }
        return "customer:" + new Tokenizer().getToken(place);
    }

    @Override
    public boolean isFromURL() {
        return isFromURL;
    }

    public String getCustomerId() {
        return customerId;
    }

    @Override
    public boolean isNew() {
        return customerId.equalsIgnoreCase("new");
    }

    public int getSearchType() {
        return searchType;
    }

    public void setSearchType(int searchType) {
        this.searchType = searchType;
    }

    @Override
    public String toString() {
        return "CustomerPlace [customerId=" + customerId + ", isFromURL="
            + isFromURL + ", searchType=" + searchType + ", contractType=" + contractType.name() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((customerId == null) ? 0 : customerId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        CustomerPlace other = (CustomerPlace) obj;
        if (customerId == null) {
            if (other.customerId != null)
                return false;
        } else if (!customerId.equals(other.customerId))
            return false;
        return contractType == other.contractType;
    }

    public enum ContractType {
        NEW, EXISTING
    }

    @Prefix(value = "customer")
    public static class Tokenizer implements PlaceTokenizer<CustomerPlace> {
        @Override
        public String getToken(CustomerPlace place) {
            if (place.getPreviousHistoryToken() != null && !place.getPreviousHistoryToken().equals("")) {
                return place.getCustomerId() + MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getSearchType() +
                    MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getPreviousHistoryToken();
            } else {
                return place.getCustomerId() + MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getSearchType();
            }
        }

        @Override
        public CustomerPlace getPlace(String token) {
            if (token != null && token.length() > 0) {
                int index = token.indexOf(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                if (index > -1) {
                    return new CustomerPlace(token.substring(0, index),
                        Integer.parseInt(token.substring(index + 1)), token.substring(index + 2));
                }
            }
            return new CustomerPlace(token);
        }
    }
}
