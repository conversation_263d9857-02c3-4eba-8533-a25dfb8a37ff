<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="superMeterElement" labelText="{msg.getTaskScheduleSuperMeter}:" required="true">
                <g:ListBox styleName="gwt-TextBox" ui:field="superMeterBox" title="{msg.getTaskScheduleSuperMeter}" />
            </p1:FormElement> 
            <p1:FormElement ui:field="meterReadingTypeElement" labelText="{msg.getTaskScheduleMeterReadingType}:" required="true">
                <g:ListBox ui:field="meterReadingTypeBox" title="{msg.getTaskScheduleMeterReadingType}" />
            </p1:FormElement> 
            <p1:FormElement ui:field="timePeriodElement" labelText="{msg.getTaskScheduleTimePeriod}:" required="true">
                <g:ListBox ui:field="timePeriodBox" title="{msg.getTaskScheduleTimePeriod}" />
            </p1:FormElement> 
        </p1:FormRowPanel>
        
    </g:FlowPanel>    

</ui:UiBinder> 