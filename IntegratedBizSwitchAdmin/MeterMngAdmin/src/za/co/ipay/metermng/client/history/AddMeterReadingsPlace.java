package za.co.ipay.metermng.client.history;

import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * AddMeterReadingsPlace is used to get to one of the add meter readings demo screens.
 * <AUTHOR>
 */
public class AddMeterReadingsPlace extends Place {
    
    public static final String SINGLE_METER_TYPE = "single";
    public static final String SUPER_METER_TYPE = "super";
    public static final String THIN_PAYMENT_MODE = "thin";
        
    private String meterType;
    private String paymentMode;
    
    public AddMeterReadingsPlace() {
        this.meterType = "";
        this.paymentMode = "";
    }
    
    public AddMeterReadingsPlace(String meterType, String paymentMode) {
        this.meterType = meterType;
        this.paymentMode = paymentMode;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } 
        if (!(o instanceof AddMeterReadingsPlace)) {
            return false;
        }
        AddMeterReadingsPlace p = (AddMeterReadingsPlace) o;
        return ValidateUtil.isEqual(p.getMeterType(), getMeterType()) 
                && ValidateUtil.isEqual(p.getPaymentMode(), getPaymentMode());
    }
    
    @Override
    public int hashCode() {
        int code = 27;
        if (meterType != null) {
            code += meterType.hashCode();
        }
        if (paymentMode != null) {
            code += paymentMode.hashCode();
        }
        return code;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AddMeterReadingsPlace:");
        sb.append(" meterType:").append(meterType);
        sb.append(" paymentMode:").append(paymentMode);
        return sb.toString();
    }

    public String getMeterType() {
        return meterType;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public static String getPlaceAsString(AddMeterReadingsPlace place) {
        return "addmeterreadings:" + new AddMeterReadingsPlace.Tokenizer().getToken(place);
    }
    
    @Prefix(value = "addmeterreadings")
    public static class Tokenizer implements PlaceTokenizer<AddMeterReadingsPlace> {
        
        @Override
        public String getToken(AddMeterReadingsPlace place) {
            return place.getMeterType() + MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getPaymentMode();
        }

        @Override
        public AddMeterReadingsPlace getPlace(String token) {
            String[] tokens = token.split("\\"+MeterMngStatics.PLACE_TOKEN_SEPARATOR);
            if (tokens != null && tokens.length == 2) {
                return new AddMeterReadingsPlace(tokens[0], tokens[1]);
            } else {
                return new AddMeterReadingsPlace();
            }
        }
    }    
}
