package za.co.ipay.metermng.client.view.workspace.importfile;

import java.util.logging.Logger;

import com.google.gwt.place.shared.Place;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.ImportFilePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class ImportFileWorkspaceFactory implements WorkspaceFactory {
	
	private ClientFactory clientFactory;
    private static Logger logger = Logger.getLogger(ImportFileWorkspaceFactory.class.getName());

	public ImportFileWorkspaceFactory(ClientFactory clientFactory) {
		this.clientFactory = clientFactory;
		clientFactory.getWorkspaceContainer().register(this);
	}

	@Override
	public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_FILE_IMPORT)) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                    MediaResourceUtil.getInstance().getLockedIcon(), 
                    MessagesUtil.getInstance().getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;               
        }
        try {
            ImportFileWorkspaceView view = new ImportFileWorkspaceView(clientFactory, (ImportFilePlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            logger.info("ERROR: workspacecreation failed, exception= " + e.getMessage());
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof ImportFilePlace);
	}

}
