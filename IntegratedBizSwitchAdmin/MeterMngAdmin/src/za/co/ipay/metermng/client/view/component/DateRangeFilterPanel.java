package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;

public class DateRangeFilterPanel extends Composite {
    private static DateRangeFilterPanelUiBinder uiBinder = GWT.create(DateRangeFilterPanelUiBinder.class);

    interface DateRangeFilterPanelUiBinder extends UiBinder<Widget, DateRangeFilterPanel> {
    }

    @UiField HorizontalPanel panelDates;
    @UiField DateBox filterStartDatebox;
    @UiField DateBox filterEndDatebox;

    private ListDataProvider<?> dataProvider;
    
    /**
     * This is a Ui factory method that allows all the workspace's UiBinder XML
     * files to use the same instance of the UiMessages.
     * 
     * @return The UiMessages used to get i18n strings for the UiBinder XML files.
     */
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    public DateRangeFilterPanel() {
        initWidget(uiBinder.createAndBindUi(this));

        StrictDateFormat format = new StrictDateFormat(
                DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat()));
        filterStartDatebox.setFormat(format);
        KeyUpHandler dateKeyUpHandler = new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleDateFilter();
            }
        };
        filterStartDatebox.getTextBox().addKeyUpHandler(dateKeyUpHandler);
        ValueChangeHandler<Date> dateValueChangeHandler = new ValueChangeHandler<Date>() {
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                handleDateFilter();
            }
        };
        filterStartDatebox.addValueChangeHandler(dateValueChangeHandler);
        filterEndDatebox.setFormat(format);
        filterEndDatebox.getTextBox().addKeyUpHandler(dateKeyUpHandler);
        filterEndDatebox.addValueChangeHandler(dateValueChangeHandler);
    }

    public boolean isValid(Date value, String filter) {
        String filterDates[] = filter.split(",");
        // will check if the date is within the two filter dates. adding 1 day to the
        // end date to make the date inclusive
        return !((!filterDates[0].isEmpty() && new Date(Long.parseLong(filterDates[0])).after(value))
                || (filterDates.length == 2 && new Date(Long.parseLong(filterDates[1]) + 86400000).before(value)));
    }

    private void handleDateFilter() {
        if (dataProvider != null) {
            Date start = filterStartDatebox.getValue();
            Date end = filterEndDatebox.getValue();
            if (start == null && end == null) {
                if (dataProvider instanceof IPayDataProvider) {
                    ((IPayDataProvider<?>) dataProvider).resetFilter();
                } else if (dataProvider instanceof IPayDataProviderWithListOfFilters) {
                    ((IPayDataProviderWithListOfFilters<?>) dataProvider).resetFilter();
                }
            } else {
                String startText = "";
                if (start != null) {
                    startText += start.getTime();
                }
                String endText = "";
                if (end != null) {
                    endText += end.getTime();
                }
                if (dataProvider instanceof IPayDataProvider) {
                    ((IPayDataProvider<?>) dataProvider).setFilter(startText + "," + endText);
                } else if (dataProvider instanceof IPayDataProviderWithListOfFilters) {
                    List<String> filterList = new ArrayList<String>();
                    filterList.add(startText + "," + endText);
                    ((IPayDataProviderWithListOfFilters<?>) dataProvider).setFilter(filterList);
                }
            }
        }
    }

    public void setDataProvider(ListDataProvider<?> dataProvider) {
        this.dataProvider = dataProvider;
    }
    
    public void changeFilter(boolean isDateSelected) {
        panelDates.setVisible(isDateSelected);
        filterStartDatebox.setValue(null);
        filterEndDatebox.setValue(null);
    }
}
