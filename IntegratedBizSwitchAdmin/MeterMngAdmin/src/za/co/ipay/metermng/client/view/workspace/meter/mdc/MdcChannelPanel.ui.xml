<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
    
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    
    <ui:style>
        .panel_spacing {
            display: inline;
            margin_top: 0.1em;
            margin-right: 1em; 
            margin-left: 0.1em;
            vertical-align: top;
        }
    </ui:style>  

    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="channelValueElement" labelText="{msg.getChannelValueLabel}:" required="true" helpMsg="{msg.getChannelValueHelp}">
                <g:TextBox ui:field="channelValueBox" visibleLength="20" styleName="gwt-TextBox" />
            </p1:FormElement>
                        
            <p1:FormElement ui:field="activeElement" helpMsg="{msg.getChannelActiveHelp}">
                <g:CheckBox ui:field="activeBox" text="{msg.getChannelActive}" checked="true" styleName="gwt-Label-bold-left"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="nameElement" labelText="{msg.getChannelNameLabel}:" required="true">
                <g:TextBox ui:field="nameBox" visibleLength="50" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="descripElement" labelText="{msg.getChannelDescripLabel}:">
                <g:TextBox ui:field="descripBox" visibleLength="50" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="billingDetElement" labelText="{msg.getBillingDetNameLabel}:" helpMsg="{msg.getBillingDetNameChannelHelp}">
                <g:ListBox ui:field="billingDetListBox"  multipleSelect="true" visibleItemCount="5" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="meterReadingTypeElement" labelText="{msg.getChannelMeterReadingTypeLabel}:" required="true">
                <g:ListBox ui:field="meterReadingTypeListBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
                
        <p1:FormRowPanel>
            <p1:FormElement ui:field="timeIntervalElement" labelText="{msg.getChannelTimeIntervalLabel}:" helpMsg="{msg.getChannelTimeIntervalHelp}">
                <g:ListBox ui:field="timeIntervalListBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
 
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="maxSizeElement" labelText="{msg.getChannelMaxSizeLabel}:" required="true" helpMsg="{msg.getChannelMaxSizeHelp}">
                <p1:BigDecimalValueBox ui:field="maxSizeBox" styleName="gwt-TextBox-ipay" visibleLength="15"/>
            </p1:FormElement>
        </p1:FormRowPanel>

        <p1:FormRowPanel>
            <p1:FormElement ui:field="readingMultiplierElement" labelText="{msg.getChannelReadingMultiplierLabel}:" required="false" helpMsg="{msg.getChannelReadingMultiplierHelp}">
                <p1:BigDecimalValueBox ui:field="readingMultiplierBox" styleName="gwt-TextBox-ipay" visibleLength="15"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelOverrideMeterModelsTitle}:" styleName="{style.panel_spacing}"/>
            <g:TextArea ui:field="overrideMeterModels" text="" width="50" enabled="false"/>
        </p1:FormRowPanel>
        
    </g:FlowPanel>
    
</ui:UiBinder> 