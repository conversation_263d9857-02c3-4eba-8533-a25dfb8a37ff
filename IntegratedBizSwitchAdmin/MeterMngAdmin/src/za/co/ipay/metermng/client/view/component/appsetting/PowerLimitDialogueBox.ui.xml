<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui='urn:ui:com.google.gwt.uibinder'
	xmlns:g='urn:import:com.google.gwt.user.client.ui'
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>
	</ui:style>
	
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
	
	<g:FlowPanel>
	      <p1:FormRowPanel>
	          <p1:FormElement debugId="valueElement" ui:field="powerLimitValueElement"
							  labelText="{msg.getPowerlimitEditValuePrompt}:" helpMsg="{msg.getPowerlimitEditValueHelp}"
							  required="true">
	              <g:IntegerBox debugId="nameBox" text="" ui:field="powerLimitValueTextBox"
							 title="{msg.getPowerlimitEditValuePrompt}" width="10" visibleLength="10"/>
	          </p1:FormElement>
	      </p1:FormRowPanel>
	      <p1:FormRowPanel>
	          <g:Button text="{msg.getAddButton}" ui:field="addUpdateButton"/>
	          <g:Button text="{msg.getRemoveButton}" ui:field="removeUpdateButton"/>
	          <g:Button text="{msg.getCancelButton}" ui:field="cancelButton"/>
	      </p1:FormRowPanel>
	</g:FlowPanel>
</ui:UiBinder>