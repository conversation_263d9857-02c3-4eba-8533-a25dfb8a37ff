package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;

public class DisplayEngineeringToken extends BaseComponent {

    @UiField Label valueUsagePointName;
    @UiField Label valueMeterNumber;
    @UiField Label valueTokenTypeName;
    @UiField Label valueToken1;
    @UiField Label lblVerifyToken1;
    @UiField Label valueToken2;
    @UiField Label lblVerifyToken2;
    @UiField Label valueToken3;
    @UiField Label lblVerifyToken3;
    @UiField Label valueToken4;
    @UiField Label lblVerifyToken4;
    @UiField Label valueOldSGC;
    @UiField Label valueNewSGC;
    @UiField Label valueOldKRN;
    @UiField Label valueNewKRN;
    @UiField Label valueOldTI;
    @UiField Label valueNewTI;
    @UiField Label valuePowerLimitUnits;
    @UiField Label valueUnits;
    @UiField Label valueClearTid;
    @UiField Label valueDescription;
    @UiField Label valueTransDate;
    @UiField Label valueUser;
    @UiField Label valueUserRef;
    @UiField Label valueImportFileName;
    @UiField HorizontalPanel hpUsagePointName;
    @UiField HorizontalPanel hpMeterNumber;
    @UiField HorizontalPanel hpToken2;
    @UiField HorizontalPanel hpToken3;
    @UiField HorizontalPanel hpToken4;
    @UiField HorizontalPanel hpOldSGC;
    @UiField HorizontalPanel hpNewSGC;
    @UiField HorizontalPanel hpOldKRN;
    @UiField HorizontalPanel hpNewKRN;
    @UiField HorizontalPanel hpOldTI;
    @UiField HorizontalPanel hpNewTI;
    @UiField HorizontalPanel hpPowerLimitUnits;
    @UiField HorizontalPanel hpUnits;
    @UiField HorizontalPanel hpDescription;
    @UiField HorizontalPanel hpClearTid;
    @UiField HorizontalPanel hpUserRef;
    @UiField HorizontalPanel hpImportFileName;

    @UiField FlowPanel stsTokenVerifiedPanel1;
    @UiField Label lblTokenValue1;
    @UiField Label lblTokenClassValue1;
    @UiField Label lblTokenSubclassValue1;
    @UiField Label lblTokenIdValue1;
    @UiField Label lblTokenDateValue1;
    @UiField Label lblUnitsValue1;

    @UiField FlowPanel stsTokenVerifiedPanel2;
    @UiField Label lblTokenValue2;
    @UiField Label lblTokenClassValue2;
    @UiField Label lblTokenSubclassValue2;
    @UiField Label lblTokenIdValue2;
    @UiField Label lblTokenDateValue2;
    @UiField Label lblUnitsValue2;

    @UiField FlowPanel stsTokenVerifiedPanel3;
    @UiField Label lblTokenValue3;
    @UiField Label lblTokenClassValue3;
    @UiField Label lblTokenSubclassValue3;
    @UiField Label lblTokenIdValue3;
    @UiField Label lblTokenDateValue3;
    @UiField Label lblUnitsValue3;

    @UiField FlowPanel stsTokenVerifiedPanel4;
    @UiField Label lblTokenValue4;
    @UiField Label lblTokenClassValue4;
    @UiField Label lblTokenSubclassValue4;
    @UiField Label lblTokenIdValue4;
    @UiField Label lblTokenDateValue4;
    @UiField Label lblUnitsValue4;
    @UiField Label lblSGC;
    @UiField Label lblKRN;
    @UiField Label lblTI;

    @UiField Button sendReprintBtn;
    @UiField Label lblUnits;

    private PopupPanel engineeringTransactionPopup;

    private StsEngineeringTokenData stsEngineeringTokenData;

    private boolean isUserRefAvailable = false;

    private static DisplayEngineeringTokenUiBinder uiBinder = GWT.create(DisplayEngineeringTokenUiBinder.class);

    interface DisplayEngineeringTokenUiBinder extends UiBinder<Widget, DisplayEngineeringToken> {
    }

    public DisplayEngineeringToken(ClientFactory clientFactory, StsEngineeringTokenData theData,
                                   PopupPanel engineeringTransactionPopup, boolean isUserRefAvailable) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        this.isUserRefAvailable = isUserRefAvailable;
        this.engineeringTransactionPopup = engineeringTransactionPopup;
        setStsEngineeringTokenData(theData);
        setListeners();
        checkPermissions();
    }

    private void setListeners() {
        sendReprintBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        showReprintOptions();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        lblVerifyToken1.addClickHandler(new VerifyTokenClickHandler(1));
        lblVerifyToken2.addClickHandler(new VerifyTokenClickHandler(2));
        lblVerifyToken3.addClickHandler(new VerifyTokenClickHandler(3));
        lblVerifyToken4.addClickHandler(new VerifyTokenClickHandler(4));
    }

    private void checkPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_REPRINT)) {
            sendReprintBtn.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_VERIFYTOKEN)) {
            lblVerifyToken1.removeFromParent();
            lblVerifyToken2.removeFromParent();
            lblVerifyToken3.removeFromParent();
            lblVerifyToken4.removeFromParent();
        }
    }

    public void setStsEngineeringTokenData(StsEngineeringTokenData stsEngineeringTokenData) {
        this.stsEngineeringTokenData = stsEngineeringTokenData;
        boolean showVerifyLinks = true;

        if (this.stsEngineeringTokenData.getUsagePointName() != null) {
            valueUsagePointName.setText(this.stsEngineeringTokenData.getUsagePointName());
        } else {
            hpUsagePointName.removeFromParent();
        }
        if (this.stsEngineeringTokenData.getMeterNumber() != null) {
            valueMeterNumber.setText(this.stsEngineeringTokenData.getMeterNumber());
        } else {
            hpMeterNumber.removeFromParent();
        }
        String tokenTypeName = this.stsEngineeringTokenData.getTokenTypeName();
        valueTokenTypeName.setText(tokenTypeName);
        valueToken1.setText(this.stsEngineeringTokenData.getToken1());
        Messages messagesInstance = MessagesUtil.getInstance();
        if (stsEngineeringTokenData.getStsEngTokenTypeId() != StsEngineeringTokenTypeE.KEY_CHANGE.getId()) {
            lblSGC.setText(messagesInstance.getMessage("engineering.token.display.sgc"));
            lblKRN.setText(messagesInstance.getMessage("engineering.token.display.krn"));
            lblTI.setText(messagesInstance.getMessage("engineering.token.display.ti"));
        }
        if (this.stsEngineeringTokenData.getToken2() != null) {
            valueToken2.setText(this.stsEngineeringTokenData.getToken2());
        } else {
            hpToken2.removeFromParent();
        }
        if (this.stsEngineeringTokenData.getToken3() != null && !this.stsEngineeringTokenData.getToken3().isEmpty()) {
            valueToken3.setText(this.stsEngineeringTokenData.getToken3());
        } else {
            hpToken3.removeFromParent();
        }
        if (this.stsEngineeringTokenData.getToken4() != null && !this.stsEngineeringTokenData.getToken4().isEmpty()) {
            valueToken4.setText(this.stsEngineeringTokenData.getToken4());
        } else {
            hpToken4.removeFromParent();
        }
        if(this.stsEngineeringTokenData.getOldSupGroup() != null) {
            valueOldSGC.setText(this.stsEngineeringTokenData.getOldSupGroup());
        } else {
            hpOldSGC.removeFromParent();
        }
        if(this.stsEngineeringTokenData.getNewSupGroup() != null) {
            valueNewSGC.setText(this.stsEngineeringTokenData.getNewSupGroup());
        } else {
            hpNewSGC.removeFromParent();
            showVerifyLinks = false;
        }
        if (this.stsEngineeringTokenData.getOldKeyRev() != null) {
            valueOldKRN.setText(this.stsEngineeringTokenData.getOldKeyRev().toString());
        } else {
            hpOldKRN.removeFromParent();
        }
        if (this.stsEngineeringTokenData.getNewKeyRev() != null) {
            valueNewKRN.setText(this.stsEngineeringTokenData.getNewKeyRev().toString());
        } else {
            hpNewKRN.removeFromParent();
            showVerifyLinks = false;
        }

        if (this.stsEngineeringTokenData.getOldTariffIdx() != null) {
            valueOldTI.setText(this.stsEngineeringTokenData.getOldTariffIdx());
        } else {
            hpOldTI.removeFromParent();
        }

        if (this.stsEngineeringTokenData.getNewTariffIdx() != null) {
            valueNewTI.setText(this.stsEngineeringTokenData.getNewTariffIdx());
        } else {
            hpNewTI.removeFromParent();
            showVerifyLinks = false;
        }

        lblVerifyToken1.setVisible(showVerifyLinks);
        lblVerifyToken2.setVisible(showVerifyLinks);
        lblVerifyToken3.setVisible(showVerifyLinks);
        lblVerifyToken4.setVisible(showVerifyLinks);

        if (this.stsEngineeringTokenData.getUnits() != null
                && tokenTypeName != null
                && tokenTypeName.equals("Power Limit")) {
            valuePowerLimitUnits.setText(this.stsEngineeringTokenData.getUnits().toPlainString());
        } else {
            hpPowerLimitUnits.removeFromParent();
        }

        if (this.stsEngineeringTokenData.getUnits() != null
                && (tokenTypeName == null || !tokenTypeName.equals("Power Limit"))) {
            valueUnits.setText(this.stsEngineeringTokenData.getUnits().toPlainString());
        } else {
            hpUnits.removeFromParent();
        }

        if (this.stsEngineeringTokenData.isClearTid()) {
            valueClearTid.setText(MessagesUtil.getInstance().getMessage("option.positive"));
        } else {
            hpClearTid.removeFromParent();
        }

        if (this.stsEngineeringTokenData.getDescription() != null) {
            valueDescription.setText(this.stsEngineeringTokenData.getDescription());
        } else {
            hpDescription.removeFromParent();
        }
        if (this.stsEngineeringTokenData.getTransDate() != null) {
            valueTransDate.setText(this.stsEngineeringTokenData.getTransDate().toString());
        }
        valueUser.setText(this.stsEngineeringTokenData.getUserRecEntered());
        if (this.stsEngineeringTokenData.getUserRef() !=null && isUserRefAvailable) {
            valueUserRef.setText(this.stsEngineeringTokenData.getUserRef());
        } else {
            hpUserRef.removeFromParent();
        }

        if (this.stsEngineeringTokenData.getImportFileName() != null) {
            hpImportFileName.setVisible(true);
            valueImportFileName.setText(this.stsEngineeringTokenData.getImportFileName());
        } else {
            hpImportFileName.removeFromParent();
        }

        if ("Set Phase".equals(tokenTypeName)) {
            lblUnits.setText(messagesInstance.getMessage("meter.units",
                    new String[] { messagesInstance.getMessage("unit.watts.symbol") }) + ":");
        } else {
            clientFactory.getMeterRpc().getMeter(this.stsEngineeringTokenData.getMeterId(), new ClientCallback<MeterData>() {
                @Override
                public void onSuccess(MeterData result) {
                    lblUnits.setText(MessagesUtil.getInstance().getMessage("meter.units",
                            new String[] { MeterMngClientUtils
                                    .getServiceResourceSymbol(result.getMeterModelData().getServiceResourceId()) })
                            + ":");
                }
            });
        }
    }

    private void showReprintOptions() {
        Map<String, String> tokenInfo = new HashMap<>();
        String tokenTypeDescrip = stsEngineeringTokenData.getTokenTypeName() + " " + MessagesUtil.getInstance().getMessage("messaging.txn.token");

        if (stsEngineeringTokenData.getStsEngTokenTypeId()==StsEngineeringTokenTypeE.KEY_CHANGE.getId()) {
            tokenTypeDescrip += ("\n"+MessagesUtil.getInstance().getMessage("meter.token.code1"));
        }
        tokenInfo.put(stsEngineeringTokenData.getToken1()+"^1", tokenTypeDescrip);
        String token2 = stsEngineeringTokenData.getToken2();
        if (token2 != null && !token2.isEmpty()) {
            tokenInfo.put(token2+"^2", MessagesUtil.getInstance().getMessage("meter.token.code2"));
        }
        String token3 = stsEngineeringTokenData.getToken3();
        if (token3 != null && !token3.isEmpty()) {
            tokenInfo.put(token3+"^3", MessagesUtil.getInstance().getMessage("meter.token.code3"));
        }
        String token4 = stsEngineeringTokenData.getToken4();
        if (token4 != null && !token4.isEmpty()) {
            tokenInfo.put(token4+"^4", MessagesUtil.getInstance().getMessage("meter.token.code4"));
        }

        new TokenReprintWidget(clientFactory, engineeringTransactionPopup, tokenInfo, stsEngineeringTokenData, null);
    }

    class VerifyTokenClickHandler implements ClickHandler {

        private int tokenNumber;

        public VerifyTokenClickHandler(int tokenNumber) {
            this.tokenNumber = tokenNumber;
        }

        @Override
        public void onClick(final ClickEvent event) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    handleClickEvent(event);
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }

        private  void handleClickEvent(ClickEvent event) {
            WaitingDialog.WaitingDialogUtil.getInstance(MediaResource.MediaResourceUtil.getInstance().getWaitIcon(),
                    event.getClientX()+20, event.getClientY()+20);
            StsMeterData stsMeterData = new StsMeterData();
            stsMeterData.setAlgorithmCode(stsEngineeringTokenData.getStsAlgorithmCode());
            if (StsEngineeringTokenTypeE.KEY_CHANGE.getId()== stsEngineeringTokenData.getStsEngTokenTypeId()) {
                stsMeterData.setSupplierGroupRef(stsEngineeringTokenData.getOldSupGroup());
                stsMeterData.setTariffIndex(stsEngineeringTokenData.getOldTariffIdx());
                stsMeterData.setKeyRevisionNum(stsEngineeringTokenData.getOldKeyRev());
            } else {
                stsMeterData.setSupplierGroupRef(stsEngineeringTokenData.getNewSupGroup());
                stsMeterData.setTariffIndex(stsEngineeringTokenData.getNewTariffIdx());
                stsMeterData.setKeyRevisionNum(stsEngineeringTokenData.getNewKeyRev());
            }
            final String token;
            switch (tokenNumber) {
                case 2:
                    token = stsEngineeringTokenData.getToken2();
                    break;
                case 3:
                    token = stsEngineeringTokenData.getToken3();
                    break;
                case 4:
                    token = stsEngineeringTokenData.getToken4();
                    break;
                default:
                    token = stsEngineeringTokenData.getToken1();
            }
            clientFactory.getMeterRpc().verifyToken(token, stsEngineeringTokenData.getMeterNumber(),
                    stsMeterData, new ClientCallback<VerifyTokenDto>() {
                        @Override
                        public void onSuccess(VerifyTokenDto result) {
                            WaitingDialog waiting = WaitingDialog.WaitingDialogUtil.getCurrentInstance();
                            if (waiting != null) {
                                waiting.hide();
                            }
                            if (result!=null) {
                                if (result.getVerificationError() != null) {
                                    Dialogs.displayErrorMessage(result.getVerificationError(),
                                            MediaResource.MediaResourceUtil.getInstance().getErrorIcon(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                } else {
                                    BigDecimal units = new BigDecimal((double) result.getTransferAmt()/10).setScale(1, BigDecimal.ROUND_HALF_UP);
                                    switch (tokenNumber) {
                                        case 2:
                                            lblTokenValue2.setText(token);
                                            lblTokenClassValue2.setText(result.getTokenClassName() + " (" + result.getTokenClass() + ")");
                                            lblTokenSubclassValue2.setText(result.getSubClassName() + " (" + result.getSubClass() + ")");
                                            lblTokenIdValue2.setText(result.getTokenId().toString());
                                            lblTokenDateValue2.setText(Format.FormatUtil.getInstance().formatDateTime(result.getDateGenerated()));
                                            lblUnitsValue2.setText(units.toPlainString());
                                            stsTokenVerifiedPanel2.setVisible(true);
                                            lblVerifyToken2.setVisible(false);
                                            break;
                                        case 3:
                                            lblTokenValue3.setText(token);
                                            lblTokenClassValue3.setText(result.getTokenClassName() + " (" + result.getTokenClass() + ")");
                                            lblTokenSubclassValue3.setText(result.getSubClassName() + " (" + result.getSubClass() + ")");
                                            lblTokenIdValue3.setText(result.getTokenId().toString());
                                            lblTokenDateValue3.setText(Format.FormatUtil.getInstance().formatDateTime(result.getDateGenerated()));
                                            lblUnitsValue3.setText(units.toPlainString());
                                            stsTokenVerifiedPanel3.setVisible(true);
                                            lblVerifyToken3.setVisible(false);
                                            break;
                                        case 4:
                                            lblTokenValue4.setText(token);
                                            lblTokenClassValue4.setText(result.getTokenClassName() + " (" + result.getTokenClass() + ")");
                                            lblTokenSubclassValue4.setText(result.getSubClassName() + " (" + result.getSubClass() + ")");
                                            lblTokenIdValue4.setText(result.getTokenId().toString());
                                            lblTokenDateValue4.setText(Format.FormatUtil.getInstance().formatDateTime(result.getDateGenerated()));
                                            lblUnitsValue4.setText(units.toPlainString());
                                            stsTokenVerifiedPanel4.setVisible(true);
                                            lblVerifyToken4.setVisible(false);
                                            break;
                                        default:
                                            lblTokenValue1.setText(token);
                                            lblTokenClassValue1.setText(result.getTokenClassName() + " (" + result.getTokenClass() + ")");
                                            lblTokenSubclassValue1.setText(result.getSubClassName() + " (" + result.getSubClass() + ")");
                                            lblTokenIdValue1.setText(result.getTokenId().toString());
                                            lblTokenDateValue1.setText(Format.FormatUtil.getInstance().formatDateTime(result.getDateGenerated()));
                                            lblUnitsValue1.setText(units.toPlainString());
                                            stsTokenVerifiedPanel1.setVisible(true);
                                            lblVerifyToken1.setVisible(false);
                                    }

                                }
                            } else {
                                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("verification.error.timeout"),
                                        MediaResource.MediaResourceUtil.getInstance().getErrorIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                            }
                        }
                    });
        }
    }

}
