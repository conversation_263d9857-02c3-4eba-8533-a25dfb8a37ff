package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.client.history.PricingStructurePlace;

import com.google.gwt.event.shared.GwtEvent;

public class OpenPricingStructureEvent extends GwtEvent<OpenPricingStructureEventHandler> {

    public static Type<OpenPricingStructureEventHandler> TYPE = new Type<OpenPricingStructureEventHandler>();
    private PricingStructurePlace pricingStructurePlace;
    
    public OpenPricingStructureEvent(PricingStructurePlace pricingStructurePlace) {
        this.pricingStructurePlace = pricingStructurePlace;
    }
    
    public PricingStructurePlace getPricingStructurePlace() {
        return pricingStructurePlace;
    }

    @Override
    public Type<OpenPricingStructureEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenPricingStructureEventHandler handler) {
        handler.openPricingStructure(this);
    }

}
