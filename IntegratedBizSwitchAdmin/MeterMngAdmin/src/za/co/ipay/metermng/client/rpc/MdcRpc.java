package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.List;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

@RemoteServiceRelativePath("secure/mdc.do")
public interface MdcRpc extends RemoteService {

    public Integer getMdcCount() throws ServiceException;
    
    public ArrayList<Mdc> getMdcs(int startRow, int pageSize, String sortField, boolean isAscending) 
        throws ServiceException;
    
    public List<Mdc> getMdcs(Boolean enabled) throws ServiceException;
    
    public Mdc getMdcById(Long MdcId) throws ServiceException;
    
    public void saveMdc(Mdc mdc) throws ValidationException, ServiceException, AccessControlException;
}
