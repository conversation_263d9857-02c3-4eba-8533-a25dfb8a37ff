package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.accesscontrol.domain.Permission;
import za.co.ipay.accesscontrol.domain.User;
import za.co.ipay.accesscontrol.domain.UserRole;
import za.co.ipay.accesscontrol.gwt.shared.GWTAdminUser;
import za.co.ipay.accesscontrol.service.IAccessControlService;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.mybatis.generated.mapper.UserGroupMapper;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.UserGroup;
import za.co.ipay.metermng.mybatis.generated.model.UserGroupExample;
import za.co.ipay.metermng.shared.dto.user.MeterMngUserGroup;
import za.co.ipay.metermng.shared.dto.user.UserData;

/**
 * UserGroupService provides all the business functionality for user groups and related data like access control users.
 * <AUTHOR>
 */
public class UserGroupService {

    /** The basic permission for meter management users which is used to search for users. */
    private String meterMngPermission;
    /** The dao/mapper for userGroup data. */
    private UserGroupMapper userGroupMapper;
    /** Service to access group data. */
    private GroupService groupService;
    /** Service to read/write to the access control database. */
    private IAccessControlService accessControlService;   
    
    private static Logger logger = Logger.getLogger(UserGroupService.class);
    
    /**
     * Method to check if there are any UserGroup instances with a set group. 
     * @return True if there is existing UserGroup data.
     */
    @Transactional(readOnly=true)
    public boolean isExistingUserGroups() {
        UserGroupExample example = new UserGroupExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<UserGroup> userGroups = userGroupMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (userGroups.size() == 1) {
            return true;
        } else {
            return false;
        }
    }
    
    @Transactional(readOnly=true)
    public GenGroup getUserAssignedGroup(Long userId) {
        if (userId != null) {
            logger.info("Getting user's assigned group userId:"+userId);
            UserGroupExample example = new UserGroupExample();
            example.createCriteria().andUserIdEqualTo(userId);
            List<UserGroup> groups = userGroupMapper.selectByExample(example);
            if (groups.size() == 1) {
                return groupService.getGenGroup(groups.get(0).getGenGroupId());
            }
        }
        return null;
    }    
    
    @Transactional(readOnly=true)
    public List<MeterMngUserGroup> getAllUserGroups() {
        //Note: No pagination of userGroups - assuming a manageable amount of data for the users of the system
        List<MeterMngUserGroup> users = new ArrayList<MeterMngUserGroup>();
        MeterMngUserGroup user = null;
        Map<Long, GenGroup> groups = new HashMap<Long, GenGroup>(); //cache of groups
        UserGroupExample example = new UserGroupExample();
        example.setOrderByClause(" user_id ");
        List<UserGroup> userGroups = userGroupMapper.selectByExample(example);
        for(UserGroup userGroup : userGroups) {
            user = new MeterMngUserGroup(getAdminUser(userGroup.getUserId()));
            if (!groups.containsKey(userGroup.getGenGroupId())) {
                groups.put(userGroup.getGenGroupId(), groupService.getGenGroup(userGroup.getGenGroupId()));
            }
            user.setAssignedGroup(groups.get(userGroup.getGenGroupId()));            
            user.setGroupPath(groupService.getPath(user.getAssignedGroup().getId()));
            user.setAssignedGroupHierarchy(groupService.getGroupHierarchy(user.getAssignedGroup().getGroupHierarchyId()));
            users.add(user);
        }
        return users;
    }
    
    private GWTAdminUser getAdminUser(Long userId) {
        User user = accessControlService.findUserById(userId);
        if (user != null) {
            GWTAdminUser gwtUser = new GWTAdminUser();
            gwtUser.setId(user.getId());
            gwtUser.setUserName(user.getUsername());
            ArrayList<String> roleNames = new ArrayList<String>();
            ArrayList<String> permissionNames = new ArrayList<String>();
            for (UserRole userRole : user.getUserRoles()) {
                roleNames.add(userRole.getRole().getName());
                List<Permission> permissions =  userRole.getRole().getPermissions();
                for (Permission permission : permissions) {
                    permissionNames.add(permission.getName());
                }
            }
            gwtUser.setRoles(roleNames);
            gwtUser.setPermissions(permissionNames);            
            return gwtUser;
        } else {
            return null;
        }
    }
        
    @Transactional(readOnly=true)
    public List<UserData> getUserSuggestions(String query, int limit, String organisation) {
        List<UserData> dtos = new ArrayList<UserData>();
        if (query != null && !query.trim().equals("")) {
            //TODO do we want to include users without any org (like ipay users) if we have a specific org currently?
            logger.info("Searching for users: "+query+" limit:"+limit+" org:"+organisation);
            List<User> users = accessControlService.findUsersByPermissionNameAndUsernameLike(meterMngPermission, query, organisation);
            logger.info("Retrieved users: "+users.size());
            for(User u : users) {
                dtos.add(new UserData(u.getId(), u.getUsername(), u.getEmail()));
            }
        } else {
            logger.debug("Ignoring invalid query: "+query);
        }
        return dtos;
    }
    
    @Transactional
    @PreAuthorize("hasRole('mm_user_group_admin')")
    public UserGroup saveUserGroup(Long userId, Long groupId) throws ValidationException, ServiceException {
        if (userId != null) {
            if (groupId != null) {
                UserGroup userGroup = null;
                UserGroupExample example = new UserGroupExample();
                example.createCriteria().andUserIdEqualTo(userId);
                List<UserGroup> groups = userGroupMapper.selectByExample(example);                
                if (groups.size() == 1) {
                    userGroup = groups.get(0); 
                    userGroup.setGenGroupId(groupId);
                    if (userGroupMapper.updateByPrimaryKey(userGroup) != 1) {
                        throw new ServiceException("usergroup.error.save");
                    }
                } else {
                    userGroup = new UserGroup();
                    userGroup.setUserId(userId);
                    userGroup.setGenGroupId(groupId);
                    if (userGroupMapper.insert(userGroup) != 1) {
                        throw new ServiceException("usergroup.error.update");
                    }
                }    
                return userGroup;
            } else {
                throw new ValidationException(new ValidationMessage("usergroup.error.group", true));
            }
        } else {
           throw new ValidationException(new ValidationMessage("usergroup.error.user", true)); 
        }
    }
    
    @Transactional
    @PreAuthorize("hasRole('mm_user_group_admin')")
    public void deleteUserGroup(Long userId) throws ValidationException, ServiceException {
        if (userId != null) {
            UserGroupExample example = new UserGroupExample();
            example.createCriteria().andUserIdEqualTo(userId);
            List<UserGroup> userGroups = userGroupMapper.selectByExample(example);
            if (userGroups.size() == 1) {
                if (userGroupMapper.deleteByPrimaryKey(userGroups.get(0).getId()) != 1) {
                    throw new ServiceException("usergroup.error.delete");
                }
            } else {
                throw new ValidationException(new ValidationMessage("usergroup.error.usergroup.none", true));
            }
        } else {
            throw new ValidationException(new ValidationMessage("usergroup.error.user", true));
        }
    }

    public void setUserGroupMapper(UserGroupMapper userGroupMapper) {
        this.userGroupMapper = userGroupMapper;
    }

    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    public void setAccessControlService(IAccessControlService accessControlService) {
        this.accessControlService = accessControlService;
    }

    public void setMeterMngPermission(String meterMngPermission) {
        this.meterMngPermission = meterMngPermission;
    }
}
