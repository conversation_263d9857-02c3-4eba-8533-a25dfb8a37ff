package za.co.ipay.metermng.server.mybatis.service;

import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.dto.IdValueNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.datatypes.MeterPhaseE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.mybatis.custom.mapper.MdcChannelCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.TariffWithCalcClass;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxChargeScheduleMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.PricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsAlgorithmMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsTokenTechMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeScheduleExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.AuxTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterDataDecoder;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterModelExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.MeterTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.PaymentMode;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructureExample;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReading;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithm;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithmExample;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroupExample;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTech;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTechExample;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonExample;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddSuperMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class LookupService {
    
    private PricingStructureMapper pricingStructureMapper;
    private MdcChannelCustomMapper mdcChannelCustomMapper; 
    private StsAlgorithmMapper stsAlgorithmMapper;
    private StsTokenTechMapper stsTokenTechMapper;
    private StsSupplyGroupMapper stsSupplyGroupMapper;
    private AuxTypeMapper auxTypeMapper;
    private AuxChargeScheduleMapper auxChargeScheduleMapper;
    private AuxAccountMapper auxAccountMapper;
    private EndDeviceStoreMapper endDeviceStoreMapper;
    private MeterTypeMapper meterTypeMapper;
    private TouSeasonMapper seasonMapper;

    private MeterTypeService meterTypeService;
    private ServiceResourceService serviceResourceService;
    private PaymentModeService paymentModeService;
    private MeterService meterService;
    private MeterModelService meterModelService;
    private BlockingTypeService blockingTypeService;
    private RegisterReadingService registerReadingService;
    private PricingStructureService pricingStructureService;
    private MdcService mdcService;

    private static Logger logger = Logger.getLogger(LookupService.class);

    public LookupService() {
        super();
    }

    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getPricingStructureLookupListFromMeterModel(Long currentGroupId, Long meterModelId, boolean hasTariffCurrentlyRunning) {
        MeterModelData mmd = meterModelService.getMeterModelById(meterModelId);
        return getAvailablePricingStructures(currentGroupId, mmd.getServiceResourceId(), mmd.getMeterTypeId(), mmd.getPaymentModeIds(), hasTariffCurrentlyRunning);
    }
    
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailablePricingStructures(Long currentGroupId, Long serviceResourceId,
                                                                   Long meterTypeId, ArrayList<Long> paymentModeIds,
                                                                   boolean hasTariffCurrentlyRunning) {
        if (serviceResourceId==null || meterTypeId==null || paymentModeIds == null || paymentModeIds.isEmpty()) {
            return getAvailablePricingStructures(currentGroupId, hasTariffCurrentlyRunning);
        }
        List<PricingStructure> list;
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        if (hasTariffCurrentlyRunning) {
            list = pricingStructureService.getPSsWithRunningTariffByMeterModel(currentGroupId, serviceResourceId, meterTypeId, paymentModeIds);
        } else {
            PricingStructureExample pricingStructureExample = new PricingStructureExample();
            PricingStructureExample.Criteria criteria = pricingStructureExample.createCriteria();
            criteria.andRecordStatusEqualTo(RecordStatus.ACT);
            criteria.andServiceResourceIdEqualTo(serviceResourceId);
            criteria.andMeterTypeIdEqualTo(meterTypeId);
            criteria.andPaymentModeIdIn(paymentModeIds);
            if (currentGroupId != null) {
                criteria.andGenGroupIdEqualTo(currentGroupId);
            }
            list = (ArrayList<PricingStructure>) pricingStructureMapper.selectByExample(pricingStructureExample);
        }
        if (list == null || list.size() < 1)
            return null;
        for (int i = 0; i < list.size(); i++) {
            lookupList.add(makePricingStructureLookupListItem(list.get(i)));
        }
        return lookupList;
    }
    
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailablePricingStructuresByMeterModelId(Long currentGroupId, Long meterModelId, boolean hasTariffCurrentlyRunning) {
        MeterModel mm = meterModelService.getMeterModelById(meterModelId);
        return getAvailablePricingStructures(currentGroupId, mm.getServiceResourceId(), mm.getMeterTypeId(), meterModelService.getPaymentModesByMeterModel(meterModelId), hasTariffCurrentlyRunning);
    }
    
    @SuppressWarnings("deprecation")
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailablePricingStructures(Long currentGroupId, boolean hasTariffCurrentlyRunning) {
        List<PricingStructure> list = new ArrayList<>();
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        if (hasTariffCurrentlyRunning) {
            list = pricingStructureService.getPSsWithRunningTariff(currentGroupId);
        } else {
            PricingStructureExample pricingStructureExample = new PricingStructureExample();
            PricingStructureExample.Criteria criteria = pricingStructureExample.createCriteria();
            criteria.andRecordStatusEqualTo(RecordStatus.ACT);
            if (currentGroupId != null) {
                criteria.andGenGroupIdEqualTo(currentGroupId);
            }
            list = (ArrayList<PricingStructure>) pricingStructureMapper.selectByExample(pricingStructureExample);
            list.sort(new PricingStructureComparator());
        }
        
        if (list == null || list.size() < 1)
            return null;
        for (int i = 0; i < list.size(); i++) {
            LookupListItem lui = makePricingStructureLookupListItem(list.get(i));
            lui.setExtraInfo(list.get(i).getPaymentModeId().toString());
            lookupList.add(lui);
        }
        return lookupList;
    }
     
    public class PricingStructureComparator implements Comparator<PricingStructure> {
        @Override
        public int compare(PricingStructure o1, PricingStructure o2) {
            return o1.getName().compareTo(o2.getName());
        }
    }
    
    private LookupListItem makePricingStructureLookupListItem(PricingStructure ps) {
        LookupListItem lui = new LookupListItem(ps.getId().toString(), ps.getName());
        
        TariffWithCalcClass currentTariff = pricingStructureService.getCurrentOrFirstTariffByPricingStructureId(ps.getId());
        TariffWithCalcClass firstTariff = pricingStructureService.getAllTariffByPricingStructureId(ps.getId()).get(0);
        HashMap<String, Object> extraInfoMap = new HashMap<>();
        String calcClass = currentTariff.getCalcClass();
        if (calcClass.equals("za.co.ipay.metermng.tariff.impl.RegisterReadingThinTariff")) {
            extraInfoMap.put("regReadPS", "true");
        } else {
            extraInfoMap.put("regReadPS", "false");
        }
        extraInfoMap.put("firstTariffStartDate", firstTariff.getStartDate());
        extraInfoMap.put("paymentModeId", ps.getPaymentModeId().toString());
        extraInfoMap.put("serviceResourceId", ps.getServiceResourceId().toString()); 
        extraInfoMap.put("accessGroupId", ps.getAccessGroupId() == null ? null : ps.getAccessGroupId().toString());
        lui.setExtraInfoMap(extraInfoMap);
        return lui;
    }
    
    @SuppressWarnings("deprecation")
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailableAlgCodes() {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        StsAlgorithmExample stsaExample = new StsAlgorithmExample();
        stsaExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        stsaExample.setOrderByClause("sts_algorithm_name");
        ArrayList<StsAlgorithm> list = (ArrayList<StsAlgorithm>) stsAlgorithmMapper.selectByExample(stsaExample);
        if (list == null || list.size() < 1)
            return null;
        LookupListItem lli = new LookupListItem("-1", "");
        lookupList.add(lli);
        for (int i = 0; i < list.size(); i++) {
            lli = new LookupListItem(list.get(i).getId().toString(), (list.get(i).getName() + " - " + list.get(i)
                    .getStsAlgorithmCode()),list.get(i).isDefaultAlgorithm());
            lli.setExtraInfo(list.get(i).getStsAlgorithmCode());
            lookupList.add(lli);
        }
        return lookupList;
    }

    @SuppressWarnings("deprecation")
    public ArrayList<LookupListItem> getAvailableTtCodes() {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        StsTokenTechExample ststtExample = new StsTokenTechExample();
        ststtExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ststtExample.setOrderByClause("sts_token_tech_name");
        ArrayList<StsTokenTech> list = (ArrayList<StsTokenTech>) stsTokenTechMapper.selectByExample(ststtExample);
        if (list == null || list.size() < 1)
            return null;
        LookupListItem lli = new LookupListItem("-1", "");
        lookupList.add(lli);
        for (int i = 0; i < list.size(); i++) {
            lli = new LookupListItem(list.get(i).getId().toString(), (list.get(i).getName() + " - " + list.get(i)
                    .getStsTokenTechCode()), list.get(i).isDefaultTokenTech());
            lli.setExtraInfo(list.get(i).getStsTokenTechCode());
            lookupList.add(lli);
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableSupplyGroupCodes() {
        return getAvailableSupplyGroupCodes(false);
    }
    
    @SuppressWarnings("deprecation")
    public ArrayList<LookupListItem> getAvailableSupplyGroupCodes(boolean excludeExpired) {
        StsSupplyGroupExample stssgExample = new StsSupplyGroupExample();
        stssgExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        stssgExample.setOrderByClause("supply_group_name");
        List<StsSupplyGroup> list = stsSupplyGroupMapper.selectByExample(stssgExample);
        if (list.isEmpty()){
            return null;
        }

        ArrayList<LookupListItem> lookupList = new ArrayList<>();
        lookupList.add(new LookupListItem("-1", ""));
        LookupListItem lli = null;
        HashMap<String, Object> extraInfoMap;
        Date now = new Date();
        for (StsSupplyGroup sgc : list) {
            if (excludeExpired) {
                Date issuedUntil = sgc.getIssuedUntilDate();
                Date expiry = sgc.getExpiryDate();
                if (issuedUntil == null || issuedUntil.after(now)) { 
                    //all good, must be greater than now
                } else {
                    continue;
                }
                if (expiry == null || expiry.after(now)) {
                    //all good, must be greater than now
                } else {
                    continue;
                }
            }
            
            String text = String.format("%s - SGC: %s - KRN: %d",
                    sgc.getName(), sgc.getSupplyGroupCode().trim(), sgc.getKeyRevisionNum());
            String extraInfo = String.format("SGC:%s:KRN:%d", sgc.getSupplyGroupCode().trim(), sgc.getKeyRevisionNum());
            lli = new LookupListItem(sgc.getId().toString(), text, sgc.isDefaultSupplyGroup());
            lli.setExtraInfo(extraInfo);
            
            extraInfoMap = new HashMap<>();
            if (sgc.getBaseDate() != null)  {
                extraInfoMap.put("baseDate", sgc.getBaseDate().toString());
            }
            lli.setExtraInfoMap(extraInfoMap);
            lookupList.add(lli);
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableAuxTypes() {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        AuxTypeExample auxTypeExample = new AuxTypeExample();
        auxTypeExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        auxTypeExample.setOrderByClause("aux_type_name");
        ArrayList<AuxType> list = (ArrayList<AuxType>) auxTypeMapper.selectByExample(auxTypeExample);
        if (list == null || list.size() < 1)
            return null;
        lookupList.add(new LookupListItem("", ""));
        for (int i = 0; i < list.size(); i++) {
            lookupList.add(new LookupListItem(list.get(i).getId().toString(), list.get(i).getName()));
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableAuxChargeSchedules(Long currentGroupId) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        AuxChargeScheduleExample auxCSchExample = new AuxChargeScheduleExample();
        AuxChargeScheduleExample.Criteria criteria = auxCSchExample.createCriteria();
        criteria.andRecordStatusEqualTo(RecordStatus.ACT)
                .andAccountSpecificEqualTo(false);
        if (currentGroupId != null) {
            criteria.andGenGroupIdEqualTo(currentGroupId);
        }
        auxCSchExample.setOrderByClause("schedule_name");
        ArrayList<AuxChargeSchedule> list = (ArrayList<AuxChargeSchedule>) auxChargeScheduleMapper
                .selectByExample(auxCSchExample);
        if (list == null || list.size() < 1)
            return null;
        lookupList.add(new LookupListItem("", ""));
        for (int i = 0; i < list.size(); i++) {
            AuxChargeSchedule sched = list.get(i);
            LookupListItem lli = new LookupListItem(sched.getId().toString(), sched.getScheduleName());
            HashMap<String, Object> extraInfoMap = new HashMap<>();
            Long schedCycleId = sched.getChargeCycleId();
            if (schedCycleId != null &&
                    (schedCycleId.equals(CycleE.DAILY.getId()) || schedCycleId.equals(CycleE.MONTHLY.getId()))) { 
                extraInfoMap.put("Instalment", "true");
            }
            lli.setExtraInfoMap(extraInfoMap);
            lookupList.add(lli);
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableAuxAccounts(Long customerId) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        AuxAccountExample auxAccountExample = new AuxAccountExample();
        auxAccountExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT)
                .andCustomerAgreementIdEqualTo(customerId);
        auxAccountExample.setOrderByClause("account_name");
        ArrayList<AuxAccount> list = (ArrayList<AuxAccount>) auxAccountMapper.selectByExample(auxAccountExample);
        if (list == null || list.size() < 1)
            return null;
        lookupList.add(new LookupListItem("", ""));
        for (int i = 0; i < list.size(); i++) {
            lookupList.add(new LookupListItem(list.get(i).getId().toString(), list.get(i).getAccountName()));
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableEndDeviceStores(Long currentGroupId, Long sessionGroupId) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        EndDeviceStoreExample endDeviceStoreExample = new EndDeviceStoreExample();
        EndDeviceStoreExample.Criteria criteria = endDeviceStoreExample.createCriteria();
        if (currentGroupId != null) {
            criteria.andGenGroupIdEqualTo(currentGroupId);
        }
        if (sessionGroupId != null) {
            criteria.andAccessGroupIdEqualTo(sessionGroupId);
        }
        criteria.andRecordStatusEqualTo(RecordStatus.ACT);
        endDeviceStoreExample.setOrderByClause("store_name");
        ArrayList<EndDeviceStore> list = (ArrayList<EndDeviceStore>) endDeviceStoreMapper
                .selectByExample(endDeviceStoreExample);
        if (list == null || list.size() < 1)
            return null;
        
        EndDeviceStore eds = null;
        LookupListItem listItem = null;
        HashMap<String, Object> extraInfoMap = null;
        for (int i = 0; i < list.size(); i++) {
            eds = list.get(i);
            extraInfoMap = new HashMap<>();
            listItem = new LookupListItem(eds.getId().toString(), eds.getName());
            extraInfoMap.put("accessGroupId", eds.getAccessGroupId());
            listItem.setExtraInfoMap(extraInfoMap);
            lookupList.add(listItem);
        }
        return lookupList;
    }

    public ArrayList<LookupListItem> getAvailableMeterTypes() {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        MeterTypeExample meterTypeExample = new MeterTypeExample();
        meterTypeExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<MeterType> list = (ArrayList<MeterType>) meterTypeMapper.selectByExample(meterTypeExample);
        for (int i = 0; i < list.size(); i++) {
            lookupList
                    .add(new LookupListItem(list.get(i).getValue() + ":" + list.get(i).getId(), list.get(i).getName()));
        }
        return lookupList;

    }

    public ArrayList<LookupListItem> getAvailableSeasons(boolean includeBlankEntry) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        TouSeasonExample seasonExample = new TouSeasonExample();
        seasonExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<TouSeason> list = (ArrayList<TouSeason>) seasonMapper.selectByExample(seasonExample);
        if (includeBlankEntry) {
            lookupList.add(new LookupListItem("-1", ""));
        }
        for (int i = 0; i < list.size(); i++) {
            lookupList.add(new LookupListItem(list.get(i).getId().toString(), list.get(i).getName()));
        }
        return lookupList;
    }

    @Transactional(readOnly = true)
    public PtrScreenDataDto getPtrScreenData() throws ServiceException {
        PtrScreenDataDto screenData = new PtrScreenDataDto();
        // meter types
        ArrayList<IdNameDto> dtos = new ArrayList<IdNameDto>();
        List<MeterType> types = meterTypeService.getMeterTypes(true);
        for (MeterType type : types) {
            dtos.add(new IdNameDto(type.getId(), type.getName()));
        }
        screenData.setMeterTypes(dtos);

        // service resources
        dtos = new ArrayList<IdNameDto>();
        List<ServiceResource> resources = serviceResourceService.getServiceResources(true);
        for (ServiceResource sr : resources) {
            dtos.add(new IdNameDto(sr.getId(), sr.getName()));
        }
        screenData.setServiceResources(dtos);

        // payment modes
        dtos = new ArrayList<IdNameDto>();
        List<PaymentMode> modes = paymentModeService.getPaymentModes(true);
        for (PaymentMode mode : modes) {
            dtos.add(new IdNameDto(mode.getId(), mode.getName()));
        }
        screenData.setPaymentModes(dtos);

        // meter phases
        dtos = new ArrayList<IdNameDto>();
        for (MeterPhaseE meterPhase : MeterPhaseE.values()) {
            dtos.add(new IdNameDto(meterPhase.getId(), meterPhase.getName()));
        }
        screenData.setMeterPhases(dtos);

        //data decoders
        dtos = new ArrayList<IdNameDto>();
        List<MeterDataDecoder> decoders = meterModelService.getAvailableDataDecoders();
        for (MeterDataDecoder decoder : decoders) {
            dtos.add(new IdNameDto(decoder.getId(), decoder.getDecoderClassName()));
        }
        screenData.setDataDecoders(dtos);

        return screenData;
    }
    
    @Transactional(readOnly=true)
    public AddMeterReadingScreenDataDto getAddMeterReadingScreenData() throws ServiceException { 
        AddMeterReadingScreenDataDto data = new AddMeterReadingScreenDataDto();
        
        ArrayList<IdValueNameDto> dtos = new ArrayList<IdValueNameDto>();
        List<PaymentMode> modes = paymentModeService.getPaymentModes(true);
        for (PaymentMode mode : modes) {
            dtos.add(new IdValueNameDto(mode.getId(), mode.getValue(), mode.getName()));
        }
        data.setPaymentModes(dtos);
                       
        dtos = new ArrayList<IdValueNameDto>();
        List<MeterReadingType> types = meterService.getMeterReadingTypes(Boolean.TRUE, MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE_WILDCARD);
        for (MeterReadingType type : types) {
            dtos.add(new IdValueNameDto(type.getId(), type.getValue(), type.getName()));
        }
        types = meterService.getMeterReadingTypes(Boolean.TRUE, MeterMngStatics.POWER_DEMAND_METER_READING_TYPE_WILDCARD);
        for (MeterReadingType type : types) {
            dtos.add(new IdValueNameDto(type.getId(), type.getValue(), type.getName()));
        }
        types = meterService.getMeterReadingTypes(Boolean.TRUE, MeterMngStatics.POTABLE_WATER_VOLUME_METER_READING_TYPE_WILDCARD);
        for (MeterReadingType type : types) {
            dtos.add(new IdValueNameDto(type.getId(), type.getValue(), type.getName()));
        }

        data.setReadingTypes(dtos);
        
        return data;
    }
    
    @Transactional(readOnly=true)
    public AddSuperMeterReadingScreenDataDto getAddSuperMeterReadingScreenData() throws ServiceException { 
        AddMeterReadingScreenDataDto d = getAddMeterReadingScreenData();
        AddSuperMeterReadingScreenDataDto data = new AddSuperMeterReadingScreenDataDto();
        data.setPaymentModes(d.getPaymentModes());
        data.setReadingTypes(d.getReadingTypes());
        data.setSuperMeters(new ArrayList<>(meterService.getSuperMeters()));
        return data;
    }
    
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailableMeterModels() {
        MeterModelExample meterModelExample = new MeterModelExample();
        meterModelExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<MeterModel> list = meterModelService.getAvailableMeterModels();
        return constructLookupListForMeterModels(list);
    }
    
    @SuppressWarnings("deprecation")
    private ArrayList<LookupListItem> constructLookupListForMeterModels(ArrayList<MeterModel> list) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        Collections.sort(list, new MeterModelComparator());
        LookupListItem lookupListItem;
        for (int i = 0; i < list.size(); i++) {
            MeterModel meterModel = list.get(i);
            lookupListItem = new LookupListItem(String.valueOf(meterModel.getId()), meterModel.getName());
            HashMap<String, Object> extraInfoMap = new HashMap<>();
            lookupListItem.setExtraInfo(String.valueOf(meterModel.getMeterTypeId()));
            lookupListItem.setExtraInfo2(String.valueOf(meterModel.isNeedsBreakerId()));
            lookupListItem.setExtraInfo3(String.valueOf(meterModel.isNeedsEncKey()));
            if (ServiceResourceE.GAS.getId() == meterModel.getServiceResourceId().intValue()
                    || ServiceResourceE.WATER.getId() == meterModel.getServiceResourceId().intValue()) {
                extraInfoMap.put("hasPowerLimit", "false");
            } else if (ServiceResourceE.ELEC.getId() == meterModel.getServiceResourceId().intValue()) {
                extraInfoMap.put("hasPowerLimit", "true");
            }
            if (meterModel.getMdcId() != null) {
                extraInfoMap.put("mdcId", meterModel.getMdcId().toString());
                extraInfoMap.put("mdcName", mdcService.getMdc(meterModel.getMdcId()).getName());
                List<MdcChannelReadingsDto> channelDtoList = mdcChannelCustomMapper.getChannelsAndReadingTypeInfoForMeterModel(meterModel.getId());
                if (channelDtoList == null || channelDtoList.isEmpty()) {
                    logger.info("getChannelsForPricingStructureAndMeterModel: channelDtoList is empty");
                    extraInfoMap.put("hasChannels", "false");
                } else {
                    extraInfoMap.put("hasChannels", "true");
                }
            }
            extraInfoMap.put("uriPresent", String.valueOf(meterModel.isUriPresent()));

            lookupListItem.setExtraInfoMap(extraInfoMap);
            lookupList.add(lookupListItem);
        }
        return lookupList;
    }
    
    public class MeterModelComparator implements Comparator<MeterModel> {
        @Override
        public int compare(MeterModel o1, MeterModel o2) {
            return o1.getName().compareTo(o2.getName());
        }
    }
    
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getAvailableMeterModels(PricingStructure pricingStructure) {
        return getAvailableMeterModels(meterModelService.getAvailableMeterModels(pricingStructure));
    }

    private ArrayList<LookupListItem> getAvailableMeterModels(ArrayList<MeterModel> list) {
        return constructLookupListForMeterModels(list);
    }
    
    @Transactional(readOnly=true)
    public ArrayList<LookupListItem> getAvailableMeterModelsByPricingStructureId(Long pricingStructureId) {
        return getAvailableMeterModels(pricingStructureMapper.selectByPrimaryKey(pricingStructureId));
    }
    
    @Transactional(readOnly=true)
    public ArrayList<LookupListItem> getAllBlockingTypes() {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        List<BlockingType> blockingTypes = blockingTypeService.getAllBlockingTypes();
        for (BlockingType bt : blockingTypes) {
            LookupListItem lookupListItem = new LookupListItem(bt.getId().toString(), bt.getTypeName());
            lookupList.add(lookupListItem);
        }
        return lookupList;
    }

    @Transactional(readOnly = true)
    public ArrayList<IdNameDto> getPaymentModes() throws ServiceException {
         ArrayList<IdNameDto> dtos = new ArrayList<IdNameDto>();
         // payment modes
        List<PaymentMode> modes = paymentModeService.getPaymentModes(true);
        for (PaymentMode mode : modes) {
            dtos.add(new IdNameDto(mode.getId(), mode.getName()));
        }
        return dtos;
    }


    @Transactional(readOnly = true)
    public MeterUpMdcChannelInfo getMeterUpMdcChannelInfo(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        List<MdcChannelReadingsDto> channelDtoList = mdcChannelCustomMapper.getChannelsAndReadingTypeInfoForMeterModel(meterUpMdcChannelInfo.getMeterModelId());
        if (channelDtoList == null || channelDtoList.isEmpty()) {
            logger.info("getChannelsForPricingStructureAndMeterModel: channelDtoList is empty");
            return null;
        }
        
        if (meterUpMdcChannelInfo.getPricingStructureId() != null) {
            TariffWithCalcClass currentTariff = pricingStructureService.getCurrentOrFirstTariffByPricingStructureId(meterUpMdcChannelInfo.getPricingStructureId());
            if (!currentTariff.getCalcClass().equals("za.co.ipay.metermng.tariff.impl.RegisterReadingThinTariff")) {
                logger.info("The tariff is not a Reg tariff.");
                return null;
            }
        }
        
        List<Long> currentChannelIds = new ArrayList<Long>();
        for (MdcChannelReadingsDto mcr : channelDtoList) {
            currentChannelIds.add(mcr.getId());
            mcr.setBillingDetNameList(mdcChannelCustomMapper.getbillingDetNamesForMdcChannel(mcr.getId()));
            if (meterUpMdcChannelInfo.getUsagePointId() != null && meterUpMdcChannelInfo.getUpMeterInstallId() != null) {
                RegisterReading rr = registerReadingService.fetchRegisterReadingsForMeterUsagepointUpmeterinstallChannel(meterUpMdcChannelInfo, mcr.getId());
                if (rr != null) {
                    mcr.setInitialReading(rr.getReadingValue());
                    mcr.setReadingTimestamp(rr.getReadingTimestamp());
                    if (meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings() == null || rr.getReadingTimestamp().after(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())) {
                        meterUpMdcChannelInfo.setLastReadingDateExistingOldReadings(rr.getReadingTimestamp());
                    }
                    meterUpMdcChannelInfo.setPreExistingReadings(true);
                }
            }
        }
        logger.info("getChannelsForPricingStructureAndMeterModel: channelDtoList.size() = " + channelDtoList.size());
        
        //check if were pre-existing readings for this meter + up + install with other channels (eg. MDC might have changed etc.)
        if (!currentChannelIds.isEmpty() && meterUpMdcChannelInfo.getUpMeterInstallId() != null) {
            RegisterReading rr = registerReadingService.fetchRegisterReadingsForMeterUsagepointUpmeterinstallOldChannels(meterUpMdcChannelInfo, currentChannelIds);
            if (rr != null) {
                meterUpMdcChannelInfo.setPreExistingReadings(true);
                meterUpMdcChannelInfo.setPreExistingDiffMdc(true);
                if (meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings() == null || rr.getReadingTimestamp().after(meterUpMdcChannelInfo.getLastReadingDateExistingOldReadings())) {
                    meterUpMdcChannelInfo.setLastReadingDateExistingOldReadings(rr.getReadingTimestamp());
                }
                logger.info("Pre-Existing register readings from DIFFERENT MdcChannels for meterId=" + meterUpMdcChannelInfo.getMeterId() 
                            + " usagePointId=" + meterUpMdcChannelInfo.getUsagePointId() + " upMeterInstallId=" + meterUpMdcChannelInfo.getUpMeterInstallId());   
            }
        }
        
        meterUpMdcChannelInfo.setChannelList(channelDtoList);
        return meterUpMdcChannelInfo;
    }

    @SuppressWarnings("deprecation")
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getPricingStructureLookupListForStartDate(Long currentGroupId, Date startDate) {
        List<PricingStructure> list = pricingStructureService.getPSsWithRunningTariffForDate(currentGroupId, startDate);
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        for (int i = 0; i < list.size(); i++) {
            PricingStructure pricingStructure = list.get(i);
            LookupListItem lui = makePricingStructureLookupListItem(pricingStructure);
            lui.setExtraInfo(pricingStructure.getPaymentModeId().toString());
            lookupList.add(lui);
        }
        return lookupList;
    }
    
    @Transactional(readOnly = true)
    public ArrayList<LookupListItem> getDeviceStoreLookupListForAccessGroup(Long currentGroupId, Long accessGroupId, boolean allRegionsPerm) {
        ArrayList<LookupListItem> lookupList = new ArrayList<LookupListItem>();
        EndDeviceStoreExample endDeviceStoreExample = new EndDeviceStoreExample();
        EndDeviceStoreExample.Criteria criteria = endDeviceStoreExample.createCriteria();
        EndDeviceStoreExample.Criteria criteriaGlobal = endDeviceStoreExample.createCriteria();
        if (currentGroupId != null) {
            criteria.andGenGroupIdEqualTo(currentGroupId);
        }
        if (accessGroupId != null && !allRegionsPerm) {
            criteria.andAccessGroupIdEqualTo(accessGroupId);
            criteriaGlobal.andAccessGroupIdIsNull();
        }
        criteria.andRecordStatusEqualTo(RecordStatus.ACT);
        criteriaGlobal.andRecordStatusEqualTo(RecordStatus.ACT);
        endDeviceStoreExample.or(criteriaGlobal);
        endDeviceStoreExample.setOrderByClause("store_name");
        ArrayList<EndDeviceStore> list = (ArrayList<EndDeviceStore>) endDeviceStoreMapper
                .selectByExample(endDeviceStoreExample);
        if (list == null || list.size() < 1)
            return null;
        for (int i = 0; i < list.size(); i++) {
            lookupList.add(new LookupListItem(list.get(i).getId().toString(), list.get(i).getName()));
        }
        return lookupList;
    }
    
    //--------------------------------------------------------------------------------------------------------------
    public void setPricingStructureMapper(PricingStructureMapper pricingStructureMapper) {
        this.pricingStructureMapper = pricingStructureMapper;
    }
    
    public void setMdcChannelCustomMapper(MdcChannelCustomMapper mdcChannelCustomMapper) {
        this.mdcChannelCustomMapper = mdcChannelCustomMapper;
    }
    
    public void setStsAlgorithmMapper(StsAlgorithmMapper stsAlgorithmMapper) {
        this.stsAlgorithmMapper = stsAlgorithmMapper;
    }

    public void setStsTokenTechMapper(StsTokenTechMapper stsTokenTechMapper) {
        this.stsTokenTechMapper = stsTokenTechMapper;
    }

    public void setStsSupplyGroupMapper(StsSupplyGroupMapper stsSupplyGroupMapper) {
        this.stsSupplyGroupMapper = stsSupplyGroupMapper;
    }

    public void setAuxTypeMapper(AuxTypeMapper auxTypeMapper) {
        this.auxTypeMapper = auxTypeMapper;
    }

    public void setAuxChargeScheduleMapper(AuxChargeScheduleMapper auxChargeScheduleMapper) {
        this.auxChargeScheduleMapper = auxChargeScheduleMapper;
    }

    public void setAuxAccountMapper(AuxAccountMapper auxAccountMapper) {
        this.auxAccountMapper = auxAccountMapper;
    }

    public void setEndDeviceStoreMapper(EndDeviceStoreMapper endDeviceStoreMapper) {
        this.endDeviceStoreMapper = endDeviceStoreMapper;
    }

    public void setMeterTypeMapper(MeterTypeMapper meterTypeMapper) {
        this.meterTypeMapper = meterTypeMapper;
    }
    
    public void setTouSeasonMapper(TouSeasonMapper touSeasonMapper) {
        this.seasonMapper = touSeasonMapper;
    }

    public void setMeterTypeService(MeterTypeService meterTypeService) {
        this.meterTypeService = meterTypeService;
    }
    
    public void setServiceResourceService(ServiceResourceService serviceResourceService) {
        this.serviceResourceService = serviceResourceService;
    }

    public void setPaymentModeService(PaymentModeService paymentModeService) {
        this.paymentModeService = paymentModeService;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
    
    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setBlockingTypeService(BlockingTypeService blockingTypeService) {
        this.blockingTypeService = blockingTypeService;
    }
    
    public void setRegisterReadingService(RegisterReadingService registerReadingService) {
        this.registerReadingService = registerReadingService;
    }

    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }
    
    public void setMdcService(MdcService mdcService) {
        this.mdcService = mdcService;
    }
}
