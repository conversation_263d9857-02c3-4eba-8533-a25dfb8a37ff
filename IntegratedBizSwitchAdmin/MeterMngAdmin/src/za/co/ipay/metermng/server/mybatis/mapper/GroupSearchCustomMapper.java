package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.session.RowBounds;

import za.co.ipay.metermng.shared.MeterOnlineBulkKernelData;

/**
 * GroupSearchCustomMapper provides custom select methods to retrieve corresponding search results for the specified search criteria.
 * <AUTHOR>
 */
public interface GroupSearchCustomMapper {
    
    @SelectProvider(type=GroupSearchQueryBuilder.class, method="getMeterOnlineBulkKernelDataByGenGroupLeafNodesCount")
    public int getMeterOnlineBulkKernelDataByGenGroupLeafNodesCount(@Param("selGrpIdList") ArrayList<Long> selGrpIdList, 
                                                                    @Param("sortColumn") String sortColumn, 
                                                                    @Param("filterColumn") String filterColumn,
                                                                    @Param("filterString") String filterString, 
                                                                    @Param("filterDate") Date filterDate,
                                                                    @Param("order") String order,
                                                                    @Param("now") Date now);
                                                                
    @SelectProvider(type=GroupSearchQueryBuilder.class, method="selectMeterOnlineBulkKernelDataByGenGroupLeafNodes")
    @Results(value = {
            @Result(column = "usage_point_id", property = "usagePointId"),
            @Result(column = "usage_point_name", property = "usagePointName"),
            @Result(column = "installation_date", property = "installationDate"),
            @Result(column = "customer_agreement_id", property = "customerAgreementId"),
            @Result(column = "service_location_id", property = "serviceLocationId"),
            @Result(column = "record_status", property = "recordStatus"),
            @Result(column = "me.meter_id", property="meterId"),
            @Result(column = "meter_num", property = "meterNum"),
            @Result(column = "end_device_store_id", property = "deviceStoreId"),
            @Result(column = "breaker_id", property = "breakerId"),
            @Result(column = "replace_reason_log_id", property = "replaceReasonLogId"),
            @Result(column = "enc_key", property ="encKey"),
            @Result(column = "sts_curr_supply_group_id", property = "stsCurrSupplyGroupCodeId"),
            @Result(column = "sts_curr_supply_group_code", property = "stsCurrSupplyGroupCode"),
            @Result(column = "sts_token_tech_id", property = "stsTokenTechCodeId"),
            @Result(column = "sts_algorithm_code_id", property = "stsAlgorithmCodeId"),
            @Result(column = "sts_curr_key_revision_num", property = "stsCurrKeyRevisionNum"),
            @Result(column = "sts_curr_tariff_index", property = "stsCurrTariffIndex"),
            @Result(column = "meter_type_id", property="meterTypeId"),
            @Result(column = "meter_type_name", property = "meterTypeName"),
            @Result(column = "meter_model_id", property="meterModelId"),
            @Result(column = "model_name", property = "meterModelName"),
            @Result(column = "mdc_id", property = "mdcId"),
            @Result(column = "surname", property = "surname"),
            @Result(column = "phone1", property = "phone1"),
            @Result(column = "suite_num", property = "suiteNum"),
            @Result(column = "store_name", property = "deviceStoreName"),
            @Result(column = "meter_uri_address", property = "meterUriAddress"),
            @Result(column = "meter_uri_port", property = "meterUriPort"),
            @Result(column = "meter_uri_protocol", property = "meterUriProtocol"),
            @Result(column = "meter_uri_params", property = "meterUriParams")
          })
    public List<MeterOnlineBulkKernelData> selectMeterOnlineBulkKernelDataByGenGroupLeafNodes(
                                                                @Param("selGrpIdList") ArrayList<Long> selGrpIdList, 
                                                                @Param("sortColumn") String sortColumn, 
                                                                @Param("filterColumn") String filterColumn,
                                                                @Param("filterString") String filterString, 
                                                                @Param("filterDate") Date filterDate,
                                                                @Param("order") String order, 
                                                                @Param("now") Date now,
                                                                RowBounds rowBounds);
                            
}
