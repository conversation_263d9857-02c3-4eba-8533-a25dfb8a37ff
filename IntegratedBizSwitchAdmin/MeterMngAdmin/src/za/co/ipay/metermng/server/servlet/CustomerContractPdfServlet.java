package za.co.ipay.metermng.server.servlet;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import be.quodlibet.boxable.BaseTable;
import be.quodlibet.boxable.Cell;
import be.quodlibet.boxable.Row;
import be.quodlibet.boxable.datatable.DataTable;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.log4j.Logger;
import org.apache.pdfbox.pdfparser.PDFStreamParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;
import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountService;
import za.co.ipay.metermng.server.mybatis.service.GroupService;
import za.co.ipay.metermng.server.mybatis.service.PricingStructureService;
import za.co.ipay.metermng.server.mybatis.service.ServiceResourceService;
import za.co.ipay.metermng.server.rpc.SearchRpcImpl;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

/**
 * CustomerContractPdfServlet is used to generate a customer contract as a pdf file so that the user can save the file for off line use.
 * 
 * <AUTHOR>
 */
public class CustomerContractPdfServlet extends SuperExportDataServlet {
    
    private static Logger logger = Logger.getLogger(CustomerContractPdfServlet.class);

    private SearchRpc searchRpc;
    private GroupService groupService;
    private PricingStructureService pricingStructureService;
    private ServiceResourceService serviceResourceService;
    private AuxAccountService auxAccountService;
    private SimpleDateFormat dateFormat;
    private SimpleDateFormat dateTimeFormat;
    DecimalFormat currencyFormat;
    private PDFont normalfont = PDType1Font.TIMES_ROMAN;
    private PDFont boldfont = PDType1Font.TIMES_BOLD;


    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        ApplicationContext ac = (ApplicationContext) config.getServletContext().getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
        this.searchRpc = (SearchRpcImpl) ac.getBean("searchRpc");
        this.groupService = ac.getBean(GroupService.class);
        this.pricingStructureService = (PricingStructureService) ac.getBean("pricingStructureService");
        this.serviceResourceService = (ServiceResourceService) ac.getBean("serviceResourceService");
        this.auxAccountService = (AuxAccountService) ac.getBean("auxAccountService");
        if (searchRpc == null || messageSource == null || formatSource == null) {
           throw new ServletException("Missing beans not set in the CustomerContractPdfServlet: " + searchRpc + " " + messageSource + " " + formatSource);
        }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }
    
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }
    
    protected void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String localeSt = request.getParameter("locale");
        Locale locale = request.getLocale();
        if (localeSt != null && !localeSt.isEmpty()) {
            String[] localeParts = localeSt.split("_");
            locale = new Locale(localeParts[0], localeParts[1]);
        }

        dateFormat = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("date.pattern"), locale));
        dateTimeFormat = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("datetime.pattern"), locale));
        currencyFormat = new DecimalFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("currency.pattern"), locale));
        String logoUrl = request.getParameter("logo");

        try {
            MeterMngUser user = getCurrentUser(request);
            if (user != null) {
                String usagepointname = request.getParameter("usagepoint");
                UsagePointData usagePointData = searchRpc.getUsagePointDataByUsagePointName(usagepointname);
                String filename = usagePointData.getName()+"_"
                        +usagePointData.getCustomerAgreementData().getCustomerData().getName()+"_"+ GregorianCalendar.getInstance().get(GregorianCalendar.YEAR)
                        +".pdf";

                ByteArrayOutputStream output = createCustomerContract(user.getOrganisationFullName(), usagePointData, logoUrl, locale);
                response.addHeader("Content-Type", "application/force-download");
                response.addHeader("Content-Disposition", "attachment; filename=\""+filename+"\"");
                response.getOutputStream().write(output.toByteArray());
            } else {
                logger.error("No session/logged in user found.");
                writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied"), locale));
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error"), locale));
        }
    }

    @Override
    protected String getExportFileName(HttpServletRequest request) {
        return null;
    }

    public ByteArrayOutputStream createCustomerContract(String mancoDescription, UsagePointData usagePointData, String logoUrl, Locale locale) {

        ByteArrayOutputStream output = new ByteArrayOutputStream();

        if (mancoDescription==null || mancoDescription.isEmpty()) {
            mancoDescription = messageSource.getMessage(new DefaultMessageSourceResolvable("application.default.title"),locale);
        }

        PDDocument doc = null;
        try {

            ClassLoader classLoader = getClass().getClassLoader();
            String template = "/templates/customerContractTemplate_"+ locale.getLanguage()+"_"+locale.getCountry() +".pdf";
            if (classLoader.getResource(template)==null) {
                template = "/templates/customerContractTemplate.pdf";
            }
            boolean drawLogo = !(locale.getCountry().equalsIgnoreCase("cv")); //don't draw logo for cape verde as it's included in the template
            File file  = new File(classLoader.getResource(template).getFile());
            doc = PDDocument.load(file);

            PDDocumentInformation pdd = doc.getDocumentInformation();//ToDo setup doc info
            PDDocumentCatalog docCatalog = doc.getDocumentCatalog();
            PDAcroForm acroForm = docCatalog.getAcroForm();

            String path = (getServletContext().getRealPath("")+ File.separator + logoUrl);
            PDImageXObject logo = PDImageXObject.createFromFile(path,doc);

            String insertValue = "";
            CustomerAccount customerAccount = usagePointData.getCustomerAgreementData().getCustomerAccount();
            CustomerData customerData = usagePointData.getCustomerAgreementData().getCustomerData();
            Location physicalLocation = customerData.getPhysicalLocation();
            Location serviceLocation = usagePointData.getServiceLocation();
            long psID = usagePointData.getUpPricingStructureData().getPricingStructure().getId();
            Tariff tariff = pricingStructureService.getCurrentOrFirstTariffByPricingStructureId(psID);
            MeterData meterData = usagePointData.getMeterData();
            //usagePointData.getUpgengroups().getOrDefault("")
            PDField pageNumField = null;
            PDField page2NumField = null;
            GroupType atividadeEconomicaGroupType = null;
            for (PDField field : acroForm.getFieldTree()) {

                switch (field.getFullyQualifiedName()) {
                    case "heading":
                        insertValue = mancoDescription;
                        break;
                    case "agreement_ref":
                        insertValue = usagePointData.getCustomerAgreementData().getAgreementRef();
                        break;
                    case "current_date":
                        insertValue = dateFormat.format(new Date());
                        break;
                    case "title":
                        insertValue = customerData.getTitle();
                        break;
                    case "initials":
                        insertValue = customerData.getInitials();
                        break;
                    case "first_name":
                        insertValue = customerData.getFirstnames();
                        break;
                    case "surname":
                        insertValue = customerData.getSurname();
                        break;
                    case "full_name":
                        insertValue = customerData.getFirstnames() + " " + customerData.getSurname();
                        break;
                    case "company_name":
                        insertValue = customerData.getCompanyName();
                        break;
                    case "tax_number":
                        insertValue = customerData.getTaxNum();
                        break;
                    case "email1":
                        insertValue = customerData.getEmail1();
                        break;
                    case "email2":
                        insertValue = customerData.getEmail2();
                        break;
                    case "phone1":
                        insertValue = customerData.getPhone1();
                        break;
                    case "phone2":
                        insertValue = customerData.getPhone2();
                        break;
                    case "erf":
                        insertValue = physicalLocation==null?"":physicalLocation.getErfNumber();
                        break;
                    case "street":
                        insertValue = physicalLocation==null?"":physicalLocation.getStreetNum();
                        break;
                    case "building_name":
                        insertValue = physicalLocation==null?"":physicalLocation.getBuildingName();
                        break;
                    case "suite_number":
                        insertValue = physicalLocation==null?"":physicalLocation.getSuiteNum();
                        break;
                    case "address1":
                        insertValue = physicalLocation==null?"":physicalLocation.getAddressLine1();
                        break;
                    case "address2":
                        insertValue = physicalLocation==null?"":physicalLocation.getAddressLine2();
                        break;
                    case "address3":
                        insertValue = physicalLocation==null?"":physicalLocation.getAddressLine3();
                        break;
                    case "latitude":
                        insertValue = physicalLocation==null||physicalLocation.getLatitude()==null?"":String.valueOf(physicalLocation.getLatitude());
                        break;
                    case "longitude":
                        insertValue = physicalLocation==null||physicalLocation.getLongitude()==null?"":String.valueOf(physicalLocation.getLongitude());
                        break;
                    case "up_name":
                        insertValue = usagePointData.getName();
                        break;
                    case "local_de_consumo":
                        insertValue = "(CIL = "+usagePointData.getName()+")";
                        break;
                    case "up_installation_date":
                        insertValue = dateTimeFormat.format(usagePointData.getInstallationDate());
                        break;
                    case "up_activation_date":
                        insertValue = dateFormat.format(usagePointData.getActivationDate());
                        break;
                    case "up_status":
                        insertValue = usagePointData.getRecordStatus().equals(RecordStatus.ACT)
                                ?messageSource.getMessage(new DefaultMessageSourceResolvable("status.active"),locale)
                                :messageSource.getMessage(new DefaultMessageSourceResolvable("status.inactive"),locale);
                        break;
                    case "up_erf_number":
                        insertValue = serviceLocation==null||serviceLocation.getErfNumber()==null?"":serviceLocation.getErfNumber();
                        break;
                    case "up_street_number":
                        insertValue = serviceLocation==null||serviceLocation.getStreetNum()==null?"":serviceLocation.getStreetNum();
                        break;
                    case "up_building_name":
                        insertValue = serviceLocation==null||serviceLocation.getBuildingName()==null?"":serviceLocation.getBuildingName();
                        break;
                    case "up_suite_number":
                        insertValue = serviceLocation==null||serviceLocation.getSuiteNum()==null?"":serviceLocation.getSuiteNum();
                        break;
                    case "up_pricing_structure":
                        insertValue = usagePointData.getUpPricingStructureData().getPricingStructure()==null?"":usagePointData.getUpPricingStructureData().getPricingStructure().getName();
                        break;
                    case "up_address1":
                        insertValue = serviceLocation==null||serviceLocation.getAddressLine1()==null?"":serviceLocation.getAddressLine1();
                        break;
                    case "up_address2":
                        insertValue = serviceLocation==null||serviceLocation.getAddressLine2()==null?"":serviceLocation.getAddressLine2();
                        break;
                    case "up_address3":
                        insertValue = serviceLocation==null||serviceLocation.getAddressLine3()==null?"":serviceLocation.getAddressLine3();
                        break;
                    case "up_latitude":
                        insertValue = serviceLocation==null||serviceLocation.getLatitude()==null?"":String.valueOf(serviceLocation.getLatitude());
                        break;
                    case "up_longitude":
                        insertValue = serviceLocation==null||serviceLocation.getLongitude()==null?"":String.valueOf(serviceLocation.getLongitude());
                        break;
                    case "up_tariff":
                        insertValue = tariff==null?"":tariff.getName();
                        break;
                    case "meter_number":
                        insertValue = meterData==null?"":meterData.getMeterNum();
                        break;
                    case "resource_type":
                        insertValue = "";
                        if ((meterData!=null && meterData.getMeterModelData()!=null && meterData.getMeterModelData().getServiceResourceId()!=null)) {
                            ServiceResource serviceResource = serviceResourceService.getServiceResource(meterData.getMeterModelData().getServiceResourceId());
                            if (serviceResource != null) {
                                insertValue = serviceResource.getName();
                            }
                        }
                        break;
                    case "serial_number":
                        insertValue = meterData==null?"":meterData.getSerialNum();
                        break;
                    case "breaker_id":
                        insertValue = meterData==null || meterData.getBreakerId()==null?"":meterData.getBreakerId();
                        break;
                    case "meter_type":
                        insertValue = meterData==null?"":meterData.getMeterType().getName();
                        break;
                    case "sgc":
                        insertValue = meterData==null||meterData.getStsMeter()==null?"":meterData.getStsMeter().getStsCurrSupplyGroupCode();
                        break;
                    case "meter_model":
                        insertValue = meterData==null||meterData.getMeterModelData()==null?"":meterData.getMeterModelData().getName();
                        break;
                    case "power_limit":
                        insertValue = "";
                        if (meterData!=null && meterData.getPowerLimit()!=null) {
                            insertValue = meterData.getPowerLimit().toPlainString();
                            insertValue+=messageSource.getMessage(new DefaultMessageSourceResolvable("unit.watts.symbol"), locale);
                        }
                        break;
                    case "power_limit_amps":
                        insertValue = "";
                        // TODO If any issues or bugs occur in this code block,
                        // Amps can be calculated using the voltage found in app settings (powerlimit.voltage),
                        // instead of parsing the label string for the amp value, as is currently being done
                        if (meterData!=null && meterData.getPowerLimit()!=null) {
                            insertValue = meterData.getPowerLimitLabel();
                            try {
                                String[] amps = insertValue.split("\\(");
                                insertValue = amps[1].replaceFirst("A\\)", " amps");
                            } catch (Exception e) {
                                insertValue = meterData.getPowerLimitLabel();
                            }
                        }
                        break;
                    case "account_name":
                        insertValue = customerAccount==null?"":customerAccount.getAccountName();
                        break;
                    case "account_balance":
                        insertValue = customerAccount==null||customerAccount.getAccountBalance()==null?"":currencyFormat.format(customerAccount.getAccountBalance().doubleValue());
                        break;
                    case "notification_email":
                        insertValue = customerAccount==null||customerAccount.getNotificationEmail()==null?"":customerAccount.getNotificationEmail().replaceAll("\\s","").replaceAll(",", ",\r");
                        break;
                    case "low_balance_threshold":
                        insertValue = customerAccount==null||customerAccount.getLowBalanceThreshold()==null?"":currencyFormat.format(customerAccount.getLowBalanceThreshold().doubleValue());
                        break;
                    case "notification_phone":
                        insertValue = customerAccount==null||customerAccount.getNotificationPhone()==null?"":customerAccount.getNotificationPhone().replaceAll("\\s","").replaceAll(",", ",\r");
                        break;
                    case "tipo_de_uso":
                        insertValue = "";
                        if (atividadeEconomicaGroupType ==null) {
                            String type;
                            try {
                                type = messageSource.getMessage(new DefaultMessageSourceResolvable("group.type.for.cape.verde.contract"), locale);
                            } catch (Exception e) {
                                type = "Atividade econômica";
                            }
                            atividadeEconomicaGroupType = groupService.getGroupTypeByName(type);
                        }
                        if (atividadeEconomicaGroupType != null) {
                            UpGenGroupLinkData upGenGroupLinkData = usagePointData.getUpgengroups()
                                    .get(atividadeEconomicaGroupType.getId());
                            if (upGenGroupLinkData != null) {
                                GenGroup genGroup = groupService.getGenGroup(upGenGroupLinkData.getGenGroupId());
                                if (genGroup != null) {
                                    insertValue = genGroup.getName();
                                }
                            }
                        }
                        break;
                    case "cae":
                    case "permanente":
                    case "tipo_de_cliente":
                    case "fornecimento":
                        insertValue = field.getValueAsString();
                        break;
                    case "pagesNum":
                        pageNumField = field;
                        break;
                    case "pagesNum2":
                        page2NumField = field;
                        break;

                    default:
                        insertValue = "";
                }
                field.setValue(insertValue);
                field.setReadOnly(true);
            }

            PDPage page1 = doc.getPage(0);
            PDFStreamParser parser = new PDFStreamParser(page1);
            parser.parse();
            PDPageContentStream contentStream;
            PDRectangle box = page1.getMediaBox();
            if (drawLogo) {
                contentStream = new PDPageContentStream(doc, page1, PDPageContentStream.AppendMode.APPEND, true);
                contentStream.drawImage(logo, 15f, box.getHeight()-(logo.getHeight()+15f), logo.getWidth()*1f, logo.getHeight()*1f);
                contentStream.close();
            }
            PDPage page2 = null;
            try {
                page2 = doc.getPage(1);

                List<AuxAccountData> auxAccountDataList = auxAccountService.getAuxAccountsByCustomerAgr(usagePointData.getCustomerAgreementData());
                if (auxAccountDataList != null && !auxAccountDataList.isEmpty()) {
                    box = page2.getMediaBox();
                    if (drawLogo) {
                        contentStream = new PDPageContentStream(doc, page2, PDPageContentStream.AppendMode.APPEND, true);
                        contentStream.drawImage(logo, 15f, box.getHeight() - (logo.getHeight() + 15f), logo.getWidth() * 1f, logo.getHeight() * 1f);
                        contentStream.close();
                    }
                    List<List> data = new ArrayList();
                    List<String> headings = new ArrayList<>(Arrays.asList(messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.auxtype"), locale),
                            messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.auxname"), locale),
                            messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.principleamount"), locale),
                            messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.balance"), locale),
                            messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.status"), locale)));
                    data.add(headings);
                    for (AuxAccountData auxAccountData : auxAccountDataList) {
                        data.add(new ArrayList<>(
                                Arrays.asList(auxAccountData.getAuxType().getName(),
                                        auxAccountData.getAccountName(),
                                        auxAccountData.getPrincipleAmount() == null ? "" : currencyFormat.format(auxAccountData.getPrincipleAmount().doubleValue()),
                                        auxAccountData.getBalance() == null ? "" : currencyFormat.format(auxAccountData.getBalance().doubleValue()),
                                        auxAccountData.getRecordStatus().equals(RecordStatus.ACT)
                                                ? messageSource.getMessage(new DefaultMessageSourceResolvable("status.active"), locale)
                                                : messageSource.getMessage(new DefaultMessageSourceResolvable("status.inactive"), locale))
                        ));
                    }
                    float yStartTable = box.getHeight() - (logo.getHeight() + 60f);
                    BaseTable auxAcountTable = new BaseTable(yStartTable, yStartTable, 15f, box.getWidth() - 30f,
                            15f, doc, page2, true, true);
                    DataTable t = new DataTable(auxAcountTable, page2);
                    t.addListToTable(data, DataTable.HASHEADER);
                    Iterator<Row<PDPage>> rows = auxAcountTable.getRows().iterator();
                    Row<PDPage> row;
                    List<Cell<PDPage>> cells;
                    while (rows.hasNext()) {
                        row = rows.next();
                        cells = row.getCells();
                        for (Cell cell : cells) {
                            cell.setFillColor(Color.WHITE);
                            cell.setFontSize(12);
                            if (row.isHeaderRow()) {
                                cell.setFont(boldfont);
                            } else {
                                cell.setFont(normalfont);
                            }
                        }
                    }
                    float yStart = auxAcountTable.draw();
                    pageNumField.setValue("Page 1 of 2");
                    pageNumField.setReadOnly(true);
                    page2NumField.setValue("Page 2 of 2");
                    page2NumField.setReadOnly(true);
                    addSignatureField(doc, page2, yStart - 60, locale);
                } else {
                    doc.removePage(page2);
                    pageNumField.setValue("Page 1 of 1");
                    pageNumField.setReadOnly(true);
                    addSignatureField(doc, page1, 50, locale);
                }
            } catch (Exception e) {
                logger.error("No page 2 in contract");
            }

            doc.save(output);

        } catch (IOException ioEx) {
            System.out.println("Exception while trying to create blank document - " + ioEx);
        } finally {
            if (doc != null) {
                try {
                    doc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return output;
    }

    private void addSignatureField( PDDocument doc, PDPage page, float starty, Locale locale) throws IOException {
        PDPageContentStream contentStream = new PDPageContentStream(doc, page, PDPageContentStream.AppendMode.APPEND, true);
        contentStream.beginText();
        contentStream.setFont(PDType1Font.TIMES_BOLD, 12);
        contentStream.newLineAtOffset(15,starty);
        contentStream.showText(messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.signature"),locale)
                +": ___________________________        "
                +messageSource.getMessage(new DefaultMessageSourceResolvable("print.customer.contract.signature.date"),locale)
                +": ___________________________");
        contentStream.endText();
        contentStream.close();
    }
}