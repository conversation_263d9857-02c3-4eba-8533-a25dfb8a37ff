package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;

import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;

public interface ICalendarSuggestionMapper {
    
    @Select("select * from tou_season where lower(tou_season_name) like #{name}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonMapper.BaseResultMap")
    public List<TouSeason> findSeasonByLikeLowerName(@Param("name") String name, RowBounds rowbounds);
    
    @Select("select * from tou_period where lower(period_code) like #{query} or lower(period_name) like #{query} order by period_code")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.TouPeriodMapper.BaseResultMap")
    public List<TouPeriod> findPeriodByLikeLowerName(@Param("query") String code, RowBounds rowbounds);

    @Select("select * from tou_day_profile where (lower(day_profile_code) like #{query} or lower(day_profile_name) like #{query}) and tou_calendar_id=#{calId} order by day_profile_code")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.TouDayProfileMapper.BaseResultMap")
    public List<TouDayProfile> findDayProfileByCalendarIdLikeLowerName(@Param("calId") Long calendarId, @Param("query") String thequery, RowBounds rowBounds);
}
