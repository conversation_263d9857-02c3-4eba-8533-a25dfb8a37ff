package za.co.ipay.metermng.server.bulkupload;


import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.NoCurrentUserException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.bulkupload.ICsvMapToData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;


@Controller
public abstract class BulkUploadController {
    
    @Autowired
    private ServletContext servletContext;

    @Autowired
    private AppSettingService appSettingService;
    
    @Autowired
    public ExposedReloadableResourceBundleMessageSource importMessageSource;

    public void setImportMessageSource(ExposedReloadableResourceBundleMessageSource importMessageSource) {
        this.importMessageSource = importMessageSource;
    }
    
    public static final String FILE_SEPARATOR = System.getProperty("file.separator");
    private Logger logger = Logger.getLogger("BulkUploadController");
    //private DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    protected String ourRef;
    protected String user;
    protected boolean enableMultiUp = false;
    protected boolean ignoreDups = false;
    protected String fileName;
    
    protected String headingIndicator;
    public HashMap<Integer, String> csvFieldMap;
    

    @RequestMapping(method=RequestMethod.POST)
    public @ResponseBody String handleFileUpload(@RequestParam("action") String actionParam,
                                                 @RequestParam("ignoreDups") String ignoreDupsParam,
                                                 @RequestParam("file") MultipartFile file) {
        
        String fn = file.getOriginalFilename();
        logger.info("**** Uploading file: " + fn + " : with actionParam=" + actionParam + " "  );
        
        ourRef = getOurRefFromFilename(fn);
        ignoreDups = ignoreDupsParam.equals("true") ? true : false;
        
        //enableMultiUp is only relevant when agr ref is part of upload. When auto allocate agr ref, dups not catered for 
        AppSetting appSetting = appSettingService.getAppSettingByKey(AppSettings.MULTI_USAGE_POINT_ENABLE);
        if (appSetting != null && appSetting.getValue().equalsIgnoreCase("true")) {
            enableMultiUp = true;
        }
        
        if (!file.isEmpty()) {         
            try {                
                if (actionParam.equals("uploadCsv")) {
                    String fullFileName = processFile(file);
                    if (fullFileName == null) {
                        return "EXCEPTION:bulk.upload.invalid.cannot.create.dir";
                    }
                    saveFilenameInSession(fullFileName);
                    String result = validateCsv(fullFileName);
                    logger.info("**** validateCsv(" + fullFileName + ") completed");
                    return result;
                } else if (actionParam.equals("processTrans")) {
                    String result = processTransactions();
                    logger.info("**** processTransactions() for ourRef=" + ourRef + " completed. result=" + result);
                    return result;
                } else {
                    return "EXCEPTION:bulk.upload.file.action.unknown";
                }
            } catch (Exception e) {
                logger.error("**** Error uploading file:", e);
                return "EXCEPTION: " + e.getLocalizedMessage() + " / " + e.getMessage();
            }
        } else {
            return "EXCEPTION:customer.trans.upload.file.none";
        }
    }
    
    protected String getOurRefFromFilename(String filename) {
        int indexOfHyphen = filename.lastIndexOf('-');
        int indexOfPeriod = filename.lastIndexOf('.');
        return filename.substring(indexOfHyphen + 1, indexOfPeriod);
    }

    private String processFile(MultipartFile file) throws Exception {
            File dir = new File((String) servletContext.getInitParameter("baseFolder"));
        try {
            if (!dir.mkdirs()) {
                if (!dir.isDirectory()) { 
                    return null;
                }
            }
        } catch (Exception e) {
            logger.debug("**** EXCEPTION creating directory=" + e.getMessage());
            return null;
        }
        
        String fileDate = new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date());
        String fname = "TransUpload_" + fileDate + "_" + getUser().getUserName() + "-" + ourRef + ".csv";
        File copyFile = new File(dir, fname);
        file.transferTo(copyFile);
        logger.info("**** Filename copyFile.getPath()=" + copyFile.getPath());
        return copyFile.getPath();
    }
    
    protected MeterMngUser getUser() throws ServiceException {
        HttpSession httpSession = getSession();
        if (httpSession != null) {
            MeterMngUser user = (MeterMngUser) httpSession.getAttribute(MeterMngStatics.METER_MNG_USER_ATTR);
            if (user == null) {
                logger.error("**** No current user in session");
                throw new NoCurrentUserException("error.session.user.none");
            } else {
                return user;
            }
        } else {
            logger.error("**** No current session");
            throw new NoCurrentUserException("error.session.none");
        }
    }
    
    private HttpSession getSession() {
        HttpServletRequest request = null; 
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        if (requestAttributes != null) {
             request = requestAttributes.getRequest();
             return request.getSession(false);  
        } else {
            return null;
        }
        
    }
    
    private void saveFilenameInSession(String filename) {
        HttpSession httpSession = getSession();
        if (httpSession != null) {
            httpSession.setAttribute("ProcessUploadFile", filename);
            logger.info("**** Session filename saved, ProcessUploadFile=" + filename);
        } else {
            logger.error("**** No current session");
            throw new NoCurrentUserException("error.session.none");
        }
    }
    
    protected String getFilenameFromSession() {
        HttpSession httpSession = getSession();
        if (httpSession != null) {
            String filename = (String) httpSession.getAttribute("ProcessUploadFile");
            if (filename == null) {
                logger.error("**** No filename in session");
            } else {
                logger.info("**** ProcessUploadFile retrieved from session=" + filename);
            }
            return filename;
        } else {
            logger.error("**** No current session");
            throw new NoCurrentUserException("error.session.none");
        }
    }
 
    //----------------------------------------------------------------------------------------------------------------------
    private  String  validateCsv(String fileName) {
        validationPreProcessing();
        
        StringBuilder errorTrans = new StringBuilder();
        StringBuilder validTrans = new StringBuilder();
        int noErrors = 0;
        int noValid = 0;
        int noIgnored = 0;
        
        String  thisLine = null;
        BufferedReader br = null;
        try{
            br = new BufferedReader(new FileReader(fileName));
            while ((thisLine = br.readLine()) != null && noErrors < 16) {
                if (thisLine != null && !thisLine.isEmpty()) {
                    //logger.info(">>>RC: validateCsv: thisLLine= " +  thisLine + " headingIndicator=" + headingIndicator + " csvFieldMap.size()=" + csvFieldMap.size());
                    if (thisLine.contains("Info: Required") || thisLine.contains(importMessageSource.getMessage(new DefaultMessageSourceResolvable("bulk.upload.template.required.first"), null))) {
                        continue; 
                    }
                    if (thisLine.contains(headingIndicator) && csvFieldMap.isEmpty()) {
                        //for headerLine construct the index to DataName Map
                        constructCsvFieldMap(thisLine);
                        //add headings to returnstrings
                        validTrans.append(thisLine).append(System.lineSeparator());
                        errorTrans.append(thisLine).append(System.lineSeparator());
                        continue;
                    }
                   
                    thisLine = thisLine.replace("\"", "");           //in case the .csv export surrounds with quotes!
                    String errorString = validateTrans(thisLine);
                    if (errorString.contains("dupIgnore")) {
                        noIgnored++;
                    } else if (errorString.isEmpty() || errorString.length() == 0) {
                        //if all are valid, will send back the first 15 valid lines - but only if NO errors 
                        if (noErrors == 0) {

                            if (noValid < 16) {
                                logger.debug("**** VALIDLINE=" + thisLine);
                                validTrans.append(thisLine).append(System.lineSeparator());
                            } 
                            noValid++;
                        }
                    } else {
                        logger.debug("**** ERRORLINE=" + thisLine);
                        errorTrans.append(thisLine).append(",").append(errorString).append(System.lineSeparator());
                        noErrors++;
                    }
                }
            }    
        } catch (BulkUploadException bue) {
            bue.printStackTrace();
            logger.error("**** EXCEPTION READING FILE: " + bue.getMessage() + " fileName=" + fileName);
            if (bue.getMessage() != null && bue.getMessage().contains(ICsvMapToData.UNKNOWN_COLUMN_HEADING)) {
                return ("EXCEPTION:BadHeading:bulk.upload.file.unrecognized.heading.error_" + bue.getMessage().substring(bue.getMessage().indexOf(":") + 1));
            } else {
                return "EXCEPTION:bulk.upload.file.error";
            } 
        }catch(Exception e){
            e.printStackTrace();
            logger.error("**** EXCEPTION READING FILE: " + e.getMessage() + " fileName=" + fileName);
            if (e.getMessage() != null && e.getMessage().contains(ICsvMapToData.UNKNOWN_COLUMN_HEADING)) {
                return ("EXCEPTION:BadHeading:bulk.upload.file.unrecognized.heading.error_" + e.getMessage().substring(e.getMessage().indexOf(":") + 1));
            } else {
                return "EXCEPTION:bulk.upload.file.error";
            }    
        }finally{
            if (noIgnored > 0) {
                logger.info("Bulk Validation Step: Number of Duplicate Meter Numbers that will be ignored in process step = " + noIgnored);
            }
            try {
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
                logger.error("**** EXCEPTION CLOSING FILE: " + e.getMessage());
                return "EXCEPTION:bulk.upload.file.error";
            }
        }

        if (noErrors == 0 && noValid == 0 && noIgnored == 0) {
            return ("EXCEPTION:bulk.upload.file.no.meterupdata");
        }
        String returnString = "";
        if (noErrors > 0) {
            returnString = errorTrans.toString();
        } else {
            returnString = validTrans.toString();
        }
        int lastIndx = returnString.lastIndexOf(System.lineSeparator());
        if (lastIndx > 0) {
            returnString = returnString.substring(0, lastIndx);
        }
        return returnString;
    }

    abstract void validationPreProcessing();                                    // setup mapping lists & set Heading value that will be used to recognize the heading line, eg. "Meter Num"; setup specific info needed for validations
    abstract void constructCsvFieldMap(String headingLine) throws BulkUploadException;   // from the heading line construct a map of index --> dataName
    abstract String validateTrans(String thisLine);                             // validate a single transaction
    
    /*
     * Prepare an errormessage
     * Params:  typeError - determines the error text key for messages.properties
     *          fieldNameKey - messages.properties key for name of field (generally the heading text key)   
     */
    public String addValidationError(String typeError, String fieldNameKey) {
        return new StringBuilder().append(getValidationErrorKey(typeError)).append("_")
                .append(importMessageSource.getMessage(new DefaultMessageSourceResolvable(fieldNameKey), null))
                .append(";").toString();
    }

    private String getValidationErrorKey(String typeError) {
        String errorKey = "invalid.field";
        if (typeError.equals("required")) {
            errorKey = "invalid.required.field";
        } else if (typeError.equals("nonexisting")) {
            errorKey = "invalid.nonexisting.field";
        } else if (typeError.equals("duplicate")) {
            errorKey = "duplicate.field";
        } else if (typeError.equals("invalid")) {
            errorKey = "invalid.field";
        } else if (typeError.equals("parsedate")) {
            errorKey = "invalid.parsedate";
        } else if (typeError.equals("regex")) {
            errorKey = "invalid.regex";
        }
        return "bulk.upload." + errorKey;
    }

    //----------------------------------------------------------------------------------------------------------------------
    /*
     * Call abstract method which sets up a processinglist. Then afterwards, process the list.
     * The processing is transactional - so the entire upload must be successful for the job to complete; but in some cases (eg. account balances) - transactions have already been created & sent to bizswitch.  
     */
    public String processTransactions() throws Exception {
        processTransactionsPreProcessing();

        fileName = getFilenameFromSession();
        if (fileName == null) {
            logger.debug("**** EXCEPTION : processTransactions(): Filename is null");
            return "EXCEPTION:bulk.upload.file.process.error";
        }

        user = getUser().getUserName();
        ourRef = getOurRefFromFilename(fileName);
        
        String  thisLine = null;
        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(fileName));
            while ((thisLine = br.readLine()) != null) {
                if (thisLine != null && !thisLine.isEmpty()) {
                    if (thisLine.contains("Info: Required") || thisLine.contains(importMessageSource.getMessage(new DefaultMessageSourceResolvable("bulk.upload.template.required.first"), null))) {
                        continue; 
                    }

                    if (thisLine.contains(headingIndicator) && csvFieldMap.isEmpty()) {
                        //for headerLine construct the index to DataName Map
                        constructCsvFieldMap(thisLine);
                        continue;
                    }
                    thisLine = thisLine.replace("\"", "");     //in case the .csv export surrounds with quotes!
                    addToProcessList(thisLine);
                }
            }
        }catch(Exception e){
            e.printStackTrace();
            logger.error("**** EXCEPTION READING FILE: " + e.getMessage());
            return "EXCEPTION:bulk.upload.file.error";
        }finally{
            try {
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
                logger.error("**** EXCEPTION CLOSING FILE: " + e.getMessage());
                return "EXCEPTION:bulk.upload.file.error";
                
            }
        }

        
        try {
            return processUpload();
        } catch (Exception e) {
            logger.error("**** processUpload Exception= " + e.getLocalizedMessage() + " / " + e.getMessage());
            throw new Exception(e.getLocalizedMessage() + " / " + e.getMessage());             
        }
    }
    
    abstract void processTransactionsPreProcessing(); //set up mapping lists & specific processing preparation 
    abstract void addToProcessList(String thisLine);  //create List/s of objects to upload
    abstract String processUpload() throws Exception;   //execute the upload process
}



