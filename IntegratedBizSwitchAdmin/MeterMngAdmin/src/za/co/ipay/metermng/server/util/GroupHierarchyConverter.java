package za.co.ipay.metermng.server.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;
import za.co.ipay.metermng.shared.GroupHierarchyData;


/**
 * A helper utility class to convert Group Hierarchy data.
 * <AUTHOR>
 */
public class GroupHierarchyConverter {
    
    private static Logger logger = Logger.getLogger(GroupHierarchyConverter.class);
        
    public static GroupHierarchy convertGroupHierarachyData(GroupHierarchyData groupHierarchyData) {
        if (groupHierarchyData != null) {
            GroupHierarchy groupHierarchy = new GroupHierarchy();
            groupHierarchy.setDescription(groupHierarchyData.getDescription());
            groupHierarchy.setGroupTypeId(groupHierarchyData.getGroupTypeId());
            groupHierarchy.setId(groupHierarchyData.getId());
            groupHierarchy.setName(groupHierarchyData.getName());
            groupHierarchy.setParentId(groupHierarchyData.getParentId());
            groupHierarchy.setRecordStatus(groupHierarchyData.getRecordStatus());
            groupHierarchy.setAccessGroup(groupHierarchyData.isAccessGroup());
            return groupHierarchy;
        } else {
            return null;
        }
    }
    
    public static GroupHierarchyData convertGroupHierarachy(GroupHierarchy groupHierarchy) {
        if (groupHierarchy != null) {
            GroupHierarchyData data = new GroupHierarchyData();
            data.setDescription(groupHierarchy.getDescription());
            data.setGroupTypeId(groupHierarchy.getGroupTypeId());
            data.setId(groupHierarchy.getId());
            data.setName(groupHierarchy.getName());
            data.setParentId(groupHierarchy.getParentId());
            data.setRecordStatus(groupHierarchy.getRecordStatus());
            data.setAccessGroup(groupHierarchy.isAccessGroup());
            return data;
        } else {
            return null;
        }
    }
    
    public static ArrayList<GroupHierarchyData> convertGroupHierarchies(List<GroupHierarchy> hierarchies) {
        ArrayList<GroupHierarchyData> data = new ArrayList<GroupHierarchyData>();
        logger.debug("Converting hierarchies: "+hierarchies);
        GroupHierarchy gh = null;
        GroupHierarchyData d = null;
        for(int i=0;i<hierarchies.size();i++) {
            gh = hierarchies.get(i);
            logger.debug("Converting hierarchy: "+gh.getId()+", "+gh.getName());
            d = GroupHierarchyConverter.convertGroupHierarachy(gh);
            d.setLevel(i+1);
            data.add(d);
        }
        return data;
    }

    private GroupHierarchyConverter() {
        //no instances required
    }
}
