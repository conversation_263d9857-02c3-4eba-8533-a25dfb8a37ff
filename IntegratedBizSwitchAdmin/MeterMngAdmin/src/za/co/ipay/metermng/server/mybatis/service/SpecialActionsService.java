package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsLogMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionsMapper;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsExample;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionsExample;

public class SpecialActionsService {

    private SpecialActionsMapper specialActionsMapper;
    private SpecialActionReasonsMapper specialActionReasonsMapper;
    private SpecialActionReasonsLogMapper specialActionReasonsLogMapper;
    private ExposedReloadableResourceBundleMessageSource messageSource;

    private static Logger logger = Logger.getLogger(AppSettingService.class);
    
    
    public void setSpecialActionsMapper(SpecialActionsMapper spActionsMapper) {
        this.specialActionsMapper = spActionsMapper;
    }
    
    public void setSpecialActionReasonsMapper(SpecialActionReasonsMapper spActionReasonsMapper) {
        this.specialActionReasonsMapper = spActionReasonsMapper;
    }

    public void setSpecialActionReasonsLogMapper(
			SpecialActionReasonsLogMapper specialActionReasonsLogMapper) {
		this.specialActionReasonsLogMapper = specialActionReasonsLogMapper;
	}

	public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }
    //---------------------------------------------------------------------------------------------------
    @Transactional(readOnly=true)
    public SpecialActions getSpecialAction(Long specialActionId) {
        if (specialActionId != null) {
            return specialActionsMapper.selectByPrimaryKey(specialActionId);
        } else {
            return null;
        }
    }
    
//    @Transactional(readOnly=true)
//    public Integer getSpecialActionsCount() throws ServiceException {
//        SpecialActionsExample example = new SpecialActionsExample();
//        example.createCriteria().andIdIsNotNull();
//        int count = specialActionsMapper.countByExample(example);
//        return count;
//    }

    @Transactional(readOnly=true)
    public ArrayList<SpecialActions> getSpecialActions() throws ServiceException {
        SpecialActionsExample example = new SpecialActionsExample();
        example.createCriteria().andIdIsNotNull();
        return new ArrayList<SpecialActions>(specialActionsMapper.selectByExample(example));
    }
    
    @Transactional(readOnly=true)
    public SpecialActions getSpecialActionByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            SpecialActionsExample example = new SpecialActionsExample();
            example.createCriteria().andSpecialActionsNameEqualTo(name);
            List<SpecialActions> specialActions = specialActionsMapper.selectByExample(example);
            if (specialActions.isEmpty()) {
                return null;
            } else {
                return specialActions.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=false)
    @PreAuthorize("hasRole('mm_special_actions')")
    public SpecialActions saveSpecialAction(SpecialActions specialActions) throws ValidationException, ServiceException {
        SpecialActions existing = (SpecialActions) getSpecialActionByName(specialActions.getSpecialActionsName());
        if (existing != null && !existing.getId().equals(specialActions.getId())) {
            throw new ValidationException(new ValidationMessage("special.actions.name.duplicate", new String[]{specialActions.getSpecialActionsName()}, true));
        }
        
        if (specialActions.getId() == null) {
                throw new ValidationException(new ValidationMessage("special.actions.error.new", new String[]{specialActions.getSpecialActionsName()}, true));
        } else {
            String errorMsg = null;
            
            // ToDo validation
            if (specialActions.getInputType()==null) {
            	errorMsg = "special.actions.error.inputtype.required";
            }
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                if (specialActionsMapper.updateByPrimaryKey(specialActions) != 1) {
                    throw new ValidationException(new ValidationMessage("error.save", new String[]{"special.action.name"}, true));
                }
            } else {
                throw new ValidationException(new ValidationMessage(errorMsg, true));
            }
        }
        return specialActions;
    }
 
    @Transactional(readOnly=true)
    public ArrayList<SpecialActionReasons> getSpecialActionReasons(Long specialActionId, boolean activeOnly) throws ValidationException, ServiceException {
        if (specialActionId != null) {
        	SpecialActionReasonsExample example = new SpecialActionReasonsExample();
        	Criteria criteria =  example.createCriteria().andSpecialActionsIdEqualTo(specialActionId);
            if (activeOnly) {
            	criteria.andRecordStatusEqualTo(RecordStatus.ACT);
            }
            return new ArrayList<SpecialActionReasons>(specialActionReasonsMapper.selectByExample(example));
        } else {
            return null;
        }
    }

    @Transactional(readOnly=true)
    public SpecialActionReasons getSpecialActionReasonsByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            SpecialActionReasonsExample example = new SpecialActionReasonsExample();
            example.createCriteria().andReasonNameEqualTo(name);
            List<SpecialActionReasons> specialActionReasons = specialActionReasonsMapper.selectByExample(example);
            if (specialActionReasons.isEmpty()) {
                return null;
            } else {
                return specialActionReasons.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=true)
    public SpecialActions getSpecialActionByValue(String value) throws ValidationException, ServiceException {
        if (value != null && !value.trim().equals("")) {
            SpecialActionsExample example = new SpecialActionsExample();
            example.createCriteria().andSpecialActionsValueEqualTo(value);
            List<SpecialActions> specialActions = specialActionsMapper.selectByExample(example);
            if (specialActions.isEmpty()) {
                return null;
            } else {
                return specialActions.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=true)
    public SpecialActionReasonsLog getSpecialActionReasonsLog(Long logId) throws ValidationException, ServiceException {
        return specialActionReasonsLogMapper.selectByPrimaryKey(logId);
    }    
    
    @Transactional(readOnly=false)
    @PreAuthorize("hasRole('mm_special_actions')")
	public SpecialActionReasons insertSpecialActionReason(SpecialActionReasons specialActionReason) throws ValidationException, ServiceException, AccessControlException {
        if (specialActionReasonsMapper.insert(specialActionReason) == 1) {
            return specialActionReason;
        } else {
            throw new ServiceException("special.action.reasons.error.save");
        }
	}
    
    @Transactional(readOnly=false)
    @PreAuthorize("hasRole('mm_special_actions')")
	public SpecialActionReasons updateSpecialActionReason(SpecialActionReasons specialActionReason) throws ValidationException, ServiceException, AccessControlException {
        if (specialActionReasonsMapper.updateByPrimaryKey(specialActionReason) == 1) {
            return specialActionReason;
        } else {
            throw new ServiceException("special.action.reasons.error.save");
        }
	}
    
    public boolean getMridExistence(String mrid, Long id) {
        SpecialActionReasonsExample example = new SpecialActionReasonsExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(id != null) {
            criteria.andIdNotEqualTo(id);
        }
        return !specialActionReasonsMapper.selectByExample(example).isEmpty();
    }

}
