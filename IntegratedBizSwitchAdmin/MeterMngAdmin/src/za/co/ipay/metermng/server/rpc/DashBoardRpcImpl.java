package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.DashBoardRpc;
import za.co.ipay.metermng.server.mybatis.service.DashBoardService;
import za.co.ipay.metermng.server.mybatis.service.VendingActivityService;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataBigDecimalDto;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataCountTableDto;

public class DashBoardRpcImpl extends BaseMeterMngRpc implements DashBoardRpc {

    private static final long serialVersionUID = 1L;

    private VendingActivityService vendingActivityService;
    private DashBoardService dashBoardService;

    public DashBoardRpcImpl() {
        logger = Logger.getLogger(DashBoardRpcImpl.class);
    }
    
    public void setVendingActivityService(VendingActivityService vendingActivityService) {
        this.vendingActivityService = vendingActivityService;
    }

    public void setDashBoardService(DashBoardService dashBoardService) {
        this.dashBoardService = dashBoardService;
    }

    @Override
	public ArrayList<TsDataCountTableDto> getVendingActivity()
			throws ValidationException, ServiceException {
		return  vendingActivityService.getVendingActivity(getUser().getCurrentGroupId());
	}

    @Override
    public ArrayList<TsDataBigDecimalDto> getBuyingIndexData()
            throws ValidationException, ServiceException {
        return dashBoardService.getBuyingIndexData(getUser().getCurrentGroupId());
    }
}
