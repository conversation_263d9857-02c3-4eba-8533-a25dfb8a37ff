package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.MdcChannelRpc;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.mybatis.generated.model.ModelChannelConfig;
import za.co.ipay.metermng.mybatis.generated.model.TimeInterval;
import za.co.ipay.metermng.server.mybatis.service.MdcChannelService;
import za.co.ipay.metermng.server.mybatis.service.TimeIntervalService;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;
import za.co.ipay.metermng.shared.dto.meter.ModelChannelConfigDto;

public class MdcChannelRpcImpl extends BaseMeterMngRpc implements MdcChannelRpc {

    private static final long serialVersionUID = 1L;
    
    private MdcChannelService mdcChannelService;
    private TimeIntervalService timeIntervalService;

    //-------------------------------------------------------------------------------------------------------------
    public void setMdcChannelService(MdcChannelService mdcChannelService) {
        this.mdcChannelService = mdcChannelService;
    }  
    
    public void setTimeIntervalService(TimeIntervalService timeIntervalService) {
        this.timeIntervalService = timeIntervalService;
    }  
    
    //-------------------------------------------------------------------------------------------------------------
    public MdcChannelRpcImpl() {
        this.logger = Logger.getLogger(MdcChannelRpcImpl.class);
    }

    @Override
    public List<MdcChannel> getMdcChannels(Boolean enabled, Long mdcId) throws ServiceException {
        return mdcChannelService.getMdcChannels(enabled, mdcId);
    }
    
    @Override
    public List<MdcChannel> getMdcChannelsNotOverriddenByMeter(Long mdc_id, Long meter_model_id) throws ServiceException {
        return mdcChannelService.getMdcChannelsNotOverriddenByMeter(mdc_id, meter_model_id);
    }
    
    @Override   
    public List<MdcChannelDto> getMdcChannelDtos(Long mdcId) throws ServiceException {
        return mdcChannelService.getMdcChannelDtos(mdcId);
    }
    
    @Override 
    public MdcChannelDto getMdcChannelDto(Long mdcChannelId) throws ServiceException{
        return mdcChannelService.getMdcChannelDto(mdcChannelId);
    }

    @Override    
    public MdcChannelDto saveMdcChannel(MdcChannelDto mdcChannelDto) throws ValidationException, ServiceException, AccessControlException {
        return mdcChannelService.saveMdcChannel(mdcChannelDto);
    }
    
    @Override   
    public List<MdcChannel> getMdcChannelsForReadingTypesAndMeter(ArrayList<Long> readingTypeIds, Long meterId) {
        return mdcChannelService.getMdcChannelsForReadingTypesAndMeter(readingTypeIds, meterId);
    }
    
    @Override 
    public List<TimeInterval> getTimeIntervals() throws ServiceException {
        return timeIntervalService.getTimeIntervals(true);
    }

    @Override   
    public List<ModelChannelConfigDto> getModelChannelConfigs(Long meterModelId) throws ServiceException {
        return mdcChannelService.getModelChannelConfigs(meterModelId);
    }
    
    @Override   
    public ModelChannelConfigDto saveModelChannelConfig(ModelChannelConfigDto modelChannelConfigDto) throws ServiceException {
        return mdcChannelService.saveModelChannelConfigs(modelChannelConfigDto);
    }

    @Override 
    public void deleteModelChannelConfig(ModelChannelConfig modelChannelConfig) throws ServiceException {
        mdcChannelService.deleteModelChannelConfig(modelChannelConfig);
    }
    
    @Override 
    public void deleteAllModelChannelConfigForMeter(Long meter_model_id) throws ServiceException {
        mdcChannelService.deleteAllModelChannelConfigForMeter(meter_model_id);
    }
}
