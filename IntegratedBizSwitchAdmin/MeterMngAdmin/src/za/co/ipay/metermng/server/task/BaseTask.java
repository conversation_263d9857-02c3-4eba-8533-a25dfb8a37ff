package za.co.ipay.metermng.server.task;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.EmailService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.ScheduleService;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;
import za.co.ipay.utils.files.CsvWriter;

public abstract class BaseTask implements Task {
    
    public static final String LINE_SEPARATOR = System.getProperty("line.separator");

    protected ScheduleService scheduleService;
    protected MeterService meterService;
    protected AppSettingService appSettingService;
    protected ExposedReloadableResourceBundleMessageSource messageSource;
    protected ExposedReloadableResourceBundleMessageSource formatSource;
    protected EmailService emailService;
    private boolean useDefaultLocale;
    private String defaultLocaleName;
    
    private static Logger logger = Logger.getLogger(BaseTask.class);
    
    protected Date[] getDateRange(String timePeriod) {
        Date start = null;
        Date end = null;                
        if (MeterMngStatics.PREVIOUS_DAY_VALUE.equals(timePeriod)) {            
            Calendar now = Calendar.getInstance();
            //start
            now.add(Calendar.DAY_OF_YEAR, -1);
            now.set(Calendar.HOUR_OF_DAY, 0);
            now.set(Calendar.MINUTE, 0);
            now.set(Calendar.SECOND, 0);
            now.set(Calendar.MILLISECOND, 0);
            start = now.getTime();
            //end
            now.set(Calendar.HOUR_OF_DAY, 23);
            now.set(Calendar.MINUTE, 59);
            now.set(Calendar.SECOND, 59);
            now.set(Calendar.MILLISECOND, 999);
            end = now.getTime();
            return new Date[]{ start, end };
        } else if (MeterMngStatics.PREVIOUS_WEEK_VALUE.equals(timePeriod)) {
            Calendar now = Calendar.getInstance();
            //end
            now.add(Calendar.DAY_OF_YEAR, -1);
            now.set(Calendar.HOUR_OF_DAY, 23);
            now.set(Calendar.MINUTE, 59);
            now.set(Calendar.SECOND, 59);
            now.set(Calendar.MILLISECOND, 999);
            end = now.getTime();            
            //start
            now.add(Calendar.DAY_OF_YEAR, -6);
            now.set(Calendar.HOUR_OF_DAY, 0);
            now.set(Calendar.MINUTE, 0);
            now.set(Calendar.SECOND, 0);
            now.set(Calendar.MILLISECOND, 0);
            start = now.getTime();   
            return new Date[]{ start, end };
        } else if (MeterMngStatics.PREVIOUS_MONTH_VALUE.equals(timePeriod)) {
            Calendar now = Calendar.getInstance();
            now.add(Calendar.MONTH, -1);
            now.set(Calendar.DAY_OF_MONTH, 1);
            now.set(Calendar.HOUR_OF_DAY, 0);
            now.set(Calendar.MINUTE, 0);
            now.set(Calendar.SECOND, 0);
            now.set(Calendar.MILLISECOND, 0);
            start = now.getTime();
            //end
            now.set(Calendar.DAY_OF_MONTH, now.getActualMaximum(Calendar.DAY_OF_MONTH));
            now.set(Calendar.HOUR_OF_DAY, 23);
            now.set(Calendar.MINUTE, 59);
            now.set(Calendar.SECOND, 59);
            now.set(Calendar.MILLISECOND, 999);
            end = now.getTime();
            return new Date[]{ start, end };
        } else {
            return null;
        }
    }
    
    protected File writeCsvFile(String meterNumber, MeterReadingType readingType, CsvWriter writer, Locale locale) throws ServiceException {
        String fileName = getFileName(messageSource, locale, meterNumber, readingType, ".csv");
        File file = new File(System.getProperty("java.io.tmpdir") +System.getProperty("file.separator") + fileName);
        PrintWriter out = null;
        try {
            if (file.exists()) {
                if (!file.delete()) {
                    //PrintWriter will truncate any existing file so not a train smash
                    logger.error("Trying to delete current version of file but unable to: "+file.getAbsolutePath());
                }
            }
            out = new PrintWriter(file);
            String contents = writer.getCSVContents();
            out.println(contents);
            out.flush();
            return file;
        } catch (IOException io) {
            logger.error("Error writing contents to file:", io);
            throw new ServiceException("Unable to write CSV file: "+fileName);
        } finally {
            if (out != null) {
                out.close();
            }
        }        
    }
        
    protected Locale getLocale() {
        if (useDefaultLocale) {
            if (defaultLocaleName != null) {
                return StringUtils.parseLocaleString(defaultLocaleName);
            } else {
                return Locale.getDefault();
            }
        } else {
            //TODO in future the TaskSchedule should have a Locale attribute and this determines which locale to use.
            //For now we just the default locale.
            return Locale.getDefault();
        }
    }
    
    protected ItemValuePairDto getFromEmailDets() { 
        Map<String, String> fromEmailDetailsMap = appSettingService.getFromEmailAppSettingDetails();
        return new ItemValuePairDto(fromEmailDetailsMap.get("fromName"), fromEmailDetailsMap.get("fromEmail"));
    }
    
    @Override
    public void setScheduleService(ScheduleService scheduleService) {
        this.scheduleService = scheduleService;
    }

    @Override
    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }    
    
    @Override
    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Override
    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }
    
    @Override
    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }
    
    @Override
    public void setUseDefaultLocale(boolean useDefaultLocale) {
        this.useDefaultLocale = useDefaultLocale;
    }
    
    @Override
    public void setDefaultLocaleName(String defaultLocaleName) {
        this.defaultLocaleName = defaultLocaleName;
    }

    public void setFormatSource(ExposedReloadableResourceBundleMessageSource formatSource) {
        this.formatSource = formatSource;
    }
}
