package za.co.ipay.metermng.server.mybatis.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.ipayxml.ReqMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.ControlType;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.Param;
import za.co.ipay.ipayxml.mdc.ControlResMessage;
import za.co.ipay.ipayxml.mdc.DeviceId;
import za.co.ipay.ipayxml.mdc.DeviceIdType;
import za.co.ipay.ipayxml.mdc.Mdc;
import za.co.ipay.ipayxml.mdc.Override;
import za.co.ipay.ipayxml.metermng.CustBillPayRevReqMessage;
import za.co.ipay.ipayxml.metermng.CustBillPayRevResMessage;
import za.co.ipay.ipayxml.mng.PingReqMessage;
import za.co.ipay.ipayxml.mng.PingResMessage;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.custom.mapper.AuxSupportMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.CustomerAccountCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.NotifyCustomer;
import za.co.ipay.metermng.mybatis.generated.mapper.AccountTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AccountTransTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.BillPayTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MdcMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.PayTypeDetailsMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.PayTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsLogMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.VoteMapper;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.AccountTransExample;
import za.co.ipay.metermng.mybatis.generated.model.AccountTransType;
import za.co.ipay.metermng.mybatis.generated.model.AccountTransTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.BillPayTrans;
import za.co.ipay.metermng.mybatis.generated.model.BillPayTransExample;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccountHist;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.CustomerExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.network.MessageService;
import za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper;
import za.co.ipay.metermng.server.mybatis.model.CustomerWithUsagePointId;
import za.co.ipay.metermng.shared.AccountTransData;
import za.co.ipay.metermng.shared.BillPayTransData;
import za.co.ipay.metermng.shared.CustomerAccountTransData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.user.UserData;
import za.co.ipay.metermng.tariff.NotificationEmail;
import za.co.ipay.metermng.tariff.thin.AccountAdjustment;
import za.co.ipay.metermng.tariff.thin.AccountAdjustmentResult;
import za.co.ipay.metermng.tariff.thin.IAccountAdjustmentProcessor;
import za.co.ipay.metermng.tariff.thin.IpayXmlMessageWithDelay;
import za.co.ipay.utils.StringUtils;
import za.co.ipay.utils.ThreadUtils;
import za.co.ipay.utils.i18n.ITranslationAware;

public class CustomerService {
    private static final Logger logger = Logger.getLogger(CustomerService.class);
    private CustomerMapper customerMapper;
    private CustomerAgreementMapper customerAgreementMapper;
    private LocationService locationService;
    private ICustomerSuggestionMapper customerSuggestionMapper;
    private CustomerAccountMapper customerAccountMapper;
    private CustomerAccountCustomMapper customerAccountCustomMapper;
    private CustomerAccountHistMapper customerAccountHistMapper;
    private CustomerAgreementService customerAgreementService;
    private CustomerAccountService customerAccountService;
    private IpayXmlMessageService ipayXmlMessageService;
    private MdcMapper mdcMapper;
    private AccountTransMapper accountTransMapper;
    private AccountTransTypeMapper accountTransTypeMapper;
    private AuxAccountMapper auxAccountMapper;
    private AuxSupportMapper auxSupportMapper;
    private MeterMapper meterMapper;
    private ITranslationAware translationAware;
    private UsagePointService usagePointService;
    private MeterModelService meterModelService;
    private EmailService emailService;
    private ExposedReloadableResourceBundleMessageSource messageSource;
    private SpecialActionReasonsLogMapper specialActionReasonsLogMapper;
    private IAccountAdjustmentProcessor accountAdjustmentProcessor;
    private BillPayTransMapper billPayTransMapper;
    private PayTypeMapper payTypeMapper;
    private PayTypeDetailsMapper payTypeDetailsMapper;
    private VoteMapper voteMapper;
    private MessageService messageService;
    private AppSettingService appSettingService;

    public void setAccountAdjustmentProcessor(IAccountAdjustmentProcessor accountAdjustmentProcessor) {
        this.accountAdjustmentProcessor = accountAdjustmentProcessor;
    }

    public void setCustomerMapper(CustomerMapper customerMapper) {
        this.customerMapper = customerMapper;
    }

    public void setCustomerSuggestionMapper(ICustomerSuggestionMapper customerSuggestionMapper) {
        this.customerSuggestionMapper = customerSuggestionMapper;
    }

    public void setCustomerAgreementMapper(CustomerAgreementMapper customerAgreementMapper) {
        this.customerAgreementMapper = customerAgreementMapper;
    }

    public void setCustomerAccountMapper(CustomerAccountMapper customerAccountMapper) {
        this.customerAccountMapper = customerAccountMapper;
    }

    public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setCustomerAgreementService(CustomerAgreementService customerAgreementService) {
        this.customerAgreementService = customerAgreementService;
    }

    public void setCustomerAccountService(CustomerAccountService customerAccountService) {
        this.customerAccountService = customerAccountService;
    }

    public void setIpayXmlMessageService(IpayXmlMessageService ipayXmlMessageService) {
        this.ipayXmlMessageService = ipayXmlMessageService;
    }

    public void setMdcMapper(MdcMapper mdcMapper) {
        this.mdcMapper = mdcMapper;
    }

    public void setAccountTransMapper(AccountTransMapper accountTransMapper) {
        this.accountTransMapper = accountTransMapper;
    }

    public void setAccountTransTypeMapper(AccountTransTypeMapper accountTransTypeMapper) {
        this.accountTransTypeMapper = accountTransTypeMapper;
    }

    public void setAuxAccountMapper(AuxAccountMapper auxAccountMapper) {
        this.auxAccountMapper = auxAccountMapper;
    }

    public void setAuxSupportMapper(AuxSupportMapper auxSupportMapper) {
        this.auxSupportMapper = auxSupportMapper;
    }

    public void setMeterMapper(MeterMapper meterMapper) {
        this.meterMapper = meterMapper;
    }

    public void setTranslationAware(ITranslationAware translationAware) {
        this.translationAware = translationAware;
    }

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    public void setSpecialActionReasonsLogMapper(SpecialActionReasonsLogMapper specialActionReasonsLogMapper) {
        this.specialActionReasonsLogMapper = specialActionReasonsLogMapper;
    }

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void setBillPayTransMapper(BillPayTransMapper billPayTransMapper) {
        this.billPayTransMapper = billPayTransMapper;
    }

    public void setPayTypeMapper(PayTypeMapper payTypeMapper) {
        this.payTypeMapper = payTypeMapper;
    }

    public void setPayTypeDetailsMapper(PayTypeDetailsMapper payTypeDetailsMapper) {
        this.payTypeDetailsMapper = payTypeDetailsMapper;
    }

    public void setVoteMapper(VoteMapper voteMapper) {
        this.voteMapper = voteMapper;
    }

    public void setMessageService(MessageService messageService) {
        this.messageService = messageService;
    }

    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }
    //---------------------------------------------------------------------------------------------------------------
    @Transactional(readOnly = true)
    public Customer getCustomerById(Long customerId) {
        return customerMapper.selectByPrimaryKey(customerId);
    }

    @Transactional(readOnly = true)
    public Customer getCustomerByAgreementId(Long customerAgreementId) {
        return customerMapper.selectByPrimaryKey(customerAgreementService.getCustomerAgreementById(customerAgreementId).getCustomerId());
    }


    @Transactional(readOnly = true)
    public Customer getCustomerByIdNumber(String customerIdNumber) {
        List<Customer> custList = customerSuggestionMapper.findByLikeLowerIdNumber(customerIdNumber, new RowBounds());
        if (custList != null && custList.size() == 1) {
            return custList.get(0);
        } else {
            return null;
        }
    }


    @Transactional(readOnly = true)
    public List<Customer> getCustomerSearchSuggestions(String query, Long groupId, int limit) {
        if (groupId == null) {
            return customerSuggestionMapper.findByLikeLowerSurname(query.toLowerCase(), new RowBounds(0, limit));
        } else {
            return customerSuggestionMapper.findByLikeLowerSurnameAndGroup(query.toLowerCase(), groupId, new RowBounds(0, limit));
        }
    }

    @Transactional(readOnly = true)
    public List<Customer> getCustomerByIdNumberSearchSuggestions(String query, Long groupId, int limit) {
        if (groupId == null) {
            return customerSuggestionMapper.findByLikeLowerIdNumber(query.toLowerCase(), new RowBounds(0, limit));
        } else {
            return customerSuggestionMapper.findByLikeLowerIdNumberAndGroup(query.toLowerCase(), groupId, new RowBounds(0, limit));
        }
    }

    @Transactional(readOnly = true)
    public List<CustomerWithUsagePointId> getCustomerWithUpSearchByIdNumberSuggestions(String query, Long groupId, int limit) {
        if (groupId == null) {
            return customerSuggestionMapper.findWithUpByLikeLowerIdNumber(query.toLowerCase(), new RowBounds(0, limit));
        } else {
            return customerSuggestionMapper.findWithUpByLikeLowerIdNumberAndGroup(query.toLowerCase(), groupId, new RowBounds(0, limit));
        }
    }

    @Transactional(readOnly = true)
    public List<CustomerWithUsagePointId> getCustomerWithUpSearchByAgreementRefSuggestions(String query, Long groupId, int limit) {
        RowBounds bounds = new RowBounds(0, limit);
        return groupId != null
                ? customerSuggestionMapper.findByLikeLowerAgreementRefAndGroup(query.toLowerCase(), groupId, bounds)
                : customerSuggestionMapper.findByLikeLowerAgreementRef(query.toLowerCase(), bounds);
    }

    @Transactional(readOnly = true)
    public List<CustomerWithUsagePointId> getCustomerWithUpSearchSuggestions(String query, Long groupId, int limit) {
        if (groupId == null) {
            return customerSuggestionMapper.findWithUPByLikeLowerSurname(query.toLowerCase(), new RowBounds(0, limit));
        } else {
            return customerSuggestionMapper.findWithUPByLikeLowerSurnameAndGroup(query.toLowerCase(), groupId, new RowBounds(0, limit));
        }
    }

    @Transactional
    public CustomerAgreementData updateCustomer(CustomerAgreementData customerAgreement, LocationData physicalLocation,
                                                boolean updateCustomer, boolean updateCustomerAgreement, boolean updateCustomerAccount, String userName) throws ServiceException, ValidationException {

        Customer customer = customerAgreement.getCustomerData();
        CustomerAccount account = customerAgreement.getCustomerAccount();

        if (physicalLocation != null) {
            if (physicalLocation.getId() == null) {
                physicalLocation.setMrid(MridUtil.getMrid());
                updateCustomer = true;
            }
            physicalLocation = locationService.saveLocation(physicalLocation);
            if ((customer.getPhysicalLocationId() == null && physicalLocation.getId() != null)
                    || (customer.getPhysicalLocationId() != null && physicalLocation.getId() == null)
                    || (customer.getPhysicalLocationId() != null && physicalLocation.getId() != null
                    && !customer.getPhysicalLocationId().equals(physicalLocation.getId()))) {
                customer.setPhysicalLocationId(physicalLocation.getId());
                updateCustomer = true;
            }
        }

        if (updateCustomer || customer.getId() == null) {
            if (customer.getCustomerKindId() == null) {
                customer.setCustomerKindId(1L);
            }
            if (customer.getLocaleValue() == null) {
                customer.setLocaleValue("en_ZA");
            }
            if (customer.getId() == null) {
                if (customerMapper.insert(customer) != 1) {
                    throw new ServiceException("customer.error.save");
                }
                updateCustomerAgreement = true;
            } else {
                if (customerMapper.updateByPrimaryKey(customer) != 1) {
                    throw new ServiceException("customer.error.save");
                }
            }
        }

        //check for duplicate account name
        CustomerAccount existingAccount = customerAccountService.getCustomerAccountByAccountName(account.getAccountName());
        if (existingAccount != null && !existingAccount.getCustomerId().equals(account.getCustomerId())) {
            throw new ValidationException(new ValidationMessage("error.field.accountname.duplicate", new String[]{account.getAccountName()}, true));
        }
        if (updateCustomerAccount || account.getId() == null) {
            if (account.getCustomerId() == null) {
                account.setCustomerId(customer.getId());
            }
            if (account.getRecordStatus() == null) {
                account.setRecordStatus(customer.getRecordStatus());
            }
            if (account.getId() == null) {
                updateCustomerAgreement = true;
            }
            updateCustomerAccount(account, userName);
        }


        if (!customerAgreement.isAgreementEmpty()) {
            customerAgreement.setCustomerId(customer.getId());
            customerAgreement.setCustomerAccountId(account.getId());
            if (customerAgreement.getRecordStatus() == null) {
                customerAgreement.setRecordStatus(customer.getRecordStatus());
            }

            ServerValidatorUtil.getInstance().validateDataForValidationMessages(customerAgreement);
            //get customerAgreement by ref; check if customer id is the same, else not unique
            CustomerAgreement existing = customerAgreementService.getCustomerAgreementByAgreementRef(customerAgreement.getAgreementRef());
            if (existing != null && !existing.getCustomerId().equals(customerAgreement.getCustomerId())) {
                throw new ValidationException(new ValidationMessage("error.field.agreementref.duplicate", new String[]{customerAgreement.getAgreementRef()}, true));
            }

            if (updateCustomerAgreement || customerAgreement.getId() == null) {
                updateCustomerAgreement(customerAgreement);
            }
        }


        CustomerAgreementData customerAgreementData = new CustomerAgreementData(customerAgreement);
        CustomerData customerData = new CustomerData(customer);
        customerData.setPhysicalLocation(physicalLocation);
        customerAgreementData.setCustomerData(customerData);
        customerAgreementData.setCustomerAccount(getCustomerAccountById(account.getId()));
        return customerAgreementData;
    }

    @Transactional
    public void updateCustomer(Customer customer) {
        if (customerMapper.updateByPrimaryKey(customer) != 1) {
            throw new ServiceException("customer.error.save");
        }
    }

    @Transactional
    public void updateCustomerAgreement(CustomerAgreement customerAgreement) throws ValidationException, ServiceException {
        if (customerAgreement.getId() == null) {
            customerAgreement.setMrid(MridUtil.getMrid());
            if (customerAgreementMapper.insert(customerAgreement) != 1) {
                throw new ServiceException("customeraggreement.error.save");
            }
        } else {
            if (customerAgreementMapper.updateByPrimaryKey(customerAgreement) != 1) {
                throw new ServiceException("customeraggreement.error.save");
            }
        }
    }

    @Transactional
    public void updateCustomerAccount(CustomerAccount customerAccount, String userName) throws ServiceException {

        if (customerAccount.getId() == null) {
            customerAccount.setMrid(MridUtil.getMrid());
            if (customerAccountMapper.insert(customerAccount) != 1) {
                throw new ServiceException("customeraccount.error.save");
            }
        } else {
            if (customerAccountCustomMapper.updateCustomerAccountExUI(customerAccount.getId(),
                    customerAccount.getAccountName(),
                    customerAccount.getLowBalanceThreshold(),
                    customerAccount.getNotificationEmail(),
                    customerAccount.getNotificationPhone(),
                    customerAccount.getNotifyNewAux(),
                    customerAccount.getNotifyAuxAdjustment())!= 1) {
                throw new ServiceException("customeraccount.error.save");
            }
            //Manually for now update history!
            CustomerAccountHist accountHist = new CustomerAccountHist();
            accountHist.setDateRecModified(new Date());
            accountHist.setUserRecEntered(userName);
            accountHist.setUserAction("update");
            accountHist.setId(customerAccount.getId());  //customer_account_id
            accountHist.setMrid(customerAccount.getMrid());
            accountHist.setMridExternal(customerAccount.isMridExternal());
            accountHist.setCustomerId(customerAccount.getCustomerId());
            accountHist.setAccountName(customerAccount.getAccountName());
            accountHist.setAccountBalance(customerAccount.getAccountBalance());
            accountHist.setLowBalanceThreshold(customerAccount.getLowBalanceThreshold());
            accountHist.setCreditLimit(customerAccount.getCreditLimit());
            accountHist.setRecordStatus(customerAccount.getRecordStatus());
            accountHist.setNotificationEmail(customerAccount.getNotificationEmail());
            accountHist.setNotificationPhone(customerAccount.getNotificationPhone());
            accountHist.setNotifyLowBalance(customerAccount.isNotifyLowBalance());
            accountHist.setNotifyNewAux(customerAccount.getNotifyNewAux());
            accountHist.setNotifyAuxAdjustment(customerAccount.getNotifyAuxAdjustment());

            if (customerAccountHistMapper.insert(accountHist) != 1) {
                throw new ServiceException("customeraccount.error.save");
            }
        }
    }
    
    @Transactional
    public int clearAccessGroupForCustomer(Long customerId) {
        int updated = 0;
        Customer customer = customerMapper.selectByPrimaryKey(customerId);
        customer.setAccessGroupId(null);
        updated = customerMapper.updateByPrimaryKey(customer);
        if (updated != 1) {
            throw new ServiceException("customer.error.save");
        }
        return updated;
    }
    
    @Transactional
    public int updateAccessGroupForCustomer(Long customerId, Long accessGroupId) {
        int updated = 0;
        Customer customer = customerMapper.selectByPrimaryKey(customerId);
        customer.setAccessGroupId(accessGroupId);
        updated = customerMapper.updateByPrimaryKey(customer);
        if (updated != 1) {
            throw new ServiceException("customer.error.save");
        }
        return updated;
    }
    
    @Transactional
    public void updateAccessGroupForCustomerAgeement(Long customerAgreementId, Long accessGroupId) {
        Customer customer = getCustomerByAgreementId(customerAgreementId);
        if (accessGroupId != null && customer.getAccessGroupId() == null) {
            customer.setAccessGroupId(accessGroupId);
            int updated = customerMapper.updateByPrimaryKey(customer);
            if (updated != 1) {
                throw new ServiceException("customer.error.save");
            }
        }
    }
    
    @Transactional(readOnly = true)
    public boolean isExistingCustomersWithGroup() {
        CustomerExample example = new CustomerExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<Customer> customers = customerMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (customers.size() == 1) {
            return true;
        } else {
            return false;
        }
    }

    @Transactional(readOnly = true)
    public CustomerAccount getCustomerAccountById(Long customerAccountId) {
        return customerAccountMapper.selectByPrimaryKey(customerAccountId);
    }


    @Transactional(readOnly = true)
    public List<NotifyCustomer> getEmailNotifyCustomerSuggestions(String query, Long groupId, int limit) {
        if (ValidateUtil.isNotNullOrBlank(query)) {
            return customerAccountCustomMapper.getEmailNotifyCustomers(query.toLowerCase() + MeterMngStatics.WILDCARD, groupId, new RowBounds(0, limit));
        } else {
            return new ArrayList<NotifyCustomer>();
        }
    }

    @Transactional(readOnly = true)
    public List<NotifyCustomer> getSmsNotifyCustomerSuggestions(String query, Long groupId, int limit) {
        if (ValidateUtil.isNotNullOrBlank(query)) {
            return customerAccountCustomMapper.getSmsNotifyCustomers(query.toLowerCase() + MeterMngStatics.WILDCARD, groupId, new RowBounds(0, limit));
        } else {
            return new ArrayList<NotifyCustomer>();
        }
    }

    @Transactional(readOnly = true)
    public List<NotifyCustomer> getNotifyCustomerSuggestions(String query, Long groupId, int limit) {
        if (ValidateUtil.isNotNullOrBlank(query)) {
            return customerAccountCustomMapper.getNotifyCustomers(query.toLowerCase() + MeterMngStatics.WILDCARD, groupId, new RowBounds(0, limit));
        } else {
            return new ArrayList<NotifyCustomer>();
        }
    }

    @Transactional(readOnly = true)
    public NotifyCustomer getNotifyCustomer(Long customerAccountId) {
        if (customerAccountId != null) {
            return customerAccountCustomMapper.getNotifyCustomer(customerAccountId);
        } else {
            return null;
        }
    }

    public IpayResponseData sendSyncAccountBalance(MeterData meterData, Long customerAccountId) throws Exception {
        ControlType controlType = ControlType.SYNC_BALANCE;
        Param[] parameters = new Param[1];

        //read latest customer account for balance
        parameters[0] = new Param("balance", String.valueOf(customerAccountCustomMapper.getAccountBalanceForAccountId(customerAccountId).doubleValue()));

        return sendIpayXmlMessage(meterData, controlType, null, parameters);
    }

    public IpayResponseData sendIpayXmlMessage(MeterData meterData, ControlType controlType, Override override, Param[] parameters) throws Exception {
        Mdc mdc = null;
        try {
            Long mdcId = meterData.getMeterModelData().getMdcId();
            if (mdcId != null) {
                mdc = new Mdc(mdcMapper.selectByPrimaryKey(mdcId).getValue());
            }
        } catch (NullPointerException n) {
            //will send on null
        }

        //read latest meter row for device info
        Meter meter = meterMapper.selectByPrimaryKey(meterData.getId());
        DeviceIdType deviceIdType = DeviceIdType.METER_NUMBER;
        String meterComm = meter.getMeterNum();
        if (meter.isMridExternal()) {
            deviceIdType = DeviceIdType.UNIQUE_ID;
            meterComm = meter.getMrid();
        }
        DeviceId deviceId = new DeviceId(meterComm, deviceIdType);
        
        if (controlType.isConnectDisconnectType() && !StringUtils.isNullOrEmpty(meter.getBreakerId())) {
            Param breakerId = new ControlReqMessage.Param(za.co.ipay.ipayxml.mdc.Constants.PARAM_BREAKER_ID, meter.getBreakerId());
            if(parameters == null || parameters.length == 0) {
                parameters = new ControlReqMessage.Param[1];
                parameters[0] = breakerId;
            } else {
                ControlReqMessage.Param[] newParameters = new ControlReqMessage.Param[parameters.length + 1];
                System.arraycopy(parameters, 0, newParameters, 0, parameters.length);
                newParameters[parameters.length] = breakerId;
                parameters = newParameters;
            }
        }
        
        ControlReqMessage req = new ControlReqMessage(ipayXmlMessageService.getClient(),
                ipayXmlMessageService.getTerm(),
                mdc, deviceId, controlType, parameters, override);
        logger.info(req.toString());

        ControlResMessage res = null;
        try {
            res = (ControlResMessage) ipayXmlMessageService.sendIpayXml(req);
        } catch (Exception e) {
            logger.info("SENDMESSAGE sendIpayXml FAILURE: exception.toString()=" + e.toString());
            if (e.toString().contains("ConnectException")) {
                res = null;
            } else {
                throw new Exception(e);
            }
        }
        if (res == null)
            return null;
        logger.info(res.toString());

        IpayResponseData data = new IpayResponseData();
        data.setResRef(res.getRef());
        data.setResCode(res.getResCode());
        data.setResMsg(res.getRes());
        return data;

    }

    public ArrayList<CustomerAccountTransData> fetchCustomerAccountTransactions(CustomerAgreementData customerAgreementData) throws ServiceException {
        AccountTransExample example = new AccountTransExample();
        example.createCriteria().andCustomerAccountIdEqualTo(customerAgreementData.getCustomerAccountId());
        example.setOrderByClause("date_entered desc");
        List<AccountTrans> accTransList = accountTransMapper.selectByExample(example);

        ArrayList<CustomerAccountTransData> theList = new ArrayList<CustomerAccountTransData>();
        HashMap<Long, String> transTypeMap = new HashMap<Long, String>();

        for (AccountTrans act : accTransList) {
            CustomerAccountTransData catData = new CustomerAccountTransData(act);
            catData.setAgreementRef(customerAgreementData.getAgreementRef());
            catData.setCustomerName(customerAgreementData.getCustomerData().getName());

            if (!transTypeMap.containsKey(act.getAccountTransTypeId())) {
                AccountTransTypeExample tTypeEx = new AccountTransTypeExample();
                tTypeEx.createCriteria().andIdEqualTo(act.getAccountTransTypeId());
                List<AccountTransType> typesList = accountTransTypeMapper.selectByExample(tTypeEx);
                String type = "";
                if (!typesList.isEmpty()) {
                    type = typesList.get(0).getName();
                }
                transTypeMap.put(act.getAccountTransTypeId(), type);
            }
            catData.setTransType(transTypeMap.get(act.getAccountTransTypeId()));

            catData.setSpecialActionReasonsLog(getSpecialActionReasonLog(act));

            theList.add(catData);
        }
        return theList;
    }

    public ArrayList<CustomerAccountTransData> fetchAuxiliaryAccountTransactions(Long auxAccountId) throws ServiceException {
        AccountTransExample example = new AccountTransExample();
        example.createCriteria().andAuxAccountIdEqualTo(auxAccountId);
        example.setOrderByClause("date_entered desc");
        List<AccountTrans> accTransList = accountTransMapper.selectByExample(example);

        ArrayList<CustomerAccountTransData> theList = new ArrayList<CustomerAccountTransData>();
        HashMap<Long, String> transTypeMap = new HashMap<Long, String>();

        for (AccountTrans act : accTransList) {
            CustomerAccountTransData catData = new CustomerAccountTransData(act);

            if (!transTypeMap.containsKey(act.getAccountTransTypeId())) {
                AccountTransTypeExample tTypeEx = new AccountTransTypeExample();
                tTypeEx.createCriteria().andIdEqualTo(act.getAccountTransTypeId());
                List<AccountTransType> typesList = accountTransTypeMapper.selectByExample(tTypeEx);
                String type = "";
                if (!typesList.isEmpty()) {
                    type = typesList.get(0).getName();
                }
                transTypeMap.put(act.getAccountTransTypeId(), type);
            }
            catData.setTransType(transTypeMap.get(act.getAccountTransTypeId()));

            catData.setSpecialActionReasonsLog(getSpecialActionReasonLog(act));

            theList.add(catData);
        }
        return theList;
    }

    private SpecialActionReasonsLog getSpecialActionReasonLog(AccountTrans act) {

        SpecialActionReasonsLog result = null;

        if (act.getSpecialActionReasonsLogId() != null) {
            result = specialActionReasonsLogMapper.selectByPrimaryKey(act.getSpecialActionReasonsLogId());
        }

        return result;
    }

    public void setCustomerAccountCustomMapper(CustomerAccountCustomMapper customerAccountCustomMapper) {
        this.customerAccountCustomMapper = customerAccountCustomMapper;
    }

    public void setCustomerAccountHistMapper(CustomerAccountHistMapper customerAccountHistMapper) {
        this.customerAccountHistMapper = customerAccountHistMapper;
    }

    @Transactional
    public AccountAdjustmentResult inputAccountAdjustment(AccountTransData accountTrans) throws ServiceException {
        CustomerAgreement customerAgreement = customerAgreementService.getCustomerAgreementById(accountTrans.getCustomerAgreementId());
        if (customerAgreement == null) {
            logger.error("inputAccountAdjustment: customerAgreement not found! accountTrans.getCustomerAgreementId()=" + accountTrans.getCustomerAgreementId());
            throw new ServiceException("customer.txn.no.agreement");
        }
        Customer customer = getCustomerById(customerAgreement.getCustomerId());
        /*
         * NB!!!
         * Usage point could be null. For example for the collecting service payments 
         * against accounts only there may be no usage point at all.
         * Also in the case of an STS meter with where service payments are collected 
         * for it, the usage point may not be null, but there would be no mdc.
         */
        UsagePoint usagePoint = usagePointService.getUsagePointByCustomerAgreementId(customerAgreement.getId());
        Meter meter = null;
        MeterModel meterModel = null;
        if (usagePoint != null && usagePoint.getMeterId() != null) {
            meter = meterMapper.selectByPrimaryKey(usagePoint.getMeterId());
            meterModel = meterModelService.getMeterModelById(meter.getMeterModelId());
        }

        if (accountTrans.getSpecialActionReasonsLog() != null) {
            if (specialActionReasonsLogMapper.insert(accountTrans.getSpecialActionReasonsLog()) != 1) {
                throw new ServiceException("customer.auxaccount.error.save");
            }
            accountTrans.setSpecialActionReasonsLogId(accountTrans.getSpecialActionReasonsLog().getId());
        }

        AccountAdjustment accountAdjustment = new AccountAdjustment(logger, translationAware,
                ipayXmlMessageService.getClient(), ipayXmlMessageService.getTerm(), 
                customer, usagePoint, customerAgreement, meter, meterModel, accountTrans);
        return accountAdjustmentProcessor.processAccountAdjustment(accountAdjustment, null);
    }

    @Transactional
    public BigDecimal inputAuxAccountAdjustment(AccountTransData accountTrans) throws ServiceException {
        if (accountTrans.getCustomerAccountId() != null || accountTrans.getAuxAccountId() == null) {
            logger.info("CustomerService: inputAuxAccountAdjustment(): CustomerAccountId / AuxAccountId null issues!. CustomerAccountId= " + accountTrans.getCustomerAccountId() + " auxAccount our ref=" + accountTrans.getOurRef() + "  amt=" + accountTrans.getAmtInclTax());
            throw new ServiceException("customer.auxaccount.error.id");
        }
        auxSupportMapper.updateAuxAccountBalance(accountTrans.getAmtInclTax(), accountTrans.getAuxAccountId());
        AuxAccount auxAcc = auxAccountMapper.selectByPrimaryKey(accountTrans.getAuxAccountId());

        accountTrans.setId(null);
        accountTrans.setResultantBalance(auxAcc.getBalance());
        if (accountTrans.getSpecialActionReasonsLog() != null) {
            if (specialActionReasonsLogMapper.insert(accountTrans.getSpecialActionReasonsLog()) != 1) {
                throw new ServiceException("customer.auxaccount.error.save");
            }
            accountTrans.setSpecialActionReasonsLogId(accountTrans.getSpecialActionReasonsLog().getId());
        }

        accountTransMapper.insert(accountTrans);

        return auxAcc.getBalance();
    }

    public void sendAccountAdjustmentMessages(final AccountAdjustmentResult accountAdjustmentResult) throws ServiceException {
        //From the result, send the ipayxml messages, note that some may have a delay before sending:
        List<IpayXmlMessageWithDelay> ipayXmlMessageList = accountAdjustmentResult.getIpayXmlMessageList();
        for (final IpayXmlMessageWithDelay ipayXmlMessageWithDelay : ipayXmlMessageList) {
            // Send message here in seperate thread: 
            new Thread(new Runnable() {
                public void run() {
                    sendIpayXmlMessage(ipayXmlMessageWithDelay);
                }
            }).start();
        }

        Map<String, String> fromEmailDetailsMap = appSettingService.getFromEmailAppSettingDetails();
        final String fromName = fromEmailDetailsMap.get("fromName");
        final String fromEmail = fromEmailDetailsMap.get("fromEmail");
       
        if (accountAdjustmentResult.getNotifications().size() > 0) {
            //From the result, send the emails
            new Thread(new Runnable() {
                public void run() {
                    sendEmails(fromName, fromEmail, accountAdjustmentResult);
                }
            }).start();
        } else {
            logger.info("CustomerService: Account Adjustment: NO emails to send");
        }
    }
    
    void sendIpayXmlMessage(IpayXmlMessageWithDelay ipayXmlMessageWithDelay) throws ServiceException {
        if (ipayXmlMessageWithDelay.getDelayBeforeSending() > 0L) {
            // delaying before sending
            logger.trace("Delay before sending message: " + ipayXmlMessageWithDelay.getDelayBeforeSending());
            ThreadUtils.sleep(ipayXmlMessageWithDelay.getDelayBeforeSending());
        }

        logger.info("Sending Notification: " + ipayXmlMessageWithDelay.getIpayXmlMessage().toString());
        try {
            ControlResMessage res = (ControlResMessage) ipayXmlMessageService.sendIpayXml((ReqMessage) ipayXmlMessageWithDelay.getIpayXmlMessage());
            logger.info("Notification message result: " + res.toString());
        } catch (Exception e) {
            logger.error("CustomerService: Account Adjustment: sendIpayXmlMessage() failed: Exception=" + e.getMessage());
            //throw new ServiceException("customer.txn.notification.failure");
        }
    }

    void sendEmails(String fromName, String fromEmail, AccountAdjustmentResult accountAdjustmentResult) throws ServiceException {
        for (NotificationEmail notificationEmail : accountAdjustmentResult.getNotifications()) {
            logger.info("Sending email to: " + notificationEmail.getAddresses() + " subject: " + notificationEmail.getSubject()
                    + " message: " + notificationEmail.getMessage());
            try {
                List<String> addresses = notificationEmail.getAddresses();
                emailService.sendEmail(fromName, fromEmail, UserData.from(addresses),
                        notificationEmail.getSubject(), notificationEmail.getMessage(), null);
            } catch (Exception e) {
                logger.error("Error sending email to: " + notificationEmail.getAddresses() + " subject: " + notificationEmail.getSubject()
                        + " message: " + notificationEmail.getMessage() + "   ERRORMESSAGE=" + e.getMessage());
                //throw new ServiceException("customer.txn.send.email.failure");
            }
        }
    }

    public String inputBulkAccountAdjustments(ArrayList<AccountTrans> accountTransList) throws Exception {
        logger.info("Checking trans constructed properly before send: accountTransList.size()=" + accountTransList.size());
        PingReqMessage pingReq = new PingReqMessage(ipayXmlMessageService.getClient(), ipayXmlMessageService.getTerm());
        try {
            PingResMessage res = (PingResMessage) ipayXmlMessageService.sendIpayXml(pingReq);
            if (res == null) {
                logger.info("**** inputBulkAccountAdjustments: SocketTimeOutException returns null!");
                throw new Exception(" SocketTimeOut ");
            }
        } catch (Exception e) {
            logger.info("**** inputBulkAccountAdjustments: " + e.getMessage());
            throw new Exception("BulkException:," + e.getMessage() + " : " + messageSource.getMessage(new DefaultMessageSourceResolvable("customer.trans.upload.Bizswitch.down"), null));
        }

        int noSuccessfulTrans = 0;
        int noDuplicates = 0;
        for (AccountTrans accountTrans : accountTransList) {
            try {
                AccountAdjustmentResult accountAdjustmentResult = singleAccountAdjustmentForBulk(new AccountTransData(accountTrans));
                if (accountAdjustmentResult == null) {      //already processed
                    noDuplicates++;
                    continue;
                }
                noSuccessfulTrans++;
                sendAccountAdjustmentMessages(accountAdjustmentResult);

            } catch (Exception e) {
                throw new Exception("BulkException:," + e.getMessage() + " : " + constructErrorCountString(accountTransList.size(), noSuccessfulTrans, noDuplicates));
            }
        }
        return "SUCCESS:," + constructSuccessCountString(accountTransList.size(), noSuccessfulTrans, noDuplicates);
    }

    public String inputBulkAuxAccountAdjustments(ArrayList<AccountTrans> accountTransList) throws Exception {
        logger.info("Checking trans constructed properly before send: accountTransList.size()=" + accountTransList.size());
        int noSuccessfulTrans = 0;
        int noDuplicates = 0;
        for (AccountTrans accountTrans : accountTransList) {
            try {
                BigDecimal accountAdjResult = singleAuxAccountAdjustmentForBulk(new AccountTransData(accountTrans));
                if (accountAdjResult == null) { // already processed
                    noDuplicates++;
                    continue;
                }
                noSuccessfulTrans++;

            } catch (Exception e) {
                throw new Exception("BulkException:," + e.getMessage() + " : " + constructErrorCountString(accountTransList.size(), noSuccessfulTrans, noDuplicates));
            }
        }
        return "SUCCESS:," + constructSuccessCountString(accountTransList.size(), noSuccessfulTrans, noDuplicates);
    }

    private String constructSuccessCountString(int total, int noSuccessfulTrans, int noDuplicates) {
        return messageSource.getMessage("customer.trans.upload.successful.counts",
                new String[]{String.valueOf(total), String.valueOf(noSuccessfulTrans), String.valueOf(noDuplicates)},
                null);
    }

    private String constructErrorCountString(int total, int noSuccessfulTrans, int noDuplicates) {
        return "," + total + "," + noSuccessfulTrans + "," + noDuplicates;
    }

    @Transactional
    private AccountAdjustmentResult singleAccountAdjustmentForBulk(AccountTransData accountTrans) throws ServiceException {
        //check trans doesn't exist already
        AccountTransExample example = new AccountTransExample();
        example.createCriteria().andCustomerAccountIdEqualTo(accountTrans.getCustomerAccountId()).andOurRefEqualTo(accountTrans.getOurRef());
        List<AccountTrans> existingTransList = accountTransMapper.selectByExample(example);
        if (existingTransList == null || existingTransList.isEmpty()) {
            return inputAccountAdjustment(accountTrans);           //not duplicate
        } else {
            return null;
        }
    }

    @Transactional
	private BigDecimal singleAuxAccountAdjustmentForBulk(AccountTransData accountTrans) throws ServiceException {
        // check trans doesn't exist already
        AccountTransExample example = new AccountTransExample();
        example.createCriteria().andAuxAccountIdEqualTo(accountTrans.getAuxAccountId()).andOurRefEqualTo(accountTrans.getOurRef());
        List<AccountTrans> existingTransList = accountTransMapper.selectByExample(example);
        if (existingTransList == null || existingTransList.isEmpty()) {
            return inputAuxAccountAdjustment(accountTrans); // not duplicate
        } else {
            return null;
        }
    }

    @Transactional
    public List<BillPayTransData> fetchBillPayments(Long customerAgreementId) {
        BillPayTransExample billPayTransExample = new BillPayTransExample();
        billPayTransExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        ArrayList<BillPayTransData> billPayTransDataList = new ArrayList<BillPayTransData>();
        billPayTransMapper.selectByExample(billPayTransExample).forEach(billPayTrans -> {
            BillPayTransData billPayTransData = new BillPayTransData(billPayTrans);
            Long payTypeId = billPayTrans.getPayTypeId();
            if (payTypeId != null) {
                billPayTransData.setPayTypeName(payTypeMapper.selectByPrimaryKey(payTypeId).getName());
            }
            Long payTypeDetailsId = billPayTrans.getPayTypeDetailsId();
            if (payTypeDetailsId != null) {
                billPayTransData
                        .setPayTypeDetailsName(payTypeDetailsMapper.selectByPrimaryKey(payTypeDetailsId).getName());
            }
            Long usagePointId = billPayTrans.getUsagePointId();
            if (usagePointId != null) {
                billPayTransData.setUsagePointName(usagePointService.getUsagePointById(usagePointId).getName());
            }
            Long voteId = billPayTrans.getVoteId();
            if (voteId != null) {
                billPayTransData.setVoteName(voteMapper.selectByPrimaryKey(voteId).getVoteName());
            }
            Long reversalReasonLogId = billPayTrans.getReversalReasonLogId();
            if (reversalReasonLogId != null) {
                SpecialActionReasonsLog specialActionReasonsLog = specialActionReasonsLogMapper
                        .selectByPrimaryKey(reversalReasonLogId);
                billPayTransData.setReversalReason(specialActionReasonsLog.getReasonText());
                billPayTransData.setReversedBy(specialActionReasonsLog.getUsername());
            }
            billPayTransDataList.add(billPayTransData);
        });
        return billPayTransDataList;
    }
    
    public boolean getMridExistence(String mrid, Long custId) {
        CustomerExample example = new CustomerExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(custId != null) {
            criteria.andIdNotEqualTo(custId);
        }
        return !customerMapper.selectByExample(example).isEmpty();
    }
    
    public boolean getCustRefExistence(String custRef, Long custId) {
        CustomerExample example = new CustomerExample();
        Criteria criteria = example.createCriteria().andCustomerReferenceEqualTo(custRef);
        if(custId != null) {
            criteria.andIdNotEqualTo(custId);
        }
        return !customerMapper.selectByExample(example).isEmpty();
    }

    @Transactional
    public IpayResponseData sendBillPaymentReversalMsg(BillPayTrans billPayTrans, String userName,
            SpecialActionReasonsLog specialActionReasonsLog, String comment) {
        try {
            String reason = null;
            if (specialActionReasonsLog != null) {
                reason = specialActionReasonsLog.getReasonText();
            }
            String refReceived = billPayTrans.getRefReceived();
            CustBillPayRevResMessage custBillPayRevResMessage = (CustBillPayRevResMessage) messageService
                    .sendMessage(new CustBillPayRevReqMessage(billPayTrans.getClient(), billPayTrans.getTerminal(),
                            null, refReceived, billPayTrans.getProvider(), userName, reason, comment));
            if (custBillPayRevResMessage == null) {
                logger.debug("CustomerService: sendBillPaymentReversalMsg - connectivity error");
                return null;
            }
            logger.info("CustomerService: sendBillPaymentReversalMsg(refReceived=" + refReceived + ") response="
                    + custBillPayRevResMessage.toString());
            IpayResponseData data = new IpayResponseData();
            data.setResRef(custBillPayRevResMessage.getRef());
            data.setResCode(custBillPayRevResMessage.getResCode());
            data.setResMsg(custBillPayRevResMessage.getRes());
            data.setOrigRef(refReceived);
            return data;
        } catch (Exception e) {
            logger.info("EXCEPTION: CustomerService: sendBillPaymentReversalMsg exception=" + e);
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Transactional
    public int updateNotifyPreferences(CustomerAccount customerAccount) {
        return customerAccountMapper.updateByPrimaryKey(customerAccount);
    }
}
