package za.co.ipay.metermng.server.rpc;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.AuxAccountsRpc;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.AuxAccountData;

import com.google.gwt.user.server.rpc.RemoteServiceServlet;

public class AuxAccountsRpcImpl extends RemoteServiceServlet implements AuxAccountsRpc {

	private static final long serialVersionUID = -8703890532051061504L;
	
	private AuxAccountService auxAccountService;
	
	public void setAuxAccountService(AuxAccountService auxAccountService) {
		this.auxAccountService = auxAccountService;
	}

	@Override
	public AuxAccountData updateAuxAccount(AuxAccountData auxAccount) throws ValidationException, ServiceException {
	    ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxAccount);
	    return auxAccountService.updateAuxAccount(auxAccount);
	}
}
