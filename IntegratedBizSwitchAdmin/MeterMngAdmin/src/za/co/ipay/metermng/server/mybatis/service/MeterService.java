package za.co.ipay.metermng.server.mybatis.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.gwt.thirdparty.guava.common.base.Strings;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.ipayxml.io.IpayXmlMessageServiceConfig;
import za.co.ipay.ipayxml.metermng.CustVendRevReqMessage;
import za.co.ipay.ipayxml.metermng.CustVendRevResMessage;
import za.co.ipay.ipayxml.metermng.NotifyAggregatorsReqMessage;
import za.co.ipay.ipayxml.ststoken.VerifyReqMessage;
import za.co.ipay.ipayxml.ststoken.VerifyResMessage;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.MeterBalancingCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.MeterCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.MeterReadingCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.ReadingQualityCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.VendMapper;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.custom.model.MeterReadingExt;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterBalancingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.RegisterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterBalancing;
import za.co.ipay.metermng.mybatis.generated.model.MeterBalancingExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.MeterReading;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReading;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReadingExample;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.network.MessageService;
import za.co.ipay.metermng.server.mybatis.mapper.IMeterCountMapper;
import za.co.ipay.metermng.server.util.Tid;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.dashboard.MeterCountDto;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.meter.MeterSearchDto;
import za.co.ipay.metermng.shared.dto.meter.SuperSubMeterDto;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;
import za.co.ipay.utils.StringUtils;

public class MeterService {

    private MeterMapper meterMapper;
    private MeterCustomMapper meterCustomMapper;
    private MeterReadingMapper meterReadingMapper;
    private MeterReadingCustomMapper meterReadingCustomMapper;
    private MeterReadingTypeMapper meterReadingTypeMapper;
    private ReadingQualityCustomMapper readingQualityCustomMapper;
    private MeterBalancingMapper meterBalancingMapper;    
    private MeterBalancingCustomMapper meterBalancingCustomMapper;
    private MeterReadingGeneratorService meterReadingGeneratorService;
    private STSMeterService stsMeterService;
    private IMeterCountMapper meterCountMapper;
    private RegisterReadingMapper registerReadingMapper;
    
    private PricingStructureService pricingStructureService;
    private UsagePointMapper usagePointMapper;
    private VendMapper vendMapper;
    private MessageService messageService;
    private SupplyGroupService supplyGroupService;

    public static final String SINGLE_AGGREGATION_TYPE = "single";
    public static final String BATCH_AGGREGATION_TYPE = "batch";

    private String defaultTerm = "";
    private String defaultClient = "";
    
    private static Logger logger = Logger.getLogger(MeterService.class);

    @Transactional(readOnly=true)
    public Meter getMeterByNumber(String number) {
        if (number != null && !number.trim().equals("")) { 
            logger.info("Getting meter for number: "+number);
            MeterExample example = new MeterExample();
            example.createCriteria().andMeterNumEqualTo(number);
            List<Meter> meters = meterMapper.selectByExample(example);
            if (meters.size() == 1) {
                return meters.get(0);
            } else {
                return null;
            }
        } else {
            logger.info("getMeterByNumber returns null for: "+number);
            return null;
        }
    }

    @Transactional(readOnly=true)
    public Meter getMeterByMrid(String mrid) {
        if (mrid != null && !mrid.trim().equals("")) { 
            logger.info("Getting meter for mrid: "+mrid);
            MeterExample example = new MeterExample();
            example.createCriteria().andMridEqualTo(mrid);
            List<Meter> meters = meterMapper.selectByExample(example);
            if (meters.size() == 1) {
                return meters.get(0);
            } else {
                return null;
            }
        } else {
            logger.info("getMeterByMrid returns null for: "+mrid);
            return null;
        }
    }
   
    @Transactional(readOnly=true)
    public MeterDto getMeterDto(String number, Long userGroupId) {
        return meterCustomMapper.getMeterForMeterNumber(number, userGroupId);
    }
    
    @Transactional(readOnly=true)
    public boolean isCorrespondingMeterGroup(Long meterId, Long currentGroupId) {
        MeterDto meter = meterCustomMapper.getMeterForMeterId(meterId, currentGroupId);
        if (meter != null) {
            return true;
        } else {
            return false;
        }
    }
    
    @Transactional(readOnly=true)
    public MeterDto getMeterDto(String number) {
        return meterCustomMapper.getMeterDto(number);
    }
    
    @Transactional(readOnly=true)
    public Meter getMeterById(Long id) {
        if (id != null) {
            return meterMapper.selectByPrimaryKey(id);
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly=true)
    public List<Meter> getMetersByEndDeviceStoreId(Long endDeviceStoreId) {
        MeterExample ex = new MeterExample();
        ex.createCriteria().andEndDeviceStoreIdEqualTo(endDeviceStoreId);
        ex.setOrderByClause("meter_num asc");
        List<Meter> list = meterMapper.selectByExample(ex);
        return list;
    }
    
    @Transactional(readOnly=true)
    public List<MeterDto> getMeterSuggestions(Long currentGroupId, MeterSearchDto searchDto, int limit)
            throws ServiceException {
        String meterNumber = searchDto.getMeterNumber();
        logger.debug(
                "Getting meter suggestions: " + meterNumber + " limit:" + limit + " currentGroupId:" + currentGroupId);
        List<MeterDto> meters;
        if (Strings.isNullOrEmpty(meterNumber)) {
            meters = new ArrayList<MeterDto>();
        } else {
            String paymentMode = searchDto.getPaymentMode();
            String meterNumberSearch = meterNumber + MeterMngStatics.WILDCARD;
            RowBounds rowBounds = new RowBounds(0, limit);
            if (Strings.isNullOrEmpty(paymentMode)) {
                meters = meterCustomMapper.getMeterSuggestionsInGroup(meterNumberSearch, currentGroupId, rowBounds);
            } else {
                meters = meterCustomMapper.getMeterSuggestionsWithPaymentModeInGroup(meterNumberSearch, currentGroupId,
                        paymentMode, rowBounds);
            }
            meters.removeIf(meter -> meterNumber.equalsIgnoreCase(meter.getNumber()));
        }
        return meters;
    }
    
    @Transactional(readOnly=true)
    public List<MeterDto> getMetersInDeviceStoresSuggestions(MeterSearchDto searchDto, Long usersGroupId, int limit)
            throws ServiceException {
        String meterNumber = searchDto.getMeterNumber();
        logger.debug("Getting meters in device stores suggestions: " + meterNumber + " limit:" + limit);
        List<MeterDto> meters;
        if (Strings.isNullOrEmpty(meterNumber)) {
            meters = new ArrayList<MeterDto>();
        } else {
            Long deviceStoreId = searchDto.getDeviceStoreId();
            String meterNumberSearch = meterNumber + MeterMngStatics.WILDCARD;
            RowBounds rowBounds = new RowBounds(0, limit);
            if (deviceStoreId != null && deviceStoreId.compareTo(-1L) > 0) {
                logger.debug("getMetersInDeviceStoresSuggestions: For deviceStoreId=" + deviceStoreId);
                meters = meterCustomMapper.getMetersInDeviceStoresSuggestions(meterNumberSearch, deviceStoreId,
                        rowBounds);
            } else {
                logger.debug("getMetersInDeviceStoresSuggestions: For ALL deviceStores (id came in null)");
                meters = meterCustomMapper.getMetersAllDeviceStoresSuggestions(meterNumberSearch, usersGroupId,
                        rowBounds);
            }
            meters.removeIf(meter -> meterNumber.equalsIgnoreCase(meter.getNumber()));
        }
        return meters;
    }
    
    @Transactional(readOnly=true)
    public List<MeterDto> getMetersAssignedToUpSuggestions(MeterSearchDto meterSearchDto, Long usersGroupId, int limit) {
        logger.debug("Getting meters in device stores suggestions: "+meterSearchDto.getMeterNumber()+" limit:"+limit);
        if (!Strings.isNullOrEmpty(meterSearchDto.getMeterNumber())) {
            return meterCustomMapper.getAssignedMetersSuggestions(meterSearchDto.getMeterNumber()+MeterMngStatics.WILDCARD, usersGroupId, new RowBounds(0, limit));
        } else {
            return new ArrayList<MeterDto>();
        }
    }
    
    @Transactional
    public void saveMeter(MeterData meterData) throws ValidationException, ServiceException {
        boolean probablyIsNewMeter = meterData.getId() == null;
        if(probablyIsNewMeter){
            meterData.setAggregatorNotified(false);
        }
        saveAndReturnMeter(meterData);
        sendAggregationRequest(probablyIsNewMeter, SINGLE_AGGREGATION_TYPE);
    }

    @Transactional
    public void saveBulkUploadMeter(MeterData meterData) {
        saveAndReturnMeter(meterData);
    }

    @Transactional
    public MeterData saveAndReturnMeter(MeterData meterData) throws ValidationException, ServiceException {

        // Validate attributes
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(meterData);           
        // Check meterNumber is not duplicate
        Meter existing = getMeterByNumber(meterData.getMeterNum());
        if (existing != null && !existing.getId().equals(meterData.getId())) {
            throw new ValidationException(new ValidationMessage("meter.error.alreadyexists", true));
        }        
        if (meterData.getMrid()==null) {
            meterData.setMrid(MridUtil.getMrid());
        }
        
        // Insert or update
        if (meterData.getId() == null) {
            meterData.setAggregatorNotified(false);
            if (meterMapper.insert(meterData) != 1) {    
                throw new ServiceException("meter.error.save");
            }
           
        } else {
            if (meterMapper.updateByPrimaryKey(meterData) != 1) {
                throw new ServiceException("meter.error.save");
            }
        }
        if (meterData.getStsMeter() != null) {
      		meterData.getStsMeter().setId(meterData.getId());
            meterData.getStsMeter().setMrid(meterData.getMrid());
            stsMeterService.updateStsMeter(meterData.getStsMeter(), meterData.getGenKeychangeTokenChoiceNone());
        }
        
        return meterData;
    } 
    
    @Transactional(readOnly=true)
    public ArrayList<MeterReadingType> getMeterReadingTypes(Boolean active) throws ServiceException {
        MeterReadingTypeExample example = new MeterReadingTypeExample();
        if (active != null) {
            if (active) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("type_name");
        return new ArrayList<MeterReadingType>(meterReadingTypeMapper.selectByExample(example));
    }
    
    @Transactional(readOnly=true)
    public MeterReadingType getMeterReadingTypeByValue(String value) {
        MeterReadingTypeExample example = new MeterReadingTypeExample();
        MeterReadingTypeExample.Criteria criteria = example.createCriteria();
        criteria.andValueEqualTo(value);
        criteria.andRecordStatusNotEqualTo(RecordStatus.DEL);
        List<MeterReadingType> types = meterReadingTypeMapper.selectByExample(example);
        if (types.isEmpty()) {
            return null;
        } else {
            return types.get(0);
        }
    }
    
    @Transactional(readOnly=true)
    public List<MeterReadingType> getMeterReadingTypes(Boolean active, String typeValue) {
        MeterReadingTypeExample example = new MeterReadingTypeExample();
        MeterReadingTypeExample.Criteria criteria = example.createCriteria();
        criteria.andValueLike(typeValue);
        if (active != null) {
            if (active) {
                criteria.andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                criteria.andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            criteria.andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("type_name");
        return meterReadingTypeMapper.selectByExample(example);
    }
    
    @Transactional(readOnly=true)
    public MeterReadingType getMeterReadingType(Boolean active, String value) throws ServiceException {
        List<MeterReadingType> types = getMeterReadingTypes(active, value);
        if (types.isEmpty()) {
            return null;
        } else {
            return types.get(0);
        }
    }
    
    @Transactional(readOnly=true)
    public MeterReadingType getMeterReadingType(Long id) throws ServiceException {
        if (id != null) {
            return meterReadingTypeMapper.selectByPrimaryKey(id);
        } else {
            return null;
        }
    }
        
    @Transactional(readOnly=true)
    public List<MeterDto> getSuperMeters() throws ServiceException {
        return meterBalancingCustomMapper.getSuperMeters();
    }
    
    @Transactional(readOnly=true)
    public SuperSubMeterDto getSubMetersForSuperMeter(Long superMeterId) throws ServiceException { 
        ArrayList<MeterDto> subMeters = new ArrayList<MeterDto>(meterBalancingCustomMapper.getSubMeters(superMeterId));
        SuperSubMeterDto dto = new SuperSubMeterDto();
        dto.setSuperMeterId(superMeterId);
        dto.setSubMeters(subMeters);
        return dto;
    }
    
    @Transactional(readOnly=false)
    public Void saveSuperSubMeters(Long superMeterId, ArrayList<Long> subMeterIds) throws ValidationException, ServiceException {
        if (superMeterId == null) {
            throw new ValidationException(new ValidationMessage("energybalancing.error.super.id", true));
        }
        if (subMeterIds == null || subMeterIds.size() == 0) {
            throw new ValidationException(new ValidationMessage("energybalancing.error.sub.ids", true));
        }
        
        //Delete the existing instances
        MeterBalancingExample example = new MeterBalancingExample();
        example.createCriteria().andSuperMeterIdEqualTo(superMeterId);
        meterBalancingMapper.deleteByExample(example);
        
        //Add new instances
        MeterBalancing mb = null;
        for(Long subMeter : subMeterIds) {
            mb = new MeterBalancing();
            mb.setSuperMeterId(superMeterId);
            mb.setSubMeterId(subMeter);
            if (meterBalancingMapper.insert(mb) != 1) {
                throw new ServiceException("energybalancing.error.save", true);
            }
        }
        
        return null;
    }
    
    @Transactional(readOnly=false)
    public Void deleteSuperMeter(Long superMeterId) throws ValidationException, ServiceException {
        if (superMeterId == null) {
            throw new ValidationException(new ValidationMessage("energybalancing.error.super.id", true));
        }
        
        
        
        MeterBalancingExample example = new MeterBalancingExample();
        example.createCriteria().andSuperMeterIdEqualTo(superMeterId);
        List<MeterBalancing> existing = meterBalancingMapper.selectByExample(example);
        if (existing.isEmpty()) {
            throw new ValidationException(new ValidationMessage("energybalancing.error.delete.none", true));
        } else {
            meterBalancingMapper.deleteByExample(example);
        }
        
        return null;
    }
    
    @Transactional(readOnly=true)
    public MeterReadingsDto getMeterReadings(Long meterId, Long meterReadingTypeId, Date start, Date end) throws ServiceException {
        if (meterId != null && meterReadingTypeId != null && start != null && end != null && start.before(end)) {
            MeterReadingType meterReadingType = getMeterReadingType(meterReadingTypeId);
            List<MeterReadingExt> meterReadingsExt = meterReadingCustomMapper.getMeterReadingByIdAndReadingType(meterId, meterReadingTypeId, start, end);
            return processMeterReadings(meterReadingType, MeterReadingsDto.SINGLE_METER, getMeterNumberForId(meterId), meterReadingsExt);            
        }
        return new MeterReadingsDto();
    }

    private MeterReadingsDto processMeterReadings(MeterReadingType meterReadingType, String name, String meter, List<MeterReadingExt> meterReadings) {
        if (!meterReadings.isEmpty()) {
            ArrayList<MeterReadingDto> readings = new ArrayList<MeterReadingDto>();
            Date startDate = null;
            Date endDate = null;
            Double min = null;
            Double max = null;
            Double value = null;
            MeterReadingDto dto = null;
            for(MeterReadingExt meterReading : meterReadings) {
                if (startDate == null) {
                    startDate = meterReading.getReadingStart();
                }
                endDate = meterReading.getReadingEnd();
                value = round(meterReading.getReadingValue().doubleValue());                
                if (min == null || value.doubleValue() < min.doubleValue()) {
                    min = value;
                }
                if (max == null || max.doubleValue() < value.doubleValue()) {
                    max = value;
                }
                meterReading.setMeterNum(meter);
                dto = new MeterReadingDto(meterReading.getMeterNum(), meterReading.getDateCreated(), meterReading.getReadingStart(), meterReading.getReadingEnd(), value, meterReading.getReceiptNum());
                readings.add(dto);
            }            
            MeterReadingsDto meterReadingsDto = new MeterReadingsDto(meterReadingType, startDate, endDate, min, max, name, readings); 
            logger.info("Got meterReadings: "+meterReadingsDto);
            return meterReadingsDto;
        } else {
            logger.info("No meter readings to process");
            return new MeterReadingsDto();
        }
    }
    
    private double round(double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    
    @Transactional(readOnly=true)
    public String getMeterNumberForId(Long id) {
        if (id != null) {
            return meterCustomMapper.getMeterNumForMeterId(id);
        } else {
            return null;
        }
    }
    
    private MeterDto getSuperMeter(Long balancingMeterId) {
        return new MeterDto(balancingMeterId, getMeterNumberForId(balancingMeterId));
    }
    
    private List<MeterDto> getSubMeters(Long superMeterId) {
        MeterBalancingExample example = new MeterBalancingExample();
        example.createCriteria().andSuperMeterIdEqualTo(superMeterId);
        List<MeterBalancing> balancing = meterBalancingMapper.selectByExample(example);
        List<MeterDto> dtos = new ArrayList<MeterDto>(balancing.size());
        for(MeterBalancing b : balancing) {
            dtos.add(new MeterDto(b.getSubMeterId(), getMeterNumberForId(b.getSubMeterId())));
        }
        return dtos;
    }
    
    private List<Long> getSubMeterIds(Long superMeterId) {
        MeterBalancingExample example = new MeterBalancingExample();
        example.createCriteria().andSuperMeterIdEqualTo(superMeterId);
        List<MeterBalancing> balancing = meterBalancingMapper.selectByExample(example);
        List<Long> ids = new ArrayList<Long>(balancing.size());
        for(MeterBalancing b : balancing) {
            ids.add(b.getSubMeterId());
        }
        return ids;
    }
    
    @Transactional(readOnly=true)
    public MeterReadingsDto getMeterBalancingReadings(Long balancingMeterId, Long meterReadingTypeId, Date start, Date end)
        throws ServiceException {
        if (balancingMeterId != null && meterReadingTypeId != null && start != null && end != null && start.before(end)) {
            MeterDto superMeter = getSuperMeter(balancingMeterId);
            if (superMeter != null) {
                List<MeterDto> meters = getSubMeters(balancingMeterId);
                if (!meters.isEmpty()) {  
                    logger.info("Got sub meters: "+meters.size());
                    
                    //Get super meter's readings 
                    //TODO this could be null if virtual super meter
                    List<MeterReadingExt> meterReadings = meterReadingCustomMapper.getMeterReadingByIdAndReadingType(balancingMeterId, meterReadingTypeId, start, end);
                    MeterReadingType meterReadingType = getMeterReadingType(meterReadingTypeId);
                    String superMeterLabel = "Super Meter";
                    MeterReadingsDto dto = processMeterReadings(meterReadingType, MeterReadingsDto.SUPER_METER, superMeterLabel, meterReadings);
                    dto.setBalancingMeter(superMeter.getNumber());
                    
                    //Make a copy of the super meter's readings for the totals
                    ArrayList<MeterReadingDto> superReadings = dto.getReadings().get(MeterReadingsDto.SUPER_METER);
                    if (superReadings != null) {                        
                        String subMeterLabel = "Sub Meters";
                        ArrayList<MeterReadingDto> totals = new ArrayList<MeterReadingDto>(superReadings.size());
                        for(MeterReadingDto reading : superReadings) {
                            totals.add(new MeterReadingDto(subMeterLabel, reading.getCreated(), reading.getStart(), reading.getEnd(), 0.0, null));
                        }
                        dto.getReadings().put(MeterReadingsDto.METER_TOTALS, totals);
                                                
                        //Get individual sub-meters readings and total them as a comparison with the super-meter's
                        for(MeterDto meter : meters) {
                            meterReadings = meterReadingCustomMapper.getMeterReadingByIdAndReadingType(meter.getId(), meterReadingTypeId, start, end);
                            //By default, don't add the readings to the results as there could be lots of meters
                            calculateMeterReadings(dto, meter.getNumber(), subMeterLabel, meterReadings, false);
                        }
                        return dto;            
                    } else {
                        logger.info("No readings for super meter available");
                    }
                } else {
                    logger.info("No sub-meters found for balancingMeterId: "+balancingMeterId);
                }
            } else {
                logger.info("No super meter found for balancingMeterId: "+balancingMeterId);
            }
        } else {
            logger.info("Invalid input for meterBalancing");
        }
        return new MeterReadingsDto();
    }
    
    private void calculateMeterReadings(MeterReadingsDto dto, String meterNumber, String meterLabel, 
                                          List<MeterReadingExt> meterReadings, boolean addToResults) {
        if (!meterReadings.isEmpty()) {
            ArrayList<MeterReadingDto> totals = dto.getReadings().get(MeterReadingsDto.METER_TOTALS);
            MeterReadingDto totalsReading = null;
            ArrayList<MeterReadingDto> readings = new ArrayList<MeterReadingDto>();
            Double value = null;
            MeterReadingDto reading = null;
            MeterReadingExt meterReading = null;
            for(int i=0;i<meterReadings.size();i++) {
                meterReading = meterReadings.get(i); 
                value = round(meterReading.getReadingValue().doubleValue());
                //update the start, end, min and max if necessary
                if (dto.getStartDate() == null || meterReading.getReadingStart().before(dto.getStartDate())) {
                    dto.setStartDate(meterReading.getReadingStart());
                }
                if (dto.getEndDate() == null || meterReading.getReadingEnd().after(dto.getEndDate())) {
                    dto.setEndDate(reading.getEnd());
                }                
                if (value < dto.getMinReading()) {
                    dto.setMinReading(value);
                }
                if (dto.getMaxReading() < value.doubleValue()) {
                    dto.setMaxReading(value);
                }
                //add the reading
                reading = new MeterReadingDto(meterLabel, meterReading.getDateCreated(), meterReading.getReadingStart(), meterReading.getReadingEnd(), value, null);                
                readings.add(reading);
                //Increase the totals reading
                if (i < totals.size()) {
                    totalsReading = totals.get(i);
                    if (totalsReading.getStart().equals(meterReading.getReadingStart())
                            && totalsReading.getEnd().equals(meterReading.getReadingEnd())) {
                        totalsReading.setReading(round(totalsReading.getReading() + value));
                        totalsReading.setReceiptNum(meterReading.getReceiptNum());
                    } else {
                        logger.warn("Unmatching totals vs meter reading for index: "+i+" "+meterReading.getReadingStart()+" "+meterReading.getReadingEnd());
                    }
                } else {
                    logger.warn("No total for index: "+i);
                }
            }
            if (addToResults) {
                dto.getReadings().put(meterNumber, readings);
            } else {
                dto.getSubMeters().add(meterNumber);
            }
        }
    }
    
    @Transactional(readOnly=true)
    public ArrayList<EnergyBalancingDto> checkEnergyBalancingMeters(Long meterReadingTypeId, Date startDate, Date endDate, double percentVariation) 
        throws ServiceException {
        ArrayList<EnergyBalancingDto> energyBalancing = new ArrayList<EnergyBalancingDto>();        
        MeterReadingType type = getMeterReadingType(meterReadingTypeId);       
        if (type != null) {            
            List<MeterDto> superMeters = getSuperMeters();
            logger.info(superMeters.size()+" super meters");
            EnergyBalancingDto dto = null;
            for (MeterDto superMeter : superMeters) {
                dto = checkEnergyBalancingMeter(superMeter, meterReadingTypeId, startDate, endDate, percentVariation);
                if (dto != null) {
                    energyBalancing.add(dto);
                    logger.info("Added energyBalancing: " + dto);
                }
            } // end for
        } else {
            logger.error("Unknown meterReadingTypeId:" + meterReadingTypeId);
        }
        return energyBalancing;
    }
    
    @Transactional(readOnly=true)
    public EnergyBalancingDto checkEnergyBalancingMeter(MeterDto superMeter, Long meterReadingTypeId, Date startDate, Date endDate, double percentVariation) {
        logger.info("Checking superMeter's reading: "+superMeter.getNumber());
        // Super meter's values
        double superMeterTotal = 0.0;
        List<BigDecimal> values = meterReadingCustomMapper.getMeterReadingsValues(superMeter.getId(), meterReadingTypeId, startDate, endDate);
        for (BigDecimal value : values) {
            superMeterTotal += value.doubleValue();
        }
        logger.info("Calculated superMeter total: "+superMeterTotal);

        // Sub-meters values
        double subMetersTotal = 0.0;
        List<Long> subMeterIds = getSubMeterIds(superMeter.getId());
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("subMeterIds", subMeterIds);
        params.put("meterReadingTypeId", meterReadingTypeId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        List<BigDecimal> subMeterValues = meterReadingCustomMapper.getSubMetersReadingsValues(params);
        for (BigDecimal subMeterValue : subMeterValues) {
            subMetersTotal += subMeterValue.doubleValue();
        }
        logger.info("Calculated subMeters total: "+subMetersTotal);
        
        // Check percent
        double totalPercent = 0.0;
        if (superMeterTotal > 0.0) {
            totalPercent = Math.abs(((superMeterTotal - subMetersTotal) / Math.abs(superMeterTotal)) * 100);
            logger.info("EnergyBalancing: superMeterTotal: " + superMeterTotal + " subMetersTotal:" + subMetersTotal + " totalPercent:" + totalPercent);
        }
        
        if (subMetersTotal > 0 && totalPercent >= percentVariation) {
            EnergyBalancingDto dto = new EnergyBalancingDto();
            dto.setSuperMeterId(superMeter.getId());
            dto.setSuperMeterNumber(superMeter.getNumber());
            dto.setSuperMeterReading(BigDecimal.valueOf(superMeterTotal));
            dto.setTotalSubMeters(BigDecimal.valueOf(subMetersTotal));
            dto.setVariation(BigDecimal.valueOf(totalPercent));
            return dto;
        } else {
            logger.info("Skipping superMeter: " + superMeter.getNumber() + " calculated variance%: " + totalPercent+" superMeterTotal:"+superMeterTotal);
            return null;
        }
    }
 
    @Transactional(readOnly = false)
    public Long addMeterReadings(Long meterId, Date start, Date end, int intervalMinutes,
            ArrayList<Long> readingTypeIds, int deleteExistingReadings, Date zeroStart, Date zeroEnd, int zeroInstances,
            Date missingStart, Date missingEnd, int missingInstances, ArrayList<Long> mdcChannelIds)
            throws ValidationException, ServiceException {
        // Valid input?
        if (meterId == null || start == null || end == null || readingTypeIds == null || readingTypeIds.isEmpty()) {
            throw new ValidationException(new ValidationMessage("demo.addmeterreadings.invalid.input", true));
        }

        // Delete old readings?
        if (deleteExistingReadings != MeterMngStatics.DELETE_EXISTING_READINGS_APPEND) {
            Date startTemp = null;
            Date endTemp = null;
            if (deleteExistingReadings == MeterMngStatics.DELETE_EXISTING_READINGS_SELECTED) {
                startTemp = start;
                endTemp = end;
            }

            if (mdcChannelIds == null) {
                deleteMeterReadings(meterId, startTemp, endTemp, readingTypeIds);
            } else {
                deleteRegisterReadings(meterId, startTemp, endTemp, readingTypeIds);
            }
        }

        if (mdcChannelIds == null) {
            // delete any meter reading fact rows
            meterReadingGeneratorService.deleteMeterReadingFactsForMeter(meterId);
            logger.debug("Deleted meter reading Facts for meterId: " + meterId);
        }

        // Get the meter reading types
        Map<Long, String> readingTypeValues = new HashMap<Long, String>();
        for (Long readingTypeId : readingTypeIds) {
            readingTypeValues.put(readingTypeId, meterReadingTypeMapper.selectByPrimaryKey(readingTypeId).getValue());
        }

        // Get the meter's usage point
        Long usagePointId = meterCustomMapper.getUsagePointIdForMeterId(meterId);

        // Generate per reading interval from start to end and save meter readings per
        // meter reading type, as well as meter Reading Fact rows per day
        meterReadingGeneratorService.generateMeterReadings(meterId, usagePointId, start, end, intervalMinutes,
                readingTypeIds, readingTypeValues, zeroStart, zeroEnd, zeroInstances, missingStart, missingEnd,
                missingInstances, mdcChannelIds);
        logger.debug("Completed adding readings for meterId: " + meterId);

        return usagePointId;
    }
    
    @Transactional(readOnly=false)
    public IpayResponseData doTariffCalc(Long usagePointId) {
        UsagePoint up = new UsagePoint();
        up = usagePointMapper.selectByPrimaryKey(usagePointId);
        try {
            return pricingStructureService.sendTariffCalculation(up.getMrid());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Transactional(readOnly = false)
    public void deleteMeterReadings(Long meterId, Date start, Date end, ArrayList<Long> readingTypeIds) {
        if (meterId != null) {
            // Delete any reading_quality rows
            if (start != null && end != null) {
                String meterReadingTypeIds = "";
                for (Long readingTypeId : readingTypeIds) {
                    meterReadingTypeIds += ", " + readingTypeId;
                }
                readingQualityCustomMapper.deleteReadingQualitiesForMeterAndDateRange(meterId, start, end,
                        meterReadingTypeIds.substring(2));
            } else {
                readingQualityCustomMapper.deleteReadingQualitiesForMeter(meterId);
            }
            // Delete the meter readings
            MeterReadingExample example = new MeterReadingExample();
            MeterReadingExample.Criteria criteria = example.createCriteria().andMeterIdEqualTo(meterId);
            if (start != null && end != null) {
                criteria.andReadingStartGreaterThanOrEqualTo(start).andReadingStartLessThan(end)
                        .andMeterReadingTypeIdIn(readingTypeIds);
            }
            meterReadingMapper.deleteByExample(example);
            logger.debug("Deleted meter readings for meterId: " + meterId);
        }
    }

    @Transactional(readOnly = false)
    public void deleteRegisterReadings(Long meterId, Date start, Date end, ArrayList<Long> readingTypeIds) {
        RegisterReadingExample example = new RegisterReadingExample();
        za.co.ipay.metermng.mybatis.generated.model.RegisterReadingExample.Criteria criteria = example.createCriteria()
                .andMeterIdEqualTo(meterId);
        if (start != null && end != null) {
            criteria.andReadingTimestampGreaterThanOrEqualTo(start).andReadingTimestampLessThan(end)
                    .andMeterReadingTypeIdIn(readingTypeIds);
        }
        registerReadingMapper.deleteByExample(example);
        logger.debug("Deleted register readings for meterId: " + meterId);
    }
    
    @Transactional(readOnly=false)
    public void addSuperMeterReadings(Long superMeterId, 
                                       Date start, Date end, int intervalMinutes,
                                       Long readingTypeId, 
                                       boolean deleteExistingSuperMeterReadings,                                       
                                       boolean regenerateSubMeterReadings,
                                       List<MeterReadingVariation> variations) 
        throws ValidationException, ServiceException {
        //Valid input?
        if (superMeterId == null || start == null || end == null || readingTypeId == null) {
            throw new ValidationException(new ValidationMessage("demo.addsupermeterreadings.invalid.input", true));
        }
        
        List<MeterDto> subMeters = getSubMeters(superMeterId);
        if (subMeters == null || subMeters.isEmpty()) {
            throw new ValidationException(new ValidationMessage("demo.addsupermeterreadings.invalid.input.submeters", true));
        }
        List<Long> subMeterIds = new ArrayList<Long>(subMeters.size());
        for(MeterDto subMeter : subMeters) {
            subMeterIds.add(subMeter.getId());
        }
        
        //Get the meter reading type
        MeterReadingType meterReadingType = meterReadingTypeMapper.selectByPrimaryKey(readingTypeId);

        //Delete old super meter readings?
        if (deleteExistingSuperMeterReadings) {
            deleteMeterReadings(superMeterId, null, null, null);
            
            //delete any meter reading fact rows
            meterReadingGeneratorService.deleteMeterReadingFactsForMeter(superMeterId);
            logger.debug("Deleted meter reading Facts for meterId: "+superMeterId);
        } else {
            //Check if there are any existing meter readings between the specified start and end which will mess up
            //if new data readings are inserted in the same range (graph wont display properly, etc)
            MeterReadingsDto dto = getMeterReadings(superMeterId, readingTypeId, start, end);
            if (dto.getReadings().size() > 0) {
                throw new ValidationException(new ValidationMessage("demo.addsupermeterreadings.error.super.duplicates", true));
            }
        }
        
        //Delete/generate sub meters readings?
        if (regenerateSubMeterReadings) {
            List<Long> readingTypeIds = new ArrayList<Long>();
            readingTypeIds.add(meterReadingType.getId());
            Map<Long, String> readingTypeValues = new HashMap<Long, String>();
            readingTypeValues.put(meterReadingType.getId(), meterReadingType.getValue());            
            for(Long subMeterId : subMeterIds) {
                deleteMeterReadings(subMeterId, null, null, null);
                
                //delete any meter reading fact rows
                meterReadingGeneratorService.deleteMeterReadingFactsForMeter(subMeterId);
                logger.debug("Deleted meter reading Facts for meterId: "+subMeterId);
                
                Long subUsagePointId = meterCustomMapper.getUsagePointIdForMeterId(subMeterId);
                meterReadingGeneratorService.generateMeterReadings(subMeterId, subUsagePointId, start, end, intervalMinutes, readingTypeIds, readingTypeValues, null, null, -1, null, null, -1,null);
            }
        }
        
        //Get the super meter's usage point
        Long superMeterUsagePointId = meterCustomMapper.getUsagePointIdForMeterId(superMeterId);
        
        //Generate ther super meter's readings using the sub meters readings to calculate the reading values
        meterReadingGeneratorService.generateSuperMeterReadings(superMeterId, superMeterUsagePointId, start, end, intervalMinutes, meterReadingType, subMeterIds, variations);
        
        logger.debug("Completed adding super meter meterReadings for meterId: "+superMeterId+" subMeters: "+subMeterIds.toString());
    }

    public void sendAggregationRequest(boolean withNewMeters, String aggregationType) {
        new Thread(() -> {
            try {
                IpayXmlMessageServiceConfig config = messageService.getCurrentServer();
                if (config != null) {
                    if (!StringUtils.isNullOrEmpty(config.getDefaultClient())) {
                        defaultClient = config.getDefaultClient();
                    }
                    if (!StringUtils.isNullOrEmpty(config.getDefaultTerm())) {
                        defaultTerm = config.getDefaultTerm();
                    }
                }
                if (withNewMeters) {
                    long seqNum = RandomUtils.nextLong();
                    String dateTime = Calendar.getInstance().getTime().toString();
                    NotifyAggregatorsReqMessage reqMessage =
                            new NotifyAggregatorsReqMessage.Builder().setClient(defaultClient).setTerm(defaultTerm)
                                    .setTime(dateTime).setSeqNum(Long.toString(seqNum)).build(aggregationType);
                    messageService.sendMessage(reqMessage);
                    // What do we do with this response message?
                }
            } catch (Exception e) {
                logger.error("Error during sendAggregationRequest", e);
            }
        }).start();
    }
    
    public Long selectLastCustAgrTransId(Long customerAgreementId) {
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        
        List<Long> lastCustAgrTransIds = vendMapper.selectLastCustAgrTransId(customerAgreementId, rowBounds);
        if (lastCustAgrTransIds.isEmpty() || lastCustAgrTransIds == null) {
            return null;
        } else {
            return lastCustAgrTransIds.get(0);
        }
    }
    
    @Transactional
    public IpayResponseData doVendReversal(CustomerTrans customerTrans, boolean allowOlderReversals, String userName,
            SpecialActionReasonsLog specialActionReasonsLog, String comment) throws ServiceException {
    	try {
            String reason = null;
            if (specialActionReasonsLog != null) {
                reason = specialActionReasonsLog.getReasonText();
            }
            CustVendRevReqMessage.Builder custVendRevReqBuilder = new CustVendRevReqMessage.Builder(
                    customerTrans.getClient(), customerTrans.getTerminal())
                            .setOrigRef(customerTrans.getVendRefReceived()).setUserName(userName)
                            .setAllowOlderReversals(allowOlderReversals).setReason(reason).setComment(comment);
            CustVendRevReqMessage req = custVendRevReqBuilder.build();
            CustVendRevResMessage res = (CustVendRevResMessage) messageService.sendMessage(req);

			if (res == null) {   //connection error
				logger.debug("MeterService: doVendReversal(vendRefReceived=" + customerTrans.getVendRefReceived() + " vendRes = null - connectivity error");
	            return null;
			}    
			logger.info("MeterService: doVendReversal(vendRefReceived=" + customerTrans.getVendRefReceived() + "  allowOlderReversals=" + allowOlderReversals + " response=" + res.toString());
			
			IpayResponseData data = new IpayResponseData();
	        data.setResRef(res.getRef());
	        data.setResCode(res.getResCode());
	        data.setResMsg(res.getRes());
	        data.setOrigRef(customerTrans.getVendRefReceived());
	        data.setExtraData(res.getOtherReversalsThisMonth());
	        return data;
			
		} catch (Exception e) {
			logger.info("EXCEPTION: MeterService: doVendReversal(vendRefReceived=" + customerTrans.getVendRefReceived() + "  allowOlderReversals=" + allowOlderReversals + ") exception=" + e); 
			throw new ServiceException(e.getMessage());
		}
    }
    
    @Transactional(readOnly=true)
    public ArrayList<MeterCountDto> getMeterCountByModel(Long genGroupId) {
        return meterCountMapper.getMeterCountByMeterModelForGenGroup(genGroupId);
    }

    @Transactional(readOnly = true)
    public MeterReadingDto getReadingsDateRangeForMeter(Long meterId, ArrayList<Long> readingTypeIds,
            boolean intervalReadings) {
        MeterReadingDto meterReadingDto = new MeterReadingDto();
        if (intervalReadings) {
            MeterReadingExample example = new MeterReadingExample();
            za.co.ipay.metermng.mybatis.generated.model.MeterReadingExample.Criteria criteria = example.createCriteria()
                    .andMeterIdEqualTo(meterId);
            if (readingTypeIds != null && !readingTypeIds.isEmpty()) {
                criteria.andMeterReadingTypeIdIn(readingTypeIds);
            }
            example.setOrderByClause("reading_start");
            List<MeterReading> readings = meterReadingMapper.selectByExample(example);
            if (readings != null && !readings.isEmpty()) {
                meterReadingDto.setStart(readings.get(0).getReadingStart());
                meterReadingDto.setEnd(readings.get(readings.size() - 1).getReadingStart());
            }
        } else {
            RegisterReadingExample example = new RegisterReadingExample();
            za.co.ipay.metermng.mybatis.generated.model.RegisterReadingExample.Criteria criteria = example
                    .createCriteria().andMeterIdEqualTo(meterId);
            if (readingTypeIds != null && !readingTypeIds.isEmpty()) {
                criteria.andMeterReadingTypeIdIn(readingTypeIds);
            }
            example.setOrderByClause("reading_timestamp");
            List<RegisterReading> readings = registerReadingMapper.selectByExample(example);
            if (readings != null && !readings.isEmpty()) {
                meterReadingDto.setStart(readings.get(0).getReadingTimestamp());
                meterReadingDto.setEnd(readings.get(readings.size() - 1).getReadingTimestamp());
            }
        }
        return meterReadingDto;
    }
    
    public boolean getMridExistence(String mrid, Long meterId) {
        MeterExample example = new MeterExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(meterId != null) {
            criteria.andIdNotEqualTo(meterId);
        }
        return !meterMapper.selectByExample(example).isEmpty();
    }
    
    @Transactional
    public void updateMeterPowerLimit(Meter meterDb, BigDecimal powerLimit, String powerLimitLabel) {
        Meter meter = new Meter();
        meter.setId(meterDb.getId());
        meter.setPowerLimit(powerLimit);
        meter.setPowerLimitLabel(powerLimitLabel);
        meter.setMridExternal(meterDb.isMridExternal());
        meter.setAggregatorNotified(meterDb.isAggregatorNotified());
        meterMapper.updateByPrimaryKeySelective(meter);
    }

    @Transactional
    public int clearAccessGroupForMeter(Long meterId) {
        int updated = 0;
        Meter meter = meterMapper.selectByPrimaryKey(meterId);
        meter.setAccessGroupId(null);
        updated = meterMapper.updateByPrimaryKey(meter);
        if (updated != 1) {
            throw new ServiceException("meter.error.save");
        }
        return updated;
    }

    @Transactional
    public int updateAccessGroupForMeter(Long meterId, Long accessGroupId) {
        int updated = 0;
        Meter meter = meterMapper.selectByPrimaryKey(meterId);
        meter.setAccessGroupId(accessGroupId);
        updated = meterMapper.updateByPrimaryKey(meter);
        if (updated != 1) {
            throw new ServiceException("meter.error.save");
        }
        return updated;
    }

    @Transactional(readOnly=true)
    public boolean isMeterAttachedToMeterModel(Long meterModelId) {
        MeterExample example = new MeterExample();
        example.createCriteria().andMeterModelIdEqualTo(meterModelId);
        return meterMapper.selectByExample(example).size() > 0;
    }
    
    public VerifyTokenDto verifyToken(String token, String meterNumber, StsMeterData stsMeterData) {
        VerifyTokenDto verifyTokenDto = null;
        try {
            logger.info("MeterService: sendVerifyTokenRequestMessage(meter=" + meterNumber);
            IpayXmlMessageServiceConfig config = messageService.getCurrentServer();
            if (config != null) {
                if (!StringUtils.isNullOrEmpty(config.getDefaultClient())) {
                    defaultClient = config.getDefaultClient();
                }
                if (!StringUtils.isNullOrEmpty(config.getDefaultTerm())) {
                    defaultTerm = config.getDefaultTerm();
                }
            }

            if (stsMeterData.getAlgorithmCode()==null) {
                StsMeter stsMeter = stsMeterService.getMeterByMeterNumber(meterNumber);
                if (stsMeter != null) {
                    stsMeterData.setAlgorithmCode(stsMeter.getStsAlgorithmCode());
                } else {
                    throw new ServiceException("Meter not found");
                }
            }

            VerifyReqMessage verifyReqMessage = new VerifyReqMessage(defaultClient, defaultTerm, meterNumber,
                    stsMeterData.getSupplierGroupRef(), stsMeterData.getTariffIndex(),
                    String.valueOf(stsMeterData.getKeyRevisionNum()), token, stsMeterData.getAlgorithmCode());
            VerifyResMessage res = (VerifyResMessage) messageService.sendMessage(verifyReqMessage);
            if (res == null) {   //connection error
                logger.debug("MeterService: sendVerifyReq(res = null - connectivity error");
            } else if (!res.isSuccessful()) {
                verifyTokenDto = new VerifyTokenDto(res.getRes());
            } else {
                TokenClass tokenClass = TokenClass.getTokenClass(Integer.valueOf(res.getTokenClass()));
                SubClass subClass = null;
                if (tokenClass == TokenClass.CREDIT_TRANSFER) {
                    subClass = CreditTransferSubClass.getSubClass(Integer.valueOf(res.getTokenSubClass()));
                } else if (tokenClass == TokenClass.MANAGEMENT_METER_SPECIFIC) {
                    subClass = MeterSpecificManagementSubClass.getSubClass(Integer.valueOf(res.getTokenSubClass()));
                }
                if (subClass != null) {
                    verifyTokenDto = new VerifyTokenDto(Integer.valueOf(res.getTokenId()), res.getUnits(), tokenClass.value(), subClass.value());
                    verifyTokenDto.setTokenClassName(tokenClass.toString());
                    verifyTokenDto.setSubClassName(subClass.toString());
                    if (verifyTokenDto.verifyTokenFailed()) { //probably failed
                        verifyTokenDto = new VerifyTokenDto(Messages.MessagesUtil.getInstance().getMessage("verification.error.general"));
                    } else {
                        Short baseDate = supplyGroupService.getSupplyGroupBySgcAndKrn(stsMeterData.getSupplierGroupRef(), stsMeterData.getKeyRevisionNum()).getBaseDate();
                        Tid.BaseDate supplyGroupBaseDate = baseDate==null?Tid.BaseDate.BDT_1993:Tid.BaseDate.fromYear(baseDate);
                        Tid tid = new Tid(verifyTokenDto.getTokenId(), supplyGroupBaseDate);
                        Date tidDate = Date.from(tid.toInstant());
                        verifyTokenDto.setDateGenerated(tidDate);
                    }

                }  else {
                    verifyTokenDto = new VerifyTokenDto(Messages.MessagesUtil.getInstance().getMessage("verification.error.general"));
                }
            }

        } catch (Exception e) {
            logger.info("EXCEPTION: MeterService: sendVerifyTokenRequestMessage(meter=" + meterNumber+") exception=" + e);
            throw new ServiceException(e.getMessage());
        }
        return verifyTokenDto;
    }

    public enum TokenClass {

        CREDIT_TRANSFER(0),
        MANAGEMENT_NON_METER_SPECIFIC(1),
        MANAGEMENT_METER_SPECIFIC(2);

        private int value;

        static Map<Integer, TokenClass> map = new HashMap<>();
        static {
            for (TokenClass t: values()) {
                map.put(t.value, t);
            }
        }

        TokenClass(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }

        public static TokenClass getTokenClass(int value) throws IllegalArgumentException {
            TokenClass tokenClass = map.get(value);
            if (tokenClass == null)
                throw new IllegalArgumentException(String.format("Unable to map value '%d' to TokenClass", value));
            return tokenClass;
        }
    }

    public interface SubClass {
        public int value();
    }

    public enum CreditTransferSubClass implements SubClass {

        ELECTRICITY(0),
        WATER(1),
        GAS(2),
        TIME(3);

        int value;

        static Map<Integer, CreditTransferSubClass> map = new HashMap<>();
        static {
            for (CreditTransferSubClass s: values()) {
                map.put(s.value, s);
            }
        }

        CreditTransferSubClass(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }

        public static boolean isValidValue(int value) {
            return map.containsKey(value);
        }

        public static CreditTransferSubClass getSubClass(int value) throws IllegalArgumentException {
            CreditTransferSubClass subClass = map.get(value);
            if (subClass == null)
                throw new IllegalArgumentException(String.format("Unable to map value '%d' to SubClass", value));
            return subClass;
        }
    }

    public enum MeterSpecificManagementSubClass implements SubClass {

        SET_MAX_POWER_LIMIT(0),
        CLEAR_CREDIT(1),
        SET_TARIFF_RATE(2),
        SET_1ST_SECTION_DECODER_KEY(3),
        SET_2ND_SECTION_DECODER_KEY(4),
        SET_3RD_SECTION_DECODER_KEY(8),
        SET_4TH_SECTION_DECODER_KEY(9),
        CLEAR_TAMPER_CONDITION(5),
        SET_MAX_PHASE_POWER_UNBALANCE_LIMIT(6),
        SET_WATER_METER_FACTOR(7),
        RESERVED_STS(10),
        RESERVED_PROPRIETARY_11(11),
        RESERVED_PROPRIETARY_12(12),
        RESERVED_PROPRIETARY_13(13),
        RESERVED_PROPRIETARY_14(14),
        RESERVED_PROPRIETARY_15(15);

        private int value;

        static Map<Integer, SubClass> map = new HashMap<>();
        static {
            for (MeterSpecificManagementSubClass s: values()) {
                map.put(s.value, s);
            }
        }

        MeterSpecificManagementSubClass(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }

        public static boolean isValidValue(int value) {
            return map.containsKey(value);
        }

        public static SubClass getSubClass(int value) throws IllegalArgumentException {
            SubClass subClass = map.get(value);
            if (subClass == null)
                throw new IllegalArgumentException(String.format("Unable to map value '%d' to SubClass", value));
            return subClass;
        }
    }



    public void setMeterMapper(MeterMapper meterMapper) {
        this.meterMapper = meterMapper;
    }

    public void setMeterCustomMapper(MeterCustomMapper meterCustomMapper) {
        this.meterCustomMapper = meterCustomMapper;
    }

    public void setMeterReadingMapper(MeterReadingMapper meterReadingMapper) {
        this.meterReadingMapper = meterReadingMapper;
    }

    public void setMeterReadingCustomMapper(MeterReadingCustomMapper meterReadingCustomMapper) {
        this.meterReadingCustomMapper = meterReadingCustomMapper;
    }

    public void setMeterReadingTypeMapper(MeterReadingTypeMapper meterReadingTypeMapper) {
        this.meterReadingTypeMapper = meterReadingTypeMapper;
    }

    public void setReadingQualityCustomMapper(ReadingQualityCustomMapper readingQualityCustomMapper) {
        this.readingQualityCustomMapper = readingQualityCustomMapper;
    }

    public void setMeterReadingGeneratorService(MeterReadingGeneratorService meterReadingGeneratorService) {
        this.meterReadingGeneratorService = meterReadingGeneratorService;
    }

    public void setMeterBalancingMapper(MeterBalancingMapper meterBalancingMapper) {
        this.meterBalancingMapper = meterBalancingMapper;
    }

    public void setMeterBalancingCustomMapper(MeterBalancingCustomMapper meterBalancingCustomMapper) {
        this.meterBalancingCustomMapper = meterBalancingCustomMapper;
    }
    
    public void setStsMeterService(STSMeterService stsMeterService) {
        this.stsMeterService = stsMeterService;
    }

    public void setMeterCountMapper(IMeterCountMapper meterCountMapper) {
        this.meterCountMapper = meterCountMapper;
    }
    
    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    public void setSupplyGroupService(SupplyGroupService supplyGroupService) {
        this.supplyGroupService = supplyGroupService;
    }

    public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
        this.usagePointMapper = usagePointMapper;
    }

    public void setMessageService(MessageService messageService) {
        this.messageService = messageService;
    }
    public void setVendMapper(VendMapper vendMapper) {
    	this.vendMapper = vendMapper;
    }
    
    public void setRegisterReadingMapper(RegisterReadingMapper registerReadingMapper) {
        this.registerReadingMapper = registerReadingMapper;
    }
}
