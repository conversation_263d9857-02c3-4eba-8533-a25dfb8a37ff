package za.co.ipay.metermng.server.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoField;

public class Tid {

    public static final int MIN_VALUE = 0;
    public static final int MAX_VALUE = 0xFFFFFF;  // 24 bits

    protected static final ZoneId UTC = ZoneId.of("UTC");
    protected static final LocalTime ONE_MINUTE_PAST_MIDNIGHT = LocalTime.MIDNIGHT.plusMinutes(1);

    protected static final Instant BASE_DATE_1993, BASE_DATE_2014, BASE_DATE_2035;
    static {
        ZonedDateTime zdt1993 = ZonedDateTime.of(LocalDateTime.of(1993, Month.JANUARY, 1, 00, 00), UTC);
        BASE_DATE_1993 = zdt1993.toInstant();
        BASE_DATE_2014 = zdt1993.withYear(2014).toInstant();
        BASE_DATE_2035 = zdt1993.withYear(2035).toInstant();
    }

    protected static final int offset1993, offset2014, offset2035;  // Base date number of minutes since 1 January 1970.
    static {
        offset1993 = (int)(BASE_DATE_1993.getEpochSecond() / 60);
        offset2014 = (int)(BASE_DATE_2014.getEpochSecond() / 60);
        offset2035 = (int)(BASE_DATE_2035.getEpochSecond() / 60);
    }

    public static enum BaseDate {

        BDT_1993(BASE_DATE_1993, offset1993),
        BDT_2014(BASE_DATE_2014, offset2014),
        BDT_2035(BASE_DATE_2035, offset2035);

        Instant instant;
        int minutesSinceEpoch;

        BaseDate(Instant instant, int minutesSinceEpoch) {
            this.instant = instant;
            this.minutesSinceEpoch = minutesSinceEpoch;
        }

        public Instant getInstant() {
            return instant;
        }

        public static BaseDate fromInstant(Instant instant) {
            if (instant.equals(BASE_DATE_1993))
                return BDT_1993;
            if (instant.equals(BASE_DATE_2014))
                return BDT_2014;
            if (instant.equals(BASE_DATE_2035))
                return BDT_2035;
            throw new IllegalArgumentException(instant + " is not a valid base date");
        }

        public static BaseDate fromYear(int year) {
            switch (year) {
                case 1993: return BDT_1993;
                case 2014: return BDT_2014;
                case 2035: return BDT_2035;
                default:
                    throw new IllegalArgumentException(year + " is not a valid base date");
            }
        }

        public int toYear() {
            switch(this) {
                case BDT_1993: return 1993;
                case BDT_2014: return 2014;
                case BDT_2035: return 2035;
                default:
                    throw new UnsupportedOperationException(String.format("Unable to convert %s to year", this));
            }
        }
    }

    protected int value;
    protected BaseDate baseDate;  // Null if unknown

    public Tid(int value) {
        this(value, null);
    }

    public Tid(int value, BaseDate baseDate) {
        if ((value < MIN_VALUE) || (value > MAX_VALUE))
            throw new IllegalArgumentException(String.format("Token Identifier (TID) value must be in range %s to %s", MIN_VALUE, MAX_VALUE));
        this.value = value;
        this.baseDate = baseDate;
    }

    public Tid(Instant time, BaseDate baseDate) {
        value = (int) ((time.getEpochSecond() / 60) - baseDate.minutesSinceEpoch);

        if (value < 0)
            throw new IllegalArgumentException(String.format("Date %s is before base date %s", time, baseDate.instant));

        // Increment TID if it is reserved special value (1 minute past midnight).
        if (isSpecialTid(time))
            value++;

        this.baseDate = baseDate;
    }

    public Tid(BaseDate baseDate) {
        this(Instant.now(), baseDate);
    }

    public int getValue() {
        return value;
    }

    public BaseDate getBaseDate() {
        return baseDate;
    }

    public void setBaseDate(BaseDate baseDate) {
        this.baseDate = baseDate;
    }

    public byte[] toBytes() {
        byte[] tidBytes = new byte[3];
        toBytes(tidBytes, 0);
        return tidBytes;
    }

    public void toBytes(byte[] targetBytes, int beginIndex) {
        if (targetBytes.length - beginIndex < 3)
            throw new IllegalArgumentException("TID requires 3 bytes");

        targetBytes[beginIndex] = (byte)(value >> 16);
        targetBytes[beginIndex + 1] = (byte)(value >> 8);
        targetBytes[beginIndex + 2] = (byte)value;
    }

    public static Tid fromBytes(byte[] tidBytes) {
        return fromBytes(tidBytes, null);
    }

    public static Tid fromBytes(byte[] tidBytes, BaseDate baseDate) {
        if (tidBytes.length != 3)
            throw new IllegalArgumentException("TID requires 3 bytes");
        return fromBytes(tidBytes, baseDate, 0);
    }

    public static Tid fromBytes(byte[] tidBytes, int beginIndex) {
        return fromBytes(tidBytes, null, beginIndex);
    }

    public static Tid fromBytes(byte[] tidBytes, BaseDate baseDate, int beginIndex) {
        if (tidBytes.length - beginIndex < 3)
            throw new IllegalArgumentException("TID requires 3 bytes");
        return new Tid(((tidBytes[beginIndex] & 0xFF) << 16) | ((tidBytes[beginIndex + 1] & 0xFF) << 8) | (tidBytes[beginIndex + 2] & 0xFF), baseDate);
    }

    public Instant toInstant() {
        if (baseDate == null)
            throw new UnsupportedOperationException("Cannot convert to Instant when BaseDate is null");
        return Instant.ofEpochSecond((value + baseDate.minutesSinceEpoch) * 60L);
    }

    protected static boolean isSpecialTid(Instant time) {
        ZonedDateTime zdt = ZonedDateTime.ofInstant(time, UTC);
        return ((zdt.get(ChronoField.HOUR_OF_DAY) == 0) && (zdt.get(ChronoField.MINUTE_OF_HOUR) == 1));
    }

    public static Tid generateSpecialTid(LocalDate date, BaseDate baseDate) {
        ZonedDateTime zdt = ZonedDateTime.of(date, ONE_MINUTE_PAST_MIDNIGHT, UTC);

        int tid = (int) ((zdt.toEpochSecond() / 60) - baseDate.minutesSinceEpoch);
        if (tid < 0)
            throw new IllegalArgumentException(String.format("Date %s is before base date %s", date, baseDate));

        return new Tid(tid, baseDate);
    }

    public Tid nextTid() {
        Tid tid = new Tid(value + 1, baseDate);
        if (isSpecialTid(tid.toInstant()))
            tid.value++;
        return tid;
    }

    @Override
    public String toString() {
        if (baseDate == null)
            return Integer.toString(value);
        return String.format("%s (%s)", Integer.toString(value), baseDate);
    }

    public static void main(String[] args) {

        Tid tid = new Tid(0xD7BF98, BaseDate.BDT_1993);
        System.out.println(String.format("%s (%06X)", tid, tid.value));
        System.out.println(tid.toInstant());
        System.out.println(tid.value);

        tid = new Tid(0xD7BF99, BaseDate.BDT_1993);
        System.out.println(String.format("%s (%06X)", tid, tid.value));
        System.out.println(tid.toInstant());
        System.out.println(tid.value);

    }

}

