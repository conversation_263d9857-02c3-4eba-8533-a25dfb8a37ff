package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.rpc.ImportFileDataRpc;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileType;
import za.co.ipay.metermng.server.mybatis.service.ImportFileDataService;
import za.co.ipay.metermng.shared.ImportFileTypePermissionsE;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileActionParamsDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileResultDto;
import za.co.ipay.metermng.shared.dto.importfile.KeyChangeDto;

public class ImportFileDataRpcImpl extends BaseMeterMngRpc implements ImportFileDataRpc {

    private static final long serialVersionUID = 1L;

    private ImportFileDataService importFileService;

    public ImportFileDataRpcImpl() {
        this.logger = Logger.getLogger(ImportFileDataRpcImpl.class);
    }

    public void setImportFileDataService(ImportFileDataService importFileService) {
        this.importFileService = importFileService;
    }

    @Override
    public List<ImportFileType> getActiveImportFileTypes(boolean enableAccessGroups) throws ServiceException {
        List<ImportFileType> importFileTypes = importFileService.getActiveImportFileTypes();
        ArrayList<String> permissions = getUser().getPermissions();
        importFileTypes.removeIf(importFileType -> {
            String typeClass = importFileType.getTypeClass();
            if (typeClass.equals(ImportFileTypePermissionsE.MM_BULK_BLOCKING.getTypeClass())) {
                return !permissions.contains(ImportFileTypePermissionsE.MM_BULK_BLOCKING.name().toLowerCase())
                        && !permissions.contains(ImportFileTypePermissionsE.MM_BULK_UNBLOCKING.name().toLowerCase());
            }

            ImportFileTypePermissionsE perm = ImportFileTypePermissionsE.fromTypeClass(typeClass);
            if (perm == null) {
                return true;
            } else {
                return !permissions.contains(perm.name().toLowerCase());
            }
        });
        return importFileTypes;
    }
    
    @Override
    public ImportFileType getImportFileTypeById(Long importFileTypeId) {
        return importFileService.getImportFileTypeById(importFileTypeId);
    }
    
    @Override
    public ImportFileDto getImportFileDtoById(Long importFileId) {
        return importFileService.getImportFileDtoById(importFileId);
    }
    
    @Override
    public ImportFileActionParamsDto getImportFileParamData(String userName, String typeClass, String importFileName) {
        return importFileService.getImportFileParamData(userName, typeClass, importFileName);
    }

    @Override
    public ImportFileListDto selectImportFiles(int start, int pageSize, String sortColumn, String filterColumn, String filterString, Date filterDate, String order, boolean enableAccessGroups) {
        List<ImportFileType> importFileTypes = getActiveImportFileTypes(enableAccessGroups);
        String[] importfileTypeIds = new String[importFileTypes.size()];
        int index = 0;
        for (ImportFileType importFileType : importFileTypes) {
            importfileTypeIds[index++] = Long.toString(importFileType.getId());
        }
        Boolean blockingPermission = null;
        if (importFileTypes.stream().filter(importFileType -> ImportFileTypePermissionsE.MM_BULK_BLOCKING.getTypeClass()
                .equals(importFileType.getTypeClass())).findAny().isPresent()) {
            ArrayList<String> permissions = getUser().getPermissions();
            boolean containsUnblocking = permissions
                    .contains(ImportFileTypePermissionsE.MM_BULK_UNBLOCKING.name().toLowerCase());
            if (permissions.contains(ImportFileTypePermissionsE.MM_BULK_BLOCKING.name().toLowerCase())) {
                if (!containsUnblocking) {
                    blockingPermission = true;
                }
            } else if (containsUnblocking) {
                blockingPermission = false;
            } 
            
        }
        ImportFileListDto importFiles = importFileService.selectImportFiles(start, pageSize, sortColumn, 
                filterColumn, filterString, filterDate, order, importfileTypeIds, blockingPermission);
        return importFiles;
    }
    
    @Override
    public ImportFileItemListDto selectImportFileItems(Long importFileId, 
            int start, int pageSize, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) {
        return importFileService.selectImportFileItems(importFileId, start, pageSize, sortColumn, filterColumn, filterString, filterDate, order);
    }
    
    @Override
    public String updateImportFileItem(ImportFileItemDto importFileItem) throws Exception {
        return importFileService.updateImportFileItem(importFileItem);
    }
    
    @Override
    public void updateImportFileParams(ImportFileActionParamsDto dto) throws ServiceException {
        importFileService.updateImportFileParams(dto);
    }
    
    @Override
    public ImportFileResultDto importSelectedItems(String username, ImportFile importFile, List<Long> selectedItemsList, boolean isAccessGroupsEnabled) {
        return importFileService.importSelectedItems(username, getUser().getCurrentGroupId(), getUser().getSessionGroupId(), importFile, selectedItemsList, isAccessGroupsEnabled);
    }
   
    @Override
    public ImportFileResultDto importAllItems(String username, ImportFile importFile, boolean isAccessGroupsEnabled) {
        return importFileService.importAllItems(username, getUser().getCurrentGroupId(), getUser().getSessionGroupId(), importFile, isAccessGroupsEnabled);
    }
    
    @Override
    public List<ImportFileItem> extractFailedItems(ImportFile importFile) {
        return importFileService.extractFailedItems(importFile);
    }   
    
    @Override
    public List<KeyChangeDto> getKeyChangeTokensforBulkRef(String bulkRef) {
        return importFileService.getKeyChangeTokensforBulkRef(bulkRef);
    }
 
    @Override
    public void stopImportAll(String username, ImportFile importFile) {
        importFileService.stopImportAll(username, importFile);
    }
}
