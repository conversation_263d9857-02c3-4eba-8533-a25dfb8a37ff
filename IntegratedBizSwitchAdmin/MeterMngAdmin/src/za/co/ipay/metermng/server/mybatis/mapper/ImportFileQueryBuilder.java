package za.co.ipay.metermng.server.mybatis.mapper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import org.apache.log4j.Logger;

import za.co.ipay.utils.CalendarUtils;

/**
 * ImportFileQueryBuilder is used to build up the queries used to look for combinations of import file selections
 * <AUTHOR>
 */
public class ImportFileQueryBuilder {
    
    private static Logger logger = Logger.getLogger(ImportFileQueryBuilder.class);
    
    //------------------------------------------------------------------------------------------------------------------
    //ImportFile
    public static String getImportFilesCount(Map<String, Object> params) {   
        return performImportFileSearch(params, true);
    }    
                         
    public static String selectImportFiles(Map<String, Object> params) { 
        return performImportFileSearch(params, false);
    }
    
    private static String performImportFileSearch(Map<String, Object> params, boolean count) {    
        String sortColumn = (String) params.get("sortColumn");
        String filterColumn = (String) params.get("filterColumn");
        //had to use two objects filterString & filterDate because 'java.lang.Object' has no instantiable subtypes
        String filterString = (String) params.get("filterString");   
        Date filterDate = (Date) params.get("filterDate");
        String order = (String) params.get("order");
        String[] importfileTypeIds = (String[]) params.get("importfileTypeIds");
        Boolean blockingPermission = (Boolean) params.get("blockingPermission");
        
        logger.debug("performImportFileSearch: sortColumn= " + sortColumn + "  order= " + order);
        
        StringBuilder sbSelect = new StringBuilder();
        StringBuilder sbWhere = new StringBuilder();
        
        if (count) {
            //Select count only
            sbSelect.append("select count(1) ");
        } else {
            //Select
            sbSelect.append("select f.*, t.file_type_name, t.file_type_class, t.allow_items_multiple_import, t.has_action_params ");     
        }
        
        //From
        sbSelect.append("from import_file f join import_file_type t on f.import_file_type_id = t.import_file_type_id ");
                
        //Where
        String sbAndOrWhere = " where ";
        
        if (filterColumn != null && filterString != null) { 
            sbWhere.append(sbAndOrWhere).append(filterColumn).append(" like '%").append(filterString).append("%' ");

        } else if (filterColumn != null && filterColumn.toLowerCase().contains("upload_start") && filterDate != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Date dtFrom = new Date(CalendarUtils.clearTime(filterDate).getTime());
            String dtFromStr = sdf.format(dtFrom);

            Date dtTo = new Date(dtFrom.getTime());
            String dtToStr = sdf.format(CalendarUtils.addDays(dtTo, 1));

            logger.info("dtFrom=" + dtFromStr + " dtTo=" + dtToStr);
            sbWhere.append(sbAndOrWhere).append(filterColumn).append(" >= '").append(dtFromStr).append("' ");
            sbWhere.append(" and ").append(filterColumn).append(" < '").append(dtToStr).append("' ");
        } 
        
        if (importfileTypeIds != null && importfileTypeIds.length != 0) {
            if (sbWhere.length() == 0) {
                sbWhere.append(sbAndOrWhere);
            } else {
                sbWhere.append("and ");
            }
            String permissionsWhere = "";
            for (String importfileTypeId : importfileTypeIds) {
                permissionsWhere += ", " + importfileTypeId;
            }
            sbWhere.append("t.import_file_type_id in (" + permissionsWhere.substring(2) + ") ");
            if (blockingPermission != null) {
                sbWhere.append("and (f.action_params is null or f.action_params ");
                if (blockingPermission.booleanValue()) {
                    sbWhere.append("not ");
                }
                sbWhere.append("like '%\"blockingTypeId\":null%') ");
            }
        }
        
        sbSelect.append(sbWhere);
        
        //order by
        if (!count && sortColumn != null) {
            sbSelect.append(" order by ").append(sortColumn).append(" ").append(order);
        }
        
        String selectString = sbSelect.toString();
        logger.debug("performImportFileSearch sqlStmt= " + selectString);
        return selectString;
    }

    //------------------------------------------------------------------------------------------------------------------
    //ImportFileItem
    public static String getImportFileItemsCount(Map<String, Object> params) {   
        return performImportFileItemSearch(params, true);
    }    
                         
    public static String selectImportFileItems(Map<String, Object> params) { 
        return performImportFileItemSearch(params, false);
    }
    
    private static String performImportFileItemSearch(Map<String, Object> params, boolean count) {  
        Long importFileId = (Long) params.get("importFileId");
        String sortColumn = (String) params.get("sortColumn");
        String filterColumn = (String) params.get("filterColumn");
        //had to use two objects filterString & filterDate because 'java.lang.Object' has no instantiable subtypes
        String filterString = (String) params.get("filterString");   
        Date filterDate = (Date) params.get("filterDate");
        String order = (String) params.get("order");
        
        logger.debug("performImportFileItemSearch: sortColumn= " + sortColumn + "  order= " + order);
        
        StringBuilder sbSelect = new StringBuilder();
        StringBuilder sbWhere = new StringBuilder();
        
        if (count) {
            //Select count only
            sbSelect.append("select count(1) ");
        } else {
            //Select
            sbSelect.append("select * ");     
        }
        
        //From
        sbSelect.append("from import_file_item where import_file_id = ");
        sbSelect.append(importFileId);
                
        //Where
        String sbAndOrWhere = " and ";
        
        if (filterColumn != null && filterString != null) { 
            sbWhere.append(sbAndOrWhere).append(filterColumn);
            if (filterColumn.toLowerCase().contains("successful")) {
                String filterStr = filterString.trim().toLowerCase();
                if (filterStr.equals("true")) {
                    sbWhere.append(" = biz_true ");
                } else if (filterStr.equals("false")) {
                    sbWhere.append(" = biz_false ");
                }
            } else {
                sbWhere.append(" like '%").append(filterString).append("%' ");
            }

        } else if (filterColumn != null && filterColumn.toLowerCase().contains("date") && filterDate != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Date dtFrom = new Date(CalendarUtils.clearTime(filterDate).getTime());
            String dtFromStr = sdf.format(dtFrom);

            Date dtTo = new Date(dtFrom.getTime());
            String dtToStr = sdf.format(CalendarUtils.addDays(dtTo, 1));

            logger.info("dtFrom=" + dtFromStr + " dtTo=" + dtToStr);
            sbWhere.append(sbAndOrWhere).append(filterColumn).append(" >= '").append(dtFromStr).append("' ");
            sbWhere.append(" and ").append(filterColumn).append(" < '").append(dtToStr).append("' ");
        } 
        
        sbSelect.append(sbWhere);
        
        //order by
        if (!count && sortColumn != null) {
            sbSelect.append(" order by ").append(sortColumn).append(" ").append(order);
        }
        
        String selectString = sbSelect.toString();
        logger.debug("performImportFileItemSearch sqlStmt= " + selectString);
        return selectString;
    }
    //------------------------------------------------------------------------------------------------------------------

}
