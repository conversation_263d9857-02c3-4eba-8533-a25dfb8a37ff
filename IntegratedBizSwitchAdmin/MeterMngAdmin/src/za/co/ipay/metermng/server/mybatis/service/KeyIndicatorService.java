package za.co.ipay.metermng.server.mybatis.service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;

import org.apache.log4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.metermng.server.mybatis.mapper.IKeyIndicatorsMapper;
import za.co.ipay.metermng.shared.dto.dashboard.KeyIndicatorDto;

public class KeyIndicatorService {

    private Date todaysDate;
    private Date monthStartDate;
    private Date lastMonthStartDate;
    				

    private static Logger logger = Logger.getLogger(KeyIndicatorService.class);
    
    private IKeyIndicatorsMapper iKeyIndicatorsMapper;
    private ExposedReloadableResourceBundleMessageSource messageSource;
    
    private String getUsagePointMeterTotalLastMonth;
    
	public void setiKeyIndicatorsMapper(IKeyIndicatorsMapper iKeyIndicatorsMapper) {
		this.iKeyIndicatorsMapper = iKeyIndicatorsMapper;
	}
	
    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }
    
    @Transactional(readOnly = true)
    public ArrayList<KeyIndicatorDto> getKeyIndicators(Long genGroupId) {
    	
    	todaysDate = Date.valueOf(LocalDate.now());
        monthStartDate = Date.valueOf(LocalDate.now().withDayOfMonth(1));
        lastMonthStartDate = Date.valueOf(LocalDate.now().withDayOfMonth(1).minusMonths(1));
    	
		ArrayList<KeyIndicatorDto> arrayListOfKeyIndicatorDtos = new ArrayList<>();
		
		KeyIndicatorDto totalSalesIndicator = this.getTotalSalesIndicator(genGroupId);
		KeyIndicatorDto transactionsIndicator = this.getTransactionsIndicator(genGroupId);
		KeyIndicatorDto transactingMeters = this.getTransactingMeters(genGroupId);
		KeyIndicatorDto newMetersInstalled = this.getNewMetersInstalled(genGroupId);
		KeyIndicatorDto newActiveMetersInstalled = this.getNewActiveMetersInstalled(genGroupId);
		KeyIndicatorDto usagePointMetersTotal = this.getUsagePointMeterTotal (genGroupId);
		KeyIndicatorDto activeUsagePointMetersTotal = this.getActiveUsagePointMeterTotal (genGroupId);
		KeyIndicatorDto deviceStoreMetersTotal = this.getDeviceStoreMetersTotal(genGroupId, usagePointMetersTotal);	
		
		arrayListOfKeyIndicatorDtos.add(totalSalesIndicator);		
		arrayListOfKeyIndicatorDtos.add(transactionsIndicator);		
		arrayListOfKeyIndicatorDtos.add(transactingMeters);
		arrayListOfKeyIndicatorDtos.add(newMetersInstalled);
		arrayListOfKeyIndicatorDtos.add(newActiveMetersInstalled);
		arrayListOfKeyIndicatorDtos.add(usagePointMetersTotal);
		arrayListOfKeyIndicatorDtos.add(activeUsagePointMetersTotal);
		arrayListOfKeyIndicatorDtos.add(deviceStoreMetersTotal);
		
		return arrayListOfKeyIndicatorDtos;
	}
    
    // Total Sales
	private KeyIndicatorDto getTotalSalesIndicator(Long genGroupId) {
		KeyIndicatorDto totalSalesIndicator = new KeyIndicatorDto();
		totalSalesIndicator.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.total.sales"),null));
		totalSalesIndicator.setValueToday(iKeyIndicatorsMapper.getTotalSalesToday(genGroupId, this.todaysDate).toString());
		totalSalesIndicator.setValueMonthToDate(iKeyIndicatorsMapper.getTotalSalesMonthToDate(genGroupId, this.monthStartDate).toString());
		totalSalesIndicator.setValueLastMonth(iKeyIndicatorsMapper.getTotalSalesLastMonth(genGroupId, 
				this.monthStartDate, 
				this.lastMonthStartDate).toString());
		totalSalesIndicator.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.total.sales"),null));
		
		return totalSalesIndicator;
	}
	
	// Transactions
	private KeyIndicatorDto getTransactionsIndicator(Long genGroupId) {
		KeyIndicatorDto transactionsIndicator = new KeyIndicatorDto();
		transactionsIndicator.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.transactions"),null));
		transactionsIndicator.setValueToday(iKeyIndicatorsMapper.getTransactionsToday(genGroupId, this.todaysDate));
		transactionsIndicator.setValueMonthToDate(iKeyIndicatorsMapper.getTransactionsMonthToDate(genGroupId, this.monthStartDate));
		transactionsIndicator.setValueLastMonth(iKeyIndicatorsMapper.getTransactionsLastMonth(genGroupId, 
				this.monthStartDate, 
				this.lastMonthStartDate));
		transactionsIndicator.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.transactions"),null));
		
		return transactionsIndicator;
	}
	
	// Transacting MetersLocalDate
	private KeyIndicatorDto getTransactingMeters(Long genGroupId) {
		KeyIndicatorDto transactingMeters = new KeyIndicatorDto();
		transactingMeters.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.transacting.meters"),null));
		transactingMeters.setValueToday(iKeyIndicatorsMapper.getTransactingMetersToday(genGroupId, this.todaysDate));
		transactingMeters.setValueMonthToDate(iKeyIndicatorsMapper.getTransactingMetersMonthToDate(genGroupId, this.monthStartDate));
		transactingMeters.setValueLastMonth(iKeyIndicatorsMapper.getTransactingMetersLastMonth(genGroupId, 
				this.monthStartDate, 
				this.lastMonthStartDate));
		transactingMeters.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.transacting.meters"),null));
		
		return transactingMeters;
	}

	// New Meters Installed
	private KeyIndicatorDto getNewMetersInstalled(Long genGroupId) {
		KeyIndicatorDto newMetersInstalledIndicator = new KeyIndicatorDto();
		newMetersInstalledIndicator.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.new.meters.installed"),null));
		newMetersInstalledIndicator.setValueToday(iKeyIndicatorsMapper.getNewMetersInstalledToday(genGroupId, this.todaysDate));
		newMetersInstalledIndicator.setValueMonthToDate(iKeyIndicatorsMapper.getNewMetersInstalledMonthToDate(genGroupId, this.monthStartDate));
		newMetersInstalledIndicator.setValueLastMonth(iKeyIndicatorsMapper.getNewMetersInstalledLastMonth(genGroupId, 
				this.monthStartDate, 
				this.lastMonthStartDate));
		
		newMetersInstalledIndicator.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.new.meters.installed"),null));
		
		return newMetersInstalledIndicator;
	}
	
	// Active New Meters Installed
	private KeyIndicatorDto getNewActiveMetersInstalled(Long genGroupId) {
		KeyIndicatorDto newActiveMetersInstalledIndicator = new KeyIndicatorDto();
		newActiveMetersInstalledIndicator.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.new.active.meters.installed"),null));
		newActiveMetersInstalledIndicator.setValueToday(iKeyIndicatorsMapper.getNewActiveMetersInstalledToday(genGroupId, this.todaysDate));
		newActiveMetersInstalledIndicator.setValueMonthToDate(iKeyIndicatorsMapper.getNewActiveMetersInstalledMonthToDate(genGroupId, this.monthStartDate));
		newActiveMetersInstalledIndicator.setValueLastMonth(iKeyIndicatorsMapper.getNewActiveMetersInstalledLastMonth(genGroupId, 
				this.monthStartDate, 
				this.lastMonthStartDate));
		
		newActiveMetersInstalledIndicator.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.new.active.meters.installed"),null));
		
		return newActiveMetersInstalledIndicator;
	}
	
	// Usage Point Meters Total
	private KeyIndicatorDto getUsagePointMeterTotal(Long genGroupId) {
		KeyIndicatorDto usagePointMetersTotal = new KeyIndicatorDto();
		usagePointMetersTotal.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.total.meters.usage.points"),null));
		usagePointMetersTotal.setValueToday(iKeyIndicatorsMapper.getUsagePointMeterTotalToday(genGroupId));
		usagePointMetersTotal.setValueMonthToDate(iKeyIndicatorsMapper.getUsagePointMeterTotalMonthToDate(genGroupId));
		
		getUsagePointMeterTotalLastMonth = iKeyIndicatorsMapper.getUsagePointMeterTotalLastMonth(genGroupId, this.monthStartDate);
		
		usagePointMetersTotal.setValueLastMonth(getUsagePointMeterTotalLastMonth);
		usagePointMetersTotal.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.total.meters.usage.points"),null));
		
		return usagePointMetersTotal;
	}
	
	// Active Usage Point Meters Total
	private KeyIndicatorDto getActiveUsagePointMeterTotal(Long genGroupId) {
		KeyIndicatorDto activeUsagePointMetersTotal = new KeyIndicatorDto();
		activeUsagePointMetersTotal.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.total.active.meters.usage.points"),null));
		activeUsagePointMetersTotal.setValueToday(iKeyIndicatorsMapper.getActiveUsagePointMeterTotalToday(genGroupId));
		activeUsagePointMetersTotal.setValueMonthToDate(iKeyIndicatorsMapper.getActiveUsagePointMeterTotalMonthToDate(genGroupId));
		activeUsagePointMetersTotal.setValueLastMonth(iKeyIndicatorsMapper.getActiveUsagePointMeterTotalLastMonth(genGroupId, this.monthStartDate));
		activeUsagePointMetersTotal.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points"),null));
		
		return activeUsagePointMetersTotal;
	}
	
	// Device Store Meters Total
	private KeyIndicatorDto getDeviceStoreMetersTotal(Long genGroupId, KeyIndicatorDto usagePointMetersTotal) {
		KeyIndicatorDto deviceStoreMetersTotal = new KeyIndicatorDto();
		deviceStoreMetersTotal.setIndicator(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.total.meters.device.store"),null));
		deviceStoreMetersTotal.setValueToday(iKeyIndicatorsMapper.getDeviceStoreMetersTotalToday(genGroupId));
		deviceStoreMetersTotal.setValueMonthToDate(iKeyIndicatorsMapper.getDeviceStoreMetersTotalMonthToDate(genGroupId));
		try {
		    //A:Total Meters at end of last month = Total meters now - what was inserted THIS month.
		    //Total Device stores meters as at end of LAST MONTH = A:Total Meters at end of last month - usage point meters at end of last month
			Long totalMetersToday = Long.parseLong(usagePointMetersTotal.getValueToday())+Long.parseLong(deviceStoreMetersTotal.getValueToday());
			Long metersAddedMonthToDate = iKeyIndicatorsMapper.getMetersAddedMonthToDate(genGroupId, this.monthStartDate);
			Long usagePointMetersTotalLastMonth = Long.parseLong(usagePointMetersTotal.getValueLastMonth());
			
			String deviceStoreMetersTotalLastMonth = String.valueOf((totalMetersToday - metersAddedMonthToDate) - usagePointMetersTotalLastMonth);
			
			deviceStoreMetersTotal.setValueLastMonth(deviceStoreMetersTotalLastMonth);
		} catch(NumberFormatException e){
			e.printStackTrace();
			deviceStoreMetersTotal.setValueLastMonth("?");
		}
		
		deviceStoreMetersTotal.setToolTip(messageSource.getMessage(new DefaultMessageSourceResolvable("dashboard.key.indicator.indicator.tooltip.total.meters.device.store"),null));
		
		return deviceStoreMetersTotal;
	}

}