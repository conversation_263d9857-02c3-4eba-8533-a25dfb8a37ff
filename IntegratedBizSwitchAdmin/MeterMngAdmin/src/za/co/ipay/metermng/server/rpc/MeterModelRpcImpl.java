package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.MeterModelRpc;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.server.mybatis.service.MeterModelService;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelScreenDataDto;

public class MeterModelRpcImpl extends BaseMeterMngRpc implements MeterModelRpc {

    private static final long serialVersionUID = 1L;

    private MeterModelService meterModelService;
    
    public MeterModelRpcImpl() {
        this.logger = Logger.getLogger(MeterModelRpcImpl.class);
    }
    
    @Override
    public MeterModelScreenDataDto getMeterModelsScreenData() throws ServiceException {
        return meterModelService.getMeterModelsScreenData();
    }

    @Override
    public Integer getMeterModelsCount() throws ServiceException {
        return meterModelService.getMeterModelsCount();
    }

    @Override
    public ArrayList<MeterModelDto> getMeterModels(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        return meterModelService.getMeterModelDtos(startRow, pageSize, sortField, isAscending);
    }
    
    @Override
    public MeterModelDto getMeterModelDtoForMeterModelId(Long meterModelId) throws ServiceException {
        return meterModelService.getMeterModelDtoForMeterModelId(meterModelId);
    }

    @Override
    public MeterModel saveMeterModel(MeterModel meterModel, ArrayList<IdNameDto> paymentModes) throws ValidationException,
            ServiceException {
        
        if (meterModelService.getMridExistence(meterModel.getMrid(), meterModel.getId())) {
            throw new ValidationException(new ValidationMessage("meter.model.mrid.external.unique.validation", true));
        }
        
        return meterModelService.saveMeterModel(meterModel, paymentModes);
    }

    @Override
    public ArrayList<Long> getMeterModelIds(Long pricingStructureId) {
        return meterModelService.getMeterModelIds(pricingStructureId);
    }

    @Override
    public MeterModelData getMeterModelById(Long meterModelId) {
        return meterModelService.getMeterModelById(meterModelId);
    }

    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }
}
