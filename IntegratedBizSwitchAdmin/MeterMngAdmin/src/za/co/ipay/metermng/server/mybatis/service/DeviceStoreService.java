package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreMapper;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreExample;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreHist;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreHistExample;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class DeviceStoreService {

    private EndDeviceStoreMapper endDeviceStoreMapper;
    private EndDeviceStoreHistMapper endDeviceStoreHistMapper;
    private LocationService locationService;
    private MeterService meterService;

    @Transactional
    @PreAuthorize("hasRole('mm_meter_store_admin')")
    public EndDeviceStoreData insert(EndDeviceStoreData deviceStore) throws ValidationException, ServiceException {
        deviceStore.getLocation().setMrid(MridUtil.getMrid());
        locationService.saveLocation(deviceStore.getLocation());
        deviceStore.setLocationId(deviceStore.getLocation().getId());
        
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(deviceStore);
        
        //check device name is not duplicate
    	EndDeviceStore existing = getDeviceStoreByName(deviceStore.getName(), deviceStore.getGenGroupId());
        if (existing != null && !existing.getId().equals(deviceStore.getId())) {
            throw new ValidationException(new ValidationMessage("deviceStore.name.duplicate", new String[]{deviceStore.getName()}, true));
        }
        
        if (endDeviceStoreMapper.insert(deviceStore) == 1) {
            return deviceStore;
        } else {
            throw new ServiceException("devicestore.error.save");
        }
    }

    @Transactional
    @PreAuthorize("hasRole('mm_meter_store_admin')")
    public EndDeviceStoreData update(EndDeviceStoreData deviceStore) throws ValidationException, ServiceException {
        if (endDeviceStoreMapper.updateByPrimaryKey(deviceStore) == 1) {
            if (deviceStore.getLocationId() == null) {
                deviceStore.getLocation().setMrid(MridUtil.getMrid());                
            }
            locationService.saveLocation(deviceStore.getLocation());
            if (deviceStore.getLocationId() == null) {
                deviceStore.setLocationId(deviceStore.getLocation().getId());
            }
            return deviceStore;
        } else {
            throw new ServiceException("devicestore.error.update");
        }
    }

    @Transactional(readOnly = true)
    public List<EndDeviceStoreData> getDeviceStores(Long currentGroupId, Long sessionGroupId) {
        EndDeviceStoreExample deviceStoreExample = new EndDeviceStoreExample();
        EndDeviceStoreExample.Criteria criteria = deviceStoreExample.createCriteria();
        if (currentGroupId != null) {
            criteria.andGenGroupIdEqualTo(currentGroupId);
        }
        if (sessionGroupId != null) {
            criteria.andAccessGroupIdEqualTo(sessionGroupId);
        }
        deviceStoreExample.setOrderByClause("store_name asc");
        List<EndDeviceStore> deviceStores = endDeviceStoreMapper.selectByExample(deviceStoreExample);
        List<EndDeviceStoreData> returnList = new ArrayList<EndDeviceStoreData>(deviceStores.size());
        Iterator<EndDeviceStore> storeIt = deviceStores.iterator();
        EndDeviceStoreData endDeviceStoreData;
        while (storeIt.hasNext()) {
            endDeviceStoreData = new EndDeviceStoreData(storeIt.next());
            if (endDeviceStoreData.getLocationId() != null) {
                endDeviceStoreData.setLocation(locationService.getLocationDataById(endDeviceStoreData.getLocationId()));
            }
            returnList.add(endDeviceStoreData);
        }
        return returnList;
    }
    
    @Transactional(readOnly = true)
    public EndDeviceStoreData getDeviceStore(Long endDeviceStoreId) {
        if(endDeviceStoreId == null){
            return new EndDeviceStoreData();
        }
        EndDeviceStore deviceStore = endDeviceStoreMapper.selectByPrimaryKey(endDeviceStoreId);
        EndDeviceStoreData endDeviceStoreData = new EndDeviceStoreData(deviceStore);
        if (endDeviceStoreData.getLocationId() != null) {
            endDeviceStoreData.setLocation(locationService.getLocationDataById(endDeviceStoreData.getLocationId()));
        }
        return endDeviceStoreData;
    }
    
    @Transactional(readOnly = true)
    public EndDeviceStoreData getHistDeviceStore(Long endDeviceStoreId, boolean usingAccessGroup) {
        if(endDeviceStoreId == null){
            return new EndDeviceStoreData();
        }
        EndDeviceStore deviceStore = endDeviceStoreMapper.selectByPrimaryKey(endDeviceStoreId);
        if (deviceStore == null && usingAccessGroup) {
            EndDeviceStoreData eds = new EndDeviceStoreData();
            eds.setName(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
            return eds;
        }
        EndDeviceStoreData endDeviceStoreData = new EndDeviceStoreData(deviceStore);
        if (endDeviceStoreData.getLocationId() != null) {
            endDeviceStoreData.setLocation(locationService.getLocationDataById(endDeviceStoreData.getLocationId()));
        }
        return endDeviceStoreData;
    }

    @Transactional(readOnly = true)
    public EndDeviceStoreData getDeviceStoreByMeter(String meterNumber) {
        Meter meter = meterService.getMeterByNumber(meterNumber);
        if(meter == null || meter.getEndDeviceStoreId() ==null){
            return new EndDeviceStoreData();
        }
        EndDeviceStore deviceStore = endDeviceStoreMapper.selectByPrimaryKey(meter.getEndDeviceStoreId());
        EndDeviceStoreData endDeviceStoreData = new EndDeviceStoreData(deviceStore);
        if (endDeviceStoreData.getLocationId() != null) {
            endDeviceStoreData.setLocation(locationService.getLocationDataById(endDeviceStoreData.getLocationId()));
        }
        return endDeviceStoreData;
    }

    @Transactional(readOnly = true)
    public boolean isExistingDeviceStoresWithGroup() {
        EndDeviceStoreExample example = new EndDeviceStoreExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<EndDeviceStore> stores = endDeviceStoreMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (stores.size() == 1) {
            return true;
        } else {
            return false;
        }
    }
    
    @Transactional(readOnly = true)
    public List<EndDeviceStoreHist> getEndDeviceStoreHistoryByEndDeviceStoreId(Long endDeviceStoreId) {
        EndDeviceStoreHistExample endDeviceStoreHistExample = new EndDeviceStoreHistExample();
        endDeviceStoreHistExample.createCriteria().andIdEqualTo(endDeviceStoreId);
        endDeviceStoreHistExample.setOrderByClause("date_rec_modified desc");
        List<EndDeviceStoreHist> list = endDeviceStoreHistMapper.selectByExample(endDeviceStoreHistExample);
        return list;
    }
    
    @Transactional(readOnly=true)
    public EndDeviceStore getDeviceStoreByName(String name, Long genGroupId) {
        EndDeviceStoreExample example = new EndDeviceStoreExample();
        example.createCriteria().andNameEqualTo(name).andGenGroupIdEqualTo(genGroupId);
        List<EndDeviceStore> ds = endDeviceStoreMapper.selectByExample(example);
        if (ds.isEmpty()) {
            return null;
        } else {
            return ds.get(0);
        }
    }

    public void setEndDeviceStoreMapper(EndDeviceStoreMapper endDeviceStoreMapper) {
        this.endDeviceStoreMapper = endDeviceStoreMapper;
    }

    public void setEndDeviceStoreHistMapper(EndDeviceStoreHistMapper endDeviceStoreHistMapper) {
        this.endDeviceStoreHistMapper = endDeviceStoreHistMapper;
    }

    public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
}
