package za.co.ipay.metermng.server.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.accesscontrol.domain.Organisation;
import za.co.ipay.accesscontrol.service.IAccessControlService;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage.Severity;
import za.co.ipay.metermng.client.rpc.GroupRpc;
import za.co.ipay.metermng.mybatis.custom.model.GenGroupClosureCombo;
import za.co.ipay.metermng.mybatis.custom.model.GroupClosureCombo;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.CustAccNotify;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.mybatis.generated.model.GroupFeature;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.GroupService;
import za.co.ipay.metermng.server.util.GenGroupConverter;
import za.co.ipay.metermng.server.util.GroupHierarchyConverter;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.NdpScheduleData;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.SelectionDataItem.SelectionDataType;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.dto.user.UserAvailableGroupsData;
import za.co.ipay.metermng.shared.group.GroupTypeData;
import za.co.ipay.utils.NestedStopWatch;

/**
 * GroupControllerImpl provides the functionality related to group type, group hierarchy, etc.
 * <AUTHOR>
 */
public class GroupRpcImpl extends BaseMeterMngRpc implements GroupRpc {

    private static final long serialVersionUID = -8799775601688230523L;

    private static final String NULL_PARENT_ID = "0";
    
    private GroupService groupService;  
    private AppSettingService appSettingService;
    private IAccessControlService accessControlService;
    
    public GroupRpcImpl() {
        logger = Logger.getLogger(GroupRpcImpl.class);
    }
    
    @Override
    public ArrayList<GroupTypeData> getAllGroupTypes() throws ServiceException, AccessControlException {
        return new ArrayList<GroupTypeData>(groupService.getGroupTypesData());
    }

    @Override
    public ArrayList<GroupTypeData> getGroupTypesWithHierarchy() throws ServiceException, AccessControlException {
        return new ArrayList<GroupTypeData>(groupService.getGroupTypesWithHierarchy());
    }

    @Override
    public ArrayList<GroupTypeData> getActiveGroupTypesWithHierarchy() throws ServiceException, AccessControlException {
        return new ArrayList<GroupTypeData>(groupService.getActiveGroupTypesWithHierarchyExclAccessGrps());
    }

    @Override
    public ArrayList<GroupType> getGroupTypesWithNoHierarchy() throws ServiceException, AccessControlException {
        return new ArrayList<GroupType>(groupService.getGroupTypesWithNoHierarchy());
    }
    
    @Override
    public List<GroupFeature> getAllGroupFeatures() throws ServiceException {
        return groupService.getAllGroupFeatures();
    }
     
    @Override
    public Boolean updateGroupType(GroupTypeData groupTypeData) throws ValidationException, ServiceException {        
        return groupService.updateGroupType(groupTypeData);
    }
        
    @Override
    public GroupType getAccessGroupType() throws ServiceException {
        return groupService.getAccessGroupGroupType();
    }
    
    @Override
    public GroupType getLocationGroupType() throws ServiceException {
        return groupService.getLocationGroupGroupType();
    }

    @Override
    public ArrayList<GroupHierarchyData> getGroupHierarchies(Long groupTypeId) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateNotNull(groupTypeId, "grouptype.field.id");
        return GroupHierarchyConverter.convertGroupHierarchies(groupService.getGroupHierarchies(groupTypeId));
    }
    
    @Override
    public GroupHierarchyData updateGroupHierarchy(GroupHierarchyData groupHierarchyData) throws ValidationException, ServiceException {        
          //Note: not setting transient level attribute in returned instance
          GroupHierarchy groupHierarchy = GroupHierarchyConverter.convertGroupHierarachyData(groupHierarchyData);
          ServerValidatorUtil.getInstance().validateDataForValidationMessages(groupHierarchy);
          GroupHierarchy saved = groupService.updateGroupHierarchy(groupHierarchy);
          return GroupHierarchyConverter.convertGroupHierarachy(saved);
    }
    
    @Override
    public void deleteGroupHierarchy(Long groupHierarchyId) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateNotNull(groupHierarchyId, "grouphierarchy.field.id");
        groupService.deleteGroupHierarchy(groupHierarchyId);
    }

    @Override
    public GroupEntity getEntity(Long id) throws ValidationException, ServiceException {
        return groupService.getEntity(id);
    }
    
    @Override
    public GroupEntity getEntityFromGenGroupId(Long genGroupId) throws ServiceException {
        return groupService.getEntityFromGenGroupId(genGroupId);
    }

    @Override
    public GroupEntity updateEntity(Long genGroupId, GroupEntity entity) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateNotNull(genGroupId, "usagepointgroup.field.id");
        groupService.saveGroupEntity(entity, genGroupId);
        return entity;
    }
    
    @Override
    public ArrayList<GenGroupData> getGroups(Long groupTypeId) throws ServiceException {
        return GenGroupConverter.convertToHierarchy(groupService.getAllGenGroups(groupTypeId));
    }

    @Override
    public GenGroupData updateGenGroup(GenGroupData genGroupData, boolean lastLevelChild) 
        throws ValidationException, ServiceException, AccessControlException {        
        GenGroup genGroup = GenGroupConverter.convertGenGroupData(genGroupData);
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(genGroup);
        if (genGroup.getId()==null) {
            //check name is unique
            if (!groupService.isGenGroupUnique(genGroup)) {
                throw new ValidationException(new ValidationMessage(Severity.ERROR, "group.error.name.nonunique"));
            }
        }
        
        if (groupService.getMridExistence(genGroupData)) {
            throw new ValidationException(new ValidationMessage("gen.group.mrid.external.unique.validation", true));
        }
        
        genGroupData = GenGroupConverter.convertGenGroup(groupService.updateGenGroup(genGroup, lastLevelChild));
        genGroupData.setPath(getPath(genGroupData.getId()));
        return genGroupData;
    }

    @Override
    public GenGroupData updateGenGroupMetadata(Long genGroupId, GisMetadata metadata) throws ValidationException, ServiceException, AccessControlException {
        GenGroup genGroup = groupService.getGenGroup(genGroupId);
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(genGroup);
        if (genGroup.getId() == null) {
            throw new ValidationException(new ValidationMessage(Severity.ERROR, "group.error.save"));
        }
        GenGroupData genGroupData = GenGroupConverter.convertGenGroup(groupService.updateGenGroupMetadata(genGroup, metadata));
        genGroupData.setPath(getPath(genGroupData.getId()));
        return genGroupData;
    }

    public GisMetadata getGenGroupMetadata(Long genGroupId) {
        GenGroup genGroup = groupService.getGenGroup(genGroupId);
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(genGroup);
        if (genGroup.getId() == null) {
            throw new ValidationException(new ValidationMessage(Severity.ERROR, "group.error.save"));
        }
        return GisMetadata.parse(genGroup.getMetadata());
    }

    @Override
    public void deleteGenGroup(GenGroupData genGroupData) throws ServiceException {
        groupService.deleteGenGroup(GenGroupConverter.convertGenGroupData(genGroupData));
    }
    
    /**
     * Method to load the GroupType's hierarchy for the current GenGroup data. This is essentially loading the active 
     * GroupTypes which are typically one or only a few levels. Per Group Type, if there are no children then load the
     * first level of active GenGroup data. If there are no children and no active gengroup data then discard the GroupType. 
     * The rest of the levels will then be loaded from the server via another RPC call as the user selects specific data
     * on the UI.  
     * @return The active group types (possible also active, first level of usage point groups).
     */
    @Override
    public ArrayList<SelectionDataItem> getGroupTypes(boolean includeAccessGroup, boolean includeLocationGroup) throws ServiceException {
        NestedStopWatch sw = new NestedStopWatch(logger);
        sw.start();
        List<GroupType> groupTypes = groupService.getGroupTypes(includeAccessGroup, includeLocationGroup);
        ArrayList<SelectionDataItem> groupTypesSelectionData = getGroupTypesSelectionData(groupTypes);
        sw.end("getGroupTypes");
        logger.info("Get group types performance:\n" +  sw.toString());
        return groupTypesSelectionData;
    }
    
    /**
     * Method to load the GroupType's hierarchy for the current GenGroup data. This is essentially loading the active 
     * GroupTypes which are typically one or only a few levels. Per Group Type, if there are no children then load the
     * first level of active GenGroup data. If there are no children and no active gengroup data then discard the GroupType. 
     * The rest of the levels will then be loaded from the server via another RPC call as the user selects specific data
     * on the UI.  
     * @return The active group type (possible also active, first level of usage point groups).
     */
    @Override
    public ArrayList<SelectionDataItem> getGroupType(Long groupTypeId) throws ServiceException {
        GroupType groupType = groupService.getGroupTypeById(groupTypeId);
        return getGroupTypesSelectionData(Arrays.asList(groupType));
    }
    
    private ArrayList<SelectionDataItem> getGroupTypesSelectionData(List<GroupType> groupTypesList) {
        ArrayList<SelectionDataItem> groupTypes = createGroupTypesHierarchy(groupTypesList);
        logger.info("Created GroupTypesHierarchy: "+groupTypes.size());
        //Check that there is first level children and if not, load the corresponding genGroup data        
        SelectionDataItem groupType = null;
        for(int i=groupTypes.size()-1;i>=0;i--) {
            groupType = groupTypes.get(i);
            logger.info("Checking groupType: "+groupType.getName());
            //GroupType with no nested GroupTypes - try and load the top level GenGroup and remove if none
            
            if (groupType.getChildren().size() == 0) {
                if (!loadGroupTypeChildren(groupType)) {
                    groupTypes.remove(i);
                    logger.info("No child for current groupType: "+groupType.getName());
                }          
            } else {
                //For each nested GroupType, try and load their children and if none remove from parent
                filterGroupTypes(groupType.getChildren(), groupType);
                if (groupType.getChildren().size() == 0) {
                    groupTypes.remove(i);
                    logger.info("No child for filtered groupType: "+groupType.getName());
                }
            }
        }
        return groupTypes;
    }
    
    /**
     * Method to build a hierarchy of Group Types for each of the current top-level GenGroups.
     * @param parents The top level GenGroups.
     * @return The hierarchy of Group Types.
     */
    private ArrayList<SelectionDataItem> createGroupTypesHierarchy(List<GroupType> groupTypesList) {
        //logger.debug("createGroupTypesHierarchy parents: "+parents.toString());
        ArrayList<SelectionDataItem> data = new ArrayList<SelectionDataItem>();
        ArrayList<GroupClosureCombo> groupTypes = null;
        ArrayList<SelectionDataItem> selectionData = null;
        ArrayList<Long> convertedGroupTypeIds = new ArrayList<Long>();
        for(GroupType groupType : groupTypesList) {
            long groupTypeId = groupType.getId();
            groupTypes = groupService.getGroupTypeClosures(groupTypeId);
            if (groupTypes.size() > 0) {
                if (!convertedGroupTypeIds.contains(groupTypeId)) {
                    logger.debug("Getting groupType closures: "+groupTypeId);
                    selectionData = convertToHierarchy(groupTypeId, groupTypes, convertedGroupTypeIds, groupType.isRequired());
                    data.addAll(selectionData);
                } else {
                    logger.info("Previously converted GroupType hierarchy: "+groupTypeId);
                }
            }
        }        
        return data;
    }
    
    /**
     * Method to recursive through a hierarchy of GroupType instances and load any active first level GenGroup data
     * for each of the GroupTypes. If the GroupType has no linked GenGroup data then remove them as there is no data to 
     * be displayed and selected for that instance.
     * @param data The current list of selection data.
     * @param groupType The current groupType.
     */
    private void filterGroupTypes(List<SelectionDataItem> data, SelectionDataItem  groupType) {
        if (groupType.getChildren().size() > 0) {            
            for(int i=groupType.getChildren().size()-1;i>=0;i--) {
                SelectionDataItem child = groupType.getChildren().get(i);
                filterGroupTypes(groupType.getChildren(), child);
            }
        }
        if (!loadGroupTypeChildren(groupType)) {
            if (groupType.getChildren().size() == 0) {
                data.remove(groupType);
                logger.debug("Removing child: "+groupType.getId()+" "+groupType.getName());
            } else {
                logger.debug("Keeping child with existing children: "+groupType.getId()+" "+groupType.getName()+" "+groupType.getChildren().size());
            }
        } else {
            logger.debug("Keeping child: "+groupType.getId()+" "+groupType.getName());
        }
    }
    
    /**
     * Method to load the first level of GenGroup data for the specified GroupType.
     * @param groupType The current groupType.
     * @return Whether any active first level (parent) GenGroup instances were found for the GroupType.
     */
    private boolean loadGroupTypeChildren(SelectionDataItem groupType) {
        Long maxGroupHierarchyId = groupService.getGroupTypeMaxGroupHierarchyId(groupType.getActualId());
        SelectionDataItem selectionData = null;
        List<GenGroupClosureCombo> combos = groupService.getParentGenGroupClosuresForGroupType(groupType.getActualId());
        if(combos.isEmpty()) {
            return false;
        }
        for(GenGroupClosureCombo combo : combos) {
            selectionData = new SelectionDataItem();
            selectionData.setId(SelectionDataItem.toIdKey(SelectionDataType.GROUP_DATA, combo.getId()));
            selectionData.setParentId(SelectionDataItem.toIdKey(groupType.getType(), combo.getParentId()));
            selectionData.setName(combo.getGroupName());  
            selectionData.setLabel(combo.getGroupHierarchyName());
            selectionData.setType(SelectionDataType.GROUP_DATA);
            selectionData.setGroupHierarchyId(combo.getGroupHierarchyId());
            selectionData.setLastLevel(maxGroupHierarchyId.equals(combo.getGroupHierarchyId()));
            groupType.getChildren().add(selectionData);            
        }
        logger.debug("Selected groupType: "+groupType+" loaded children: "+groupType.getChildren().size());
        return true;
    }
        
    /**
     * Method to convert GroupTypeClosure instances into a hierarchy.
     * @param parentId The GroupType parent's id.
     * @param groups The closure data for the GroupTypes.
     * @param convertedIds The ids of GroupTypes that have already been converted into a hierarchy.
     * @return The hierarchy of GroupTypes as selection data.
     */
    private ArrayList<SelectionDataItem> convertToHierarchy(Long parentId, ArrayList<GroupClosureCombo> groups, ArrayList<Long> convertedIds, boolean isRequired) {
        logger.debug("Converting for parentId: "+parentId);
        HashMap<String, ArrayList<SelectionDataItem>> idToChildren = new HashMap<String, ArrayList<SelectionDataItem>>();
        idToChildren.put(NULL_PARENT_ID, new ArrayList<SelectionDataItem>());
        SelectionDataItem selectionData = null;
        for(GroupClosureCombo combo : groups) {
            selectionData = new SelectionDataItem();
            selectionData.setId(SelectionDataItem.toIdKey(SelectionDataType.GROUP_TYPE, combo.getId()));
            selectionData.setParentId(SelectionDataItem.toIdKey(SelectionDataType.GROUP_TYPE, combo.getParentId()));
            selectionData.setName(combo.getGroupName());
            selectionData.setLabel(combo.getGroupName());
            selectionData.setLayoutOrder(combo.getLayoutOrder());
            
            selectionData.setLastLevel(false);
            selectionData.setType(SelectionDataType.GROUP_TYPE);
            selectionData.setRequired(isRequired);
            //logger.debug("Converted "+combo);
            if (selectionData.getParentId() == null) {
                idToChildren.get(NULL_PARENT_ID).add(selectionData);
            } else {
                if (idToChildren.containsKey(selectionData.getParentId())) {
                    idToChildren.get(selectionData.getParentId()).add(selectionData);
                } else {
                    ArrayList<SelectionDataItem> data = new ArrayList<SelectionDataItem>();
                    data.add(selectionData);
                    idToChildren.put(selectionData.getParentId(), data);
                }
            }
            if (!convertedIds.contains(combo.getId())) {
                convertedIds.add(combo.getId());
            }
        }
        
        ArrayList<SelectionDataItem> parents = idToChildren.get(NULL_PARENT_ID);
        addChildren(parents, idToChildren);
        return parents;
    }
    
    /**
     * Method to recursive through the SelectionDataItem instances and add the children List to the correct parent.
     * @param current The SelectionDataItem instances to be populated.
     * @param all The list of children data mapped by parentdId.
     */
    private static void addChildren(ArrayList<SelectionDataItem> current, HashMap<String, ArrayList<SelectionDataItem>> all) {
        for(SelectionDataItem c : current) {
            if (all.containsKey(c.getId())) {
                c.setChildren(all.get(c.getId()));
                addChildren(c.getChildren(), all);
            }
        }
    }
    
    /**
     * Method to load the children of the current selected item which is typically selected in a drop-down by the user.
     * Either a GroupType or an GenGroup instance has been selected so load the children in the appropriate way.
     * @param item The current selected item.
     * @return The children selection items or an empty list if there are no children. 
     */
    @Override
    public ArrayList<SelectionDataItem> getChildrenSelectionData(SelectionDataItem item) throws ServiceException {                
        logger.info("Getting children for selectionDataItem: "+item.getName()+" "+item.getType());
        if (SelectionDataType.GROUP_TYPE.equals(item.getType())) {
            loadGroupTypeChildren(item);
            return item.getChildren();
        } else {
            return getClosureChildren(item);
        }        
    }
   
    private ArrayList<SelectionDataItem> getClosureChildren(SelectionDataItem item) {
        ArrayList<SelectionDataItem> children = new ArrayList<SelectionDataItem>();          
        Long maxId = groupService.getMaxGroupHierarchyId(item.getActualId());        
        logger.debug("maxId:"+maxId);
        ArrayList<GenGroupClosureCombo> closureChildren = new ArrayList<GenGroupClosureCombo>(groupService.getClosureChildren(item.getActualId()));
        SelectionDataItem selectionData = null;              
        if (closureChildren.isEmpty()) {
            logger.info("No closure children found for item: "+item.getName());
            //Check if hierarchy incomplete
            GroupHierarchy nextGroupHierarchy = groupService.getNextInHierarchy(item.getGroupHierarchyId());
            if (nextGroupHierarchy != null) {
                selectionData = new SelectionDataItem();
                selectionData.setName("- No values defined -");  //TODO i18n
                selectionData.setId(SelectionDataItem.NO_VALUES_ID_KEY);
                selectionData.setLabel(nextGroupHierarchy.getName());
                selectionData.setGroupHierarchyId(nextGroupHierarchy.getId());
                selectionData.setType(SelectionDataType.GROUP_DATA);
                selectionData.setLastLevel(true);
                logger.debug("GroupHierarchyId: "+ nextGroupHierarchy.getId() +" lastLevel: "+(maxId.equals(nextGroupHierarchy.getId())));
                children.add(selectionData);
            }            
        } else {
            logger.info("Got closure children:"+closureChildren.size()+" for item: "+item.getName());
            for(GenGroupClosureCombo combo : closureChildren) {
                selectionData = new SelectionDataItem();
                selectionData.setId(SelectionDataItem.toIdKey(SelectionDataType.GROUP_DATA, combo.getId()));
                selectionData.setParentId(SelectionDataItem.toIdKey(item.getType(), combo.getParentId()));
                selectionData.setName(combo.getGroupName());  
                selectionData.setLabel(combo.getGroupHierarchyName());
                selectionData.setGroupHierarchyId(combo.getGroupHierarchyId());
                selectionData.setType(SelectionDataType.GROUP_DATA);
                selectionData.setLastLevel(maxId.equals(combo.getGroupHierarchyId()));
                logger.debug("GroupHierarchyId: "+combo.getGroupHierarchyId()+" lastLevel: "+(maxId.equals(combo.getGroupHierarchyId())));
                children.add(selectionData);
            }
        }
        logger.debug("Loaded children: "+children.size());
        return children;
    }
    
    @Override
    public UserAvailableGroupsData getAccessGroupsForUser(Long groupTypeId) throws ServiceException {
        UserAvailableGroupsData data = new UserAvailableGroupsData();
        if (groupTypeId != null) {            
            //Set the access groups
            data.setGroups(getGroupType(groupTypeId));
            //Set the user's path to their current group
            MeterMngUser user = getUser();
            if (user.getCurrentGroupId() != null) {                
                data.setCurrentGroupPath(groupService.getPath(user.getCurrentGroupId()));
            }
        }
        return data;
    }
    
    @Override
    public ArrayList<SelectionDataItem> getLocationGroups() throws ServiceException {
        GroupType groupType = groupService.getLocationGroupGroupType();
        return getGroupTypesSelectionData(Arrays.asList(groupType));
    }
    
    @Override   
    public CustomerAccThresholds getGlobalThresholds() throws ServiceException {
        ArrayList<AppSetting> gSettingList = appSettingService.getAllAppSettings();
        CustomerAccThresholds cat = new CustomerAccThresholds();
        for (AppSetting gSett : gSettingList) {
            if (gSett.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD)) {
                cat.setDisconnect(new BigDecimal(gSett.getValue()));
            }
            if (gSett.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD)) {
                cat.setReconnect(new BigDecimal(gSett.getValue()));
            }
            if (gSett.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD)) {
                cat.setEmergencyCredit(new BigDecimal(gSett.getValue()));
            }
            if (gSett.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD)) {
                cat.setLowBalance(new BigDecimal(gSett.getValue()));
            }
        }
        cat.setId(null);
        return cat;
    }
    
    @Override
    public CustomerAccThresholds getGenGroupThresholds(Long thresholdId) throws ServiceException {
        return groupService.getCustomerAccThreshold(thresholdId);
    }
    
    @Override   
    public CustomerAccThresholds updateThresholds(GenGroupData genGroupData, CustomerAccThresholds thresholds) throws ServiceException {
        return groupService.updateThresholds(genGroupData, thresholds);
    }
    
    @Override  
    public CustomerAccThresholds deleteThresholds(GenGroupData genGroupData, Long revertToThresholdId) throws ServiceException {
        return groupService.deleteThresholds(genGroupData, revertToThresholdId);
    }
    
    
    
    @Override  
    public NdpSchedule addNewNdpScheduleToGenGroup(GenGroupData genGroupData) throws ServiceException, AccessControlException {
        return groupService.addNewNdpScheduleToGenGroup(genGroupData);
    }
    
    @Override 
    public NdpScheduleData deleteNdpSchedule(GenGroupData genGroupData, Long revertToNdpScheduleId) throws ServiceException, AccessControlException {
        return groupService.deleteNdpSchedule(genGroupData, revertToNdpScheduleId);
    }
    
    @Override
    public ArrayList<UpGenGroupLinkDataNames> getUpGenGroupNamesList(ArrayList<UpGenGroupLinkData> genGroupIdList) throws ServiceException, AccessControlException {
        return groupService.getUpGenGroupNamesList(genGroupIdList);
    }

    public ArrayList<Long> getPath(Long genGroupId) throws ServiceException {
        return groupService.getPath(genGroupId);
    }

    @Override
    public Boolean isGenGroupNameUnique(GenGroup genGroup) throws ServiceException {
        return (genGroup.getId()==null ? groupService.isGenGroupUnique(genGroup) : Boolean.TRUE); 
    }
    @Override
    public Boolean isGenGroupMridUnique(GenGroup genGroup) throws ServiceException {
        return !groupService.getMridExistence(genGroup);
    }
    
    @Override
    public Boolean isGroupAssignedToUP(GenGroup genGroup) throws ServiceException {
        return (genGroup.getId()!=null ? groupService.isGroupAssignedToUP(genGroup) : Boolean.FALSE); 
    }

    @Override
    public ArrayList<GroupTypeData> getUsagepointGroupTypesWithHierarchy() throws ServiceException, AccessControlException {
        return new ArrayList<>(groupService.getUsagepointGroupTypesWithHierarchy());
    }

    @Override
    public List<Long> isEntityCustomFieldUsed(int fieldNumber) {
        return groupService.isGroupEntityCustomFieldUsed(fieldNumber);
    }
    
    @Override   
    public CustAccNotify updateNotifications(GenGroupData genGroupData, CustAccNotify notifications) throws ServiceException {
        return groupService.updateNotifications(genGroupData, notifications);
    }
    
    @Override   
    public CustAccNotify convertNotifyAccountToInherit(GenGroupData genGroupData, Long oldId) throws ServiceException {
        return groupService.convertNotifyAccountToInherit(genGroupData, oldId);
    }
    
    @Override
    public CustAccNotify getGenGroupNotifications(Long notificationsId) throws ServiceException {
        return groupService.getGenGroupNotifications(notificationsId);
    }

    @Override
    public Long getParentGenGroupIdForAccessGroup(Long accessGroupId) {
        return groupService.getParentGenGroupForAccessGroup(accessGroupId);
    }

    @Override
    public List<Group> getAvailableAccessGroupsByGroupHierarchy(long genGroupHierarchyId) {        
        //Clear the user's assigned group
        MeterMngUser user = getUser();
        // Need to find by context.xml configured organisation for this site so it works for users without
        // an org. Ideally the id should be available in the BaseMeterMngRpc, but to add it properly means adding 
        // accessControlService as a dependency on GWTController because it only has access to the name from the 
        // servlet init param. This doesn't seem ideal.
        // The IAccessControlService has been modified to provide a retrieve and cache method for the configured org 
        // for this type of purpose to ensure performant access
        Organisation org = accessControlService.findConfiguredManagementCompany(getOrganisation());
        if(org == null) {
            return new ArrayList<>(0);
        }
        List<Group> accessGroups = accessControlService.findGroups(org.getId());
        if(accessGroups.isEmpty()) {
            return accessGroups;
        }
        List<Long> idsForHierachy = groupService.getUsedAccessGroupIdsForHierachy(genGroupHierarchyId);
        for (Iterator<Group> iterator = accessGroups.iterator(); iterator.hasNext();) {
            Group group = iterator.next();
            for (Iterator<Long> iterator2 = idsForHierachy.iterator(); iterator2.hasNext();) {
                Long accessGroupId = iterator2.next();
                if(group.getId().equals(accessGroupId)) {
                    iterator.remove();
                    iterator2.remove();
                }
            }
        }
        
        return accessGroups;
    } 


    /** Setter methods ----------------------------------------------------------------------------------------------------*/
    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }

    public void setAccessControlService(IAccessControlService accessControlService) {
        this.accessControlService = accessControlService;
    }

}
