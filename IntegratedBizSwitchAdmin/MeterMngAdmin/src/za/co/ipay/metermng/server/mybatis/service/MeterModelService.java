package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.MeterPhaseE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.MeterModelCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.ModelSupportsPaymentModeData;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterDataDecoderMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterModelMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ModelSupportsPayModeMapper;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.MeterDataDecoder;
import za.co.ipay.metermng.mybatis.generated.model.MeterDataDecoderExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterModelExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterModelExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.MeterPhase;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.ModelSupportsPayMode;
import za.co.ipay.metermng.mybatis.generated.model.ModelSupportsPayModeExample;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;
import za.co.ipay.metermng.server.mybatis.mapper.IMeterCountMapper;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.MeterModelScreenDataDto;

public class MeterModelService {

    //mappers
    private MeterModelMapper meterModelMapper;
    private ModelSupportsPayModeMapper modelSupportsPayModeMapper;
    private MeterModelCustomMapper meterModelCustomMapper;
    private IMeterCountMapper iMeterCountMapper;
    private MeterDataDecoderMapper meterDataDecoderMapper;
    
    //other services
    private ManufacturerService manufacturerService;
    private LookupService lookupService;
    private MeterTypeService meterTypeService;
    private MdcService mdcService;
    private ServiceResourceService serviceResourceService;
    private PricingStructureService pricingStructureService;
    private MeterService meterService;

    @Transactional(readOnly=true)
    public MeterModelScreenDataDto getMeterModelsScreenData() throws ServiceException {
        MeterModelScreenDataDto screenData = new MeterModelScreenDataDto();
        
        //manufacturers
        ArrayList<IdNameDto> dtos = new ArrayList<IdNameDto>();        
        List<Manufacturer> manufacturers = manufacturerService.getManufacturers(true);        
        for(Manufacturer man : manufacturers) {
            dtos.add(new IdNameDto(man.getId(), man.getName()));
        }
        screenData.setManufacturers(dtos);
        
        // mdcs: meter data collectors
        ArrayList<IdNameDto> mdcs = new ArrayList<IdNameDto>();
        List<Mdc> mdc = mdcService.getMdcs(true);
        for (Mdc m : mdc) {
            mdcs.add(new IdNameDto(m.getId(), m.getName()));
        }
        screenData.setMdcs(mdcs);
        
        PtrScreenDataDto ptr = lookupService.getPtrScreenData();        
        screenData.setMeterTypes(ptr.getMeterTypes());
        screenData.setServiceResources(ptr.getServiceResources());
        screenData.setPaymentModes(ptr.getPaymentModes());
        screenData.setMeterPhases(ptr.getMeterPhases());
        screenData.setDecoders(ptr.getDataDecoders());
        
        return screenData;
    }
    
    @Transactional(readOnly=true)
    public Integer getMeterModelsCount() throws ServiceException {
        MeterModelExample example = new MeterModelExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        int count = meterModelMapper.countByExample(example);
        return count;
    }
    
    @Transactional(readOnly=true)
    public MeterModelData getMeterModelById(Long id) throws ServiceException {
        MeterModelData mmd = new MeterModelData(meterModelMapper.selectByPrimaryKey(id));
        mmd.setPaymentModeIds(getPaymentModesByMeterModel(id));
        if (mmd.getMdcId() != null) {
            mmd.setMdcName(mdcService.getMdc(mmd.getMdcId()).getName());
            mmd.setMdcHasChannels(mdcService.isMdcActiveChannels(mmd.getMdcId()));
        }
        return mmd;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<MeterModelDto> getMeterModelDtos(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        MeterModelExample example = new MeterModelExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);        
        List<MeterModel> models = meterModelMapper.selectByExampleWithRowbounds(example, rowBounds);
        
        //Convert to dtos
        Map<Long, Manufacturer> manufacturers = new HashMap<Long, Manufacturer>();
        Map<Long, MeterType> types = new HashMap<Long, MeterType>();
        Map<Long, ServiceResource> resources = new HashMap<Long, ServiceResource>();
        Map<Long, Mdc> mdcs = new HashMap<Long, Mdc>();
        Map<Long, MeterPhase> meterPhases = new HashMap<Long, MeterPhase>();
        Map<Long, MeterDataDecoder> decoders = new HashMap<>();

        ArrayList<MeterModelDto> dtos = new ArrayList<MeterModelDto>(models.size());
        MeterModelDto dto = null;
        for(MeterModel model : models) {
            dto = new MeterModelDto();
            dto.setMeterModel(model);
            
            if (!manufacturers.containsKey(model.getManufacturerId())) {
                manufacturers.put(model.getManufacturerId(), manufacturerService.getManufacturer(model.getManufacturerId()));
            }
            dto.setManufacturer(manufacturers.get(model.getManufacturerId()));
            
            if (!types.containsKey(model.getMeterTypeId())) {
                types.put(model.getMeterTypeId(), meterTypeService.getMeterType(model.getMeterTypeId()));
            }            
            dto.setMeterType(types.get(model.getMeterTypeId()));
            
            if (!mdcs.containsKey(model.getMdcId())) {
                mdcs.put(model.getMdcId(), mdcService.getMdc(model.getMdcId()));
            }            
            dto.setMdc(mdcs.get(model.getMdcId()));
            
            if (!resources.containsKey(model.getServiceResourceId())) {
                resources.put(model.getServiceResourceId(), serviceResourceService.getServiceResource(model.getServiceResourceId()));
            } 
            dto.setServiceResource(resources.get(model.getServiceResourceId()));

            Long meterModelId = model.getId();
            dto.setSupportedPaymentModes(
                    new ArrayList<ModelSupportsPaymentModeData>(
                            meterModelCustomMapper.getMeterModePaymentModes(meterModelId)));
            dto.setMeterAttachedToMeterModel(meterService.isMeterAttachedToMeterModel(meterModelId));

            Long meterPhaseId = model.getMeterPhaseId();
            if (meterPhaseId != null && !meterPhases.containsKey(meterPhaseId)) {
                MeterPhase meterPhase = new MeterPhase();
                meterPhase.setId(meterPhaseId);
                MeterPhaseE meterPhaseE = MeterPhaseE.fromId(meterPhaseId);
                meterPhase.setValue(String.valueOf(meterPhaseE.getValue()));
                meterPhase.setName(meterPhaseE.getName());
                meterPhases.put(meterPhaseId, meterPhase);
            }
            dto.setMeterPhase(meterPhases.get(meterPhaseId));

            if (!decoders.containsKey(model.getMeterDataDecoderId())) {
                decoders.put(model.getMeterDataDecoderId(), getDataDecoder(model.getMeterDataDecoderId()));
            }
            dto.setMeterDataDecoder(decoders.get(model.getMeterDataDecoderId()));
            
            dtos.add(dto);
        }
        return dtos;
    }
    
    @Transactional(readOnly=true)
    public MeterModelDto getMeterModelDtoForMeterModelId(Long meterModelId) throws ServiceException {
        MeterModel model = meterModelMapper.selectByPrimaryKey(meterModelId);
        MeterModelDto dto = new MeterModelDto();
        dto.setMeterModel(model);
        dto.setManufacturer(manufacturerService.getManufacturer(model.getManufacturerId()));
        dto.setMeterType(meterTypeService.getMeterType(model.getMeterTypeId()));
        dto.setMdc(mdcService.getMdc(model.getMdcId()));
        dto.setServiceResource( serviceResourceService.getServiceResource(model.getServiceResourceId()));
        dto.setSupportedPaymentModes(
                new ArrayList<ModelSupportsPaymentModeData>(
                		meterModelCustomMapper.getMeterModePaymentModes(meterModelId)));
        dto.setMeterAttachedToMeterModel(meterService.isMeterAttachedToMeterModel(meterModelId));
        dto.setMeterDataDecoder(getDataDecoder(model.getMeterDataDecoderId()));
        return dto;
    }

    @Transactional(readOnly=true)
    public ArrayList<MeterModel> getMeterModels(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        MeterModelExample example = new MeterModelExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        return new ArrayList<MeterModel>(meterModelMapper.selectByExampleWithRowbounds(example, rowBounds));
    }
    
    @Transactional(readOnly=true)
    public ArrayList<Long> getPaymentModesByMeterModel(Long meterModelId) throws ServiceException {
        List<ModelSupportsPaymentModeData> modes = meterModelCustomMapper.getMeterModePaymentModes(meterModelId);
        ArrayList<Long> paymentModes = new ArrayList<Long>(modes.size());
        for (ModelSupportsPaymentModeData ms : modes) {
            paymentModes.add(ms.getPaymentMode().getId());
        }
        return paymentModes;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<MeterModel> getAvailableMeterModels() {
        MeterModelExample meterModelExample = new MeterModelExample();
        meterModelExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<MeterModel> list = (ArrayList<MeterModel>) meterModelMapper.selectByExample(meterModelExample);
        return list;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<MeterModel> getAvailableMeterModels(PricingStructure pricingStructure) {
        ModelSupportsPayModeExample modeExample = new ModelSupportsPayModeExample(); 
        modeExample.createCriteria().andPaymentModeIdEqualTo(pricingStructure.getPaymentModeId());
        List<Long> metermodelids = new ArrayList<Long>();
        for (ModelSupportsPayMode m : modelSupportsPayModeMapper.selectByExample(modeExample)) {
            metermodelids.add(m.getMeterModelId());
        }
        MeterModelExample meterModelExample = new MeterModelExample();
        MeterModelExample.Criteria criteria =  meterModelExample.createCriteria();
        criteria.andRecordStatusEqualTo(RecordStatus.ACT);
        criteria.andServiceResourceIdEqualTo(pricingStructure.getServiceResourceId());
        criteria.andMeterTypeIdEqualTo(pricingStructure.getMeterTypeId());
        criteria.andIdIn(metermodelids);
        ArrayList<MeterModel> list = (ArrayList<MeterModel>) meterModelMapper.selectByExample(meterModelExample);
        return list;
    }
    
    @Transactional(readOnly=true)
    private ArrayList<Long> getMeterModelIds(PricingStructure pricingStructure) {
        ArrayList<Long> idlist =  new ArrayList<Long>();
        for (MeterModel mm : getAvailableMeterModels(pricingStructure)) {
            idlist.add(mm.getId());
        }
        return idlist;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<Long> getMeterModelIds(Long pricingStructureId) {
        PricingStructure pricingStructure = pricingStructureService.getPricingStructure(pricingStructureId);
        return getMeterModelIds(pricingStructure);
    }
    
    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "model_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "model_name";
            } else if ("status".equals(sortField)) {
                orderColumn = "record_status";
            } else if ("manufacturer".equals(sortField)) {
                orderColumn = "manufacturer_id";
            } else if ("serviceResource".equals(sortField)) {
                orderColumn = "service_resource_id";
            } else if ("meterType".equals(sortField)) {
                orderColumn = "meter_type_id";
            } else if ("mdc".equals(sortField)) {
                orderColumn = "mdc_id";
            } else {
                orderColumn = "meter_phase_id";
            }

        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }

    @Transactional(readOnly=true)
    public MeterModel getMeterModelByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            MeterModelExample example = new MeterModelExample();
            example.createCriteria().andNameEqualTo(name);
            List<MeterModel> ms = meterModelMapper.selectByExample(example);
            if (ms.isEmpty()) {
                return null;
            } else {
                return ms.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=false)
    public MeterModel saveMeterModel(MeterModel meterModel, ArrayList<IdNameDto> paymentModes) 
        throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(meterModel);

        String name = meterModel.getName();
        MeterModel existing = (MeterModel) getMeterModelByName(name);

        Long id = meterModel.getId();
        if (existing != null) {
            if (!existing.getId().equals(id)) {
                throw new ValidationException(
                        new ValidationMessage("meter.models.name.duplicate", new String[] { name }, true));
            }

            // if unset the needsBreakerId to false, don't allow if any meters with
            // breakerIds already use this model
            if (existing.isNeedsBreakerId() && !meterModel.isNeedsBreakerId()
                    && iMeterCountMapper.getMeterCountByMeterModelId(id) > 0) {
                throw new ValidationException(
                        new ValidationMessage("meter.model.unset.breaker.id.error", new String[] {}, true));
            }

            // if unset the needsEncKey to false, don't allow if any meters with encKeys
            // already use this model
            if (existing.isNeedsEncKey() && !meterModel.isNeedsEncKey()
                    && iMeterCountMapper.getMeterCountByMeterModelId(id) > 0) {
                throw new ValidationException(
                        new ValidationMessage("meter.model.unset.encryption.key.error", new String[] {}, true));
            }

            if (meterModel.getRecordStatus() != RecordStatus.ACT && existing.getRecordStatus() == RecordStatus.ACT
                && meterService.isMeterAttachedToMeterModel(id)) {
                throw new ValidationException(
                    new ValidationMessage("meter.model.deactivate.in.use.error", new String[] {}, true));
            }
        }

        boolean existingPaymentMode = true;
        if (id == null) {
            existingPaymentMode = false;
            if (meterModelMapper.insert(meterModel) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.models.name"}, true));
            }
        } else {
            if (meterModelMapper.updateByPrimaryKey(meterModel) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.models.name"}, true));
            }
        }
        
        //Delete old supported payment modes if necessary and save new incoming supported payment modes
        if (existingPaymentMode) {
            ModelSupportsPayModeExample deleteExample = new ModelSupportsPayModeExample();
            deleteExample.createCriteria().andMeterModelIdEqualTo(meterModel.getId());
            modelSupportsPayModeMapper.deleteByExample(deleteExample);
        }
        ModelSupportsPayMode modelSupportsPayMode = null;
        for(IdNameDto paymentMode : paymentModes) {
            modelSupportsPayMode = new ModelSupportsPayMode();
            modelSupportsPayMode.setMeterModelId(meterModel.getId());
            modelSupportsPayMode.setPaymentModeId(paymentMode.getId());
            modelSupportsPayModeMapper.insert(modelSupportsPayMode);
        }
        return meterModel;
    }
    
    public boolean getMridExistence(String mrid, Long id) {
        MeterModelExample example = new MeterModelExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(id != null) {
            criteria.andIdNotEqualTo(id);
        }
        return !meterModelMapper.selectByExample(example).isEmpty();
    }

    @Transactional(readOnly=true)
    public ArrayList<MeterDataDecoder> getAvailableDataDecoders() {
        MeterDataDecoderExample meterDataDecoderExample = new MeterDataDecoderExample();
        meterDataDecoderExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<MeterDataDecoder> list = (ArrayList<MeterDataDecoder>) meterDataDecoderMapper.selectByExample(meterDataDecoderExample);
        return list;
    }

    @Transactional(readOnly=true)
    public MeterDataDecoder getDataDecoder(Long decoderId) {
        return meterDataDecoderMapper.selectByPrimaryKey(decoderId);
    }

    public void setMeterModelMapper(MeterModelMapper meterModelMapper) {
        this.meterModelMapper = meterModelMapper;
    }

    public void setModelSupportsPayModeMapper(ModelSupportsPayModeMapper modelSupportsPayModeMapper) {
        this.modelSupportsPayModeMapper = modelSupportsPayModeMapper;
    }

    public void setMeterModelCustomMapper(MeterModelCustomMapper meterModelCustomMapper) {
        this.meterModelCustomMapper = meterModelCustomMapper;
    }

    public void setIMeterCountMapper(IMeterCountMapper iMeterCountMapper) {
        this.iMeterCountMapper = iMeterCountMapper;
    }
    
    public void setManufacturerService(ManufacturerService manufacturerService) {
        this.manufacturerService = manufacturerService;
    }

    public void setMeterTypeService(MeterTypeService meterTypeService) {
        this.meterTypeService = meterTypeService;
    }
    
    public void setMdcService(MdcService mdcService) {
        this.mdcService = mdcService;
    }

    public void setServiceResourceService(ServiceResourceService serviceResourceService) {
        this.serviceResourceService = serviceResourceService;
    }

    public void setLookupService(LookupService lookupService) {
        this.lookupService = lookupService;
    }
    
    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    public void setMeterDataDecoderMapper(MeterDataDecoderMapper meterDataDecoderMapper) {
        this.meterDataDecoderMapper = meterDataDecoderMapper;
    }
    
    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }

}
