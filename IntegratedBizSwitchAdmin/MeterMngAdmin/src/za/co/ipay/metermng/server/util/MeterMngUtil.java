package za.co.ipay.metermng.server.util;

import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.search.SearchData;

public class MeterMngUtil {
    
    private MeterMngUtil() {
        //private constructor
    }
    
    public static boolean isMeterSearchCriteria(SearchData data) {
        if (data != null && data.getCriteria() != null) { 
                if (data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NO_CUSTOMER_SEARCH)
                        || data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NO_METER_SEARCH)) {
                    //when doing this search, can also enter values on MeterSearchForm, but this is essentially a usage point search and the meter_no_usage_point checkbox is disabled
                    return false;
                } else if ((data.getCriteria().containsKey(MeterMngStatics.METER_NUMBER_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.METER_MODEL_ID_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.METER_STORE_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.METER_SGC_KRN_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.METER_NO_USAGE_POINT_SEARCH))){
                    return true;
                } else {
                    return false;
                }  
        } else {
            return false;
        }
    }

    public static boolean isCustomerSearchCriteria(SearchData data) {
        if (data != null && data.getCriteria() != null) { 
            if (data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NO_METER_SEARCH)) {
                //when doing this search, can also enter values on CustomerSearchForm, but this is essentially a usage_point_no_meter_search and the customer_no_usage_point checkbox is disabled
                return false;
            } else if ((data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_SURNAME_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_NAME_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_ID_NUMBER_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.ACCOUNT_NAME_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_PHONE_NUMBER_SEARCH)
                    || data.getCriteria().containsKey(MeterMngStatics.CUSTOMER_NO_USAGE_POINT_SEARCH)) ) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public static boolean isUsagePointSearchCriteria(SearchData data) {
        if (data != null && data.getCriteria() != null &&
                (data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NAME_SEARCH)
                        || data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1)
                        || data.getCriteria().containsKey(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH)
                        || data.getCriteria().containsKey(MeterMngStatics.PAYMENT_MODE_ID_SEARCH)
                        || data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NO_CUSTOMER_SEARCH)
                        || data.getCriteria().containsKey(MeterMngStatics.USAGE_POINT_NO_METER_SEARCH))) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isLocationSearchCriteria(SearchData data) {
        if (data != null) {
            HashMap<String, Object> criteria = data.getCriteria();
            return criteria != null && (criteria.containsKey(MeterMngStatics.LOCATION_ERF_NUMBER_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_BUILDING_NAME_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_SUITE_NUMBER_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_ADDRESS_1_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_ADDRESS_2_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_ADDRESS_3_SEARCH)
                    || criteria.containsKey(MeterMngStatics.LOCATION_GROUP_SEARCH));
        }
        return false;
    }

    public static boolean areValuesDifferent(Object value1, Object value2) {
        if (value1 != null) {
            return !value1.equals(value2);
        } else if (value2 != null) {
            return !value2.equals(value1);
        }
        return false;
    }
    
    public static boolean isValidDateFormat(String dateText, String testRegex) {
        if (testRegex == null) {   //default to "yyyy-MM-dd HH:mm:ss"
            testRegex = "\\d\\d\\d\\d-\\d\\d-\\d\\d \\d\\d:\\d\\d:\\d\\d";
        }    
        Pattern p = Pattern.compile(testRegex);
        if (dateText == null) {
            return false;
        }
        Matcher m = p.matcher(dateText);
        return m.matches();
    }
}
