package za.co.ipay.metermng.server.validation;

import java.util.Locale;

import javax.validation.MessageInterpolator;

import org.apache.log4j.Logger;
import org.springframework.context.i18n.LocaleContextHolder;

/**
 *  CustomLocaleResolverMessageInterpolator uses the current Locale to resolve the i18n message for a validation message.
 *  The Locale is set using our custom LocaleResolver instance that either uses a configured Locale or uses the client's
 *  request Locale. The Locale is access via Spring's LocaleContextHolder class.
 */
public class CustomLocaleResolverMessageInterpolator implements MessageInterpolator {

    private final MessageInterpolator delegate;
    private static Logger logger = Logger.getLogger(CustomLocaleResolverMessageInterpolator.class);
    
    public CustomLocaleResolverMessageInterpolator(MessageInterpolator delegate) {
        this.delegate = delegate;
        logger.debug("Created CustomLocaleResolverMessageInterpolator with delegate: "+delegate.getClass().getName());
    }

    @Override
    public String interpolate(String message, Context context) {
        Locale locale = LocaleContextHolder.getLocale();
        logger.debug("Interpolating validation message using current Locale: "+locale.toString());
        return interpolate(message, context, locale);
    }

    @Override
    public String interpolate(String message, Context context, Locale locale) {
        return delegate.interpolate(message, context, locale);
    }
}
