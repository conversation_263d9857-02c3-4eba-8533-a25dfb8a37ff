package za.co.ipay.metermng.server.access;

import java.util.Collection;

import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.FilterInvocation;

import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

/**
 * UserGroupVoter is used to check that the current user has either been assigned a UserGroup in Meter Management
 * or that they do not have an Access Control organisation and thus can use the default UserGroup in Meter Management.
 * This class is used in the Spring security log on process for Meter Management specifically.
 * <AUTHOR>
 */
public class UserGroupVoter implements AccessDecisionVoter<FilterInvocation> {
    
    private static Logger logger = Logger.getLogger(UserGroupVoter.class);

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return clazz.isAssignableFrom(FilterInvocation.class);
    }

    @Override
    public int vote(Authentication authentication, FilterInvocation object, Collection<ConfigAttribute> attributes) {
        logger.debug("Executing user group voter: " + this);
        HttpSession session = object.getHttpRequest().getSession();
        if(session != null) {
            MeterMngUser user = (MeterMngUser) session.getAttribute(MeterMngStatics.METER_MNG_USER_ATTR);
            if (user != null) {
                if(user.getCurrentGroup() != null) {
                    logger.debug("Already have user group, ACCESS GRANTED: currentGroup=" + user.getCurrentGroup());
                    return ACCESS_GRANTED;
                }
            }
        }
        logger.debug("User does not have a group denying access: " + authentication.getName());
        return ACCESS_DENIED;
    }
}
