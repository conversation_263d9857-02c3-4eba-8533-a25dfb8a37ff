package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.AuxTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxTypeExample.Criteria;

public class AuxTypeService {

    private AuxTypeMapper auxTypeMapper;
    
    public void setAuxTypeMapper(AuxTypeMapper auxTypeMapper) {
        this.auxTypeMapper = auxTypeMapper;
    }
    
    @Transactional
    @PreAuthorize("hasRole('mm_aux_type_admin')")
    public AuxType insert(AuxType auxType) throws ServiceException {
        if (auxTypeMapper.insert(auxType) == 1) {
            return auxType;
        } else {
            throw new ServiceException("auxtype.error.save");
        }
    }
    
    @Transactional
    @PreAuthorize("hasRole('mm_aux_type_admin')")
    public AuxType update(AuxType auxType) throws ServiceException {
        if (auxTypeMapper.updateByPrimaryKey(auxType) == 1) {
            return auxType;
        } else {
            throw new ServiceException("auxtype.error.update");
        }
    }
    
    @Transactional(readOnly=true)
    public List<AuxType> getAuxTypes() {
        AuxTypeExample auxTypeExample = new AuxTypeExample();
        auxTypeExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        return auxTypeMapper.selectByExample(auxTypeExample);
    }
    
    @Transactional
    public AuxType getAuxTypeByName(String name) {
        AuxTypeExample auxTypeExample = new AuxTypeExample();
        auxTypeExample.createCriteria().andNameEqualTo(name);
        List<AuxType> auxTypeList = auxTypeMapper.selectByExample(auxTypeExample);
        if (auxTypeList.isEmpty()) {
            return null;
        }
        return auxTypeList.get(0);
    }
    
    public boolean getMridExistence(String mrid, Long id) {
        AuxTypeExample example = new AuxTypeExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(id != null) {
            criteria.andIdNotEqualTo(id);
        }
        return !auxTypeMapper.selectByExample(example).isEmpty();
    }
    
}
