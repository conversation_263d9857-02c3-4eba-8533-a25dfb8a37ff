package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.sql.Timestamp;
import java.util.ArrayList;

public interface IVendingActivityMapper {
    @Select("SELECT trans_date "
            + "FROM customer_trans ct "
            + "INNER JOIN usage_point up "
            + "ON ct.usage_point_id = up.usage_point_id "
            + "WHERE customer_trans_type_id = 1 "    // fetch vends only
            + "AND reversed = biz_false "            // that have been successfull
            + "AND gen_group_id = #{GROUP_TYPE_ID} "// only for gen_group of user
            + "AND trans_date > #{startDate} " +
            " AND trans_date < #{endDate}")        // after given date
    ArrayList<Timestamp> getVendingData(@Param("GROUP_TYPE_ID") Long GROUP_TYPE_ID,
                                        @Param("startDate") Timestamp startDate,
                                        @Param("endDate") Timestamp endDate);
}