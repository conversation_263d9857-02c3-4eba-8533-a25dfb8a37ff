package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.custom.mapper.AdvancedSearchCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.SearchResult;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.search.SearchResultData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

/**
 * SearchService provides the search functionality for the application.
 *
 * <AUTHOR>
 */
public class SearchService {

    private static Logger logger = Logger.getLogger(SearchService.class);
    private AdvancedSearchCustomMapper advancedSearchCustomMapper;

    public void setAdvancedSearchCustomMapper(AdvancedSearchCustomMapper advancedSearchCustomMapper) {
        this.advancedSearchCustomMapper = advancedSearchCustomMapper;
    }

    @Transactional(readOnly = true)
    public void doMeterAdvancedSearchCount(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isMeterSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Total rows available
            if (data.getTotalResults() == null) {
                int totalCount = advancedSearchCustomMapper.getMeterAdvancedSearchCount(data.getCriteria(), currentGroupId, new Date());
                data.setTotalResults(totalCount);
                logger.info("Set current total search results: " + totalCount);
            } else {
                logger.info("Current total search results: " + data.getTotalResults());
            }
        }
    }
    
    @Transactional(readOnly = true)
    public void doMeterAdvancedSearch(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isMeterSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Search for current search results
            logger.info("Meter search with start:" + data.getStart() + " pageSize:" + data.getPageSize());
            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
            List<SearchResult> meters = advancedSearchCustomMapper.doMeterAdvancedSearch(data.getCriteria(), currentGroupId, new Date(), rowBounds);
            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
            SearchResultData result = null;
            for (SearchResult sr : meters) {
                result = new SearchResultData(SearchResultType.METER, sr.getMeterId());
                processSearchResultsData(data, result, sr);
                results.add(result);
            }
            logger.info("Got meters:" + results.size());
            data.addResults(results);
        }
    }


    @Transactional(readOnly = true)
    public void doCustomerAdvancedSearchCount(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isCustomerSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Total rows available
            if (data.getTotalResults() == null) {
                int totalCount = advancedSearchCustomMapper.getCustomerAdvancedSearchCount(data.getCriteria(), currentGroupId, new Date());
                data.setTotalResults(totalCount);
                logger.info("Set current total search results: " + totalCount);
            } else {
                logger.info("Current total search results: " + data.getTotalResults());
            }
        }    
    }
    
    @Transactional(readOnly = true)
    public void doCustomerAdvancedSearch(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isCustomerSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Search for current search results
            logger.info("Customer search with start:" + data.getStart() + " pageSize:" + data.getPageSize());
            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
            List<SearchResult> meters = advancedSearchCustomMapper.doCustomerAdvancedSearch(data.getCriteria(), currentGroupId, new Date(), rowBounds);
            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
            SearchResultData result = null;
            for (SearchResult sr : meters) {
                result = new SearchResultData(SearchResultType.CUSTOMER, sr.getCustomerId());
                processSearchResultsData(data, result, sr);
                results.add(result);
            }
            logger.info("Got customers:" + results.size());
            data.addResults(results);
        }
    }

    @Transactional(readOnly = true)
    public void doUsagePointAdvancedSearchCount(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isUsagePointSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Total rows available
            if (data.getTotalResults() == null) {
                int totalCount = advancedSearchCustomMapper.getUsagePointAdvancedSearchCount(data.getCriteria(), currentGroupId, new Date());
                data.setTotalResults(totalCount);
                logger.info("Set current total search results: " + totalCount);
            } else {
                logger.info("Current total search results: " + data.getTotalResults());
            }
        }
    }    
    
    @Transactional(readOnly = true)
    public void doUsagePointAdvancedSearch(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isUsagePointSearchCriteria(data)) {
            //Combine the meter and other criteria if necessary for AND searching

            //Search for current search results
            logger.info("UsagePoint search with start:" + data.getStart() + " pageSize:" + data.getPageSize());
            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
            List<SearchResult> meters = advancedSearchCustomMapper.doUsagePointAdvancedSearch(data.getCriteria(), currentGroupId, new Date() ,rowBounds);
            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
            SearchResultData result = null;
            for (SearchResult sr : meters) {
                result = new SearchResultData(SearchResultType.USAGE_POINT, sr.getUsagePointId());
                processSearchResultsData(data, result, sr);
                results.add(result);
            }
            logger.info("Got usagePoints:" + results.size());
            data.addResults(results);
        }
    }
    

    @Transactional(readOnly = true)
    public void doLocationAdvancedSearchCount(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isLocationSearchCriteria(data)) {
            if (data.getTotalResults() == null) {
                int totalCount = advancedSearchCustomMapper.getLocationAdvancedSearchCount(data.getCriteria(), currentGroupId, new Date());
                data.setTotalResults(totalCount);
                logger.info("Set current total search results: " + totalCount);
            } else {
                logger.info("Current total search results: " + data.getTotalResults());
            }
        }    
    }   

    @Transactional(readOnly = true)
    public void doLocationAdvancedSearch(SearchData data, Long currentGroupId)
            throws ValidationException, ServiceException {
        if (MeterMngUtil.isLocationSearchCriteria(data)) {

            //Search for current search results
            logger.info("Location search with start:" + data.getStart() + " pageSize:" + data.getPageSize());
            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
            List<SearchResult> meters = advancedSearchCustomMapper.doLocationAdvancedSearch(data.getCriteria(), currentGroupId, new Date() ,rowBounds);
            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
            SearchResultData result = null;
            for (SearchResult sr : meters) {
                result = new SearchResultData(SearchResultType.LOCATION, sr.getUsagePointId());
                processSearchResultsData(data, result, sr);
                results.add(result);
            }
            logger.info("Got locations:" + results.size());
            data.addResults(results);
        }
    }

    private void processSearchResultsData(SearchData data, SearchResultData result, SearchResult sr) {
        if (sr.getCustomerId() != null) {
            result.addId(MeterMngStatics.CUSTOMER_ID_SEARCH, sr.getCustomerId());
            result.addDetail(MeterMngStatics.CUSTOMER_SURNAME_SEARCH, sr.getSurname());
            result.addDetail(MeterMngStatics.CUSTOMER_NAME_SEARCH, sr.getName());
            result.addDetail(MeterMngStatics.CUSTOMER_TITLE_SEARCH, sr.getTitle());
            result.addDetail(MeterMngStatics.CUSTOMER_ID_NUMBER_SEARCH, sr.getIdNumber());
        }

        // Always select the phone number searched for if in either phone1 or phone 2, otherwise display phone1,
        //display phone 2 if no phone 1 (This logic only for customer search).
        boolean hasPhoneCriteria = data.getCriteria().containsKey("search.customer.phone.number");
        if (sr.getPhone1() != null) {
            result.addDetail(MeterMngStatics.CUSTOMER_PHONE_NUMBER_SEARCH, sr.getPhone1());

            if (hasPhoneCriteria
                    && sr.getPhone2() != null
                    && sr.getPhone2().equals(data.getCriteria().get("search.customer.phone.number"))) {
                result.addDetail(MeterMngStatics.CUSTOMER_PHONE_NUMBER_SEARCH, sr.getPhone2());
            }
        } else if (sr.getPhone2() != null) {
            result.addDetail(MeterMngStatics.CUSTOMER_PHONE_NUMBER_SEARCH, sr.getPhone2());
        }

        if (sr.getCustomVarchar1() != null) {
            result.addDetail(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1, sr.getCustomVarchar1());
        }

        if (sr.getAgreementId() != null) {
            result.addId(MeterMngStatics.CUSTOMER_AGREEMENT_ID_SEARCH, sr.getAgreementId());
            result.addDetail(MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH, sr.getAgreement());
        }
        if (sr.getAccountName() != null) {
            result.addDetail(MeterMngStatics.ACCOUNT_NAME_SEARCH, sr.getAccountName());
        }
        if (sr.getMeterId() != null) {
            result.addId(MeterMngStatics.METER_ID_SEARCH, sr.getMeterId());
            result.addDetail(MeterMngStatics.METER_NUMBER_SEARCH, sr.getMeterNumber());
        }
        if (sr.getMeterModelId() != null) {
            result.addDetail(MeterMngStatics.METER_MODEL_ID_SEARCH, sr.getMeterModelId().toString());
            result.addDetail(MeterMngStatics.METER_MODEL_NAME, sr.getMeterModelName());
        }
        if (sr.getUsagePointId() != null) {
            result.addId(MeterMngStatics.USAGE_POINT_ID_SEARCH, sr.getUsagePointId());
            result.addDetail(MeterMngStatics.USAGE_POINT_NAME_SEARCH, sr.getUsagePointName());
            if (sr.getPricingStructureId() != null) {
                result.addDetail(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH, sr.getPricingStructureId().toString());
                result.addDetail(MeterMngStatics.PRICING_STRUCTURE_NAME_SEARCH, sr.getPricingStructureName());
                result.addDetail(MeterMngStatics.PAYMENT_MODE_NAME_SEARCH, sr.getPaymentModeName());
            }
        }

    }
}
