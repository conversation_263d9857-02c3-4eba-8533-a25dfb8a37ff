package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.MeterHistMapper;
import za.co.ipay.metermng.mybatis.generated.model.MeterHist;
import za.co.ipay.metermng.mybatis.generated.model.MeterHistExample;

public class MeterHistService {
    private MeterHistMapper meterHistMapperImpl;
    
    public void setMeterHistMapperImpl(MeterHistMapper meterHistMapper) {
        this.meterHistMapperImpl = meterHistMapper;
    }
    
    @Transactional(readOnly=true)
    public List<MeterHist> getMeterHistoryByMeterId(Long meterId) {
        MeterHistExample meterHistExample = new MeterHistExample();
        meterHistExample.createCriteria().andIdEqualTo(meterId);
        meterHistExample.setOrderByClause("date_rec_modified desc");
        List<MeterHist> list = meterHistMapperImpl.selectByExample(meterHistExample);
        return list;
    }
    
    @Transactional(readOnly=true)
    public MeterHist getLatestMeterHistoryByMeterId(Long meterId) {
        MeterHistExample meterHistExample = new MeterHistExample();
        meterHistExample.createCriteria().andIdEqualTo(meterId);
        meterHistExample.setOrderByClause("date_rec_modified desc");
        List<MeterHist> list = meterHistMapperImpl.selectByExampleWithRowbounds(meterHistExample, new RowBounds(0, 1));
        if(list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

}
