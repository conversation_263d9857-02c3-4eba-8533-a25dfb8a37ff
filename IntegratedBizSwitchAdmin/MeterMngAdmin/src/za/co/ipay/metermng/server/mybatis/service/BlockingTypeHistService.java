package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import za.co.ipay.metermng.mybatis.generated.mapper.BlockingTypeHistMapper;
import za.co.ipay.metermng.mybatis.generated.model.BlockingTypeHist;
import za.co.ipay.metermng.mybatis.generated.model.BlockingTypeHistExample;

/**
 * <AUTHOR>
 */
public class BlockingTypeHistService {
    private BlockingTypeHistMapper blockingTypeHistMapper;

    public void setBlockingTypeHistMapper(BlockingTypeHistMapper blockingTypeHistMapper) {
        this.blockingTypeHistMapper = blockingTypeHistMapper;
    }

    public List<BlockingTypeHist> getBlockingTypeHistoryByBlockingTypeId(Long blockingTypeId) {
        BlockingTypeHistExample example = new BlockingTypeHistExample();
        example.createCriteria().andIdEqualTo(blockingTypeId);
        example.setOrderByClause("date_rec_modified desc");
        return blockingTypeHistMapper.selectByExample(example);
    }
}
