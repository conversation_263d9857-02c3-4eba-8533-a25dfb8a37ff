package za.co.ipay.metermng.server.mybatis.service;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountHistMapper;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.AccountTransExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountHist;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountHistExample;

import java.util.List;

public class AuxAccountHistService {
    private AuxAccountHistMapper auxAccountHistMapper;

    @Transactional(readOnly = true)
    public Long getLatestAuxAccountHistoryIdById(Long auxAccountId) {
        AuxAccountHistExample example = new AuxAccountHistExample();
        example.createCriteria().andIdEqualTo(auxAccountId);
        example.setOrderByClause("date_rec_modified desc");
        List<AuxAccountHist> list = auxAccountHistMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));

        if (list == null || list.isEmpty()) {
            return null;
        }

        return list.get(0).getAuxAccountHistId();
    }
    @Transactional(readOnly = true)
    public Long getLatestAuxAccountHistoryIdByCustomerAgreementId(Long customerAgreementId) {
        AuxAccountHistExample example = new AuxAccountHistExample();
        example.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        example.setOrderByClause("date_rec_modified desc");
        List<AuxAccountHist> auxAccountHistList = auxAccountHistMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));

        if (auxAccountHistList != null && !auxAccountHistList.isEmpty()) {
            return auxAccountHistList.get(0).getAuxAccountHistId();
        }
        return 0L;
    }
    @Transactional(readOnly = true)
    public List<AuxAccountHist> getAuxAccountHistoryByCustomerAgreementId(Long customerAgreementId) {
        AuxAccountHistExample example = new AuxAccountHistExample();
        example.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        return auxAccountHistMapper.selectByExample(example);
    }

    // Setter for auxAccountHistMapper
    public void setAuxAccountHistMapper(AuxAccountHistMapper auxAccountHistMapper) {
        this.auxAccountHistMapper = auxAccountHistMapper;
    }
}
