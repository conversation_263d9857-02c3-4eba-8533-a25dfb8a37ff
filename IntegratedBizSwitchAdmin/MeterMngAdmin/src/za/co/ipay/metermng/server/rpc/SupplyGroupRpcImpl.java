package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.server.rpc.RemoteServiceServlet;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.SupplyGroupRpc;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.server.mybatis.service.SupplyGroupService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.dto.ScreenData;
import za.co.ipay.metermng.shared.dto.StsSupplyGroupDto;

public class SupplyGroupRpcImpl extends RemoteServiceServlet implements SupplyGroupRpc {

    private static final long serialVersionUID = 1L;
    
    private SupplyGroupService supplyGroupService;
    
    @Override
    public StsSupplyGroup getSupplyGroupByTargetId(Long supplyGroupId) throws ServiceException {
        return supplyGroupService.getSupplyGroupByTargetId(supplyGroupId);
    }
    
    @Override
    public ScreenData getKeyRevisionNumbers() throws ServiceException {
        // Note: hard coded revision numbers - load from db?
        List<String> revisionNumbers = new ArrayList<String>();
        for(int i=1;i<10;i++) {
            revisionNumbers.add(Integer.toString(i));
        }
        ScreenData screenData = new ScreenData();
        screenData.addData("revisionNumbers", revisionNumbers);
        return screenData;
    }

    @Override
    public int getSupplyGroupCount() throws ServiceException {
        return supplyGroupService.getSupplyGroupCount();
    }

    @Override
    public List<StsSupplyGroupDto> getSupplyGroups(int startRow, int pageSize, String sortField, boolean isAscending) 
        throws ServiceException {
        return supplyGroupService.getSupplyGroups(startRow, pageSize, sortField, isAscending);
    }

    @Override
    public StsSupplyGroupDto updateSupplyGroup(StsSupplyGroupDto supplyGroupDto) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(supplyGroupDto);
        return supplyGroupService.updateSupplyGroup(supplyGroupDto);
    }
    
    public void setSupplyGroupService(SupplyGroupService supplyGroupService) {
        this.supplyGroupService = supplyGroupService;
    }
}
