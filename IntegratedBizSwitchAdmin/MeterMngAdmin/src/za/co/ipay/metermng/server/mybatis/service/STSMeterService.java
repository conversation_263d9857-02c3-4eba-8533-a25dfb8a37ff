package za.co.ipay.metermng.server.mybatis.service;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.mybatis.custom.mapper.StsMeterCustomMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterExample;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;

public class STSMeterService {
    
    private StsMeterMapper stsMeterMapper;
    private StsMeterCustomMapper stsMeterCustomMapper;
    
    @Transactional(readOnly=true)
    public StsMeter getMeterByMeterNumber(String meterNumber) {
        if (meterNumber == null || meterNumber.trim().equals("")) {
            return null;
        }
        StsMeterExample ex = new StsMeterExample();
        ex.createCriteria().andMeterNumEqualTo(meterNumber);
        List<StsMeter> list = stsMeterMapper.selectByExample(ex);
        if(list == null || list.size() < 1)
            return null;
        StsMeter meter = list.get(0);
        return meter;
    }
    
    @Transactional(readOnly=true)
    public StsMeter getMeterById(Long meterId) {
        if (meterId != null) {
            return stsMeterMapper.selectByPrimaryKey(meterId);
        } else {
            return null;
        }
    }
    
    @Transactional
    public StsMeter updateStsMeter(StsMeter stsMeter) throws ValidationException, ServiceException {
        return updateStsMeter(stsMeter, null);
    }
    
    @Transactional
    public StsMeter updateStsMeter(StsMeter stsMeter, Boolean isGenKeyChangeNone) throws ValidationException, ServiceException {
        //Validate
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(stsMeter);     
        // Check meterNumber is not duplicate
        StsMeter existing = getMeterByMeterNumber(stsMeter.getMeterNum());
        if (existing != null && !existing.getId().equals(stsMeter.getId())) {
            throw new ValidationException(new ValidationMessage("meter.error.alreadyexists", true));
        }

        // if not generating keychange but current supplygroup has changed then set last tid to null
        if (isGenKeyChangeNone != null && isGenKeyChangeNone 
                && existing != null 
                && !existing.getStsCurrSupplyGroupId().equals(stsMeter.getStsCurrSupplyGroupId())) {
            stsMeterCustomMapper.updateStsMeterLastTid(stsMeter.getMeterNum(), null);
        }
        
        //Save or update the meter
    	if (existing == null) {
            if (stsMeterMapper.insert(stsMeter) != 1) {
                throw new ServiceException("stsmeter.error.save", true);
            }
    	} else {
            if (stsMeterMapper.updateByPrimaryKey(stsMeter) != 1) {
                throw new ServiceException("stsmeter.error.save", true);
            }
    	}    	
    	return stsMeter;
    }   
    
    @Transactional
    public void updateStsMeterPowerLimit(StsMeter stsMeterDb, BigDecimal powerLimit, String powerLimitLabel) {
        // todo: creating a new object and then using update selective is dangerous, do not do this.
        //  If the stsMeter object has default values for fields not being set the default values will be saved
        StsMeter stsMeter = new StsMeter();
        stsMeter.setId(stsMeterDb.getId());
        stsMeter.setPowerLimit(powerLimit);
        stsMeter.setPowerLimitLabel(powerLimitLabel);
        stsMeter.setMridExternal(stsMeterDb.isMridExternal());
        stsMeter.setThreeTokens(stsMeterDb.isThreeTokens());
        stsMeterMapper.updateByPrimaryKeySelective(stsMeter);
    }
    
    public void setStsMeterMapper(StsMeterMapper stsMeterMapper) {
        this.stsMeterMapper = stsMeterMapper;
    }   
    
    public void setStsMeterCustomMapper(StsMeterCustomMapper stsMeterCustomMapper) {
        this.stsMeterCustomMapper = stsMeterCustomMapper;
    }
}
