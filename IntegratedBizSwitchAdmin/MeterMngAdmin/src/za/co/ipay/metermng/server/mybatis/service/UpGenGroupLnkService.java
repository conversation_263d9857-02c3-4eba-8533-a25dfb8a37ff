package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.HashMap;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.custom.mapper.IGenGroupMapper;
import za.co.ipay.metermng.mybatis.custom.model.GenGroupDepth;
import za.co.ipay.metermng.mybatis.generated.mapper.UpGenGroupLnkMapper;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.UpGenGroupLnk;
import za.co.ipay.metermng.mybatis.generated.model.UpGenGroupLnkExample;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;

public class UpGenGroupLnkService {

    private UpGenGroupLnkMapper upgLnkMapper;
    private IGenGroupMapper iGenGroupMapper;

    public void setUpGenGroupLnkMapper(UpGenGroupLnkMapper upGenGroupLnkMapper) {
        this.upgLnkMapper = upGenGroupLnkMapper;
    }

    public void setIGenGroupMapper(IGenGroupMapper iGenGroupMapper) {
        this.iGenGroupMapper = iGenGroupMapper;
    }
    
    private static final Logger logger = Logger.getLogger("UpGenGroupLink");
    @Transactional(readOnly = true)
    public ArrayList<UpGenGroupLinkData> getUpGenGroupListByUsagePointId(Long usagePointId) {
        UpGenGroupLnkExample usagePointGroupLnkExample = new UpGenGroupLnkExample();
        usagePointGroupLnkExample.createCriteria().andUsagePointIdEqualTo(usagePointId);
        List<UpGenGroupLnk> list = upgLnkMapper.selectByExample(usagePointGroupLnkExample);
        ArrayList<UpGenGroupLinkData> returnData = new ArrayList<>(list.size());

        if (list == null || list.size() < 1) {
            return null; // This is bad - return an empty collection is standard Java convention
        } else {
            for (UpGenGroupLnk aList : list) {
                UpGenGroupLinkData upGenGroupLinkData = new UpGenGroupLinkData(aList);
                // add group type
                upGenGroupLinkData.setGroupTypeId(iGenGroupMapper.getGroupTypeIdByGenGroupId(upGenGroupLinkData.getGenGroupId()));
                // add depth path to root
                ArrayList<Long> depthList = getPath(upGenGroupLinkData.getGenGroupId());

                Map<String, String> metadata = new HashMap<>();
                List<GenGroup> genGroupList = iGenGroupMapper.getMetadataByGenGroupId(upGenGroupLinkData.getGenGroupId());

                for (GenGroup genGroup : genGroupList) {
                    String name = genGroup.getName();
                    String metadataValue = genGroup.getMetadata();
                    if(metadataValue != null) {
                        metadata.put(name, metadataValue);
                    }
                    // custAccNotifyId can only have a value for one of the entries
                    upGenGroupLinkData.setCustAccNotifyId(genGroup.getCustAccNotifyId());
                }
                upGenGroupLinkData.setMetadata(metadata);
                upGenGroupLinkData.setDepthList(depthList);
                returnData.add(upGenGroupLinkData);
            }
        }
        return returnData;
    }

    @Transactional(readOnly = true)
    public ArrayList<Long> getPath(Long groupId) {
        ArrayList<Long> depthList = new ArrayList<Long>();
        List<GenGroupDepth> serverList = iGenGroupMapper.getPathToRootGroup(groupId);
        Long genGroupId = null;
        for (int j = 0; j < serverList.size(); j++) {
            genGroupId = serverList.get(j).getGenGroupId();
            depthList.add(genGroupId);
        }
        return depthList;
    }
    
    @Transactional(readOnly = true)
    public List<UpGenGroupLnk> getUpGenGroupListFromGroups(int start, int pageSize, List<Long> genGroupIdList) {
        RowBounds rowBounds = new RowBounds(start, pageSize);
        UpGenGroupLnkExample example = new UpGenGroupLnkExample();
        example.createCriteria().andGenGroupIdIn(genGroupIdList);
        List<UpGenGroupLnk> genGroupLinkList = upgLnkMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (genGroupLinkList.size() > 0) {
            return genGroupLinkList;
        } else {
            return new ArrayList<UpGenGroupLnk>();
        }
    }
    
    @Transactional(readOnly = true)
    public boolean isGroupAssigned(Long groupId) {
        UpGenGroupLnkExample example = new UpGenGroupLnkExample();
        example.createCriteria().andGenGroupIdEqualTo(groupId);
        return (upgLnkMapper.countByExample(example) > 0);
    }
}