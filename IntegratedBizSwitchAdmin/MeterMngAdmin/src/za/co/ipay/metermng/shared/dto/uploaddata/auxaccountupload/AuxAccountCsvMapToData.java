package za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload;

import java.util.HashMap;

import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.bulkupload.ICsvMapToData;

public class AuxAccountCsvMapToData implements ICsvMapToData {

	private HashMap<String, String> csvHeadingToDataNameMap = new HashMap<String, String>();
	private HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();

	public AuxAccountCsvMapToData() {

		csvHeadingToDataNameMap.put("Identifier Type", "identifierType");
		csvHeadingToDataNameMap.put("Identifier", "identifier");
		csvHeadingToDataNameMap.put("Aux Account Name", "auxAccountName");
		csvHeadingToDataNameMap.put("Aux Account Type", "auxTypeName");
		csvHeadingToDataNameMap.put("Account Priority", "auxAccountPriority");
		csvHeadingToDataNameMap.put("Charge Schedule Name", "chargeScheduleName");
		csvHeadingToDataNameMap.put("Principle Amount", "principleAmount");
		csvHeadingToDataNameMap.put("Balance", "balance");
		csvHeadingToDataNameMap.put("Balance Type", "balanceType");
		csvHeadingToDataNameMap.put("Suspend Until: yyyy-MM-dd HH:mm:ss", "suspendUntil");
		csvHeadingToDataNameMap.put("Start Date: yyyy-MM-dd HH:mm:ss", "startDate");
	}

	public HashMap<Integer, String> constructCsvFieldMap(String stringOfHeadingsCommaSeperated) throws BulkUploadException {
		String[] strArray = stringOfHeadingsCommaSeperated.split(",");
		for (int i = 0; i < strArray.length; i++) {
			String dataName = csvHeadingToDataNameMap.get(strArray[i].trim());
			if (dataName == null) {
				throw new BulkUploadException(ICsvMapToData.UNKNOWN_COLUMN_HEADING + ": >" + strArray[i].trim() + "<");
			}
			csvFieldMap.put(i, dataName);
		}
		return csvFieldMap;
	}

	public HashMap<String, String> getCsvHeadingToDataNameMap() {
		return csvHeadingToDataNameMap;
	}

}
