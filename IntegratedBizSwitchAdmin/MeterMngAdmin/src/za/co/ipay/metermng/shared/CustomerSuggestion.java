package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

public class CustomerSuggestion implements IsSerializable, Suggestion {

    private String suggestion;
    private String customerId;
    private String customerName;
    private String customerIdNumber;
    private boolean unassigned;
    private String originalQuery;

    public CustomerSuggestion() {
    }

    public CustomerSuggestion(String suggestion, String custId, String customerName, String customerIdNumber) {
        this.suggestion = suggestion;
        this.customerId = custId;
        this.customerName = customerName;
        this.customerIdNumber = customerIdNumber;
    }

    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        return suggestion.substring(0, suggestion.indexOf(','));
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestions) {
        this.suggestion = suggestions;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    public boolean isUnassigned() {
        return unassigned;
    }

    public void setUnassigned(boolean unassigned) {
        this.unassigned = unassigned;
    }

    public String getOriginalQuery() {
        return originalQuery;
    }

    public void setOriginalQuery(String originalQuery) {
        this.originalQuery = originalQuery;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("CustomerSuggestion [suggestion=");
        builder.append(suggestion);
        builder.append(", customerId=");
        builder.append(customerId);
        builder.append(", customerName=");
        builder.append(customerName);
        builder.append(", customerIdNumber=");
        builder.append(customerIdNumber);
        builder.append(", unassigned=");
        builder.append(unassigned);
        builder.append(", originalQuery=");
        builder.append(originalQuery);
        builder.append("]");
        return builder.toString();
    }

}
