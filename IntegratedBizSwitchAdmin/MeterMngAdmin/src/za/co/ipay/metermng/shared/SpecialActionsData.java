package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

@SuppressWarnings("serial")
public class SpecialActionsData extends SpecialActions {
    public static final String FREE_TOKEN_ISSUE = "free_token_issue";
    public static final String ADD_AUX_ACCOUNT = "add_aux_account";
    public static final String UPDATE_AUX_ACCOUNT = "update_aux_account";
    public static final String ADJUST_AUX_ACCOUNT = "adjust_aux_account";
    public static final String REASSIGN_METER = "reassign_meter";
    public static final String REMOVE_METER = "remove_meter";
    public static final String DEACTIVATE_USAGE_POINT = "deactivate_usage_point";
    public static final String CHANGE_PRICING_STRUCTURE = "change_pricing_structure";
    public static final String BLOCK_USAGE_POINT = "block_usage_point";
    public static final String UNBLOCK_USAGE_POINT = "unblock_usage_point";
    public static final String ACTIVATE_USAGE_POINT = "activate_usage_point";
    public static final String REVERSE_TRANSACTION = "reverse_transaction";
    public static final String UNASSIGN_CUSTOMER = "unassign_customer";
    public static final String INSPECT_METER = "inspect_meter";
    public static final String CHARGE_WRITEOFF = "charge_writeoff";
}
