package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.dto.IdValueNameDto;

public class AddMeterReadingScreenDataDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    protected ArrayList<IdValueNameDto> paymentModes;
    protected ArrayList<IdValueNameDto> readingTypes;
    
    public AddMeterReadingScreenDataDto() {
        this.paymentModes = new ArrayList<IdValueNameDto>();
        this.readingTypes = new ArrayList<IdValueNameDto>();
    }

    public ArrayList<IdValueNameDto> getPaymentModes() {
        return paymentModes;
    }

    public void setPaymentModes(ArrayList<IdValueNameDto> paymentModes) {
        this.paymentModes = paymentModes;
    }

    public ArrayList<IdValueNameDto> getReadingTypes() {
        return readingTypes;
    }

    public void setReadingTypes(ArrayList<IdValueNameDto> readingTypes) {
        this.readingTypes = readingTypes;
    }    
}
