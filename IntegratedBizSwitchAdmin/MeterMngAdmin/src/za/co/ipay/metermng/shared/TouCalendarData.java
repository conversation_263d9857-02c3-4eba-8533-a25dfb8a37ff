package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.HashMap;

import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;

import com.google.gwt.user.client.rpc.IsSerializable;

public class TouCalendarData extends TouCalendar implements IsSerializable {

    private static final long serialVersionUID = -6272344939276027620L;

    private ArrayList<TouSeasonDateData> assignedSeasons = new ArrayList<TouSeasonDateData>();
    private ArrayList<TouDayProfileData> dayProfilesList = new ArrayList<TouDayProfileData>();
    private HashMap<Long, TouDayProfile> assignedDayProfiles = new HashMap<Long, TouDayProfile>();
    private ArrayList<TouCalendarSeason> calendarSeasons = new ArrayList<TouCalendarSeason>();
    private ArrayList<PricingStructure>  pricingStructures = null;
   
    public TouCalendarData() {
        
    }
    public TouCalendarData(TouCalendar touCalendar) {
        setTouCalendar(touCalendar);
    }

    public void setTouCalendar(TouCalendar touCalendar) {
        this.setId(touCalendar.getId());
        this.setName(touCalendar.getName());
        this.setDescription(touCalendar.getDescription());
        this.setRecordStatus(touCalendar.getRecordStatus());
    }
    
    public void setAssignedSeasons( ArrayList<TouSeasonDateData> seasondates) {
        this.assignedSeasons = seasondates;
    }
    
    public ArrayList<TouSeasonDateData> getAssignedSeasons() {
        return assignedSeasons;
    }
    
    public TouDayProfile getAssignedDayProfile(Long dayprofileId) {
        return assignedDayProfiles.get(dayprofileId);
    }
    
    public void setAssignedDayProfiles(ArrayList<TouDayProfileData> dayprofiles) {
        dayProfilesList= dayprofiles;
        for (TouDayProfileData dp : dayprofiles) {
            assignedDayProfiles.put(dp.getId(), dp);
        }
    }
    
    public ArrayList<TouDayProfileData> getAssignedDayProfileList() {
        return dayProfilesList;
    }
    
    public ArrayList<TouCalendarSeason> getCalendarSeasons() {
        return calendarSeasons;
    }
    
    public void setCalendarSeasons(ArrayList<TouCalendarSeason> calendarSeasons) {
        this.calendarSeasons = calendarSeasons;
    }
    
    public ArrayList<PricingStructure> getPricingStructures() {
        return pricingStructures;
    }
    
    public void setPricingStructures(ArrayList<PricingStructure> pricingStructures) {
        this.pricingStructures = pricingStructures;
    }
   
    
    
}
