package za.co.ipay.metermng.shared.dto.user;

import za.co.ipay.accesscontrol.gwt.shared.GWTAdminUser;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;

/**
 * MeterMngUser is a wrapper around the access control user and their assigned Meter Management group as well as their
 * current group which is chosen by the user on the UI to view various different data.
 * <AUTHOR>
 */
public class MeterMngUser extends GWTAdminUser {

    private static final long serialVersionUID = 1L;
    
    /** The user's organisation or null if they are a super user. */
    protected String organisation;

    /** The full name of the use'rs*/
    protected String organisationFullName;

    /** The actual group that the user is linked to for their group. */
    protected GenGroup assignedGroup;
    /** The group that the user is currently using to access data. It must be either the same as their assigned group 
     *  or a child of their assigned group or it can be null if the user has no assigned group. */
    protected GenGroup currentGroup;   
    /** Whether must choose a leaf node for the currentGroup - set up by loginListener and checked in MetermngAdmin at set up time*/
    protected boolean chooseCurrentGroupleafNode;
    
    /** Whether the user is authenticated through LDAP. */
    private boolean ldapAuth = false;
    /** Whether the user's password has expired. */
    protected boolean passwordExpired = false;
    /** Whether the user's password needs to be reset. */
    protected boolean passwordRequiresReset = false;    
    /** Any warning message about the user's password. */
    protected ValidationMessage passwordWarningMsg;  
    
    /** Default empty constructor for GWT, etc. */
    public MeterMngUser() {
        super();
    }
    
    /** Copy style constructor using GWTAdminUser. */
    public MeterMngUser(GWTAdminUser adminUser) {
        super();
        if (adminUser != null) {
            this.id = adminUser.getId();
            this.userName = adminUser.getUserName();
            this.permissions = adminUser.getPermissions();
            this.roles = adminUser.getRoles();   
            this.organisation = adminUser.getManco();
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MeterMngUser: ");
        sb.append(" id:").append(getId());
        sb.append(" username:").append(getUserName());
        sb.append(" assignedGroup:");
        if (assignedGroup != null) {
            sb.append(assignedGroup.getName());
        }
        sb.append(" currentGroup:");
        if (currentGroup != null) {
            sb.append(currentGroup.getName());
        }
        sb.append(" roles:").append(getRoles().toString()); 
        sb.append(" permissions:").append(getPermissions().toString());  
        sb.append(" organisation:").append(organisation);
        return sb.toString();
    }

    public GenGroup getAssignedGroup() {
        return assignedGroup;
    }

    public void setAssignedGroup(GenGroup assignedGroup) {
        this.assignedGroup = assignedGroup;
    }

    public Long getCurrentGroupId() {
        if (currentGroup != null) {
            return currentGroup.getId();
        } else {
            return null;
        }
    }

    public GenGroup getCurrentGroup() {
        return currentGroup;
    }

    public void setCurrentGroup(GenGroup currentGroup) {
        this.currentGroup = currentGroup;
    }

    public boolean isPasswordExpired() {
        return passwordExpired;
    }

    public void setPasswordExpired(boolean passwordExpired) {
        this.passwordExpired = passwordExpired;
    }

    public boolean isPasswordRequiresReset() {
        return passwordRequiresReset;
    }

    public void setPasswordRequiresReset(boolean passwordRequiresReset) {
        this.passwordRequiresReset = passwordRequiresReset;
    }

    public ValidationMessage getPasswordWarningMsg() {
        return passwordWarningMsg;
    }

    public void setPasswordWarningMsg(ValidationMessage passwordWarningMsg) {
        this.passwordWarningMsg = passwordWarningMsg;
    }

    public boolean isLdapAuth() {
        return ldapAuth;
    }

    public void setLdapAuth(boolean ldapAuth) {
        this.ldapAuth = ldapAuth;
    }

    public String getOrganisation() {
        return organisation;
    }

    public void setOrganisation(String organisation) {
        this.organisation = organisation;
    }

    public String getOrganisationFullName() {
        return organisationFullName;
    }

    public void setOrganisationFullName(String organisationFullName) {
        this.organisationFullName = organisationFullName;
    }

    public boolean isChooseCurrentGroupleafNode() {
        return chooseCurrentGroupleafNode;
    }

    public void setChooseCurrentGroupleafNode(boolean chooseCurrentGroupleafNode) {
        this.chooseCurrentGroupleafNode = chooseCurrentGroupleafNode;
    }
    
}
