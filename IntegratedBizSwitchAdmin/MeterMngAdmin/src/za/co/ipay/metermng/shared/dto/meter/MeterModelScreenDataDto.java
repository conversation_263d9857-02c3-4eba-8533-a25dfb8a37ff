package za.co.ipay.metermng.shared.dto.meter;

import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;

public class MeterModelScreenDataDto extends PtrScreenDataDto {

    private static final long serialVersionUID = 1L;

    private ArrayList<IdNameDto> manufacturers;
    protected ArrayList<IdNameDto> mdcs;
    protected ArrayList<IdNameDto> decoders;

    public ArrayList<IdNameDto> getManufacturers() {
        return manufacturers;
    }

    public void setManufacturers(ArrayList<IdNameDto> manufacturers) {
        this.manufacturers = manufacturers;
    }

    public ArrayList<IdNameDto> getMdcs() {
        return mdcs;
    }

    public void setMdcs(ArrayList<IdNameDto> mdcs) {
        this.mdcs = mdcs;
    }

    public ArrayList<IdNameDto> getDecoders() {
        return decoders;
    }

    public void setDecoders(ArrayList<IdNameDto> decoders) {
        this.decoders = decoders;
    }

}
