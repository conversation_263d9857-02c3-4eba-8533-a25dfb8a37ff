package za.co.ipay.metermng.shared.dto.usagepoint;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;

public class MeterUpMdcChannelInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long meterId;
    private Long meterModelId;
    private Long usagePointId;
    private Long pricingStructureId;
    private Long upMeterInstallId;
    private Date installationDate;
    private List<MdcChannelReadingsDto> channelList;
    
    private boolean preExistingReadings;                   //eg. change meterModel and then update UP
    private boolean preExistingDiffMdc;                    //eg. change meterModel to one with diff MDC
    private Date lastReadingDateExistingOldReadings;       
    
    public MeterUpMdcChannelInfo() {
    }

    public MeterUpMdcChannelInfo(Long meterId, Long meterModelId, Long usagePointId, Long pricingStructureId, Long upMeterInstallId, Date installationDate) {
        this.meterId = meterId;
        this.meterModelId = meterModelId;
        this.usagePointId = usagePointId;
        this.pricingStructureId = pricingStructureId;
        this.upMeterInstallId = upMeterInstallId;
        this.installationDate = installationDate;
    }

    public Long getMeterId() {
        return meterId;
    }

    public void setMeterId(Long meterId) {
        this.meterId = meterId;
    }

    public Long getMeterModelId() {
        return meterModelId;
    }

    public void setMeterModelId(Long meterModelId) {
        this.meterModelId = meterModelId;
    }
    
    public Long getUsagePointId() {
        return usagePointId;
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
    }

    public Long getPricingStructureId() {
        return pricingStructureId;
    }

    public void setPricingStructureId(Long pricingStructureId) {
        this.pricingStructureId = pricingStructureId;
    }
    
    public Long getUpMeterInstallId() {
        return upMeterInstallId;
    }

    public void setUpMeterInstallId(Long upMeterInstallId) {
        this.upMeterInstallId = upMeterInstallId;
    }

    public Date getInstallationDate() {
        return installationDate;
    }

    public void setInstallationDate(Date installationDate) {
        this.installationDate = installationDate;
    }

    public List<MdcChannelReadingsDto> getChannelList() {
        return channelList;
    }

    public void setChannelList(List<MdcChannelReadingsDto> channelList) {
        this.channelList = channelList;
    }

    public boolean isPreExistingReadings() {
        return preExistingReadings;
    }

    public void setPreExistingReadings(boolean preExistingReadings) {
        this.preExistingReadings = preExistingReadings;
    }

    public boolean isPreExistingDiffMdc() {
        return preExistingDiffMdc;
    }

    public void setPreExistingDiffMdc(boolean preExistingDiffMdc) {
        this.preExistingDiffMdc = preExistingDiffMdc;
    }

    public Date getLastReadingDateExistingOldReadings() {
        return lastReadingDateExistingOldReadings;
    }

    public void setLastReadingDateExistingOldReadings(Date lastReadingDateExistingOldReadings) {
        this.lastReadingDateExistingOldReadings = lastReadingDateExistingOldReadings;
    }

}
