package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.custom.model.MeterDto;

public class SuperSubMeterDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long superMeterId;
    private ArrayList<MeterDto> subMeters;
    
    public SuperSubMeterDto() {
        this.subMeters = new ArrayList<MeterDto>();
    }

    public Long getSuperMeterId() {
        return superMeterId;
    }

    public void setSuperMeterId(Long superMeterId) {
        this.superMeterId = superMeterId;
    }

    public ArrayList<MeterDto> getSubMeters() {
        return subMeters;
    }

    public void setSubMeters(ArrayList<MeterDto> subMeters) {
        this.subMeters = subMeters;
    }    
}
