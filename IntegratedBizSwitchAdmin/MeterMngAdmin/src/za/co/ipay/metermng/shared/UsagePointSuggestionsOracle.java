package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

import com.google.gwt.user.client.ui.SuggestOracle;

public class UsagePointSuggestionsOracle extends SuggestOracle {

    private ClientFactory clientFactory;
    private boolean withPricingStructureInfo = false;
    
    private ArrayList<Suggestion> storeSuggestions;

    private static Logger logger = Logger.getLogger(UsagePointSuggestionsOracle.class.getName());

    public UsagePointSuggestionsOracle(ClientFactory clientFactory, boolean withPricingStructureInfo) {
        super();
        this.clientFactory = clientFactory;
        this.withPricingStructureInfo = withPricingStructureInfo;
    }

    @Override
    public void requestSuggestions(Request request, Callback callback) {
        if (request.getQuery().trim().length() > 2) {
            logger.info("Getting usagePoint suggestions: "+request.getQuery());
            clientFactory.getSearchRpc().getUsagePointSuggestions(request, withPricingStructureInfo, request.getLimit(), new ItemRequestCallback(request, callback));
        }
    }

    public boolean isDisplayStringHTML() {
        return true;
    }

    public class ItemRequestCallback extends ClientCallback<Response> {
        
        private SuggestOracle.Request req;
        private SuggestOracle.Callback callback;

        public void onSuccess(Response result) {
            storeSuggestions = (ArrayList<Suggestion>) result.getSuggestions();
            callback.onSuggestionsReady(req, result);
        }

        public ItemRequestCallback(SuggestOracle.Request _req, SuggestOracle.Callback _callback) {
            this.req = _req;
            this.callback = _callback;
        }
    }
    
    public UsagePointSuggestion getFirstSuggestion() {
        return (UsagePointSuggestion) (storeSuggestions).get(0);
    }
}
