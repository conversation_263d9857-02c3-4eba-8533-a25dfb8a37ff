package za.co.ipay.metermng.shared.dto.pricing;

import java.io.Serializable;
import java.util.List;

import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDay;

/**
 * TouCalendarSeasonsPeriodsSpecialDaysData is a wrapper around a calendar's seasons, periods and special days.
 * <AUTHOR>
 */
public class TouCalendarSeasonsPeriodsSpecialDaysData implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long touCalendarId;
    private List<TouSeason> seasons;
    private List<TouPeriod> periods;   
    private List<SpecialDayProfile> specialDays;

    public Long getTouCalendarId() {
        return touCalendarId;
    }

    public void setTouCalendarId(Long touCalendarId) {
        this.touCalendarId = touCalendarId;
    }

    public List<TouSeason> getSeasons() {
        return seasons;
    }

    public void setSeasons(List<TouSeason> seasons) {
        this.seasons = seasons;
    }

    public List<TouPeriod> getPeriods() {
        return periods;
    }

    public void setPeriods(List<TouPeriod> periods) {
        this.periods = periods;
    }

    public List<SpecialDayProfile> getSpecialDays() {
        return specialDays;
    }

    public void setSpecialDays(List<SpecialDayProfile> specialDays) {
        this.specialDays = specialDays;
    }    
}
