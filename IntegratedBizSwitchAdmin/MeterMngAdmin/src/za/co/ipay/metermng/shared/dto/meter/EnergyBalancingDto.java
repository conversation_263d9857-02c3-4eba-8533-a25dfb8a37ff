package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * EnergyBalancingDto is a wrapper around the querying of a super meter's reading vs all the sub-meters readings 
 * totalled. This is used to display whether the super meter's reading is the same as the totalled sub-meter readings. 
 * 
 * <AUTHOR>
 */
public class EnergyBalancingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /** The super meter's id. */
    private Long superMeterId;
    /** The super meter's number. */
    private String superMeterNumber;
    /** The super meter's actual reading. */
    private BigDecimal superMeterReading;
    /** The total of all the sub-meters readings. */
    private BigDecimal totalSubMeters;
    /** The % variation between the super meter's total and the sub-meters total. */
    private BigDecimal variation;
    
    public EnergyBalancingDto() {
        this.superMeterNumber = "";
        this.superMeterReading = BigDecimal.ZERO;
        this.totalSubMeters = BigDecimal.ZERO;
        this.variation = BigDecimal.ZERO;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("EnergyBalancingDto: ");
        sb.append(" superMeterId:").append(superMeterId);
        sb.append(" superMeterNumber:").append(superMeterNumber);
        sb.append(" superMeterReading:").append(superMeterReading);
        sb.append(" totalSubMeters:").append(totalSubMeters);
        sb.append(" variation:").append(variation);
        return sb.toString();
    }

    public Long getSuperMeterId() {
        return superMeterId;
    }

    public void setSuperMeterId(Long superMeterId) {
        this.superMeterId = superMeterId;
    }

    public String getSuperMeterNumber() {
        return superMeterNumber;
    }

    public void setSuperMeterNumber(String superMeterNumber) {
        this.superMeterNumber = superMeterNumber;
    }

    public BigDecimal getSuperMeterReading() {
        return superMeterReading;
    }

    public void setSuperMeterReading(BigDecimal superMeterReading) {
        this.superMeterReading = superMeterReading;
    }

    public BigDecimal getTotalSubMeters() {
        return totalSubMeters;
    }

    public void setTotalSubMeters(BigDecimal totalSubMeters) {
        this.totalSubMeters = totalSubMeters;
    }

    public BigDecimal getVariation() {
        return variation;
    }

    public void setVariation(BigDecimal variation) {
        this.variation = variation;
    }    
}
