package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

import za.co.ipay.metermng.shared.MeterMngStatics;

public class FilterCriteria implements Serializable{
    private int startRow;
    private int pageSize;
    private String sortField;
    private boolean sortAscending;
    private Pair<String, Value> columnValuesFilter;

    public int getStartRow() {
        return startRow;
    }

    public FilterCriteria setStartRow(int startRow) {
        this.startRow = startRow;
        return this;
    }

    public int getPageSize() {
        return pageSize;
    }

    public FilterCriteria setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public String getSortField() {
        return sortField;
    }

    public FilterCriteria setSortField(String sortField) {
        this.sortField = sortField;
        return this;
    }

    public boolean isSortAscending() {
        return sortAscending;
    }

    public FilterCriteria setSortAscending(boolean sortAscending) {
        this.sortAscending = sortAscending;
        return this;
    }

    public Pair<String, Value> getColumnValuesFilter() {
        return columnValuesFilter;
    }

    public FilterCriteria setColumnValuesFilter(Pair<String, Value> columnValuesFilter) {
        this.columnValuesFilter = columnValuesFilter;
        return this;
    }

    public static class Value implements Serializable{
        private String value;
        private Type valueType;

        public List<String> getValues() {
            return Arrays.asList(value.split(","));
        }

        public String getValue(){
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Type getValueType() {
            return valueType;
        }

        public void setValueType(Type valueType) {
            this.valueType = valueType;
        }
    }
    public enum Type {
        IN, NOT_IN, LIKE, NOT_LIKE, EQUAL, NOT_EQUAL
    }

    public static final class Pair<K, V> implements Serializable {
        private K k;
        private V v;
        public Pair(){}
        public Pair(K k, V v){
         this.k = k; this.v = v;
        }

        public void setKey(K k) {
            this.k = k;
        }

        public void setValue(V v) {
            this.v = v;
        }

        public K getKey() {
            return k;
        }

        public V getValue() {
            return v;
        }
    }

    public static FilterCriteria createDefaultCriteria(){
        FilterCriteria f = new FilterCriteria();
        f.setPageSize(MeterMngStatics.DEFAULT_PAGE_SIZE);

        return f;
    }
}
