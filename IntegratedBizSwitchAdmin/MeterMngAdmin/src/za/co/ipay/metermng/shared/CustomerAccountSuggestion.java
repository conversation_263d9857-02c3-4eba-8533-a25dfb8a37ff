package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

public class CustomerAccountSuggestion implements IsSerializable, Suggestion {

    private String suggestion;
    private String accountName;
    private String originalQuery;
    private boolean unassigned;


    public CustomerAccountSuggestion() {
    }

    public CustomerAccountSuggestion(String suggestion, String accountName) {
        this.suggestion = suggestion;
        this.accountName = accountName;
    }

    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        return suggestion;
    }


    public String getSuggestion() {
		return suggestion;
	}

	public void setSuggestion(String suggestion) {
		this.suggestion = suggestion;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public boolean isUnassigned() {
		return unassigned;
	}

	public void setUnassigned(boolean unassigned) {
		this.unassigned = unassigned;
	}

	public String getOriginalQuery() {
		return originalQuery;
	}

	public void setOriginalQuery(String originalQuery) {
		this.originalQuery = originalQuery;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerAccountSuggestion [suggestion=")
				.append(suggestion).append(", accountName=").append(accountName)
				.append(", originalQuery=").append(originalQuery)
				.append(", unassigned=").append(unassigned).append("]");
		return builder.toString();
	}

}
