package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

public class CustomerTransItemOutstandCharges  implements IsSerializable {
    private static final long serialVersionUID = 1L;

    List<CustomerTransItemData> vendCustTransItemOutStand = new ArrayList<CustomerTransItemData>();
    List<CustomerTransItemData> billingCustTransItemOutStand = new ArrayList<CustomerTransItemData>();
    
    public CustomerTransItemOutstandCharges() {
    }

    public List<CustomerTransItemData> getVendCustTransItemOutStand() {
        return vendCustTransItemOutStand;
    }

    public void setVendCustTransItemOutStand(
            List<CustomerTransItemData> vendCustTransItemOutStand) {
        this.vendCustTransItemOutStand = vendCustTransItemOutStand;
    }

    public List<CustomerTransItemData> getBillingCustTransItemOutStand() {
        return billingCustTransItemOutStand;
    }

    public void setBillingCustTransItemOutStand(
            List<CustomerTransItemData> billingCustTransItemOutStand) {
        this.billingCustTransItemOutStand = billingCustTransItemOutStand;
    }
    
}
