package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;
import java.util.Date;
/*
 * DTO is for use by Bulk Keychange Export
 */
public class KeyChangeDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String meterNum;
    private String userRef;
    
    private String token1;
    private String token2;
    private String token3;
    private String token4;
    
    private String fromSupGroup;
    private Integer fromKeyRev;
    private String fromTariffIdx;
    private Short fromBaseDate;
    
    private String toSupGroup;
    private Integer toKeyRev;
    private String toTariffIdx;
    private Short toBaseDate;
    
    private Date transDate;
    private String userRecEntered;
    private String bulkRef;
    
    public KeyChangeDto() {
    }

    public String getMeterNum() {
        return meterNum;
    }

    public void setMeterNum(String meterNum) {
        this.meterNum = meterNum;
    }

    public String getUserRef() {
        return userRef;
    }

    public void setUserRef(String userRef) {
        this.userRef = userRef;
    }

    public String getToken1() {
        return token1;
    }

    public void setToken1(String token1) {
        this.token1 = token1;
    }

    public String getToken2() {
        return token2;
    }

    public void setToken2(String token2) {
        this.token2 = token2;
    }

    public String getToken3() {
        return token3;
    }

    public void setToken3(String token3) {
        this.token3 = token3;
    }

    public String getToken4() {
        return token4;
    }

    public void setToken4(String token4) {
        this.token4 = token4;
    }

    public String getFromSupGroup() {
        return fromSupGroup;
    }

    public void setFromSupGroup(String fromSupGroup) {
        this.fromSupGroup = fromSupGroup;
    }

    public Integer getFromKeyRev() {
        return fromKeyRev;
    }

    public void setFromKeyRev(Integer fromKeyRev) {
        this.fromKeyRev = fromKeyRev;
    }

    public String getFromTariffIdx() {
        return fromTariffIdx;
    }

    public void setFromTariffIdx(String fromTariffIdx) {
        this.fromTariffIdx = fromTariffIdx;
    }

    public Short getFromBaseDate() {
        return fromBaseDate;
    }

    public void setFromBaseDate(Short fromBaseDate) {
        this.fromBaseDate = fromBaseDate;
    }

    public String getToSupGroup() {
        return toSupGroup;
    }

    public void setToSupGroup(String toSupGroup) {
        this.toSupGroup = toSupGroup;
    }

    public Integer getToKeyRev() {
        return toKeyRev;
    }

    public void setToKeyRev(Integer toKeyRev) {
        this.toKeyRev = toKeyRev;
    }

    public String getToTariffIdx() {
        return toTariffIdx;
    }

    public void setToTariffIdx(String toTariffIdx) {
        this.toTariffIdx = toTariffIdx;
    }

    public Short getToBaseDate() {
        return toBaseDate;
    }

    public void setToBaseDate(Short toBaseDate) {
        this.toBaseDate = toBaseDate;
    }

    public Date getTransDate() {
        return transDate;
    }

    public void setTransDate(Date transDate) {
        this.transDate = transDate;
    }

    public String getUserRecEntered() {
        return userRecEntered;
    }

    public void setUserRecEntered(String userRecEntered) {
        this.userRecEntered = userRecEntered;
    }

    public String getBulkRef() {
        return bulkRef;
    }

    public void setBulkRef(String bulkRef) {
        this.bulkRef = bulkRef;
    }
}
