package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

public class SiteData implements IsSerializable {
    
    protected Long siteId = null;
    protected String siteErfNumber;
    protected String siteStreet;
    protected String siteSuburb;
    protected String siteTown;
    protected String siteState;
    protected boolean enabled;
    protected Double longitude;
    protected Double latitude;
    
    protected Long deliveryPointId;
    
    public Long getSiteId() {
        return siteId;
    }
    
    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }
    
    public String getSiteErfNumber() {
        return siteErfNumber;
    }
    
    public void setSiteErfNumber(String siteErfNumber) {
        this.siteErfNumber = siteErfNumber;
    }
    
    public String getSiteStreet() {
        return siteStreet;
    }
    
    public void setSiteStreet(String siteStreet) {
        this.siteStreet = siteStreet;
    }
    
    public String getSiteSuburb() {
        return siteSuburb;
    }
    
    public void setSiteSuburb(String siteSuburb) {
        this.siteSuburb = siteSuburb;
    }
    
    public String getSiteTown() {
        return siteTown;
    }
    
    public void setSiteTown(String siteTown) {
        this.siteTown = siteTown;
    }
    
    public String getSiteState() {
        return siteState;
    }
    
    public void setSiteState(String siteState) {
        this.siteState = siteState;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Long getDeliveryPointId() {
        return deliveryPointId;
    }

    public void setDeliveryPointId(Long deliveryPointId) {
        this.deliveryPointId = deliveryPointId;
    }
    
    
    
}
