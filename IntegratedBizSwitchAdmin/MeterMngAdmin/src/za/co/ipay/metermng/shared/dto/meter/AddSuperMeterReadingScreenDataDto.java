package za.co.ipay.metermng.shared.dto.meter;

import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.custom.model.MeterDto;

public class AddSuperMeterReadingScreenDataDto extends AddMeterReadingScreenDataDto {

    private static final long serialVersionUID = 1L;
    
    protected ArrayList<MeterDto> superMeters;
    
    public AddSuperMeterReadingScreenDataDto() {
        this.superMeters = new ArrayList<MeterDto>();
    }

    public ArrayList<MeterDto> getSuperMeters() {
        return superMeters;
    }

    public void setSuperMeters(ArrayList<MeterDto> superMeters) {
        this.superMeters = superMeters;
    }   
}
