package za.co.ipay.metermng.shared;


import java.util.ArrayList;
import java.util.Collection;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

import com.google.gwt.user.client.ui.MultiWordSuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle;

public class DayProfileSuggestionsOracle extends MultiWordSuggestOracle {

    private ClientFactory clientFactory;
    private Long calendarId;
    private Long seasonId;
    
    public DayProfileSuggestionsOracle(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }

    @Override
    public void requestSuggestions(Request request, Callback callback) {
        clientFactory.getCalendarRpc().getDayProfileSuggestions(calendarId, seasonId, request, new ItemRequestCallback(request,callback)); 
    }

    public boolean isDisplayStringHTML() {
        return true; 
    }

    public class ItemRequestCallback extends ClientCallback<Response>{
        private SuggestOracle.Request req; 
        private SuggestOracle.Callback callback; 

        public void onFailure(Throwable caught) {
            callback.onSuggestionsReady(req,new SuggestOracle.Response()); caught.printStackTrace();
        }

        public void onSuccess(Response result) {
            callback.onSuggestionsReady(req, result);
        }

        public ItemRequestCallback(SuggestOracle.Request _req,SuggestOracle.Callback _callback) {
            this.req=_req; this.callback=_callback;
        }
    }

    public Long getCalendarId() {
        return calendarId;
    }

    public void setCalendarId(final Long calendarId) {
        this.calendarId = calendarId;
        clientFactory.getCalendarRpc().getDayProfilesByCalendar(calendarId, new ClientCallback<ArrayList<TouDayProfileData>>() {
            @Override
            public void onSuccess(ArrayList<TouDayProfileData> result) {
                Collection<Suggestion> suggs = new ArrayList<Suggestion>(result.size());
                DayProfileSuggestion sug=null;
                for (TouDayProfileData thedata:result) {
                   sug = new DayProfileSuggestion(calendarId,thedata.getCode()+"-"+thedata.getName(),thedata.getId(),thedata.getName(), thedata.getCode(),true); 
                   suggs.add(sug);
                }
                
                
                clientFactory.getDayProfileSuggestionsOracle().setDefaultSuggestions(suggs);
            }
        });
    }
    
    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }
    
   
}
