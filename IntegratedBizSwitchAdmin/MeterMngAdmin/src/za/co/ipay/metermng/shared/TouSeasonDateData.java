package za.co.ipay.metermng.shared;
import java.util.ArrayList;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonDate;

public class TouSeasonDateData extends TouSeasonDate implements Comparable<TouSeasonDateData>, IsSerializable {
    
    private static final long serialVersionUID = -2165508659229076280L;
    
    private TouSeason season;
    private ArrayList<Integer> completemonths = new ArrayList<Integer>();
    
    public TouSeasonDateData() {
        super();
    }
    
    public TouSeasonDateData(TouSeasonDate touSeasonDate) {
        super();
        this.setId(touSeasonDate.getId());
        this.setTouCalendarId(touSeasonDate.getTouCalendarId());
        this.setTouSeasonId(touSeasonDate.getTouSeasonId());
        this.setStartDay(touSeasonDate.getStartDay());
        this.setStartMonth(touSeasonDate.getStartMonth());
        this.setEndDay(touSeasonDate.getEndDay());
        this.setEndMonth(touSeasonDate.getEndMonth());
        if (this.getId() != null)
            this.setCompleteMonths();
    }

    public TouSeason getSeason() {
        return season;
    }

    public void setSeason(TouSeason season) {
        this.season = season;
    }

    public ArrayList<Integer> getCompletemonths() {
        return completemonths;
    }

    public void setCompletemonths(ArrayList<Integer> completemonths) {
        this.completemonths = completemonths;
    }
   
    public void setCompleteMonths() {
        completemonths.clear();
        int sm = getStartMonth();
        int sd = getStartDay();
        int em = getEndMonth();
        int ed = getEndDay();
        
        if (sm == em) {
            if (sm != 2) {
                if (sd==1 && ed==getLastDayOfMonth(sm)) {
                    completemonths.add(sm);
                }
            } else {
                if (sd==1 && (ed==28 || ed==29)) {
                    completemonths.add(sm);
                }
            }
        } else {
            if (sd!=1) {
                sm++;
            }
            for (int i=0; i<(em-sm); i++) {
                completemonths.add(sm+i);
            } 
            if (em != 2) {
                if (ed==getLastDayOfMonth(em)) {
                    completemonths.add(em);
                }
            } else {
                if (ed==28 || ed==29) {
                    completemonths.add(em);
                }
            }
        }
    }

    
    public int getLastDayOfMonth(int month) {
        int lastday;
        switch (month) {
        case 4:
        case 6:
        case 9:
        case 11:
            lastday = 30;
            break;
        case 2:
            lastday = 29;
            break;
        default:
            lastday = 31;
            break;
        }
        return lastday;
    }

    @Override
    public int compareTo(TouSeasonDateData o) {
        int i = getStartMonth().compareTo(o.getStartMonth());
        return (i != 0 ? i : getStartDay().compareTo(o.getStartDay()));
    }
    
    
}
