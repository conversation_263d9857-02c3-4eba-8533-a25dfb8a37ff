package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;

import za.co.ipay.metermng.shared.ChannelCompatibilityE;

public class MdcChannelMatchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private ChannelCompatibilityE match;
    private Integer activeUpCount = 0;
    private Integer inactiveUpCount = 0;
    private Integer upWithThinUnitsPsCount = 0;
    
    public MdcChannelMatchDto() {
    }

    public ChannelCompatibilityE getMatch() {
        return match;
    }

    public void setMatch(ChannelCompatibilityE match) {
        this.match = match;
    }

    public Integer getActiveUpCount() {
        return activeUpCount;
    }

    public void setActiveUpCount(Integer activeUpCount) {
        this.activeUpCount = activeUpCount;
    }

    public Integer getInactiveUpCount() {
        return inactiveUpCount;
    }

    public void setInactiveUpCount(Integer inactiveUpCount) {
        this.inactiveUpCount = inactiveUpCount;
    }

    public Integer getUpWithThinUnitsPsCount() {
        return upWithThinUnitsPsCount;
    }

    public void setUpWithThinUnitsPsCount(Integer upWithThinUnitsPsCount) {
        this.upWithThinUnitsPsCount = upWithThinUnitsPsCount;
    }
}
