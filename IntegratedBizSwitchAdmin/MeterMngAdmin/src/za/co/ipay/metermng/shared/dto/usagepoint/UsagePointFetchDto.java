package za.co.ipay.metermng.shared.dto.usagepoint;

import java.io.Serializable;
import java.util.Date;

public class UsagePointFetchDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long accessGroupId;

    private Long customerAgreementId;
    private Long meterId;
    private Long meterModelId;
    private Long serviceResourceId;
    private Long mdcId;
    private Long usagePointId;
    
    private Long currentPricingStructureId;
    private Long futurePricingStructureId;
    private Date installDate;
    
    private String meterNum;
    private String usagePointName;
    private String pricingStructureName;
    private String meterModelName;
    private String mdcName;

    private Date firstTariffStartDate;
    private Date lastCyclicChargeDate;
    
    public UsagePointFetchDto() {
        //default constructor
    }

    public Long getAccessGroupId() {
        return accessGroupId;
    }

    public void setAccessGroupId(Long accessGroupId) {
        this.accessGroupId = accessGroupId;
    }

    public Long getCustomerAgreementId() {
        return customerAgreementId;
    }

    public void setCustomerAgreementId(Long customerId) {
        this.customerAgreementId = customerId;
    }

    public Long getMeterId() {
        return meterId;
    }

    public void setMeterId(Long meterId) {
        this.meterId = meterId;
    }

    public Long getMeterModelId() {
        return meterModelId;
    }

    public void setMeterModelId(Long meterModelId) {
        this.meterModelId = meterModelId;
    }

    public Long getServiceResourceId() {
        return serviceResourceId;
    }

    public void setServiceResourceId(Long serviceResourceId) {
        this.serviceResourceId = serviceResourceId;
    }

    public Long getMdcId() {
        return mdcId;
    }

    public void setMdcId(Long mdcId) {
        this.mdcId = mdcId;
    }

    public Long getUsagePointId() {
        return usagePointId;
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
    }

    public Long getCurrentPricingStructureId() {
        return currentPricingStructureId;
    }

    public void setCurrentPricingStructureId(Long currentPricingStructureId) {
        this.currentPricingStructureId = currentPricingStructureId;
    }

    public Long getFuturePricingStructureId() {
        return futurePricingStructureId;
    }

    public void setFuturePricingStructureId(Long futurePricingStructureId) {
        this.futurePricingStructureId = futurePricingStructureId;
    }

    public Date getInstallDate() {
        return installDate;
    }

    public void setInstallDate(Date installDate) {
        this.installDate = installDate;
    }

    public String getMeterNum() {
        return meterNum;
    }

    public void setMeterNum(String meterNum) {
        this.meterNum = meterNum;
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public String getPricingStructureName() {
        return pricingStructureName;
    }

    public void setPricingStructureName(String pricingStructureName) {
        this.pricingStructureName = pricingStructureName;
    }

    public String getMeterModelName() {
        return meterModelName;
    }

    public void setMeterModelName(String meterModelName) {
        this.meterModelName = meterModelName;
    }

    public String getMdcName() {
        return mdcName;
    }

    public void setMdcName(String mdcName) {
        this.mdcName = mdcName;
    }

    public Date getFirstTariffStartDate() {
        return firstTariffStartDate;
    }

    public void setFirstTariffStartDate(Date firstTariffStartDate) {
        this.firstTariffStartDate = firstTariffStartDate;
    }

    public Date getLastCyclicChargeDate() {
        return lastCyclicChargeDate;
    }

    public void setLastCyclicChargeDate(Date lastCyclicChargeDate) {
        this.lastCyclicChargeDate = lastCyclicChargeDate;
    }

    @Override
    public String toString() {
        return "UsagePointFetchDto [customerAgreementId=" + customerAgreementId
                + ", meterId=" + meterId + ", meterModelId=" + meterModelId
                + ", mdcId=" + mdcId + ", usagePointId=" + usagePointId
                + ", serviceResourceId=" + serviceResourceId
                + ", currentPricingStructureId=" + currentPricingStructureId
                + ", futurePricingStructureId=" + futurePricingStructureId
                + ", installDate=" + installDate + ", meterNum=" + meterNum
                + ", usagePointName=" + usagePointName
                + ", pricingStructureName=" + pricingStructureName
                + ", meterModelName=" + meterModelName + ", mdcName=" + mdcName
                + ", firstTariffStartDate=" + firstTariffStartDate
                + ", lastVendCyclicChargeDate=" + lastCyclicChargeDate + "]";
    }

}

