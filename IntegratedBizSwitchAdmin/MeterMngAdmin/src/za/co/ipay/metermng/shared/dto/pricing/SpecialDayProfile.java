package za.co.ipay.metermng.shared.dto.pricing;

import java.io.Serializable;
import java.util.HashSet;

import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDay;

public class SpecialDayProfile implements Serializable {
    private static final long serialVersionUID = 1L;
    private TouSpecialDay specialDay;
    private HashSet<TouPeriod> periods;
    
    public TouSpecialDay getSpecialDay() {
        return specialDay;
    }
    public void setSpecialDay(TouSpecialDay specialDay) {
        this.specialDay = specialDay;
    }
    public HashSet<TouPeriod> getPeriods() {
        return periods;
    }
    public void setPeriods(HashSet<TouPeriod> periods) {
        this.periods = periods;
    }
    
    
    
}
