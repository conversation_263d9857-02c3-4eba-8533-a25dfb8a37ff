### !!! CAUTION !!!
### Before adding keys, ensure you have no duplicates. Duplicates could lead to unexpected behaviour.

# zachv: 2025-05-16 | Planio #30009
calendar.specialday.field.date=Date
calendar.specialday.field.date.help=Enter a date for the special day
calendar.specialday.field.year=Year

# jacciedt: 2025-04-25 | Planio #33761
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for your account
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meters will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for your account has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for your account
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for your account
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for your account is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for your account is running low. Balance is {7,number,currency} Regards, Support Team


# michalv: 2025-05-09 | Planio #32735
export.error.too.many.records=Error: Too many records to export (over {0}). Please filter on a smaller date range.

# joelc: 2025-02-06 | Planio #28349
#power.limit.edit.label.prompt=Name
#power.limit.edit.label.help=Name of the power limit value. This is text display
power.limit.edit.value.prompt=Valeur en watts
power.limit.edit.value.help=Valeur de la limite de puissance. Cette valeur doit Ãªtre numÃ©rique
error.field.powerlimit.value.already=This power limit already exists.

# joelc: 2025-02-13 | Planio #31090
pricingstructure.suggestbox.placeholder = Start typing to filter list


#rodgersn: 2025-02-06 | Planio 29656
engineering.token.display.sgc=Supply Group Code:
engineering.token.display.krn=Key Revision Number:
engineering.token.display.ti=Tariff Index:

#renciac: 2024-08-31 | Planio 17062: MMA: get email from name & address from appsetting not from messages.properties
error.email.invalid=Invalid email address.

# christoe: 2024-07-29 | Planio #28161
import.edit.bulk.mdc.item.update.success=MDC message successfully updated.

# michalv: 2024-10-09 | Planio 25922
meter.models.mdc.required.for.thin.payment=Thin payment mode requires MDC

# christoe: 2024-10-21 | Planio #30157
online.bulk.panel.error.regex.validation1=Regex validation has been configured for UP Name, Agreement Reference or Account Name.
online.bulk.panel.error.regex.validation2=Auto-generation of these fields is disabled and requires manual entry on the Meter tab.

# Michalv: 2024-09-12 | Planio #25404
error.tab.duplicate.auxaccount=AuxAccount open in another tab. Tab: ({0})
error.tab.duplicate.custaccounttrans=Customer Transaction open in another tab. Tab: ({0})

#thomasn: 2023-12-06 | Planio 12164
access_group.lbl=Access Group
access_group.update.header=Update Access Group
access_group.update.group.error=New group has not been selected.
access_group.update.pricing.error=Customer UsagePoint(s) must have GLOBAL Pricing Structure.
access_group.update.future.pricing.clear=This will also clear any future pricing structures.
access_group.update.location.groups.clear=This will clear the usage point and customer location groups.
access_group.update.confirm.lbl=Proceed?
access_group.update.button=Update Access Group
insert.unique.error.meter=The meter details must be unique.
# Used by MMC MapperStaticProxy. e.g. CustomerMapperStaticProxy
insert.unique.error.customer=The customer details must be unique.
insert.unique.error.usagepoint=The usage point details must be unique.
insert.unique.error.devicestore=The device store details must be unique.
insert.unique.error.pricingstructure=The pricing structure details must be unique.
insert.unique.error.auxchargeschedule=The Aux charge schedule details must be unique.

#thomasn: 2023-10-11 | Planio 12164
session_auth.form.submit=Submit
session_auth.form.logout=Logout
session_auth.form.instructions=Choose session authorization details
session_auth.form.role=Role:
session_auth.form.group=Group:
session_auth.form.group_and_role=Group and Role:
session_auth.form.invalid=Login Error. Invalid Form Mode
access_group.error.group_already_cleared=The group has already been cleared on these entities.
access_group.success.updated_group=Successfully updated group
workspace.usagepoint.overview=Overview
workspace.usagepoint.actions=Actions
grouphierarchy.field.is_access_group=Access Group
grouphierarchy.field.is_access_group.help=Whether to connect this group to an access control organisation access group. This will show a list of access control groups on creating groups for this hierarchy level
grouphierarchy.field.is_access_group.error.already_assigned=An organisation access group can only be linked to one hierarchy level.
grouptype.field.location.group=Location Group
groupnode.field.access_group=Access Group
groupnode.field.access_group.help=Connects an access control organisation access group to this group which will restrict access to group users. A group user in a different group would not see this group in selections.
groupnode.field.access_group.error.no_groups=This hierarchy level has enabled organisation access groups but none have been defined.
changegroup.org_group.confirm=Changing your group and/or role selection will reload the application. Would you like to continue?
login.session.reload=You will be redirected to the login screen and the application will be reloaded.
mrid.ui=Unique ID
mrid.ui.help=Enter the unique ID
mrid.ui.external=External Unique ID?
mrid.ui.external.help=Whether unique id is from an external system

# christoe: 2024-06-21 | Planio #28160, #28158
mdc.txn.relay.title=Relay
mdc.txn.relay.help=Select which relay to connect or disconnect.
mdc.txn.relay.main=Main
mdc.txn.relay.aux.one=Auxiliary 1
mdc.txn.relay.aux.two=Auxiliary 2
mdc.txn.power.limit=Power_Limit

# michalv: 2024-06-15 | Planio #29332
error.field.email3=One or multiple email addresses are invalid

# michalv: 2024-07-18 | Planio 28459
online.bulk.panel.encryptionKey.help = Enter the encryption key for this meter. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.

# christoe: 2024-02-06 | Planio #23257 [i-Switch] Send an SMS when an aux charge is loaded
notify.selection.inherit=Inherit ({0})
error.notify.selection.null=It is required to set both preferences.
group.notify.children.change.alert=Children with matching or undefined notification IDs will be updated with these changes. Continue?
group.error.notification.save=Unable to link the notification information to the group.
customer.adjust.aux.accounts=Auxiliary Account Adjustments
customer.new.aux.accounts=New Auxiliary Accounts
customer.notification.types=Notification Types
customer.manage.notification.types=Manage Notification Types

#renciac: 2024-05-20 | Planio 27858: TariffStartDate not on the first of a month at midnight
warning.tariff.start.date.not.on.month.boundary=WARNING: tariff start date is not on a month boundary (the 1st at zero hours).</br>\
Bizswitch must be configured properly for this, as mid-month tariff changes MAY cause billing problems if not handled correctly!</br>\
Please be aware that MONTHLY BILLING Cyclic charges may only start or end on a month boundary.</br>\
Continue?
error.billing.cyclic.change.midmonth.monthly=MONTHLY BILLING Cyclic charges may only be initiated / changed for tariff start ON a month boundary (the 1st of a month at zero hours).

# michalv: 2024-05-22 | Planio 28023
token.reversal.reprinted.error=You do not have permission to reverse a reprinted token.

# rodgersn: 2024-02-20 | Planio #26293
meter.uri.remove.question=Changing the meter model will clear the Meter URI Fields (if captured). Continue?
meter.models.field.uri.present=URI Present
meter.models.field.uri.present.help=This indicates if the meter has URI.
meter.uri.address=Address
meter.uri.address.help=Enter the URI address of the meter.
meter.uri.port=Port
meter.uri.port.help=Enter the URI port number of the meter.
meter.uri.protocol=Protocol
meter.uri.protocol.help=Enter the URI protocol used to communicate with the meter.
meter.uri.params=Parameters
meter.uri.params.help=Enter the URI parameters used when communicating with the meter.
meter.uri.fields=Meter URI Fields
meter.uri.fields.list=Meter URI Fields (if captured): Address, Port, Protocol, Parameters.
meter.uri.port.error=Port must be a number from 0 to 65535.
bulk.upload.meter.uri.address=Meter URI Address
bulk.upload.meter.uri.port=Meter URI Port
bulk.upload.meter.uri.protocol=Meter URI Protocol
bulk.upload.meter.uri.params=Meter URI Parameters
bulk.upload.meter.uri.not.present.address.error=The meter Model can not have a meter URI address.
bulk.upload.meter.uri.not.present.port.error=The meter Model can not have a meter URI port.
bulk.upload.meter.uri.not.present.protocol.error=The meter Model can not have a meter URI protocol.
bulk.upload.meter.uri.not.present.params.error=The meter Model can not have a meter URI parameters.
error.field.meteruriprotocol.max=Meter URI Protocol must not exceed 255 characters.
error.field.meteruriaddress.max=Meter URI Address must not exceed 100 characters.
error.field.meteruriparams.max=Meter URI Parameters value is too long.

# zachv: 2024-05-07 | Planio 28174
tariff.field.bsst.charge_name.title=Charge Name
tariff.field.bsst.charge_name.title.help=Name of the charge that will be displayed on the receipt. 

# joelc: 2024-04-26 | planio-27792
group.type.for.cape.verde.contract = -

# michalv: 2024-04-05 | planio-26839
meter.txn.tokencode1=Token Code/s

# christoe: 2023-12-28 | Planio #25194 Reason Entry for Writing Off Charges in MMA
usagepoint.charge.writeoff.enter.reason=Enter a reason for writing off charges
usagepoint.charge.writeoff.select.reason=Select a reason for writing off charges

# thomasn: 2024-02-21 | planio-25347 [Cape Verde] Analyze and create the Cape Verde tariffs
tariff.blocks.thresholdCharge.error.empty=Where Block Price is set all Threshold Charges must be set or all must be empty.
tariff.blocks.thresholdCharge.error.incomplete=Threshold Charges can be set only where block price exists.

# zachv: 2023-11-25 | Planio #25498
grouptree.show_more = Show more
grouptree.empty = Empty
grouptree.search.help = Type at least the first two letters of an item. The item could be at any level of the hierarchy of data.
suggestbox.placeholder = Type to search...

# renciac: 2023-11-20 | Planio 25211: [TANESCO UAT] Staff tariff as "FBE" with monthly cyclic charge
tariff.field.subsidised.units.title=Monthly Subsidised Units
tariff.field.subsidised.units.descrip=Description
tariff.field.subsidised.units=Units
tariff.field.subsidised.units.help=Units that are issued at a subsidised charge.
tariff.field.bsst.charge.title=Charge
tariff.field.bsst.charge.title.help=Enter the amount to charge for subsidised units 
tariff.error.bsst.charge.positive=Must be a positive value.

# renciac: 2023-11-15 | Planio 25151 [TANESCO] [MMA] Automatically loading debt for a newly installed meter
tariff.field.meter_debt.title=Meter Debt
tariff.field.meter_debt.singlephase.label=Single Phase
tariff.field.meter_debt.singlephase.label.help=Enter Amount of Debt to consumer for pre-loaded single phase units on a new meter.
tariff.field.meter_debt.threephase.label=Three Phase
tariff.field.meter_debt.threephase.label.help=Enter Amount of Debt to consumer for pre-loaded three phase units on a new meter.

# marcod: 2023-08-29| planio 13587
import.upload.file.already.uploaded.group=Duplicate Filename. This file was already uploaded by another group

# marcod: 2023-06-21 | Planio 12175 Bulk device store movements
bulk.device.store.movement.help=Select the end device store the meters should be transferred to
bulk.device.store.movement=Select TO Device Store
bulk.device.store.movement.header=Bulk Device Store Movement
bulk.device.store.movement.without.input.file.not.implemented.yet=Bulk Device Store Movement needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
bulk.device.store.movement.param=TO Device Store

# renciac: 2023-09-20 | Planio 21358 View & writeoff also billing cyclic charges
usagepoint.last.cyclic.date.info=Last Cyclic Charge Date
usagepoint.last.cyclic.dates.info=Last Cyclic Charge Dates
usagepoint.last.cyclic.vend.date=At Vend/Topup
usagepoint.last.cyclic.billing.date=At Billing
usagepoint.charge.view.writeoff.date.help=Last cyclic date is the date when periodic charges like daily and monthly charges were last paid by a consumer. Charges after this date have yet to be recovered and may be written off here by selecting a new 'End Date' which becomes the new 'Last Cyclic Date'. Depending on the tariff and payment mode, there are potentially two sets of cyclic charges, one at Vend/Topup time, one at billing time.
usagepoint.charge.view.dialog.invalid.date.both=Date selected cannot be before BOTH last cyclic dates displayed
usagepoint.charge.view.dialog.warning.last.vend.cyclic.date=Last Vend Cyclic Charge already calculated on {0}. Nothing to calculate for VEND cyclic charges. Continue?
usagepoint.charge.view.dialog.warning.last.vend.billing.date=Last Billing Cyclic Charge already calculated on {0}. Nothing to calculate for Billing cyclic charges. Continue?
usagepoint.charge.view.filter.date.help=Outstanding charges will be calculated from the above Last Cyclic dates to this new selected date. It must be AFTER the previous last Cyclic Date/s. 
usagepoint.charge.view.dialog.date=Select End Date for charges
usagepoint.charge.writeoff.vend.heading=VEND/TOPUP CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.billing.heading=BILLING CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.vend.total=The total vend charges amount including tax is
usagepoint.charge.writeoff.billing.total=The total billing charges amount including tax is
usagepoint.charge.writeoff.both.total=The total charges amount including tax is

# christoe: 2023-10-04 | planio 22545
special.action.reason.no.reason=No reason given.

# rodgersn: 2023-06-07 | Planio 21001
register.reading.txn.meter=Meter
meterreadings.table.date=Date Created
usage.point.register.reading.txn.description=Register Readings for all meters on this usage point for the time period selected

# christoe: 2023-06-27 | planio 22796
reprint.default.email.message=Dear Customer\n\nPlease find your receipt details below:\n{0}\nKind regards\n{1}

# rodgersn: 2023-02-28 | Planio 15160
meter.assign.from.units.warn=Changing the pricing structure from Thin Units will not migrate the Units balance. A manual adjustment will have to be done. Continue?
ps.paymentmode.change.warn=This change in pricing structure changes the payment mode. Please ensure all the charges and/or billings are up to date. Continue?

# joelc: 2022-12-09 | planio 7589
usagepoint.save.license.error=  Maximum usage points allowed has been reached. Please notify your System Administrator.
bulk.import.license.waring = Importing {0} active Usage Points will exceed the maximum active usage points allowed. <br/>Note that once the limit is reached, only usage points marked as Inactive will be imported.

# marcod: 2023-04-06 | Planio 21668 [ZESCO] Debt collection methods
auxchargeschedule.specific.list.item=ACCOUNT SPECIFIC
customer.auxaccount.principle.amt=Principle Amount

# renciac: 2023-04-01 | Planio 18524 New requirements for RegisterReadingThinTariff
billingdet.appliesto.group.label=Only for Billing dets that apply to others
billingdet.discount.label=Discount
billingdet.discount.help=Check to indicate that this billing determinant signifies a discount, eg. solar rebate.
billingdet.charge.type.help=This billing determinant is either a percentage charge of another billing determinant's charge or a flat rate.
billingdet.applies.to.label=Applies To
billingdet.applies.to.help=Select the master billing determinant to which the percentage value of THIS billing determinant is applied.
billingdet.lnk.error.save=Unable to save the Billing Determinant appliesTo link.
billingdet.lnk.error.both=AppliesTo and Charge_type must both be captured or neither.
billingdet.applies.to.already.in.use=Billing det/s to which this billing det applies are already in use on a tariff or template.
billingdet.applies.to.already.in.use.on.mdc=This billing Det is in use on an MDC, cannot be used as a sub billing det that applies to others. 
billingDet.in.use.cannot.change.settings=This billing det is already in use on a tariff or template, cannot change settings or record status.
billingDet.in.use.cannot.change.applies.to=This billing det or those it applies to is already in use on a tariff or template, cannot change billing dets it applies to.
billingDet.change.regread.panel.open=You currently have a RegisterReadingThinTariff under construction. Saving this billing_det change will cause that to clear. if you need to save changes there first, do not confirm this message, save that tariff first then return here to save this billing_det. Continue with this save now?
billingdet.taxable=Is AppliesTo Taxable? 
billingdet.taxable.help=Unselect if tax is not applicable to this Billing Det - ONLY for billingDets that apply to others
billingdet.lnk.error.taxable=Taxable can only be unselected when this is an appliesTo BillingDet
cyclic.charge.apply.at.lbl=Apply At:    
cyclic.charge.apply.at.vend.lbl=Vend
cyclic.charge.apply.at.billing.lbl=Billing
cyclic.charge.apply.at.error.required=You must select one.
tariff.blocks.unitcharge.negative=Billing Det Unit Charge percentage or discount must not be negative
tariff.save.failed.billingDets.changed.on.database=Save tariff Failed. Percentage / Discount BillingDets have changed on database. Incompatible with input. Refresh the page.
unitcharge.choose.charge.type=Choose type of Charge
unitcharge.type.none=None
unitcharge.type.percentage=Percentage
unitcharge.type.flatrate=Flat Rate
unitcharge.discount.type.charge.error=Discount must be either a percentage or a flat rate 

# thomasn: 2023-03-21 | Planio 17726
supply.group.in.use.error.service=Supply Group in use by one or more meters. Press cancel to load latest.
supply.group.in.use.error.lbl=Supply Group in use by one or more meters. Some fields are read-only while in this state.

# rodgersn: 2023-02-09 | Planio 20655
register.readings.total.consumption=Total consumption for date range:

# thomasn: 2023-01-10 | Planio 18459
error.positive.value=Value must be positive.

# jacciedt: 2022-12-14 | Planio 19775
export.field.receiptnum=Receipt Number

# patrickm: 2022-11-08 | Planio #19650
reprint.total.tax=Total Tax
reprint.total.tax-inclusive=Total (Tax Incl.)

# thomasn: 2022-11-08 | Planio 17785
aux.account.mrid.external.unique.validation=The Unique ID of this aux account is already in use

# marcod: 2022-10-07 | Planio 19234
error.field.powerlimit.name.range=Name must be between 1 and 255 characters.
error.field.powerlimit.name.required=Name is required.
error.field.powerlimit.value.required=Value is required.
error.field.powerlimit.value.type=Value must be an integer and greater than zero.

# rodgersn: 2022-09-29 | Planio 16034
default.template.bulk.uploads=Download Template

# jacciedt: 2022-05-05 | Planio 15256
bulk.blocking.header=Bulk Blocking : Filename: {0}
bulk.blocking.without.input.file.not.implemented.yet=Bulk Blocking generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.blocking.permission.needed=This user does not have permission to bulk upload Blocking instructions.
bulk.blocking.blocking.type=Blocking Type
bulk.blocking.blocking.type.required.field.error=Blocking Type is a required field
bulk.blocking.not.blocked=Not blocked

# thomasn: 2022-08-31 | Planio 17172
usagepoint.ps.unitstocurrency=Changing Pricing structure from thin-units will not migrate the units balance. A manual adjustment will have to be done. Continue?

# rodgersn:2022-07-28 | Planio 18092 Missing message on some translation files
customer.txn.error.amount.incl.tax.is.zero=Amount incl tax cannot be zero

# thomasn: 2022-06-07 | Planio 16665
mdc.txn.pandisplay=Pan Display(Clear Balance)
remove.meter.pandisplay=*Sends MDC Message to clear meter balance.

# jacciedt: 2022-02-28 | Planio 12375
bulk.pricing.structure.change.header=Bulk Pricing Structure Change : Filename: {0}
bulk.pricing.structure.change.without.input.file.not.implemented.yet=Bulk Pricing Structure Change generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.pricing.structure.change.permission.needed=This user does not have permission to bulk upload pricing structure change instructions.
bulk.pricing.structure.change.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
bulk.pricing.structure.change.start.date=Start Date
bulk.pricing.structure.change.start.date.help=This is the date that the selected Pricing Structure will start on. Must be future dated based on when the import is done.
bulk.pricing.structure.change.pricing.structure=Pricing Structure
bulk.pricing.structure.change.pricing.structure.help=Pricing Structures that have active tariffs based on selected Start Date. Changing from thin-units will not migrate the units balance. A manual adjustment will have to be done.
import.items.abort=Import Failed:  

# renciac: 2022-05-05 | Planio 15937 curb validation between PS and billing dets esp for TOU
error.pricingStructure.future.ps.no.tariff.at.date=The future pricing structure has no running tariff at the start date selected.

# rodgersn: 2022-05-17 | Planio #16807
customer.txn.error.tax.less.than.amount=The Tax amount cannot be less than the Amount Including Tax for negative values
customer.txn.error.tax.and.amount.different.sign=If both Amount incl Tax and Tax amount are entered, they should be both positive or both negative

# patrickm: 2022-03-24 | Planio #15899
error.tab.duplicate.customer=Customer open in another tab. Tab: ({0})
error.tab.duplicate.meter=Meter open in another tab. Tab: ({0})
error.tab.duplicate.usagepoint=Usage Point open in another tab. Tab: ({0})

# jacciedt: 2022-02-10 | Planio 14296
confirm.bill.payment.reversal=Confirm Bill Payment reversal?
bill.payment.reversal.success=Successful Bill Payment Reversal. Original Ref = {0}, Reversal Ref = {1}
reverse.payment=Reverse Payment

# renciac: 2022-01-25 | Planio 12812 New pricing_structure history
usagepoint.hist.pricing.structure=Pricing Structure
usagepoint.hist.pricing.start=Start Date
usagepoint.hist.pricing.end=End Date
usagepoint.hist.ps.change.reason=Change Reason
up_pricing_structure.header=Pricing Structure History on the Usage Point
up_pricing_structure.sub.header=Previous Pricing Structure changes made to this Usage Point
usagepoint.current.pricing.change.enter.reason=Entrez une raison pour modifier la structure de prix actuelle
usagepoint.current.pricing.change.select.reason=SÃ©lectionnez une raison pour modifier la structure de prix actuelle
usagepoint.future.pricing.change.enter.reason=Entrez une raison pour modifier la future structure de prix
usagepoint.future.pricing.change.select.reason=SÃ©lectionnez une raison pour modifier la future structure de prix
usagepoint.field.pricingstructure=Structure de prix Actuelle
usagepoint.field.future.pricingstructure=Structure de prix future
usagepoint.ps.date.modified.hd=Last Date Modified
usagepoint.ps.user.modified.hd=User 
usagepoint.ps.change.reason.hd=Change Reason

# jacciedt: 2022-01-19 | Planio 14121
reprint.key.change.notice.line.1=Your resource token is below, but your meter requires a key change before you enter it.
reprint.key.change.notice.line.2=To change your meter's key, enter the tokens listed below:

# thomasn: 2022-01-31 | planio-15259 [BizSwitch & MeterMngCommon] Add support for codes in BlockTariff
tariff.blocks.unitcharge.error.empty=Where Block Price is set all Unit Charges must be set or all must be empty.
tariff.blocks.unitcharge.error.incomplete=Unit Charges can be set only where block price exists.

# marcod: 2021-11-18 | Planio 14768 Indra integration UI changes
bulk.upload.cust.ref=Cust Reference
bulk.upload.external.unique.id=Meter External UniqueId
bulk.upload.external.cust.unique.id=Cust External UniqueId
bulk.upload.external.up.unique.id=UP External UniqueId
mrid.component.error=Unique ID is a required field
group.edit=Edit
customer.ref.label=Customer Reference
customer.ref.help=Enter a unique reference number for the customer. This reference will refer to this particular customer with in the Meter Management System
error.field.customerreference.null=Customer Reference is a required field
error.field.customerreference.range=Customer Reference must be between {min} and {max} characters.
cust.mrid.external.unique.validation=The Unique ID of this customer is already in use
cust.ref.external.unique.validation=The customer reference of this customer is already in use
up.mrid.external.unique.validation=The Unique ID of this usage point is already in use
gen.group.mrid.external.unique.validation=The Unique ID of this group is already in use
meter.model.mrid.external.unique.validation=The Unique ID of this meter model is already in use
gen.group.mrid.external.length.validation=The Unique ID must be between 1 and 100 characters
aux.type.mrid.external.unique.validation=The Unique ID of this aux type is already in use
special.action.reason.mrid.external.unique.validation=The Unique ID of this special action reason is already in use
pricing.structure.mrid.external.unique.validation=The Unique ID of this pricing structure is already in use

# renciac: 2021-12-30 | Planio 15152 Cater for changing payment mode in Pricing Structure
usagepoint.error.new.installdate.before.last.sts.vend.date=Installation date cannot be BEFORE last Vend / Topup Date: {0}
usagepoint.error.new.installdate.before.current.ps.start.date=Installation date cannot be BEFORE the start date of the current Pricing Structure on this usage point: {0}.

# renciac: 2021-12-06 | Planio 14852 [EPC] Zero value in block tariff
tariff.blocks.zero.error=Only the first Block may have a unit price = 0

# renciac: 2021-10-18 | Planio 14521 Aux Account Specific charge schedule
auxspecchargeschedule.title=Aux Account Specific Charge Schedule
auxspecchargeschedule.title.add=Add Aux Specific Charge Schedule
auxspecchargeschedule.title.update=Update Aux Specific Charge Schedule
customer.debt.status.lbl=Debt Status
customer.debt.status.help=DEBT STATUS:<br/><b>Active:</b> balance is not zero and positive<br/><b>Settled:</b> balance is zero <br/><b>Overcollected:</b> balance is negative (i.e Refund)<br/><b>Suspended:</b> suspend-until date is in the future<br/><b>Written Off:</b> LAST transaction for Aux account is type WRITE_OFF and balance = 0
customer.chargeschedule.cycle=Charge Cycle
customer.chargeschedule.chamt=Montant du cycle de charge

# renciac: 2021-09-05 | Planio 11634, 11636, 13969 ViewOutstandingCharges bugs
usagepoint.charge.view.activation.in.future=Usage point activation date is in the future, has no cyclic charges yet 
# already translated below usagepoint.charge.writeoff.dialog.heading=List of Outstanding Charges as at {0}

# patrickm: 2021-08-31 | Missing key
pricingstructure.error.active=Pricing Structure has no Tariff. Cannot be active.

# patrickm: 2021-08-20 | Planio 9834
meter.model.in.use=Meter model has one or more meters attached. Some fields are read-only while in this state.
meter.models.paymentmodes.preselected=Active payment modes cannot be removed while Meter model has meters attached.

# renciac: 2021-06-01 | Planio 11646 Bulk Keychange
supply.group.target.label=Target Supply Group / Key Revision
supply.group.target.label.help=The next Supply Group / Key Revision that this supply group will change to.  
supply.group.target.error.same=Target Supply Group cannot be the same as current
supply.group.target.validation.error=Target baseDate is less than current SGC
supply.group.target.validation.error.expired=Target SGC expiry- or issueUntil date is expired
supply.group.target.validation.nulls= NOTE: Validation between SGC and target SGC was not completed because one or more of base date, expiry date or issue until date is still null, pending update from HSM. Needs manual verification.
supplygroup.field.issued.until.date.label=Issued Until
supplygroup.field.expiry.date.label=Expiry Date
supplygroup.field.target=Target SGC/KRN 
supplygroup.base.date.label=Base Date
supplygroup.base.date.label.help=Base date used for generation of STS6 tokens
supplygroup.target.deactivate.question=This SGC/KRN is being de-activated now, but is still currently in use as a target SGC/KRN for others. Continue? 
supplygroup.base.dates.no.check=A target SGC is in play, but either or both base dates have not yet been updated by the HSM, so cannot validate against each other. Continue?
supplygroup.target.base.date.smaller.question=SGC base date > target SGC base date. Target is older SGC. Continue? 
bulk.Keychange.extract.label.meterNum=Meter Num 
bulk.Keychange.extract.label.userRef=User Ref 
bulk.Keychange.extract.label.token1=Token1 
bulk.Keychange.extract.label.token2=Token2 
bulk.Keychange.extract.label.token3=Token3 
bulk.Keychange.extract.label.token4=Token4 
bulk.Keychange.extract.label.fromSupGroup=From SupGroup 
bulk.Keychange.extract.label.fromKeyRev=From KeyRev 
bulk.Keychange.extract.label.fromTariffIdx=From TariffIdx 
bulk.Keychange.extract.label.fromBaseDate=From BaseDate 
bulk.Keychange.extract.label.toSupGroup=To SupGroup 
bulk.Keychange.extract.label.toKeyRev=To KeyRev 
bulk.Keychange.extract.label.toTariffIdx=To TariffIdx 
bulk.Keychange.extract.label.toBaseDate=To BaseDate 
bulk.Keychange.extract.label.transDate=TransDate 
bulk.Keychange.extract.label.userRecEntered=User generated 
bulk.Keychange.extract.label.importFileName=Import Filename
bulk.Keychange.extract.label.bulkRef=Bulk Ref 
bulk.Keychange.extract.none=No Keychange found for this import file
button.submit=Submit
action.params.header.label=Parameters
import.file.explanation=Upload file containing data and / or parameters for action
bulk.keychange.header=Bulk Key Change : Filename: {0}
bulk.keychange.to.header=KeyChange TO:
bulk.keychange.use.target=Use pre-captured Target SGC/KRN 
bulk.keychange.use.targetHelp=Target SGC/KRN is captured on the Supply Group page under Meters Menu. If selected, then each meter's SGC/KRN will be mapped to it's target. If no target for a particular SGC/KRN, will get an error unless also enter a specific SGC/KRN in the TO, then THAT will be used when no target. 
bulk.keychange.use.target.selected.message=Can select 'Use Target' as well as select supply Group values. If no values and an SGC/KRN has no target, will get an error. If enter values as well, these will be used for those with no target. 
bulk.keychange.supplygroupcode=New Supply Group Code
bulk.keychange.supplygroupcode.help=Select the new supply group code. 
bulk.keychange.supplygroupcode.error.required.no.target=At least, the Supply Group Code must be selected.
bulk.keychange.tariffindex.required=Tariff Index is required
bulk.keychange.tariffindex=New Tariff Index
bulk.keychange.tariffindex.help=Enter the tariff index to be changed. Required. If a meter currently has a different tariff index, a keychange will be generated.
bulk.keychange.instruction.label=Generate Key Change Tokens Instruction 
bulk.keychange.instruction.help=Issue instruction as to when the keychange tokens must be generated.
bulk.keychange.instruction.generate.keychanges.now=Generate and update Key Tokens now
bulk.keychange.instruction.generate.keychanges.next.vend=Set to generate Key Tokens with next vend
bulk.keychange.instruction.generate.keychanges.next.vend.after.date=Set to generate Key Tokens with next vend AFTER a date....
bulk.keychange.generate.keychanges.next.vend.after.date=Keychange with vend AFTER Date
bulk.keychange.generate.keychanges.next.vend.after.date.help=Keychanges will be generated at vend_time but only AFTER this date
bulk.keychange.after.date.error=After Date cannot be in the past. Code checked against now() being {0}
bulk.keychange.after.date.required=After Date is required for selected bulk instruction
bulk.keychange.overwrite.existing=Overwrite existing New SGC/KRN details?
bulk.keychange.overwrite.existing.help=Must select radiobutton to handle the case when new Supply Group details already exist on a meter: Overwrite and continue, or leave existing details and abort the keychange instruction for such meters.
bulk.keychange.overwrite.existing.error=Must choose one, overwrite or not.
import.upload.keychange.permission.needed=This user does not have permission to bulk upload KeyChange instructions. 
import.upload.cannot.change.action.params.now=An import was logged, action params can no longer be edited, only viewed. To redo items with different action parameters, will need to re-import in a new file.
import.upload.view.params.label=Params
button.process.selected=Process Selected
button.process.all=Process All
button.import.extract.keychanges=Extract Generated KeyChanges
bulk.keychange.without.input.file.not.implemented.yet=Bulk Keychange generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.items.selected.success=Selected Items successfully processed.
import.items.errors= Not all items were successfully processed.
import.items.all.success=All ImportFile items successfully processed.
import.extract.items.non=No items to extract
import.extract.items.exception=Extract Failed. Please contact Support.
import.upload.twirly.waiting.text.keychange=Keychange requests may take a while
import.upload.keychange.bulkref.label=KeyChange BulkRef 
meter.txn.bulk.import.file.name=Import FileName

# renciac: 2021-06-01 | Planio 12963 Extension to Generic upload framework for action params
import.file.parameters.needed= Please confirm and submit parameters for the import of this data. 
import.file.parameters.needed.no.data=Please confirm and submit parameters for bulk changes.\nA dummy filename will be generated.\nIf data IS needed as well as parameters import will be rejected.
import.file.param.no.data.dummy.filename= Generated filename is {0}
import.file.no.parameters=This file type {0} has no UI defined for action parameters. Contact support. Dummy file created.
import.file.no.parameters.or.setting=This file type {0} has no UI defined for action parameters or database setting for has_input_file should be {1}. Contact support.
import.file.params.save.error=Unable to save the import file with action params.
import.file.no.params.converter.error=There is no JSON parameter converter for the action params of this import file
import.file.get.params.fail=Setting up parameter panel failed. Check your filename and file type.
import.file.parameters.updated=Parameters were updated for filename: {0}
import.upload.file.settings.conflict=No file was selected to be uploaded, and conflict in bulk file type settings: input data is n or b, but action params is false
import.upload.file.no.input.data=Bulk File Type setting has_input_file = 'n', no filename needed.
import.upload.file.needs.action.params=This file type requires action parameters. Please capture on upload page.

# thomasn: 2021-07-13 | planio-10426
usagepoint.ps.start.date.lbl=Start Date
usagepoint.ps.name.lbl=Pricing Structure
usagepoint.ps.start.date.help=The date this pricing structure will be activated on. This start date is unique.
usagepoint.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
usagepoint.ps.start.date.error.unique=Start Date must be unique. A pricing structure already exists with this start date.
usagepoint.ps.view.all.btn=View All
usagepoint.ps.delete.btn=Delete
usagepoint.ps.delete.btn.confirm=Are you sure you want to delete the future pricing structure?
usagepoint.ps.save.error=Saving of usage point pricing structure failed.
usagepoint.ps.delete.error=Deleting of usage point pricing structure failed.
usagepoint.ps.delete.success=Deleted usage point pricing structure successfully.
usagepoint.ps.required=Pricing Structure required.
usagepoint.ps.future.required=Future pricing structure should not be the same as the current one above.
usagepoint.ps.future.list.help=Select the pricing structure then set a start date for it.
usagepoint.ps.future.date.help=Start date for the selected future pricing structure.
usagepoint.ps.future.lbl=Future Pricing Structure
usagepoint.ps.future.start.date.lbl=Future Pricing Structure Start Date
usagepoint.ps.historic.error=Usage point has historic pricing structure which are not compatible with the meter model. Use new usage point.
meter.new.current.pricingstructure.required=Meter ({0}) does not support the current pricing structure - {1}.
meter.new.current.pricingstructure.select=Current Pricing Structure\n(If changed all future ones are deleted)
tariff.error.save.up.ps.start.date.conflict=Unable to update the tariff start date, pricing structure already added to usage point with start date {0}.
usagepointworkspace.error.meter.unsupported.model.current=The model of meter {0} does not support the usage point's current pricing structure.
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc=WARNING: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=WARNING ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=WARNING ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
error.pricingStructure.billingDets.notsame.asmetermodel.mdc=ERROR: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=ERROR ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=ERROR ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
question.confirm.installation.date.3=Activation date change conflicts with Pricing structures. Current PS will be deleted and Future PS will be made current.

# marcod: 2021-07-27 | Planio 12179
supplygroup.field.kmc.expirydate=KMC Expiry Date
supplygroup.panel.kmc.expirydate.help=The system will automatically send an email warning every day for vending keys that are about to expire.

# renciac: 2021-07-21 | Planio 13124 Debt Instalments
aux.charge.sched.cycle.label=Charge Cycle
aux.charge.sched.cycle.label.help=For Ad-hoc, charge with every vend. For Daily / Monthly determines the cycle of when charges are levied
aux.charge.sched.cycle.amount.label=Cycle Amount
aux.charge.sched.cycle.amount.label.help=An ad hoc amount is charged on each vend. Daily or Monthly cycle amounts will be charged once a day or once a month depending on the cycle; until the Debt is paid off.
aux.charge.sched.cycle.select.error=Charge cycle selection must accompany entry of Charge Cycle amount 
aux.charge.sched.cycle.instalment.label=Daily or Monthly Cycle Instalments are standalone amount entries
customer.auxaccount.start.date.lbl=Start Date
customer.auxaccount.start.date.help=Select the auxiliary account start date
customer.auxaccount.suspend.until.help=Temporarily suspend an auxiliary account until the set date in this field. For Debt Instalment charge schedules, take note of the appSetting for 'Accumulate Aux instalments during Suspension'.
customer.auxaccount.last.charge.date.lbl=Date Last Charged
customer.auxaccount.last.charge.date.help=Last date when a vend transaction was made that paid towards this account. Does not reflect manual payments or account adjustments, only actual payments via vends/topups. Note that for charge schedules which are NOT instalments, this might have been a part payment, not a full charge.
error.field.value.monthly.charge=Value must be one of PRO_RATA, FREE or FULL 
customer.auxaccount.suspend.until.smlr.start=Suspend Until Date should not be before Start Date 
customer.auxaccount.install.suspend.info=Charge schedule using Instalments follows specific behaviour after suspension. \r\nSee appSetting 'accumulate_aux_during_suspension' 
vend.older.trans.info=Note that when reversing a transaction which is NOT the last transaction for the customer agreement, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
vend.reversal.last.with.older=Note that there were multiple reversals this month, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
auxaccount.upload.startDate=Start Date
auxaccount.upload.startDate.greater=Start Date may not be greater than Suspend Until
auxaccount.upload.startDate.format=Start Date must be either empty or properly formatted
auxaccount.upload.startDate.invalid.date=Start Date is an invalid date
auxaccount.upload.suspendUntil.invalid.date=Suspend Until is an invalid date
trans.bulk.upload.format.error.trans.date=La date de la transaction doit Ãªtre vide (par dÃ©faut pour la date de traitement) ou correctement formatÃ©e (yyyy-MM-dd HH:mm:ss) 
trans.bulk.upload.invalid.trans.date=Transaction date is invalid

# marcod: 2021-07-08 | Planio 12735
error.usagepoint.outdated=ERROR: Data on this tab is outdated due to another update. Please click reload to refresh the data.
button.reload=Reload

# jacciedt: 2021-04-15 | Planio 12792
sts.unit.generation.limit.error=The amount of units to be issued cannot be more than the configured amount of {0} units.

# thomasn: 2021-07-22 | Planio 5812
usagepoint.meter.inspection.request.btn=Send Meter Inspection Request
usagepoint.meter.inspection.request.setup.error=The Contract is not complete, confirm Physical Address data is there.
usagepoint.meter.inspection.request.processing.error=An error occurred processing the request.
usagepoint.meter.inspection.request.meterMng000=Meter inspection request processed OK. Reference={0}
usagepoint.meter.inspection.request.meterMng001=Meter inspection request general error. Reference={0}
usagepoint.meter.inspection.request.meterMng011=Meter inspection request error customer data incomplete or invalid. Reference={0}
usagepoint.meter.inspection.request.txt.comment=Enter Comment
usagepoint.meter.inspection.request.txt.comment.help=A description of the reason.

# jacciedt: 2021-04-15 | Planio 9695
no.aux.charge.schedule.defined=No Aux Charge Schedule defined

# jacciedt: 2021-03-05 | Planio 12340
customer.txn.error.tax.more.than.amount=The Tax amount cannot be more than the Amount Including Tax
customer.auxaccount.error.refund=The new balance after the adjustment will be {0}. You are not allowed to change this account into a refund.
customer.auxaccount.error.debt=The new balance after the adjustment will be {0}. You are not allowed to change this account into a debt.

# marcod: 2021-07-15 | Planio 13708
reprint.customer=Customer

# thomasn: 2021-08-18 | Planio 13381
meter.txn.engineeringtokens.column=Has Engineering Tokens

# patrickm: 2021-07-02 | Planio 13126
supplygroup.field.code.default=Default

# jacciedt: 2021-06-30 | Planio 12839
up_meter_install.remove.date=Remove Date
up_meter_install.install.ref=Install Ref
up_meter_install.remove.ref=Remove Ref
up_meter_install.header=Usage Point Meter Installations
up_meter_install.sub.header=Previous Usage Point Meter Installations made to this Usage Point

# renciac: 2021-06-28 | Planio 13515 Writeoff charges duplicates
usagepoint.charge.button.close.writeoff.and.unassign.customer=Close and Unassign Customer

# marcod: 2021-05-25 | Planio 12620
search.meter.sgc.label1=Supply Group / Key Revision
search.meter.sgc.label2=(Current or New)
search.meter.sgc.help=The search will find meters with a current or new supply group code equal to the selected SGC/KRN. A new SGC/KRN on a STS meter is populated only when a keychange is actioned. As from next vend it then becomes the current one.

# jacciedt: 2021-02-16 | Planio 11622
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.subject=Account balance low for account {12} on agreement {5}
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.sms.message=Balance for account {12} on agreement {5} is running low. Balance is {7,number,currency} Regards, Support Team
bill.payments=Bill Payments
bill.payments.provider=Provider
bill.payments.reversal.request.received=Reversal Request Received
bill.payments.pay.type=Pay Type
bill.payments.pay.type.details=Pay Type Details
bill.payments.vote.name=Vote Name
bill.payments.description=A list of bill payments made by this customer
bill.payments.transaction.type=Bill Pay Transaction Type

# renciac: 2021-05-25 | Planio 13153 PS date validation vs installDate
usagepoint.installation.date.before.tariff.start1=Installation date is before tariff start date {0}. <br/> The implication is that potential Tariff charges that are due before tariff start date will not be calculated.
usagepoint.installation.date.before.tariff.start2=<br/> Last date that cyclic charges were calculated was {0}.
usagepoint.installation.date.before.tariff.start3=<br/> No Last cyclic charge calculation date on file.
usagepoint.installation.date.before.tariff.start4=<br/> Continue?

# renciac: 2021-04-20 | Planio 7918 Bulk Tariff Generator
error.field.customerdescription.max=Customer description must be less than {max} characters.
button.export.ps.title=Export ALL pricing structure Current tariffs 
export.ps.failed.exception=Export failed. Please contact Support.
export.ps.failed.non=No Pricing Structures with current tariffs to export ???
import.edit.item.update.bulk.tariff.success=Data for tariff {0} was successfully updated.
import.ps.name.label=Pricing Structure
import.tariff.name.label=Tariff
import.tariff.edit.resave.error=Error: calcContents: {0}
import.upload.file.already.uploaded=Error: File already uploaded. Check table.
import.upload.tariff.permission.needed=This user does not have permission to import tariffs.

# patrickm: 2021-03-18 | Planio 11152
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for {6}
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for {6} is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team

# jacciedt: 2021-03-12 | Planio 12494
error.field.validity.message.content=The message content cannot be blank.

# jacciedt: 2021-03-15 | Planio 11902
customer.account.does.not.exist=Customer Account does not exist yet

# renciac: 2021-03-04 | Planio 12582
bulk.ignore.dup.meters=Ignore Duplicate Meters
bulk.ignore.dup.meters.help=If meter number already exists on Database, ignore one in upload file. If duplicates in upload file, use first one.
bulk.upload.ignore.meter.dups.changed=Ignore duplicate meters setting has changed between steps! Was {0}; now {1}
bulk.upload.successful.meter.upload=Total de {0} tÃ©lÃ©chargement du compteur traitÃ© avec succÃ¨s, {1} doublons ignorÃ©s.

# marcod: 2021-02-01 | Planio 12168
email.password.reset.message=We have received a password reset request for {0}.<br> To reset your password please click the link below.<br> {1}.
password.link.expired=The password reset link has expired. You can request another one.
password.link.used=The password reset link has been deactivated.
password.change.now= Change Password Now
password.reset.success=Your password has been changed successfully.

# jacciedt: 2021-02-12 | Planio 12340
unitsacc.balance.with.symbol=Units Balance ({0})

# jacciedt: 2021-02-04 | Planio 12330
bulk.upload.heading.metercustup=Generate Template for MeterCustUp Bulk Upload
bulk.upload.metercustup.notice=For Meter/Customer/UsagePoint Bulk Uploads, use the menu option -> Configuration -> Upload and Import Files

# jacciedt: 2021-02-05 | Planio 11950
demo.addmeterreadings.tariffCalc.failed={0} : {1} successfully added, but tariff calculation failed.
demo.addmeterreadings.success={0} : {1} successfully added.
demo.addmeterreadings.tariffCalc.success={0} : {1} successfully added and tariff calculation completed.

# joelc 20 November 2018, Planio 4328
question.custom.field.used= Note that any group entity records using this field will NOT be updated, \
  only the list of available options will be updated for future use. 
question.custom.field.used.option.yes=Update List
question.custom.field.used.option.no=Cancel

# jacciedt: 2021-01-08 | Planio 12082
bulk.upload.unitsaccountname=Units Account Name
bulk.upload.unitslowbalancethreshold=Units Low Bal Threshold
bulk.upload.unitsnotificationemail=Units Notif Email
bulk.upload.unitsnotificationphone=Units Notif Phone

# jacciedt: 2020-12-22 | Planio 11146
error.date.field.invalid=Value entered is not a valid date. Format = {0}

# renciac: 2020-12-10 | Planio 11365
### Units Account ###
unitsacc.title=Units Account
unitsacc.name.help=Enter a name for this account
unitsacc.name=Units Account Name
unitsacc.balance.help=The current units balance
unitsacc.balance=Units Balance
unitsacc.sync.help=Synchronize the units balance with the units balance on the meter
unitsacc.sync=Synchronize Balance
unitsacc.low.balance.threshold.help=When the units balance reaches this threshold a message will be sent to the customer.
unitsacc.low.balance.threshold=Units Low Balance Threshold
unitsacc.notification.email.help=A comma separated list of email addresses to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.email=Notification Email Addresses
unitsacc.notification.phone.help=A comma separated list of phone numbers to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.phone=Notification Phone Numbers
unitsacc.note=A units account is only necessary if the meter model and the pricing structure require it.
unitsacc.required=* \= Required
unitsacc.changes.cleared=Changes have been cleared.
units.account.error.save=Unable to save the units account.

# jacciedt: 2020-11-27 | Planio 11366
units.account=Units Account
units.account.transaction.history=Units Account History
units.account.transaction.description=Previous Account transactions for this Units Account
amount.cannot.be.zero=Amount cannot be zero
units.transaction.type=Units Transaction Type
units.transaction.type.sale=Sale
units.transaction.type.consumption=Consumption
units.transaction.type.manualadj=Manual Adjustment
units.transaction.type.reversal=Reversal

# renciac: 2020-11-25 | Planio 11363
# The defaultUnitsAdjustmentProcessor.notification messages has the same arguments as the defaultAccountAdjustmentProcessor, except units instead of currency
defaultUnitsAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for {6}
defaultUnitsAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meter will be disconnected.\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.disconnect.sms.message=Balance for {6} has run out and will be disconnected. Balance is {7,number} Regards, Support Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for {6}
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.sms.message=Balance for {6} is running low. Balance is {7,number} Regards, Support Team

# thomasn: 2020-10-16 | planio-9668
meter.models.battery.capacity=Capacity
meter.models.battery.capacity.help=Enter the full battery capacity value. Either in %, Months,Voltage etc.
meter.models.battery.capacity.error=Value must be positive.
meter.models.battery.threshold=Low Threshold
meter.models.battery.threshold.help=Enter the low battery threshold percentage. When crossed a low battery event will be triggered.
meter.models.battery.threshold.error=Value must be between 0 and 100.
meter.models.battery.event.lbl=Battery Event

# renciac: 2020-10-16 | Planio 11204
usagepoint.installdate.change.error.readings=Cannot change installation date, new readings on the meter / usage point, please refresh the page.
meter.replace.installation.date.error.trans=Cannot replace meter with a future installation date, new transactions on the usage point, or new readings on the meter; please refresh the page and try again.

# renciac: 2020-06-09 | Planio 9616
meter.change.activation.date=Change Activation date = New Installation date?
meter.change.activation.date.required=Please check one of the boxes for Activation Date
meter.change.activation.date.error.trans=Cannot change activation date, transactions on the usage point
meter.change.installation.date.error.trans=Cannot change installation date, transactions on the usage point, please refresh the page.
error.field.installdate.future.trans.or.possible.gap=Cannot have a future installation date. It can only be in the future if has no transactions on the UP and if have chosen to change the activation date = installation date.

# marcod : 2020-09-09 | Planio 10498
meter.units.help=Enter the number of {0} units to one decimal place only.

# marcod : 2020-10-08 | Planio 9723
meter.number.suggestion.help=Start typing the meter number, meters that start with those digits will appear in a dropdown. Click on one to select it.

# thomasn: 2020-09-01 | Planio 8404
auxaccount.upload.suspendUntil=Suspend Until
auxaccount.upload.suspendUntil.in.past=Suspend Until may not be in the past
auxaccount.upload.suspendUntil.format=Suspend Until must be either empty or properly formatted
customer.auxaccount.suspend.until.lbl=Suspend Until
customer.auxaccount.suspend.until.error=Date must be in the future.
customer.auxaccount.suspend.until=Suspended Until
customer.auxaccount.txn.history.suspend.until=Suspended Until : {0}

# jacciedt: 2020-08-13 | Planio 10007
auxtype.error.update.in.use=Unable to deactivate the auxiliary type, it is already in use.

# jacciedt: 2019-01-29 | Planio 8575
bulk.upload.invalid.regex={0} does not match its regex pattern

# jacciedt: 2020-06-17 | Planio 9605
demo.addmeterreadings.weekly=Weekly

# jacciedt: 2020-04-09 | Planio 6150
customer.unassign.unassign.customer=Unassign Customer
usagepoint.charge.button.writeoff.and.unassign.customer=Writeoff Charges and Unassign Customer

# joelc: 2020-07-10 | Planio 9609
reprint.remaining.balance=Balance
reprint.desc=Description

# renciac: 2019-12-03 | Planio 5311
file.item.panel.reg.read.reminder=REMINDER: Any meters with register reading tariffs might need an initial reading.
channel.readings.header.up=Usage Point: 
channel.readings.timestamp.label=Reading TimeStamp
channel.readings.timestamp.help=Reading timeStamp must be equal to installation date OR greater than existing reading timestamp for this meter installation 
channel.readings.table.error.heading=Error
channel.readings.partial.entry=The initial register readings for this meter's channels are not or only partially completed. Do you want to save what you have and complete the rest later?
channel.readings.preExisting.note=NOTE: There are pre-existing readings for this meter and usagepoint installation. 
channel.readings.preExisting.same.mdc.channels=These are from the same MDC Channels, as below in the table. \nPress CANCEL to keep these as is.\nIf you wish to change the init readings, enter new values. \nNote: Reading TimeStamp must be > previous.
channel.readings.preExisting.diff.mdc.channels=These seem to be from previous MDC Channels.\nLast reading date found was: {0}.\nEnter new values for the new MDC channels. Note: Reading TimeStamp must be > Prevous Last Reading Date.
channel.readings.preExisting.note.end=\nIf none of these options are desirable, please contact System Support. 
channel.readings.timestamp.install.date=Reading time stamp must be equal to installation date.
channel.readings.timestamp.previous.date=Reading time stamp must be greater than previous reading date: {0} 
button.ok=OK

warning.change.mdc.on.meter.NO.DATA=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Also NO match on inactive usage points can be changed here (when they are activated NO match will be rejected). Continue?
error.change.mdc.on.meter.NONE.MATCH.active.up=ERROR: Changing the MDC on this meterModel has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the mdc on the meter-model now.

warning.change.mdc.channel.NO.DATA=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Continue?
error.change.mdc.channel.NONE.MATCH.active.up=ERROR: Changing / (assigning a new) channel on this MDC has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the channels / billing determinants now.

warning.change.mdc.NO.DATA=WARNING: Changing the MDC will affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.inactive.with.regreadPS=WARNING: Changing the MDC might affect {0} active and {1} inactive usage points with Register Reading Tariffs. Continue?
error.deactivated.mdc.active.up=ERROR: Cannot deactivate this MDC - has {0} active and {1} inactive usage points with Register Reading Tariffs. 

warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=WARNING: Changing / creating a new Register Reading tariff on this Pricing Structure has PARTIAL match of billing determinants to some of the active usage points using the pricing structure. Continue?
error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ERROR: Changing / creating a new Register Reading tariff on this Pricing Structure has NO MATCH to billing determinants of some of the active usage points using the pricing structure. There should be at least a partial match in ALL cases.
warning.change.status.billing.det.but.in.use=Changing the status of this billing determinant might affect {0} active and {1} inactive usage points with meters assigned to a meter model that use an MDC with channels assigned to this billing determinant. Continue?
error.cannot.activate.up.model.not.channels.with.regread.ps=ERROR! Cannot activate the Usage Point. The meter model and pricing structure must have at least one channel with the same billing determinant in common.
error.incompatible.model.with.ps=ERROR! The meter model and pricing structure are not compatible. \nEither the meter model has channels but not Register reading Pricing Structure or vice versa.

# renciac: 2019-11-07 | Planio 7951
channel.field.maxsize.help=Maximum reading on the meter's register before rollover to zero
channel.field.time.interval.help=Time interval of register readings
channel.field.time.interval=Time Interval
channel.config.header=Channel Overrides
channel.config.overrides.button=Override MDC Channels
channel.config.mdc.titlename=Mdc
channel.config.field.titlename=Meter Channel Override
channel.config.title.add=Add Meter Channel Override
channel.config.title.update=Update Meter Channel Override
channel.field.titlename.help=Only shows Mdc Channels not yet overridden - must select an MDC Channel to override
channel.config.field.billingdetnames=Billing Determinant/s
channel.config.error.delete=Unable to delete the Meter Channel Override. Contact support
channel.config.delete.confirm=Are you sure you want to delete the Meter Channel Override?
channel.config.deleted=The Meter Channel Override was successfully deleted.
mdc.channel.override.metermodels=Override from Meter Model/s
mdc.channel.override.metermodels.none=No Overrides
channel.config.already.override=Channel already has override. Selected from above. 
channel.field.meter.reading.type.title=Reading Type
meter.model.change.mdc.confirm.delete.configs=Meter model had channel overrides for the previous MDC on this model. Will delete those if go ahead with change of MDC. Continue?
meter.model.channel.configs.error.delete=Unable to delete the Meter Channel Overrides for previous MDC. Contact support
channel.config.no.channels.to.override=No channels defined for this MDC. 
mdc.channel.status.change.warn.overrides=Note: this mdc channel has overrides. Continue with status change?
mdc.channel.field.time.interval=MdcChannel Time Interval
channel.override.field.time.interval=Override Time Interval
mdc.channel.field.maxsize=MdcChannel Reading Max Size
channel.override.field.maxsize=Override Reading Max Size
mdc.channel.field.reading_multiplier=MdcChannel Reading Multiplier
channel.override.field.reading_multiplier=Override Reading Multiplier
import.edit.reg.Read.item.update.success=Data for meter {0}, reading Timestamp {1} successfully updated.
import.reg.Read.channel.value.label=Channel Value
import.reg.Read.timestamp.label=Reading Timestamp
reg.read.init.readings.cancel.confirm=Cancelling means no further initial readings for this meter's channels (registers) are saved and tariff calculations will use the first or existing readings as the initial readings. Continue?
channel.readings.import.note=Note: Register readings can also be imported manually.

# barryc: 2020-05-12 | Planio 8211
tariff.error.monthlycost.name=Specify a name for the charge
tariff.error.monthlycost=A valid number must be provided
tariff.error.monthlycost.positive=Value must be positive or zero

# barryc: 2020-04-21 | Planio 7780
error.field.specialactionsdescription.max=La description doit Ãªtre entre les {min} et {max} caractÃ¨res.

# jacciedt: 2020-04-14 | Planio 8139
meter.model.deactivate.in.use.error=Cannot deactivate - there are already meters using this meter model.

# jacciedt: 2020-04-01 | Planio 7873
question.confirm.installation.date.future.date=future date
usagepoint.field.meter.activation.date=Initial Activation Date

# barryc: 2020-04-22 | Planio 8209
tariff.error.percent_charge=SpÃ©cifiez un frais.
tariff.error.unit_charge=Specify a value.

# patrickm: 2020-04-03 | Planio 8675
register.reading.txn.create_date=Date Created

#joelc: 2020-02-19 | Planio 8592
meter.models.field.data.decoder=Meter Data Decoder
meter.models.field.data.decoder.help=Some meter models require specific decoders to process their readings.  

# patrickm: 2020-01-14 | Planio 7768
blockingtype.form.dailyamount=Amount per day
blockingtype.form.dailyamount.help=Maximum amount allowed per day
blockingtype.msg.error.dailyamount=You must include the maximum amount per day template, {max_amount_per_day}, in your message.

# jacciedt: 2019-01-20 | Planio 7356
usagepoint.hist.device.move.ref=Device Movement Reference
usagepoint.device.move.ref.lbl=Device Movement Reference Number
error.field.devicemoveref.max=Device Movement Reference must be less than {max} characters.

# jacciedt: 2019-12-31 | Planio 7950
configure.user.interface=Configure User Interface
configure.user.interface.field.name=Field Name
configure.user.interface.display=Display?
configure.user.interface.validation.regex=Validation Regex
configure.user.interface.regex.failed.message=Regex Failed Message / Enumerated Values
changes.saved=Changes have been saved.
configure.user.interface.invalid.regex=One or more fields have invalid regex patterns in them.
error.required.field={0} is a required field - the record cannot be saved without it.
error.regex.invalid=The input value does not match the required pattern of: {0}
configure.user.interface.enumerated.values.label=Type in the list of possible values separated by commas:
enumerated.field=enumerated field

# jacciedt: 2019-11-18 | Planio 7264
meter.select.meter.phase=Meter Phase
meter.select.meter.phase.help=Select the correct meter phase for the meter model.

# jacciedt: 2019-12-04 | Planio 7772
meter.txn.reversal.reason=Reversal Reason
meter.txn.reversed.by=Reversed By
vend.reversal.exceeds.time.limit=Reversal unsuccessful. It exceeds the reversal time limit.

# joelc: 2019-11-25 | Planio 7487
sts.tokens.header = STS Tokens
verify.token = Verify Token
verification.error.timeout = Error verifying tokens. No response received from service.
verification.error.general=Unable to verify token
verify.token.class=Token Class
verify.token.subclass=Token Sub-Class
verify.token.id=Token ID
verify.token.units=Units
verify.token.date=Token Date

# jacciedt: 2019-11-21 | Planio 7362
energybalancing.error.duplicate.selected.meters=The selected meter is already added to the list.

# zachv: 2019-11-21 | Planio 7916
tariff.field.free.units.title=Monthly Free Units
tariff.field.percent_charge.title=Percentage Charge
tariff.field.percent_charge.add=Add Percent Charge
tariff.field.cyclic_charge.title=Cyclic Charge
tariff.field.cyclic_charge.add=Add Cyclic Charge
tariff.field.non_accruing_monthly.name=Non accruing monthly
tariff.field.non_accruing_monthly.name.help=Only applies to MONTHLY cycle. Non accruing monthly charges only charge for the month a transaction is done, any previous months where no monthly charges were levied (eg. due to no purchases or only free tokens) will not be charged for retrospectively.
tariff.field.unit_charge.title=Unit Charge
tariff.field.unit_charge.add=Add Unit Charge
tariff.field.unit_charge.name=Charge Name
tariff.field.unit_charge.name.help=The name for this charge that will end up on the customer's receipt.
tariff.field.unit_charge.is_percent=Percentage
tariff.field.unit_charge.is_percent.help=Whether or not this is charged as a percentage of the unit price or a currency charge per unit.
tariff.field.unit_charge.is_taxable=Taxable
tariff.field.unit_charge.is_taxable.help=Whether tax should be applied to this charge.
tariff.field.unit_charge=Unit Charge
tariff.field.unit_charge.help=The currency value to charge per unit purchased. This is an additional charge over and above the base unit price.
tariff.field.unit_charge_percent=Percentage of Unit Price
tariff.field.unit_charge_percent.help=This charge will be applied as a percentage of the unit price. If there are blocks it will use the block price.
tariff.error.unit_charge.name=Specify a name for the charge.
tariff.error.unit_charge.positive_not_zero=Must be a positive value and not zero.

# jacciedt: 2019-11-12 | Planio 7814
customer.auxaccount.increase.debt=Increase DEBT by:
customer.auxaccount.decrease.debt=Decrease DEBT by:
customer.auxaccount.increase.refund=Increase REFUND by:
customer.auxaccount.decrease.refund=Decrease REFUND by:
customer.auxaccount.error.amount.negative=Amount can not be negative
customer.auxaccount.error.tax.negative=Tax can not be negative
customer.auxaccount.error.adjustment=Either Increase or Decrease needs to be selected
customer.auxaccount.error.balance.refund=Balance cannot be negative or zero

# patrickm: 2019-11-02 | Planio #7801
customer.search.listbox.label=Customer Search by
customer.search.listbox.item_agr_ref=Agreement Ref
customer.search.listbox.item_id_num=ID Number
customer.search.listbox.item_surname=Surname

# jacciedt: 2019-10-24 | Planio 7812
customer.auxaccount.functions.as=Functions As

# thomasn: 2019-10-17 | planio-5133
link.blockingtype=Blocking Types
blockingtype.title=Blocking Type
blockingtype.name=Name
blockingtype.panel.error.missingvalues=One of the values ({0}, {1}, {2}, {3})\nmust be defined OR it must be a complete block.
blockingtype.title.add=Add Blocking Type
blockingtype.title.update=Update Blocking Type
blockingtype.form.typename=Name
blockingtype.form.typename.help=Name for the Blocking Type
blockingtype.form.units=Units per day
blockingtype.form.units.help=Maximum units allowed per day
blockingtype.form.complete=Complete
blockingtype.form.complete.help=Complete block means no vending will be allowed.
blockingtype.form.vends=Number of Vends
blockingtype.form.vends.help=Maximum vends allowed before Complete block
blockingtype.form.amount=Maximum Amount
blockingtype.form.amount.help=Maximum amount allowed before complete block
blockingtype.form.message=Message
blockingtype.form.message.help=Message with block details displayed to user. Use {remaining_vends} to display remaining vends before complete block. Use {remaining_amount} to display remaining amount before complete block. Use {max_units_per_day} to display max allowed units per day. Use {max_amount_per_day} to display max amount allowed per day. Use {reason_fixed} to display reason for block. Note, curly brackets denote system variables.
blockingtype.error.save=Unable to save the blocking type.
blockingtype.error.save.duplicate=Unable to save the blocking type, another blocking type with the same name already exists.
blockingtype.error.update=Unable to update the blocking type.
blockingtype.error.update.duplicate=Unable to update the blocking type, another blocking type with the same name already exists.
blockingtype.msg.error.variables=Curly brackets only allowed for valid system variables, {max_units_per_day}, {max_amount_per_day}, {remaining_amount}, {remaining_vends} or {reason_fixed}.
blockingtype.msg.error.units=You must include the units template, {max_units_per_day}, in your message.
blockingtype.msg.error.units.undefined=Units template {max_units_per_day} not allowed, unless {0} has been defined.
blockingtype.msg.error.vends=You must include the number of vends template, {remaining_vends}, in your message.
blockingtype.msg.error.vends.undefined=Vends template {remaining_vends} not allowed, unless {0} has been defined.
blockingtype.msg.error.amount=You must include the amount template, {remaining_amount}, in your message.
blockingtype.msg.error.amount.undefined=Amount template {remaining_amount} not allowed, unless {0} has been defined.
blockingtypes.header=Blocking Types
blockingtypes.title=Current Blocking Types

# jacciedt: 2019-10-10 | Planio 7514
meter.mrid.external.unique.validation=The Unique ID of this meter is already in use

# jacciedt: 2019-09-17 | Planio 5823
demo.addmeterreadings.earliest.reading=Earliest Reading
demo.addmeterreadings.latest.reading=Latest Reading
demo.addmeterreadings.zero.checkbox.text=Add zero readings
demo.addmeterreadings.zero.form.title=Zero Readings
demo.addmeterreadings.consecutive=Consecutive
demo.addmeterreadings.random=Random
demo.addmeterreadings.percentage.instances=Percentage of instances
demo.addmeterreadings.missing.checkbox.text=Add missing readings
demo.addmeterreadings.missing.form.title=Missing Readings
demo.addmeterreadings.algorithm.logic=Algorithm logic
demo.addmeterreadings.delete=Delete existing Interval Readings
demo.addmeterreadings.delete.all=All
demo.addmeterreadings.delete.selected=Selected date range
demo.addmeterreadings.append=Append
demo.addmeterreadings.link=[DEMO] Add Meter Readings
demo.addmeterreadings.header=Add Meter Readings
demo.addmeterreadings.title=Add Meter Readings
demo.addmeterreadings.title.criteria.register=Register Readings Criteria
demo.addmeterreadings.reading.variants=Choose Reading Variant
demo.addmeterreadings.delete.register=Delete existing Register Readings
demo.addmeterreadings.error.misc.start=This start date needs to be after the main start date
demo.addmeterreadings.error.misc.end=This end date needs to be before the main end date
demo.addmeterreadings.error.instances.required=Instances are required
demo.addmeterreadings.error.instances.format=Instances need to be numeric
demo.addmeterreadings.error.instances.range=Instances need to be a whole number between 0 and 100
demo.addmeterreadings.error.mdc.channel=No MDC Channel selected

# renciac: 2019-09-19 | Planio 7656
import.generic.start.label=Start of Data
error.field.enckey.max=Meter encryption key may not exceed maximum 255 characters.
error.field.powerlimitlabel.max=Power Limit LABEL may not exceed maximum 255 characters.
button.import.extract.fail=Extract Failed
import.upload.num.failed.upload.label=Num Failed upload
import.items.unsuccessful.uploads.reminder=\nREMINDER: There were {0} items not successfully uploaded in this file. They are ignored for further processing and must be manually recreated in a new file and uploaded afresh.
import.upload.completed=File Upload completed. 
bulk.upload.file.error=Error while importing the file
error.field.customvarchar1.max=UP customvarchar1 can be max 255 characters.
error.field.customvarchar2.max=UP customvarchar2 can be max 255 characters.
error.field.phonecontact1.max=Phone Contact1 must be less than {max} characters.
error.field.phonecontact2.max=Phone Contact2 must be less than {max} characters.
error.field.notificationemail.max=Customer Account Notification Email Address must be less than {max} characters.
error.field.notificationphone.max=Customer Account Notification Phone must be less than {max} characters.
import.file.item.view.still.busy=The import on this file is running. No further action can be taken until that is completed or stopped.
import.file.item.view.still.busy.stopped=Stop import instruction issued. Import will cease after current batch.
import.file.view.upload.still.busy=The upload on this file is in progress. 
button.stop.import.all=Stop Import
import.file.stopped=The import on this file was stopped.
import.file.stopped.instruction=Stop Import instruction has been issued. The import will stop after the current batch.

# renciac: 2019-09-16 | Planio 6715
bulk.upload.idNumber=Id Number

# thomasn: 2019-10-09 | Planio 6286
usagepoint.unblocking.enter.reason=Enter a reason for unblocking
usagepoint.unblocking.select.reason=Select a reason for unblocking

# thomasn: 2019-09-09 | planio-6287 Add reason when re-activating a UP
usagepoint.hist.status.reason=Status Reason

# jacciedt: 2019-09-10 | Planio 7490
meter.clearreverseflag=Clear Reverse Flag
meter.disabletriplimit=Disable Trip Limit
meter.setcurrentlimit=Set Current Limit
meter.issue.token.description.help=Enter a description for this {0}.

# robertf: 2019-09-10 | Planio 7571
customer.txn.reason=Action Reason
customer.auxaccount.history.title=Auxiliary Account History
customer.auxaccount.history.filter.title=Auxiliary Account History for : {0}
customer.auxaccount.history.filter.discription=Previous changes made to auxiliary account : {0}
customer.auxaccount.history.table.header.datemodified=Date Modified
customer.auxaccount.history.table.header.user=User
customer.auxaccount.history.table.header.action=Action
customer.auxaccount.history.table.header.type=Type
customer.auxaccount.history.table.header.accountname=Account Name
customer.auxaccount.history.table.header.balance=Balance
customer.auxaccount.history.table.header.priority=Priority
customer.auxaccount.history.table.header.chargeschedule=Charge Schedule
customer.auxaccount.history.table.header.freeissue=Free Issue
customer.auxaccount.history.table.header.status=Status
customer.auxaccount.history.table.header.updatereason=Update Reason
customer.auxaccount.history.table.header.createreason=Create Reason
customer.title.auxaccounts.history.selector.description=Select Auxiliary Account to view history.

# jacciedt: 2019-08-14 | Planio 7341
import.account.number=Account Number
import.arrears.balance=Arrears Balance
import.debtor.balance=Debtor Balance
import.edit.account.number.update.success=Data for Account Number {0} successfully updated.
import.edit.generic.update.success=Data successfully updated.

# jacciedt: 2019-07-18 | Planio 6240
meter.select.store.add=Meter Store

# jacciedt: 2019-08-15 | Planio 7310
transaction.history.graph.yaxis.label2=Number of Units

# jacciedt: 2019-08-22 | Planio 6738
tou.thin.error.tax.positive=Tax must be a positive value.
register.reading.tax.positive=Tax must be a positive value.

# jacciedt: 2019-07-29 | Planio 7197
error.field.validity.email=One of the email addresses are invalid.
reprint.warning.line.1=WARNING!!! THIS IS A REPRINT
reprint.warning.line.2=of a token issued on {0}
reprint.warning.line.3=TAX INVOICE (COPY)
reprint.warning.line.4=Reprinted on: {0}
reprint.warning.line.5=Reprinted by: {0}
reprint.credit.vend.tax.invoice=Credit Vend - Tax Invoice
reprint.util.name=Util. Name
reprint.util.dist.id=Util. Dist. ID
reprint.util.vat.no=Util. VAT No.
reprint.util.address=Util. Address
reprint.issued=Issued
reprint.token.tech=Token Tech.
reprint.alg=Alg.
reprint.sgc=SGC
reprint.krn=KRN
reprint.your.resource.token=Your {0} Token
reprint.standard.token=Standard Token
reprint.receipt.nr=Receipt #
reprint.free.basic.resource=Free Basic {0}
reprint.debt.items=Debt Items
reprint.fixed.items=Fixed Items
reprint.total.vat.excl=Total (VAT Excl.)
reprint.total.vat.incl=Total (VAT Incl.)
reprint.print=Print
reprint.deposit=Deposit
reprint.save.to.pdf=Save to PDF
reprint.electricity=Electricity
reprint.water=Water
reprint.gas=Gas

# jacciedt: 2019-08-20 | Planio 7364
error.supplygroup.server=Duplicate Supply Group Code and Key Revision Number. Specify unique values.

# jacciedt: 2019-08-15 | Planio 7449
location.field.address.line.2=Line 2
location.field.address.line.3=Line 3

# jacciedt: 2019-08-01 | Planio 7368
location.field.address.line.1=Line 1
customer.phone.1=Phone Number 1
customer.phone.2=Phone Number 2

# jacciedt: 2019-06-19 | Planio 7024
timezone.warning=Note: All tabs must be closed before the time zone can be changed.

# jacciedt: 2019-07-15 | Planio 7244
error.field.specialactionsname.range=Action Name must be between {min} and {max} characters.

# jacciedt: 2019-07-16 | Planio 7148
unit.kiloliter.symbol=kl
unit.cubicmeter.symbol=m\u00B3
meter.units=Units ({0})

# robertf: 2019-07-08 | Planio 6247
transaction.history.column.header.stdunits=Standard Units
transaction.history.column.header.fbeunits=Free Basic Units
transaction.history.column.header.stdtoken=Standard Token Total
transaction.history.column.header.fixedamt=Fixed Costs Total
transaction.history.column.header.auxamt=Auxiliary Payment Total

# thomasn: 2019-07-01 | Planio-6288
usagepoint.blocking.info.message=This usage point was blocked by {0} on the {1} 
usagepoint.blocking.info.message.reason= for the following reason: <br/> {0}
usagepoint.hist.blocking.name=Blocking Type
usagepoint.hist.blocking.reason=Blocking Reason

# renciac: 2019-06-26 | Planio 6291
bulk.upload.powerlimit.key=Power Limit
meter.powerlimit.key.error.integer=Power Limit Value must be an integer
meter.powerlimit.key.error.not.configured=There are no power limit settings configured in the app settings.
meter.powerlimit.key.error.invalid=This power limit is not configured in the app settings.

error.field.usagepointgroups.required.group=Required Usage Point group is missing: {0} 
error.field.usagepointgroups.missing.group=Hierarchy not complete : missing usage point group field for Usage Point Type: {0}
error.field.usagepointgroups.invalid.group=Invalid usage point group name {0} for Usage Point Type: {1}
error.field.usagepointgroups.incomplete.group=Incomplete hierarchy level in Usage Point Group: {0} 

bulk.upload.invalid.locationgroups.not.configured=Location groups are not configured for this system
usage.point.location.group.generate.label=Usage Point Location Group
error.field.uplocationgroups.required=Required Usage Point Location group is missing
error.field.uplocationgroups.invalid.group=Invalid location group name {0} for Usage Point Location Group
error.field.uplocationgroups.incomplete.group=Incomplete hierarchy level in Usage Point Location Group
error.field.uplocationgroups.first.level.required=Usage Point Location Group is required. At least the first level of hierarchy should be completed.

customer.location.group.generate.label=Customer Location Group
error.field.custlocationgroups.required=Required Customer Location group is missing. 
error.field.custlocationgroups.invalid.group=Invalid location group name {0} for Customer Location Group
error.field.custlocationgroups.incomplete.group=Incomplete hierarchy level in Customer Location Group
error.field.custlocationgroups.first.level.required=Customer Location Group is required. At least the first level of hierarchy should be completed.

location.field.address.suburb.name=Suburb Name

# jacciedt: 2019-06-21 | Planio 6359
billingdet.error.save.duplicate=Unable to save the Billing Determinant, another Billing Determinant with the same name already exists.

# renciac: 2019-05-29 | Planio 6237
meter.freeissue.currency=Emergency Vend

# renciac: 2019-04-25 | Planio 6235
customer.id.partial.search=No exactly matching customer Id. Doing advanced search...
error.customer.load=Unable to display the Customer.
search.get.total.label=Count Total results of the selected criteria
search.count.label=Counting total results for selected criteria ...

# renciac: 2019-04-17 | Planio 6234
customer.id.error.noentry=Enter Customer Id number or % for all
metersearch.error.nometer=Entrez un numÃ©ro de compteur or % for all
usagepoint.error.none=Entrez un nom de point d'utilisation or % for all
customer.error.noentry=Entrez un nom de famille client or % for all
customer.agreement.error.noentry=Entrez un numÃ©ro d'accord or % for all
customer.account.error.noentry=Entrez un numÃ©ro de compte or % for all

# renciac: 2019-03-25 | Planio 5961
import.upload.header=File Upload / Import
import.upload.file.upload.title=File Upload
import.filetype.select.file.help=Select the file type of the file to be uploaded & imported
import.filetype.select.labeltext=File Type
import.upload.filetype.none=Please select the file type of the file to be uploaded 
import.upload.filename.txt=Selected filename={0}
import.upload.file.select.labeltext=Select File to upload
import.upload.select.file.help=Select a movements file containing the information for uploading into the system
import.upload.csv.button=Upload File
import.upload.workspace.heading=File Upload and Import Data
link.file.import=Upload and Import Files
import.upload.filetype.error=Invalid File Type. Contact Support.
import.upload.file.none=No file was selected to be uploaded
import.upload.file.error=Error while uploading the file. Contact Support.
import.selected.items.non=No items have been selected for import.
import.upload.uploaded.files.title=Uploaded Files for Data Import
import.upload.file.name.label=File Name
import.upload.num.items.label=Num Items
import.upload.startdate.label=Upload Start
import.upload.enddate.label=Upload End
import.upload.last.imported.by.label=Last Import By
import.upload.detail=Detail
import.upload.open.label=Open

import.file.items.header=File Items
button.import.selected=Import Selected
button.import.all=Import All
import.items.title=Items for Import
import.select.label=Select
import.upload.successful.label=Upload Success
import.upload.date.label=Import Date
import.num.attempts.label=Num imports
import.last.successful.label=Import Success
import.meter.label=Meter
import.up.label=Installation
import.agrref.label=Contract
import.comment.label=Comment
import.itemdata.label=Data
import.upload.username.label=Upload User
import.last.start.label=Last Import Start
import.last.end.label=Last Import End
import.items.file.detail.header=File Detail
import.items.edit.header=View / Edit File Item
import.cancel.edit.item.confirm=Cancelling will abandon changes made above. Continue?
import.edit.item.update.success=Data for Customer {0}, meter {1} successfully updated.
import.edit.item.update.non=Data has not changed. No update necessary.

# thomasn: 2019-02-18 | Planio 6223
customer.idnumber.help=Enter the customer's ID number.
customer.idnumber=ID Number
error.field.idnumber.max=ID Number must be less than {max} characters.
customer.idnumber.column=ID Number
search.customer.idnumber=ID Number

#  renciac: 2019-01-28 | Planio 6425
# Removed: usagepoint.deactivate.info.message=This usage point was deactivated by {0} on the {1} for the following reason: <br/> {2}
usagepoint.deactivate.info.message=This usage point was deactivated by {0} on the {1} 
usagepoint.deactivate.info.message.reason= for the following reason: <br/> {0}

# rfowler: 2019-02-08 | Planio 6141
search.customer.phone1.number=Phone 1
search.customer.phone2.number=Phone 2
search.customer.phone.number=Phone Number
search.customer.custom.textfield1=Custom Field 1

search.location.header=Location Search
search.location.erf.number=Erf Number
search.location.building.name=Building Name
search.location.suite.number=Suite Number
search.location.address1=Address Line 1
search.location.address2=Address Line 2
search.location.address3=Address Line 3
search.location.type=Location Search Type
search.location.type.label=Customer/Usage Point Location
search.location.type.customer=Customer Location
search.location.type.usagepoint=Usage Point Location

# joelc: 2018-01-10 | planio-6324:Samoa - Reason for reactivation of usage point
error.field.reasonname.range=Reason name is required.
error.field.reasontext.range=Reason text is required.
specialaction.auto.deactivate.usagepoint=Deactivated as usage point is no longer complete (missing customer or meter) 
specialaction.auto.activate.usagepoint=Activated after completing required usage point data.

#  zachv: 2019-01-02 | Planio 5936
# Removed: ndp.schedule.abandon.activation.change=Schedule activation has changed. if you want to keep the setting, choose No and save / update first. Abandon setting?
question.close.tabs.dirty=All tabs will be closed, but some have unsaved changes. Do you want to discard those changes? 

# joelc: 2018-12-12 | planio-6324:Samoa - Reason for reactivation of usage point
usagepoint.activate.enter.reason=Enter a reason for this activation
usagepoint.activate.select.reason=Select a reason for this activation
special.action.reason.error.save.duplicate=This reason has already been added.

# renciac: 2018-12-06 | planio-6282
error.field.value.boolean=Value must be true or false
error.field.value.location.level=Location Group Type is NOT required. This setting cannot be set to true.

# thomasn: 2018-11-29 | planio-5296
auxaccount.upload.invalid.duplicate=Duplicate AuxAccount the (accountName & agreementRef) combination already exist!.

#RobertF 2018-11-21 Planio-6142 : [MMA] CoCT - Transaction History Bar Graph
transaction.history.graph.title=Transaction History
transaction.history.graph.description=Transactions per month for last 12 months.
transaction.history.graph.xaxis.label= Month
transaction.history.graph.yaxis.label= Number of Transactions
transaction.history.graph.series.label= Transactions count

# thomasn: 2018-11-12 | planio-6168
customer.title.mr=Mr
customer.title.mrs=Mrs
customer.title.ms=Ms
customer.title.miss=Miss
customer.title.doc=Dr
customer.title.prof=Prof
customer.title.sir=Sir

customer.email=Email Address

# joelc: 2018-10-29 | planio-6105: usage point meter download and print for Mayotte
print.customer.contract=Download Customer Contract
print.customer.contract.auxtype=Aux Type
print.customer.contract.auxname=Account Name
print.customer.contract.principleamount=Principle Amount
print.customer.contract.balance=Balance
print.customer.contract.status=Status
print.customer.contract.signature=Customer Signature
print.customer.contract.signature.date=Date 

# thomasn: 2018-10-17 | Planio 5296
auxaccount.upload.balanceType=Balance Type
auxaccount.upload.invalid.balance.amount=Balance Amount must be positive
auxaccount.upload.invalid.balancetype=Invalid Balance Type must be debt/refund.

# robertf: 2018-10-09 | Planio 5955
meter.txn.user.ref=User Ref
engineering.token.user.reference.txtbx.label=User Reference
engineering.token.user.reference.txtbx.label.help=Enter user reference for engineering token issue.

# Patrickm: 2018-11-09 | planio-6192: GIS data upload for BVM
link.metadata.upload=Upload Metadata
metadata.upload.heading=Metadata Upload
metadata.upload.data.title=Import Metadata
metadata.upload.description=Select the JSON file containing the metadata to import into the Meter Management system.
metadata.upload.error.object.creation=Error creating {0} object. Contact Support!
metadata.upload.select.file.help=Select a file containing the metadata in the specified json format for importing into the system
metadata.upload.button=Upload metadata
metadata.lat.label=Latitude
metadata.lon.label=Longitude
metadata.lon.help=Longitude metadata for this UP Group
metadata.lat.help=Latitude metadata for this UP Group
metadata.gis.saved=Successfully saved GIS information for group: {0}
metadata.gis.error.invalid.lat=Invalid latitude value. Coordinate out of range of valid GIS coordinates.
metadata.gis.error.invalid.lon=Invalid longitude value. Coordinate out of range of valid GIS coordinates.

# Patrick: 2017-12-14 | planio-5041 : Add configurable switch to device stores to determine the response of meters in the store to polling requests
devicestore.field.store_vendors_meter=Device store holds meters that have moved to another vendor
devicestore.field.store_vendors_meter_help=Whether the device store holds meters that have moved to another vendor.
devicestore.field.store_vendors_meter_help2=The fields '{0}' and '{1}'  are mutually exclusive. Therefore, you cannot have a custom message and check the box
devicestore.field.custom_message=Device Store Custom Response Message
devicestore.field.custom_message_help=The message to be shown to users when users query for meters stored in this device store.
devicestore.meters.save.dialog=This meter will be saved in '{0}' store. Please confirm this operation.
devicestore.meters.move.dialog=This meter will be moved to '{0}' store. Would you like to perform this operation?
devicestore.meters.fetch.dialog=You are fetching a meter from another vendor. Would you like to continue with this operation?

# thomasn: 2018-10-23 | planio-5956 [MMA] Add an alphanumeric SAP reference number when removing or replacing a meter on a usage point and display it in the history tables
usagepoint.hist.reason=Meter Remove/Reassign Reason

# robertf: 2018-10-01 | Planio 5954
meter.txn.powerlimits=Power Limit
meter.powerlimit.units.w=Power Limit (W)

# Renciac: 2018-09-04 | planio-5466 : Add manual reversal
button.vend.reversal=Reverse Vend
vend.reversal.confirm=Confirm Vend / Topup reversal?
vend.reversal.connection.error=No response received from service. Please refresh the page.
vend.reversal.error=Vend / Topup error: {0}
vend.reversal.fail=Only the last Vend can be reversed.
vend.reversal.success=Successful Vend Reversal. Original Ref= {0}, Reversal Ref={1}
vend.trans.already.reprinted=This transaction was been reprinted {0} time/s. First reprinted on {1} by {2}. \n
vend.reprint.null.token=Token is null. Reprint not possible. 
vend.reversed=Vend has been reversed.
meter.txn.reprint.date=Last Reprint
meter.noshow.token=Reprint for token 
vend.reprint.user.not.known=Not Known

# thomasn: 2018-09-04 | Planio 5476
readings.table.receiptnum=Receipt Number

# thomasn: 2018-09-03 | Planio 5296
customer.auxaccount.amount.pos=A positive amount indicates a REFUND
customer.auxaccount.amount.neg=A negative amount indicates a DEBT
customer.auxaccount.title=Auxiliary Account
customer.auxaccount.balance.type.debt=Debt
customer.auxaccount.balance.type.refund=Refund
customer.auxaccount.balance.type.error.required=You must select one.
customer.auxaccount.error.balance=Balance cannot be negative

# Thomas: 2018-08-29 | planio-5475 Time of use calendar allows 00:00 as end time for day profile but marks as incomplete
calendar.assign.period.end.maximum=End of day value is 23:59
calendar.assign.period.end.help=The time that the period ends. End of day value is 23:59

# Renciac: 2018-07-26 | planio-5451 Enabling STS 6
meter.token.code3=Token code 3
meter.token.code4=Token code 4
base.date.label=Base Date: 
base.date.label.help=Base date used for generation of STS6 tokens for this meter.
meter.three.tokens=Three Key Change tokens required
meter.three.tokens.help=For STS algorithm code = 07, some meters can store the STS info ON the meter and need a third keyChange token to supply the information.
meter.three.tokens.error=Three Tokens setting is only applicable to STS Algorithm Code = 07 
meter.clear.tid=TID reset 

# zachv: 2018-08-22
tariff.field.samoa.debt_charge=Debt Charge
tariff.field.samoa.debt_charge.help=Charged per unit
tariff.field.samoa.energy_charge=Energy Charge
tariff.field.samoa.energy_charge.help=Charged per unit

# robertf: 2018-07-27: #5347 Community Group used feature indicator
usagepointgroups.indicator.thresholds.tooltip = Group has custom customer account thresholds
usagepointgroups.indicator.ndp.tooltip = Group has custom NDP schedule

# zachv: 2018-06-26
tariff.field.kenya.monthly = Fixed Monthly Charge
tariff.field.kenya.monthly.help = Fixed charge applied per month.
tariff.field.kenya.fuel = Fuel Cost Charge
tariff.field.kenya.fuel.help = Variable rate per kWh, published monthly by KPLC. VAT is applied to this charge.
tariff.field.kenya.forex = Forex Charge
tariff.field.kenya.forex.help = Foreign exchange rate fluctuation adjustment (FERFA). Variable rate per kWh, published monthly by KPLC. 
tariff.field.kenya.inflation = Inflation Adjustment
tariff.field.kenya.inflation.help = Variable rate per kWh, published monthly by KPLC.
tariff.field.kenya.erc = ERC Levy
tariff.field.kenya.erc.help = Rate per kWh.
tariff.field.kenya.rep = REP Levy
tariff.field.kenya.rep.help = Percentage of base rate
tariff.field.kenya.warma = WARMA Levy
tariff.field.kenya.warma.help = Variable rate per kWh, published monthly by KPLC.

# RobertF 4th July 2018. Planio 5714
bulk.upload.enc.key= Encryption Key
meter.enc.key.error=The Meter Model requires a meter encryption key.

#RobertF June 15, 2017 Planio-5787 : Panel with the buying index chart
dashboard.buying.index.graph.title=Buying Index
dashboard.buying.index.graph.month.description=Buying Index: Transacting Meters / Active Meters (%)
dashboard.buying.index.graph.xaxis.label=Month
dashboard.buying.index.graph.yaxis.label=Buying Index

# zachv: 2018-05-04
meter.models.field.needs.encryption.key = Needs Encryption Key
meter.models.field.needs.encryption.key.help = Whether a meter encryption key needs to be entered for this meter model.
meter.encryptionkey=Encryption Key
meter.encryptionkey.help=The encryption key for this meter, for example to decrypt data received from a meter data collector
meter.encryptionkey.error=The Meter Model requires a meter encryption key.
meter.metermodelchange.remove_fields.question=Changing this meter model will cause the following fields to be cleared: {0} . Continue? 
meter.model.unset.encryption.key.error=Cannot change the Encryption Key requirement - there are already meters with encryption keys using this meter model.

# Thomas: 2018-05-10 | planio-5502 : MDC messages Override Button needs info message
mdc.txn.override.help=An override message will be sent as a priority message that will take precedence over other messages that may be pending against the meter and certain validation checks will not be applied such as validation of non-disconnect periods and message ordering.

# Thomas: 2018-04-16 | planio-5495 : OpenWayTransaltor, send text message to meter display
meter.models.field.message.display=Supports display messages
meter.models.field.message.display.help=This indicates whether this meter model supports displaying messages on the meter. E.g. Low balance message 

# zachv: 2018-04-09 | planio-5512 : MeterMng to support reading multiplier for mdc channel
channel.field.reading_multiplier=Reading Multiplier
channel.field.reading_multiplier.help=Multiplier applied to reading to normalize it into the correct data type. For example a water meter may give readings in pulses needing a multiplier such as 0.5 pulses per liter. Not supported by all mdc components.

# Patrickm: 2018-03-22 | planio-5438 : Display meter location on Map
meter.location=Meter Location
usagepoint.location=Usage Point Location

# Renciac: 2018-02-28 | Planio 5380 : Non_billable meters
customer.agreement.billable.help=Set to false  (unchecked) for Customer Agreements that are for consumption smart meters purely for measuring usage, not for purchasing. Readings for these meters are uploaded and used for Usage Graphs on, for eg. the EnergyInsight website.  
customer.agreement.billable=Billable
customer.agreement.billable.setting.check=You have set the customer agreement billable value to {0}. Please confirm the value. If set to true, this account can be topped up, if set to false it is purely for usage consumption purposes.
bulk.upload.billable=Billable
error.field.upload.billable.invalid=Billable can only be true or false.  

# Renciac: 2018-01-24 | planio-5210 : PayTypeDiscounts & Add vat inclusive / exclusive to help messages
tariff.field.percent_charge.help=Les frais en pourcentage facultatifs qui seraient retirÃ©s du montant offert comme premiÃ¨re Ã©tape du calcul tarifaire. (Includes Tax).
tariff.field.discount.help=Discount to be applied per payment type. (Includes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.discount.blockThin.help=Discount to be applied per payment type. (Excludes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.unitprice.help=Prix par kWh (Excludes Tax).
tariff.field.block.help=Specify up to eight block's unit price (excluding Tax) and threshold below. Leave the unit price and threshold blank for unused blocks.
tariff.field.unitprice.namibia.help=Prix par kWH (exclut ImpÃ´ts & excludes Tax)
tou.thin.field.monthlydemand.help=SpÃ©cifiez la quantitÃ© de frais de la demande mensuelle. (Excludes Tax).
tou.thin.field.servicecharge.help=Indiquez le montant du frais de service. (Excludes Tax).
tou.thin.field.charges.help=Saisissez un taux de tarifaire pour chacun de la saison disponibles, la pÃ©riode et les combinaisons de type de lecture. (Excludes Tax).
tou.thin.field.charges.specialday.help=Saisissez des taux tarifaires pour chacun des jours spÃ©ciaux ci-dessous. (Excludes Tax).
register.reading.rates.help=Capturez un tarif pour chacun des facteurs de facturation sÃ©lectionnÃ©s. (Excludes Tax).

# rfowler : 2018-01-23 : Planio 4756
bulk.upload.file.no.meterupdata=No meter/usage point data present in csv upload file.

# Patrick: 2017-11-27 | planio-5127 : Capture and display power limit of a meter
meter.power_limit.instructions=For Power Limit Tokens:\n1. Open the Meter Panel.\n2. In the Power Limit information block, Select the power limit from the Power Limit suggestion box.\n3. This will add a new listbox with Power Limit options - select what you require.\n4. Upon saving the meter, a popup box will be shown to input further details.
meter.power_limit.container_label=Power Limit Information
meter.power_limit.token.generate=Generate Power limit Tokens?
meter.power_limit.token.generate.help=If the meter needs to be updated to match the new Power limit details then a power limit token needs to be generated. If the record is being updated to match the meter's details then there is no need to generate tokens.
tokens.power_limit.no_gen=Do not generate power limit tokens
tokens.power_limit.gen=Generate power limit tokens

# joelc 11 January 2018, Planio 4630
grouptree.search=Search

# joelc 3 January 2018, Planio 4627
error.group.contains.usagepoint=Group currently contains usage points and cannot be deleted. 

# joelc 13 December 2017, Planio 4631
group.error.name.nonunique=Group name must be unique.

# 2017-11-20 | planio-5134 : Supplier Group Code validation issue
error.field.supplygroupcode.size=Code must be equal to {0} digits.

# RobertF 23 October 2017, Planio 4755
bulk.upload.gencsvtemplate.title=Generate Upload Template
bulk.upload.gencsvtemplate.subtitle= Required fields have been selected and cannot be toggled.
bulk.upload.file.button.gentemplate=Generate Template
bulk.upload.template.required.first=Info: Required
bulk.upload.template.required=Required
bulk.upload.template.sts.required=Required for STS meters
bulk.upload.recordstatus=Active(blank/no)
bulk.upload.installationdate.format=Install Date (yyyy-MM-dd HH:mm:ss)
bulk.upload.file.button.gentemplate.description=Select the fields you require and generate your upload template:

# joelc 13 November 2017, Planio 4636
meter.use.existing.instructions=Copy groups from existing meter
meter.not.in.groups=Meter has not been assigned to any groups
meter.copy.selected.groups=Copy selected groups to main screen

# Rencia 29 August 2017, Planio 4928
meter.attached.to.up=Meter {0} is attached to Usage Point {1}

# Thomas 7th August 2017. Planio 4815
tariff.field.minvendamount.lbl=Min Vend Amount
tariff.field.minvendamount.help=Minimum vend amount MUST be provided if tariff allows for vend of less than one unit. This will bypass the check that a vend must be for at least ONE whole unit.

# Patrick | July 19, 2017 | planio-4648: When save new Aux acc, confirm status
auxaccount.checkbox.active.status=Your Auxiliary account is not active by default. Would you like to activate this account?

# Thomas 18th July 2017 Planio 4353 MDCTrans Override UI
mdc.txn.override.lbl=Override
mdc.txn.override.none=None
mdc.txn.override.all=All
mdc.txn.override=Override

#Thomas 17th July 2017 Date validation Planio-4644
error.field.datetime.invalid=Invalid date format. Expected format ({0})

#Rencia 24 May 2017 Improve Cell phone validation
customer.trans.upload.invalid.up.no.meter=Usage Point does not have a meter attached to it
customer.trans.upload.invalid.up.not.active=Usage Point is not active

#RobertF July 14, 2017 Planio-4421 : Meter Management: Panel to monitor vending activity
dashboard.vending.activity.graph.title=Vending activity
dashboard.vending.activity.graph.description=Total vends over last 15 minute interval
dashboard.vending.activity.graph.xaxis.label= Vends
dashboard.vending.activity.graph.yaxis.label= 15 minute intervals (click chart to reset zoom)

### July 13, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.indicator.total.sales=Ventes Totales
dashboard.key.indicator.indicator.tooltip.total.sales=Ventes Totales
dashboard.key.indicator.indicator.transactions=Transactions
dashboard.key.indicator.indicator.tooltip.transactions=Montant des transactions qui se produisent pour donner des ventes totales
dashboard.key.indicator.indicator.transacting.meters=Transactions des compteurs
dashboard.key.indicator.indicator.tooltip.transacting.meters=Nombre de compteurs effectuÃ©s pour donner des ventes totales
dashboard.key.indicator.indicator.new.meters.installed=Nouveaux compteurs installÃ©s
dashboard.key.indicator.indicator.tooltip.new.meters.installed=Nouveaux compteurs qui ont Ã©tÃ© assignÃ©s Ã  des points d'utilisation
dashboard.key.indicator.indicator.new.active.meters.installed=Nouveaux compteurs ACTIVE installÃ©s
dashboard.key.indicator.indicator.tooltip.new.active.meters.installed=Nouveaux compteurs associÃ©s aux points d'utilisation et actifs
dashboard.key.indicator.indicator.total.meters.usage.points=Compteurs Total (Points d'utilisation)
dashboard.key.indicator.indicator.tooltip.total.meters.usage.points=Nombre total de compteurs attribuÃ©s aux points d'utilisation
dashboard.key.indicator.indicator.total.active.meters.usage.points=Compteurs ACTIVE totaux (Points d'utilisation)
dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points=Nombre total de compteurs assignÃ©s aux points d'utilisation et actifs
dashboard.key.indicator.indicator.total.meters.device.store=Nombre total de compteurs (Magasin d'appareils)
dashboard.key.indicator.indicator.tooltip.total.meters.device.store=Nombre total de compteurs dans le magasin d'appareils

#RobertF June 22, 2017 Planio-4420 : Panel with the sales per resource chart
dashboard.sales.per.resource.graph.title=Sales per resource
dashboard.sales.per.resource.graph.day.description=Resource sales per day
dashboard.sales.per.resource.graph.month.description=Resource sales per month
dashboard.sales.per.resource.graph.xaxis.day.label=Date (click on chart for month view)
dashboard.sales.per.resource.graph.xaxis.month.label=Date (click on chart for day view)
dashboard.sales.per.resource.graph.yaxis.label=Total sales

# Thomas 21 June 2017 UP Blocking
usagepoint.field.blocking.help=Select the blocking type for the usage point.
usagepoint.field.blocking.label=Blocking Type
usagepoint.field.blocking.type.default=Not blocked
usagepoint.blocking.enter.reason=Enter a reason for blocking
usagepoint.blocking.select.reason=Select a reason for blocking

#RobertF June 2, 2017 Planio-4419 : Panel with the count regarding the Owner and Building usage point groups added over time
dashboard.groups.added.graph.title=Groupes de points d'utilisation ajoutÃ©s
dashboard.groups.added.graph.day.description=Groupes ajoutÃ©s par jour
dashboard.groups.added.graph.month.description=Groupes ajoutÃ©s par mois
dashboard.groups.added.graph.xaxis.day.label=Date (Cliquez sur le graphique pour voir le mois)
dashboard.groups.added.graph.xaxis.month.label=Date (Cliquez sur le graphique pour voir le jour)
dashboard.groups.added.graph.yaxis.label=Groupes ajoutÃ©s

# Joel 30 May 2017  Centian Data display planio 4429
meter.centian.header=Information sur le compteur Centian
meter.centian.kwh.credit.remaining=kWh CrÃ©dit restant:
meter.centian.currency.credit.remaining=UnitÃ© monÃ©taire de crÃ©dit restant:
meter.centian.number.disconnections=Le nombre de dÃ©connexions:
meter.centian.tamper.detected=Les Ã©tats de sabotage dÃ©tectÃ©s:
meter.centian.tamper.none=Aucun Ã©tat de sabotage n'a Ã©tÃ© dÃ©tectÃ©
meter.centian.tamper.updated=Date d'info rÃ©cupÃ©rÃ©e: 
meter.centian.tamper.overpower=Dessus du Puissance
meter.centian.tamper.overvoltage=Surtension
meter.centian.tamper.lowvoltage=Tension basse
meter.centian.tamper.overfrequency=Sur frÃ©quence
meter.centian.tamper.lowfrequency=FrÃ©quence Basse
meter.centian.tamper.reverseenergy=Inverser l'Ã©nergie
meter.centian.tamper.opencover=Couverture ouverte
meter.centian.tamper.magnettamper=DÃ©tection de sabotage magnÃ©tique
meter.centian.tamper.bypassearth=Contournement/Sabotage Terrestre 
meter.centian.tamper.sequenceerror=Erreur de sÃ©quence
meter.centian.tamper.overtemperature=TempÃ©rature excessive
meter.centian.tamper.lowtemperature=TempÃ©rature Basse
meter.centian.tamper.phaseunbalance=DÃ©sÃ©quilibre de phase
meter.centian.tamper.phasevoltageloss=Perte de tension de phase
meter.centian.tamper.tariffconfigerror=Erreur de configuration tarifaire
meter.centian.tamper.metrologyfail=Ãchec de la mÃ©trologie

meter.centian.current.tamper.status.header=Ãtat de sabotage pour le compteur
meter.centian.current.tamper.status.description=Des Ã©tats de sabotage avec une tique ont Ã©tÃ© dÃ©tectÃ©s sur ce compteur.
meter.centian.current.tamper.status.description.none=Aucun Ã©tat de sabotage n'a Ã©tÃ© dÃ©tectÃ© sur ce compteur.
meter.centian.current.tamper.status.updated=Ãtat de sabotage mis Ã  jour:

#Rencia 24 May 2017 Improve Cell phone validation
meter.online.bulk.customer.phone.help=Entrez le numÃ©ro de tÃ©lÃ©phone du client auquel un jeton d'Ã©mission gratuit sera envoyÃ© par SMS, le cas Ã©chÃ©ant. Les numÃ©ros SMS doivent Ãªtre en format international, en commenÃ§ant par un +.
messaging.recipient.help=Pour les SMS, doit utiliser les numÃ©ros de tÃ©lÃ©phone internationaux.
cellPhone.pattern.description=Le champ de numÃ©ro de tÃ©lÃ©phone pour sms doit Ãªtre en format international, Ã  commencer par un +. Vous pouvez utiliser des espaces blancs, des parenthÃ¨ses (), des hypensiques et des pÃ©riodes - ceux-ci sont Ã©liminÃ©s et le numÃ©ro de tÃ©lÃ©phone rÃ©sultant (sauf le +) doit comporter au moins 4 chiffres, maximum 25 de longueur, selon vos paramÃ¨tres rÃ©gionaux. Renvoyez l'espace rÃ©servÃ©.

### May 17, 2017 : RobertF : Admin dashboard workspace ###
admin.dashboard.title=Tableau de bord (Admin)

# Patrick | May 15, 2017 | planio-4443 - Updates for help message from field behaviour change
meter.algorithmcode.help=SÃ©lectionnez le code d'algorithme correct. Ce champ est requis pour l'activation - le compteur ne peut pas Ãªtre sauvegardÃ© Ã  moins qu'un code d'algorithme ne soit sÃ©lectionnÃ©.
meter.tokentechcode.help=SÃ©lectionnez le jeton de code tech correct. Ce champ est requis pour l'activation - le compteur ne peut pas Ãªtre sauvegardÃ© Ã  moins qu'un jeton de code tech soit sÃ©lectionnÃ©.
meter.supplygroupcode.help=Entrez le code de groupe d'approvisionnement actuel. Ce champ est requis pour l'activation - l'enregistrement ne peut pas Ãªtre enregistrÃ© sans lui.
meter.tariffindex.help=Entrez l'index tarifaire actuel. Ce champ est requis pour l'activation - le dossier ne peut pas Ãªtre enregistrÃ© sans lui.

#Thomas 8th May 2017 Tariff Date validation
tariff.error.startdate.unique=Date de dÃ©but reproduite. SpÃ©cifiez une date de dÃ©but unique.
tariff.error.startdate=La date de dÃ©but doit Ãªtre Ã  l'avenir et supÃ©rieure Ã  la date de dÃ©but du tarif actuellement actif.

### May 2, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.title=Indicateurs clef
dashboard.key.indicator.description=Indicateurs clÃ©s du systÃ¨me au cours de divers dÃ©lais.
dashboard.key.indicator.indicator=Indicateur
dashboard.key.indicator.value.today=Aujourd'hui
dashboard.key.indicator.value.monthtodate=Le mois courant
dashboard.key.indicator.value.lastmonth=Le mois dernier

#Rencia 24 April 2017 Meter Bulk Online Search / Capture : Free Issue Token
meter.online.bulk.free.issue.title=Jeton d'Ã©mission gratuit
meter.online.bulk.free.issue.generate=GÃ©nÃ©rer un jeton
meter.online.bulk.free.issue.sms.token=Jeton SMS au locataire
meter.online.bulk.free.issue.sms.token.help=SÃ©lectionnez ici si vous souhaitez que le jeton d'Ã©mission gratuit soit envoyÃ© au locataire?
meter.online.bulk.free.issue.check.sms.not.selected=Vous avez choisi de gÃ©nÃ©rer un jeton d'Ã©mission gratuit, mais PAS de l'envoyer par SMS.
meter.online.bulk.free.issue.sms.invalid.phone=Pour envoyer un jeton par SMS, doit avoir un numÃ©ro de tÃ©lÃ©phone portable valide 
meter.online.bulk.free.issue.invalid.units=Le nombre d'unitÃ©s de jeton d'Ã©mission gratuit doit Ãªtre numÃ©rique et supÃ©rieur Ã  zÃ©ro 
meter.online.bulk.free.issue.sms=Jeton d'Ã©mission gratuit {2}: NumÃ©ro de compteur: {0}  Jeton: {1}
meter.online.bulk.free.issue.token.null=Compteur a Ã©tÃ© ajoutÃ© au groupe mais Impossible de gÃ©nÃ©rer le jeton d'Ã©mission gratuit.
meter.online.bulk.free.issue.token.error=Compteur a Ã©tÃ© ajoutÃ© au groupe mais erreur de jeton: {0}
credit.token.link=Afficher les jetons de crÃ©dit
eng.token.link=Afficher les jetons d'ingÃ©nierie


#Rencia 21 April 2017 Meter Bulk Online Search / Capture : Edit / Remove buttons in table
meter.online.bulk.no.edit=Seuls certains champs sur les points d'utilisation ACTIF peuvent Ãªtre modifiÃ©s ici. Utilisez le lien pour accÃ©der Ã  la page de Point D'utilisation pour les autres.
meter.online.bulk.no.remove=Le point d'utilisation n'a pas de compteur Ã  supprimer. Utilisez le lien pour accÃ©der Ã  la page de Point D'utilisation.
button.clear.panel=Effacer Panneau
button.clear.groups=Effacer Groupes
online.bulk.panel.tariffindex.help=Entrez l'index tarifaire actuel. Ce champ est requis pour l'activation. Pour modifier un compteur existant, utilisez la page de Point D'utilisation.
online.bulk.panel.supplygroupcode.help=Entrez le code de groupe d'approvisionnement actuel. Ce champ est requis pour l'activation. Pour modifier un compteur existant, utilisez la page de Point D'utilisation.
error.field.breakerid.max=L'ID du disjoncteur ne doit pas dÃ©passer 100 caractÃ¨res maximum.
meter.online.bulk.meter.updated=Compteur {0} mise Ã  jour

### April 11, 2017 : Patrick : Send Reprint ###
button.send_reprint=Envoyer une rÃ©impression
messaging.type.sms=Sms
messaging.type.email=Email
messaging.recipient=Destinataire
messaging.message.type=Type de message
messaging.message.label=Message
messaging.token.reprint.email.subject=Demande de rÃ©impression de jeton
token.label=Jeton
error.field.required.recipient.email=L'adresse e-mail du destinataire est requise
error.field.required.recipient.phone=Le numÃ©ro de tÃ©lÃ©phone du destinataire est requis
error.field.validity.phone=Le numÃ©ro de tÃ©lÃ©phone est invalide.

notification.message.send.status.sms=Message SMS envoyÃ© avec succÃ¨s
notification.message.send.status.email=Message par email envoyÃ© avec succÃ¨s

messaging.txn.date=Date de la transaction
messaging.txn.meter_no=NumÃ©ro du compteur
messaging.txn.token=Jeton

#Rencia 31 March 2017 Meter Meter Bulk Online Search / Capture : Popup more information when click on meter in table
more.info=Plus D'information - Faites glisser ici
popup.label=Champ
popup.value=Valeur

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add pricing Structure Tariff Popup
online.bulk.panel.tariff.title=Tarif actuel
meter.online.bulk.add.group.title=Ajouter / Modifier les groupes

#Robertf 27 March 2017 Usage Point page check for valid installation date
error.field.startdate.invalid=La date de dÃ©but n'est pas une date valide. Format = {0}

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add New Group function & phone no
customer.phone=NumÃ©ro de TÃ©lÃ©phone


#Rencia 10 March 2017 Meter Bulk Online Search / Capture add group entry function
grouptype.field.layout.order=Ordre de mise en page
grouptype.field.layout.order.help=C'est l'ordre dans lequel la sÃ©lection des cases de groupe de point d'utilisation sont affichÃ©es sur la page. Si elle n'est pas entrÃ©e, la valeur par dÃ©faut sera APRÃS tous les numÃ©ros, dans l'ordre alphabÃ©tique.
grouptype.field.layout.order.error.numeric=L'ordre de mise en page doit Ãªtre un nombre entier numÃ©rique.
grouptype.field.layout.order.error.duplicate=L'ordre de mise en page est reproduite. Identique au groupe {0}. 
error.field.installdate.invalid=La date d'installation n'est pas une date valide. Format = {0}

#Rencia 27 February 2017 Meter Bulk Online Search / Capture v1
meter.online.bulk.header=Ajouter des compteurs au groupe/s
meter.online.bulk.title=SÃ©lectionnez ou Ajoutez un groupe/s
meter.online.bulk.installdate=Date D'installation
meter.online.bulk.select.meters.button=SÃ©lectionnez MÃ¨tres pour le groupe/s
meter.online.bulk.usagepoint.status=Ãtat
meter.online.bulk.search.no.results=Aucun rÃ©sultat de recherche correspondant n'a Ã©tÃ© trouvÃ©.
meter.online.bulk.add.meters.to.groups=Ajouter des compteurs au groupe/s
meter.online.bulk.button.add=Ajouter le compteur
meter.online.bulk.add.meter=Ajouter le compteur
meter.online.bulk.edit.meter=Modifier le compteur

online.bulk.panel.up.group.info.title=Informations sur le point d'utilisation
online.bulk.panel.customer.info.title=Informations client
online.bulk.panel.meter.help=Commencez Ã  taper le numÃ©ro du compteur, les compteurs qui commencent avec ces chiffres pour le magasin de dispositifs sÃ©lectionnÃ© apparaÃ®tront dans une liste dÃ©roulante. Cliquez sur un pour le sÃ©lectionner.
online.bulk.panel.select.store.help=Le magasin dont les compteurs seront sÃ©lectionnÃ©s. Si aucun n'a Ã©tÃ© sÃ©lectionnÃ©, les numÃ©ros de compteur de tous les magasins seront affichÃ©s dans les suggestions du compteur. Notez que lorsque le compteur est affectÃ© Ã  un point d'utilisation, il sera automatiquement retirÃ© du magasin.
online.bulk.panel.suite.no.text=UnitÃ©
online.bulk.panel.tenant.text=Locataire
online.bulk.panel.surname.help=Peut entrer le nom de famille du locataire. La sÃ©quence numÃ©rique sera par dÃ©faut. Ceci est un champ obligatoire - le point d'utilisation ne peut pas Ãªtre activÃ© sans lui.

online.bulk.panel.error.supply.grpcode.empty=Pour les compteurs STS, le code du groupe d'approvisionnement doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.algorithm.code.empty=Pour les compteurs STS, le code du groupe d'approvisionnement doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.token.tech.code.empty=Pour les compteurs STS, le code du jeton techn doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.tariff.indx.empty=Pour les compteurs STS, l'index tarifaire doit Ãªtre entrÃ© et la longueur maximale 2
online.bulk.panel.error.key.rev.indx.empty=Pour les compteurs STS, le Code de RÃ©vision ClÃ© doit Ãªtre entrÃ© et doit Ãªtre numÃ©rique
online.bulk.panel.error.ps.meter.model.empty=Le modÃ¨le du compteur est requis pour la sÃ©lection de la structure des prix
online.bulk.panel.error.model.new.pricingstructure.required=Le modÃ¨le du compteur ne prend pas en charge la structure des prix.
online.bulk.panel.error.meter.num.not.found=Le numÃ©ro du compteur n'est pas sur la base de donnÃ©es. 
online.bulk.panel.error.meter.already.linked=Le numÃ©ro du compteur est dÃ©jÃ  liÃ© au Point D'utilisation. 
online.bulk.panel.error.meter.linked.to.diff.store=Le numÃ©ro du compteur se trouve dans un magasin diffÃ©rent, {0}. 
online.bulk.meter.error.groups.not.selected=Pour sauvegarder un compteur, tous les groupes de points D'utilisation requis doivent Ãªtre sÃ©lectionnÃ©s.

question.confirm.continue.new=Capturez-vous comme nouveau compteur pour ce regroupement? 
question.confirm.continue.open.up.page=Ouvrez le onglet Point D'utilisation pour cela?
question.confirm.continue.save.anyway=Continuer et lier ce regroupement?

meter.key.revision.help=Entrez le numÃ©ro de RÃ©vision ClÃ© actuel. Ce champ est requis pour l'activation.
meter.key.revision=RÃ©vision ClÃ©

online.bulk.sts.meter.save.error=DÃ©pannage du compteur STS, codes non trouvÃ©s.
online.bulk.meter.save.error=DifficultÃ© de sauvegarder le compteur.

meter.sts.length=Les numÃ©ros de compteur STS doivent avoir entre 11 et 13 caractÃ¨res.

#Njigi 3 March 2017
usagepoint.charge.button.writeoff=Annuler les frais
usagepoint.charge.button.upchargeview=Afficher les frais des montants en suspens
usagepoint.charge.view.dialog.heading=Afficher les frais des montants en suspens
usagepoint.charge.writeoff.dialog.heading=Liste des frais en suspens : {0}
usagepoint.charge.no.data=Aucune donnÃ©e n'est disponible pour Ãªtre visionnÃ©e.
usagepoint.charge.view.dialog.nodate.filter=SÃ©lectionnez la date.
usagepoint.charge.view.dialog.invalid.date=La date sÃ©lectionnÃ©e doit Ãªtre aprÃ¨s la derniÃ¨re date cyclique affichÃ©e
usagepoint.charge.view.dialog.invalid.date2=La date sÃ©lectionnÃ©e ne peut pas Ãªtre Ã  l'avenir
usagepoint.charge.writeoff.trans.success=Annulation traitÃ© avec succÃ¨s.
usagepoint.charge.writeoff.trans.failure=Annulation traitÃ© a Ã©chouÃ©. Contactez le support.
chargewriteoff.save.error=L'enregistrement du dossier de l'annulation des frais a Ã©chouÃ©.

#Njigi 30 January 2017 ####
auxaccount.trans.upload=Envoi aux transactions auxiliaires
auxaccount.trans.upload.heading=Envoi aux transactions auxiliaires
auxaccount.trans.upload.auxaccountname=Nom de Compte Auxiliaire
auxaccount.trans.upload.agreementref=L'Accord Ref
auxaccount.trans.upload.data.title=Importation de l'opÃ©rations d'ajustement du solde de Compte Auxiliare
auxaccount.trans.upload.data.description=SÃ©lectionnez le fichier CSV contenant les transactions Auxiliaire pour l'importation dans le systÃ¨me de gestion des compteurs.
auxaccount.trans.upload.invalid.auxaccountname=Nom de compte Auxiliaire maximum 100 caractÃ¨res
auxaccount.trans.upload.invalid.auxaccount=Aucun Compte Auxiliaire avec le nom et L'accord de RÃ©fÃ©rence fournis existe
auxaccount.trans.upload.process.failed=Erreur systÃ¨me lors de la transaction:Nom du Compte Auxiliaire= {0}, Ref. D'accord= {1}. Essayez de renvoyer le fichier.
trans.bulk.upload.amt.incl.tax=Montant incl impÃ´t
trans.bulk.upload.amt.tax=Montant de l'impÃ´t
trans.bulk.upload.trans.date=Date de la transaction
trans.bulk.upload.account.ref=RÃ©fÃ©rence du Compte
trans.bulk.upload.comment=Commentaire
trans.bulk.upload.invalid.amt.incl.tax=Montant incl. l'impÃ´t n'est pas numÃ©rique
trans.bulk.upload.invalid.amt.tax=Le montant de l'impÃ´t n'est pas numÃ©rique
trans.bulk.upload.invalid.account.ref=RÃ©fÃ©rence du compte maximum de 100 caractÃ¨res
trans.bulk.upload.invalid.comment=Commentaire maximum 255 caractÃ¨res
trans.bulk.upload.invalid.our.ref=Notre rÃ©fÃ©rence maximum 100 caractÃ¨res (Renommer fichier)
trans.bulk.upload.trans.date.in.future=La date de la transaction doit Ãªtre vide (par dÃ©faut pour la date de traitement) ou correctement formatÃ©e

#Njigi 16 January 2016 ####
auxaccount.upload=TÃ©lÃ©chargement de Compte Auxiliaire
auxaccount.upload.heading=TÃ©lÃ©chargement de compte Auxiliaire
auxaccount.upload.data.title=Importer des donnÃ©es de compte Auxiliaires
auxaccount.upload.data.description=SÃ©lectionnez le fichier CSV contenant les donnÃ©es du compte auxiliaire pour l'importation dans le systÃ¨me de gestion des compteurs.
auxaccount.upload.errors=Erreurs
auxaccount.upload.identifierType=Type d'Identificateur
auxaccount.upload.identifier=Identifiant
auxaccount.upload.auxaccountname=Nom du Compte Auxiliaire
auxaccount.upload.auxtype=Nom de type Auxiliaire
auxaccount.upload.accountpriority=PrioritÃ© au compte
auxaccount.upload.chrgschdlname=Nom de frais de l'horaire
auxaccount.upload.principleamaount=Montant Principale
auxaccount.upload.balance=Balance
auxaccount.upload.customerref=Ref. De l'Accord client
auxaccount.upload.invalid.identifiertype=TypeIdentificateur doit  Ãªtre accordRef / NomPointUtilisation / numÃ©roCompteur
auxaccount.upload.invalid.identifier=Identificateur Invalide - pas dans la base de donnÃ©es
auxaccount.upload.invalid.agreement=Point d'Utilisation n'a pas de contrat client en place
auxaccount.upload.invalid.usagepoint.or.agreement=Compteur n'a pas de Point d'Utilisation ou le Point d'Utilisation n'a pas d'accord
auxaccount.upload.invalid.auxaccountname=Nom de compte Auxiliaire maximum 100 caractÃ¨res
auxaccount.upload.invalid.principleamaount=Le montant Principale n'est pas numÃ©rique
auxaccount.upload.invalid.balance=Le montant du solde n'est pas numÃ©rique
auxaccount.upload.successful.count=Total de {0} TÃ©lÃ©chargement de Compte Auxiliare traitÃ© avec succÃ¨s.

#Antonyo 12 January 2017  ####
#power.limit.edit.label.prompt=Nom
#power.limit.edit.label.help=Nom de la valeur limite de puissance. C'est l'affichage du texte
power.limit.add.button.prompt=Ajouter la limite de puissance
power.limit.table.label.header=Nom de limite de puissance
power.limit.table.value.header=Valeur limite de puissance
power.limit.edit.popup.header=Modifier la limite de puissance

#Patrick 02 December 2016 ####
user.custom.field.datatype.text=Texte
user.custom.field.datatype.numeric=NumÃ©rique
user.custom.field.datatype.date=Date
user.custom.field.datatype.boolean=Vrai/Faux
user.custom.field.datatype.list=Liste
button.done=TerminÃ©

usagepointgroup.custom.field.error.text=Une valeur est nÃ©cessaire pour ce champ
usagepointgroup.custom.field.error.list=Vous devez sÃ©lectionner un Ã©lÃ©ment de la liste
usagepointgroup.custom.field.error.date.empty=Une date valide est nÃ©cessaire. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.date.invalid=Format de date invalide. Format (aaaa-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.numeric=Un numÃ©ro valide doit Ãªtre fourni

button.view.usagepointgroup=Afficher DÃ©tails

usagepointgroup.custom.field.default.datatype.description=Le Type de donnÃ©es pour ce champ personnalisÃ© ou une liste sÃ©parÃ©e par des virgules pour les menus dÃ©roulants
usagepointgroup.hierarchy.no.additionalinfo=Aucune information supplÃ©mentaire pour ce groupe de PointUtilisation

appsettings.popup.button.add=Ajouter ÃlÃ©ment
appsettings.popup.table.add=Nouvel ÃlÃ©ment - Cliquez pour Modifier
appsettings.popup.table.title=Liste des ÃlÃ©ments
appsettings.popup.table.note.dups=Les Ã©lÃ©ments en copie seront supprimÃ©s lorsque TerminÃ©

appsetting.field.datatype=Type de DonnÃ©es
appsetting.field.datatype.help=Type de donnÃ©es pour le ParamÃ¨tre d'Application. Pour la liste des Types de DonnÃ©es, Les Ã©lÃ©ments de la liste seront rejetÃ©s lorsque le type de donnÃ©es est changÃ© en: Texte, NumÃ©rique, Date, or Vrai/Faux.
appsetting.field.datatype.listitems=Modifier ÃlÃ©ments

appsettings.list.duplicate.item=L'Ã©lÃ©ment en copie entrÃ©.

#Njigi 22 December 2016 ####
bulk.upload.custom.fields.get.error=Erreur de base de donnÃ©es obtenant des paramÃ¨tres d'application de champ personnalisÃ©s! Contactez le support.

#Njigi 28 November 2016 ####
bulk.upload.meterupload.heading=Masse TÃ©lÃ©chargement de compteurs
bulk.upload.meterupload.enddevicestorename=Nom du Magasin d'Appareils
bulk.upload.meterupload.data.title=DÃ©tails du Compteur d'Importation
bulk.upload.meterupload.description=SÃ©lectionnez le fichier CSV contenant les dÃ©tails du compteur pour l'importation dans le SystÃ¨me de Gestion des Compteurs.

#Special Actions - joelc 01/12/16
link.special.actions=Actions avec des raisons
link.special.action.reasons=Raisons SpÃ©ciales d'Action
button.viewreasons=Afficher la liste des raisons
special.action=Action spÃ©ciale
special.actions.header=Configurer des Actions SpÃ©ciales
special.actions.title=Mettre Ã  jour les Actions SpÃ©ciales
special.action.name=Nom de l'Action
special.action.name.help=Le nom de cette action spÃ©ciale.
special.action.reason.required=Requis
special.action.reason.required.help=Est-il nÃ©cessaire ou facultatif de fournir une raison
special.action.reason.input.type=Type d'EntrÃ©e
special.action.reason.input.type.help=Est-ce que la raison est donnÃ©e en tant que texte gratuit, est-elle sÃ©lectionnÃ©e Ã  partir d'une liste de raisons ou est-ce que c'est un texte gratuit et / ou sÃ©lectionnÃ© dans une liste 
special.action.description=Description de l'Action
special.action.description.help=Une description de l'action spÃ©ciale qui permet de fournir une raison pour laquelle l'action doit Ãªtre fournie
special.actions.field.name=Nom de l'action
special.actions.field.description=Description
special.actions.field.reason.required=Requis
special.actions.field.reason.input.type=Type d'EntrÃ©e
special.actions.reason.inputtype.freetext=Entrez la raison dans la zone de texte
special.actions.reason.inputtype.selected=SÃ©lectionnez la raison de la liste
special.actions.reason.inputtype.both=Entrez la raison ou sÃ©lectionnez la liste

special.action.reasons.header=CrÃ©er des raisons
special.action.reasons.title=Raisons de l'Action spÃ©ciale
special.action.reasons.title.add=Ajouter une nouvelle raison
special.action.reasons.title.update=RÃ©vision raison
special.action.reasons.field.name=Raison
special.action.reasons.field.description=Description de la raison
special.action.reasons.field.recordstatus=Actif
special.action.reasons.name=Raison
special.action.reasons.name.help=Entrez une raison possible pour effectuer cette action. Cela sera affichÃ© dans la liste dÃ©roulante de cette action. 
special.action.reasons.description=Description
special.action.reasons.description.help=DÃ©crivez ce que c'est pour cette raison.
special.action.reasons.active=Actif
special.action.reasons.active.help=Cette raison est-elle active?
special.action.reason=Raison
special.action.and.or=et/ou
special.action.enter.reason=Entrez une raison de cette action
special.action.select.reason=SÃ©lectionnez une raison pour cette action
error.special.action.reason.required=Une raison de cette action est requise     
usagepoint.deactivate.enter.reason=Entrez une raison pour cette dÃ©sactivation
usagepoint.deactivate.select.reason=SÃ©lectionnez une raison pour cette dÃ©sactivation

#Rencia 24 November 2016  ####
bulk.upload.pricingstructure.not.active=La structure de prix n'est pas actif.

#Rencia 16 November 2016  ####
error.field.active.invalid=L'actif doit Ãªtre vide (Implique OUI) ou non.
error.field.metermodelid.null=Le modÃ¨le du compteur est requis.
error.field.ststariffindex.max=L'indice tarifaire doit Ãªtre infÃ©rieur Ã  {0} caractÃ¨res.
error.field.servicelocationid.null=Lieu du service est requis
error.field.customerkindid.null=Type de client
error.field.customerid.null=L'ID du client est requis
error.field.accountbalance.null=Le solde du compte est requis.

#Bulk Upload - standard
bulk.upload.download.sample=TÃ©lÃ©charger un exemple de tableur.
bulk.upload.download.sample.button=TÃ©lÃ©charger comme .CSV
bulk.upload.file.select.labeltext=SÃ©lectionnez le fichier CSV pour tÃ©lÃ©charger
bulk.upload.select.file.help=SÃ©lectionnez un fichier contenant les informations dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me
bulk.upload.csv.button=TÃ©lÃ©charger des donnÃ©es CSV
bulk.upload.process.button=TÃ©lÃ©chargement de Processus
bulk.upload.errors=Erreurs
bulk.upload.object.creation.error=Erreur lors de la crÃ©ation {0} objet. Contactez le support!
bulk.upload.file.action.unknown=Action de tÃ©lÃ©chargement de fichier inconnu, contacter Support
bulk.upload.file.none=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour Ãªtre tÃ©lÃ©chargÃ©
bulk.upload.invalid.filename=Nom de fichier incorrect - Un trait d'union ou une pÃ©riode manquant. Les noms de fichier sont attendus comme xxxxxx-rÃ©fÃ©rence.csv oÃ¹ xxxxxx est votre identifiant de fichier choisi, par exemple. CompteurTÃ©lÃ©charger, et <rÃ©fÃ©rence> est sauvegardÃ© comme 'notre RÃ©f' sur les transactions
bulk.upload.invalid.filename.changed=Le nom de fichier a changÃ© entre les Ã©tapes! Ãtait {0}; Ã  prÃ©sent {1}
bulk.upload.invalid.cannot.create.dir=ERREUR! Impossible de crÃ©er le rÃ©pertoire. Contactez Support.
bulk.upload.file.unrecognized.heading.error=Titre de colonne non reconnu dans le fichier de tÃ©lÃ©chargement: {0}
bulk.upload.table.heading.valid=DonnÃ©es de TÃ©lÃ©chargement Valides : Ã©chantillon des 15 premiÃ¨res lignes dans le fichier
bulk.upload.table.heading.errors=TÃ©lÃ©charger des donnÃ©es : Erreurs
bulk.upload.trans.validation.errors=Erreurs de validation trouvÃ©es. RÃ©parez et soumettre Ã  nouveau. Maximum 15 erreurs sont traitÃ©es Ã  tout moment
bulk.upload.invalid.unexpected.commas=Virgules Ã  l'intÃ©rieur des champs - ne peut pas identifier les champs sÃ©parÃ©s avec prÃ©cision 
bulk.upload.filename=Nom de fichier sÃ©lectionnÃ©={0}
bulk.upload.process.failed=Erreur systÃ¨me lors du tÃ©lÃ©chargement aprÃ¨s {0} dossiers. Essayez de renvoyer le fichier.
bulk.upload.active=Actif
#Bulk Upload Validation Errors
bulk.upload.invalid.required.field={0} Requis
bulk.upload.invalid.nonexisting.field={0} Pas sur la base de DonnÃ©es
bulk.upload.duplicate.field={0} existe dÃ©jÃ , soit sur la base de donnÃ©es, soit sur ce fichier de tÃ©lÃ©chargement
bulk.upload.invalid.field={0} est invalide
bulk.upload.invalid.parsedate=Ne peut pas analyser {0} - vÃ©rifier format
bulk.upload.file.process.error=Erreur lors du processus du fichier

#Bulk Upload - meter / Customer / Usage Point
bulk.upload.data.title.meter=Importer Compteur, Point Utilisation et DÃ©tails du client
bulk.upload.data.description.meter=SÃ©lectionnez le fichier CSV contenant le compteur et les dÃ©tails liÃ© pour l'importation dans le SystÃ¨me de Gestion des Compteurs.
bulk.upload.metertype=Type Compteur
bulk.upload.meternum=Num Compteur
bulk.upload.serialnum=Num sÃ©rie
bulk.upload.mrid=NumCompteur Externe
bulk.upload.metermodelname=ModÃ¨le Compteur
bulk.upload.enddevicestore=Magasin d'appareils
bulk.upload.breakerid=Id du Disjoncteur
bulk.upload.ststokentechcode=Code de Jeton Tech
bulk.upload.stsalgorithmcode=Code Algorithme
bulk.upload.stssupplygroupcode=Code de Groupe d'Approvisionnement
bulk.upload.stskeyrevisionnum=NumÃ©ro de RÃ©vision ClÃ©
bulk.upload.ststariffindex=Indice Tarifaire
bulk.upload.usagepointname=Nom du Point d'Utilisation
bulk.upload.installationdate=Date D'installation
bulk.upload.pricingstructurename=Structure des prix
bulk.upload.uperfnumber=PU Num ERF
bulk.upload.upstreetnum=PU Num de Rue
bulk.upload.upbuildingname=PU BÃ¢timent
bulk.upload.upsuitenum=PU Num Suite
bulk.upload.upaddressline1=PU Addr 1
bulk.upload.upaddressline2=PU Addr 2
bulk.upload.upaddressline3=PU Addr 3
bulk.upload.uplatitude=PU Latitude
bulk.upload.uplongitude=PU Longitude
bulk.upload.upcustomvarchar1=PU CaractÃ¨re PersonnalisÃ©1
bulk.upload.upcustomvarchar2=PU CaractÃ¨re PersonnalisÃ©2
bulk.upload.upcustomnumeric1=PU NumÃ©rique PersonnalisÃ©1
bulk.upload.upcustomnumeric2=PU NumÃ©rique PersonnalisÃ©2
bulk.upload.upcustomtimestamp1=PU Horodatage1
bulk.upload.upcustomtimestamp2=PU Horodatage2
bulk.upload.customerkind=Type de Client
bulk.upload.companyname=Nom de la Compagnie
bulk.upload.taxnum=Num d'impÃ´t
bulk.upload.firstnames=PrÃ©noms
bulk.upload.surname=Nom de famille
bulk.upload.initials=Initiales
bulk.upload.title=Titre
bulk.upload.email1=Email1
bulk.upload.email2=Email2
bulk.upload.phone1=TÃ©lÃ©phone1
bulk.upload.phone2=TÃ©lÃ©phone2
bulk.upload.custerfnumber=Cust Erf
bulk.upload.custstreetnum=Cust Num de Rue
bulk.upload.custbuildingname=Cust BÃ¢timent
bulk.upload.custsuitenum=Suite du Client
bulk.upload.custaddressline1=Cust Adr 1
bulk.upload.custaddressline2=Cust Adr 2
bulk.upload.custaddressline3=Cust Adr 3
bulk.upload.custlatitude=Latitude du Client
bulk.upload.custlongitude=Longitude du Client
bulk.upload.custcustomvarchar1=Cust CaractÃ¨re PersonnalisÃ©1
bulk.upload.custcustomvarchar2=Cust CaractÃ¨re PersonnalisÃ©2
bulk.upload.custcustomnumeric1=Cust NumÃ©rique PersonnalisÃ©1
bulk.upload.custcustomnumeric2=Cust NumÃ©rique PersonnalisÃ©2
bulk.upload.custcustomtimestamp1=Cust Horodatage PersonnalisÃ©1
bulk.upload.custcustomtimestamp2=Cust Horodatage PersonnalisÃ©2
bulk.upload.agreementref=Accord RÃ©f
bulk.upload.startdate=Date de DÃ©but de l'Accord
bulk.upload.accountname=Nom du compte
bulk.upload.accountbalance=Solde du Compte
bulk.upload.lowbalancethreshold=Solde de seuil bas 
bulk.upload.notificationemail=Notif Email
bulk.upload.notificationphone=Notif TÃ©lÃ©phone
bulk.upload.notifylowbalance=Notifier Solde Bas
#Invalid Meter bulk upload errors
bulk.upload.metertype.incompatible.stsinfo=Les dÃ©tails de cryptage STS sont entrÃ©s pour un type de compteur non-STS
bulk.upload.metertype.incompatible.metermodel=Le ModÃ¨le du Compteur n'est pas compatible avec le Type de Compteur
bulk.upload.invalid.pricingstructure.incompatible.metermodel=Structure de prix incompatible avec ModÃ¨le du Compteur
bulk.upload.customer.account.notfor.STS=Les compteurs STS ne doivent pas avoir d'Informations sur le Compte Client

#Rencia 11 November 2016  ####
tariff.field.namibia.neflevy=NEF ImpÃ´t
tariff.field.namibia.neflevy.help=Entrer l'impÃ´t du National Energy Fund
tariff.field.namibia.ecblevy=ECB ImpÃ´t
tariff.field.namibia.ecblevy.help=Entrer l'impÃ´t du Conseil de contrÃ´le de l'Ã©lectricitÃ© 
tariff.error.positive.or.zero=La valeur doit Ãªtre positive ou nulle

#Rencia 9 November 2016  ####
tariff.field.discount=Type de paiement RÃ©ductions
tariff.field.heading.paytype=Type de paiement
tariff.field.heading.discount=Remise %
tariff.field.heading.description=Description
tariff.error.discount=Remise % doit Ãªtre positif ou nul.
tariff.error.discount.descrip=Si la rÃ©duction % entrÃ©, et non zÃ©ro, la description est requise.

# Rencia 20 October 2016  ####
### New
tariff.field.cycle.name=Cycle
tariff.field.cycle.name.help=SÃ©lectionnez le cycle de facturation.
tariff.field.cost.help=Le montant hors impÃ´t Ã  rembourser selon la sÃ©lection de cycle.

### Took out "Monthly"
tariff.field.cost.name=Nom du CoÃ»t
tariff.field.cost.name.help=Le nom de ce coÃ»t qui finira par le reÃ§u du client.
tariff.field.cost=CoÃ»t Excl. ImpÃ´t

### Tariff Cycle Labels  NEW
tariff.cost.cycle.daily=Quotidien
tariff.cost.cycle.monthly=Mensuel
tariff.cost.cycle.error=Doit sÃ©lectionner un cycle valide.

### changed names
tariff.error.cyclic.charge.positive=Doit Ãªtre une valeur positive.
tariff.error.cyclic.charge=SpÃ©cifiez un coÃ»t pour le nom.
tariff.error.cyclic.charge.name=SpÃ©cifiez un nom pour le coÃ»t.

#Rencia 22 August 2016  ####
error.field.numeric.positive_not_zero=La valeur doit Ãªtre positive et ne pas Ãªtre nulle.
tariff.field.percent_charge_name=Nom du frais en pourcentage
tariff.field.percent_charge_name.help=Nom facultatif du pourcentage de frais qui sera affichÃ© sur le reÃ§u.
tariff.field.percent_charge=Frais en pourcentage
tariff.error.percent_charge.name=SpÃ©cifiez un nom pour le frais.
tariff.error.percent_charge.positive_not_zero=Doit Ãªtre une valeur positive et pas zÃ©ro.

##########################
#### General messages ####
##########################

application.default.title=iPay - Gestion Des Compteurs
application.title=Gestion Des Compteurs
workspace.usagepoint.information=Information et Services
unit.kilowatthour.symbol=kWh
unit.watts.symbol=W
unit.percent=%
changes_unsaved=* Les modifications non sauvegardÃ©es
menu.about_link=A propos de
application.info=PropulsÃ© par BizSwitch de iPay

###############
#### Error ####
###############
error.title=Erreur
error.general=Une erreur s'est produite et a Ã©tÃ© enregistrÃ©e. Veuillez informer L'administrateur du systÃ¨me
error.login=Votre tentative de connexion n'a pas rÃ©ussi. Veuillez rÃ©essayer.
error.denied=L'utilisateur {0} n'a pas la permission d'accÃ©der Ã  ce site.
error.accessdenied=Vous n'avez pas la permission d'accÃ©der Ã  cette fonction.
error.networkConnect=Une erreur s'est produite lors de la tentative de contact avec le serveur.
error.networkIO=Une erreur de communication sur le rÃ©seau s'est produite.
error.networkTimeout=Le serveur prend trop de temps pour rÃ©pondre.
error.networkUnknown=Une erreur inconnue s'est produite lors de la tentative de contact avec le serveur. Notifier l'administrateur.
error.delete=Impossible de supprimer
permission.denied=Permission refusÃ©e
permission.edit.denied=Modification de l'autorisation refusÃ©e
error.no.user=Aucun utilisateur actuel n'est disponible.
error.current.group=Groupe invalide spÃ©cifiÃ© pour le groupe actuel de l'utilisateur.
error.meter.accessdenied=Vous n'avez pas la permission d'accÃ©der Ã  ce compteur.
error.pricing.structure.accessdenied=Vous n'avez pas la permission de modifier l'allocation de la structure des prix.
error.pricing.structure.accessdenied.addmeter=Vous n'avez pas la permission de modifier l'allocation de la structure des prix, ne peut donc pas attacher le nouveau compteur ci-dessus Ã  ce point d'utilisation. Le compteur a Ã©tÃ© enregistrÃ© dans le magasin de pÃ©riphÃ©riques sÃ©lectionnÃ©.
error.meter.workspace.error=Erreur lors de l'ouverture de l'espace travail du compteur.
error.customer.workspace.error=Erreur lors de l'ouverture de l'espace travail du client.
error.usagepoint.workspace.error=Erreur d'ouverture de l'espace de travail du Point d'Utilisation.
error.no.user.assignedgroup=L'utilisateur actuel n'a pas de groupe assignÃ©.
error.login.locked=VÃ´tre compte a Ã©tÃ© bloquÃ©.
error.loginerror=Nom d'utilisateur ou mot de passe incorrect.
error.login.disabled=Votre compte a Ã©tÃ© dÃ©sactivÃ©.

###############
#### Login ####
###############
login.title=Connexion
login.form.title=Veuillez s'il vous plaÃ®t entrer votre nom d'utilisateur et votre mot de passe.
login.form.username=Nom d'utilisateur:
login.form.password=Mot de passe:
login.form.login=Connexion
login.form.remember_me=Souvenir de moi pendant deux semaines
login.form.password.forgotten=Mot de passe oubliÃ©?
password.form.instructions=Entrez l'adresse e-mail enregistrÃ©e de votre utilisateur.
password.email.invalid=Adresse e-mail invalide.
password.email.unknown.user=Utilisateur inconnu pour l'adresse email saisie.
password.multiple.users=Plusieurs utilisateurs correspondent Ã  l'adresse email saisie.
password.multiple.users.1=SÃ©lectionnez un ci-dessous et cliquez sur Envoyer.
password.reset=RÃ©initialiser le mot de passe. VÃ©rifiez votre adresse Ã©lectronique pour plus de dÃ©tails.
password.error.reset=Erreur: Impossible de rÃ©initialiser votre mot de passe.
adminuser.password.save.error=Erreur: Impossible d'enregistrer l'utilisateur mis Ã  jour.
email.password.reset.subject=RÃ©initialiser le mot de passe
password.form.email=Adresse Email:
password.form.submit=Soumettre

#########################
#### Password Change ####
#########################
password_change.title=Changement de mot de passe
password_change.form.title=Changer mon mot de passe
password_change.form.password1=Entrez un nouveau mot de passe:
password_change.form.password2=RÃ©-entrez le nouveau mot de passe:
password_change.form.submit=ValidÃ© le changement
password_change.success=Mot de passe changÃ© pour {0}
password_change.validate.equal=Les deux entrÃ©es de mot de passe ne sont pas Ã©gales
password_change.validate.ldap=Les utilisateurs authentifiÃ©s Ldap ne peuvent pas modifier leurs mots de passe. Contactez votre administrateur systÃ¨me Ldap.

########################################################################################################################
# GWT specific properties #
########################################################################################################################

## Errors
error.save=Erreur: {0} n'a pas pu Ãªtre enregistrÃ©.
error.field.required=Information requis.
error.field.is.required={0} est un champ obligatoire.
error.field.numeric.required={0} est un champ numÃ©rique requis.
error.no.selection=Aucune sÃ©lection valide n'a Ã©tÃ© faite.
error.data.null=Les donnÃ©es entrantes sont invalides.
error.datatype.null=Le {0} est invalide.
error.missing={0} {1} introuvable.
error.numeric.value=Entrez une valeur numÃ©rique valide.
error.token.retrieve=Impossible de rÃ©cupÃ©rer le jeton.
error.meter.load=Impossible d'afficher le compteur.
error.usagepoint.load=Impossible d'afficher le point d'utilisation.

# Field errors used by the Validation framework's annotations
error.field.id.null=L'ID est obligatoire
error.field.key.null=La clÃ© est nÃ©cessaire.
error.field.key.range=La clÃ© doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.name.null=Le nom est requis
error.field.name.range=Le nom doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.name.max=Le nom doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.value.null=Valeur est requise.
error.field.value.range=La valeur doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.description.null=La description est obligatoire.
error.field.description.range=La description doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.description.max=La description doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.recordstatus.null=L'Ãtat de l'enregistrement est requis.
error.field.contactname.null=Le nom du contact est requis.
error.field.contactname.range=Le nom du contact doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.contactemail=Le courrier Ã©lectronique du contact doit Ãªtre une adresse Ã©lectronique valide.
error.field.contactemail.max=Le courrier Ã©lectronique de contact doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.taxref.max=La rÃ©fÃ©rence impÃ´t doit Ãªtre infÃ©rieure aux caractÃ¨res {max}.
error.field.contactnumber.max=Le numÃ©ro de contact doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.supplyserver=Le code du groupe d'approvisionnement et la combinaison du numÃ©ro de rÃ©vision doivent Ãªtre uniques.
error.field.supplygroupcode.null=Le code du groupe d'approvisionnement est requis.
error.field.keyrevisionnum.null=Le numÃ©ro de rÃ©vision est requis.
error.field.supplygroupcode.format=Entrez une valeur numÃ©rique. 
error.field.supplygroupcode.range=Le code doit Ãªtre infÃ©rieur Ã  7 chiffres.
error.field.calccontents.null=Calc Contenue est un champ obligatoire.
error.field.schedulename.range=Nom du planning doit Ãªtre comprise entre {min} et {caractÃ¨res max}.
error.minmax.range.auxchargeschedule=Le montant minimum doit Ãªtre infÃ©rieur au montant maximal.
error.field.meternum.null=Le numÃ©ro du compteur est un champ obligatoire.
error.field.meternum.range=Le numÃ©ro du compteur doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.mrid.null=MRID est un champ obligatoire.
error.field.mrid.range=Le MRID doit Ãªtre entre les {min} et {max} caractÃ¨resr 
error.field.serialnum.max=Le numÃ©ro de sÃ©rie doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.meternumchecksum.max=Le seuil de contrÃ´le doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.ststokentechcode.max=Le code numerique du jeton doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.stsalgorithmcode.max=Code de l'algorithme doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.stsprevsupplygroupcode.max=Le code du groupe d'approvisionnement doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.stscurrtariffindex.max=L'indice tarifaire doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.addressline1.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.addressline2.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.addressline3.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.city.max=La ville doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.province.max=La province doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.country.max=Le pays doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.postalcode.max=Le code postal doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.erfnumber.max=Le numÃ©ro Erf doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.streetnum.max=Le numÃ©ro de la rue doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.buildingname.max=Le nom du bÃ¢timent doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.suitenum.max=NumÃ©ro de la suite doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.surname.null=Le nom de famille est obligatoire.
error.field.agreementref.null=La rÃ©fÃ©rence de l'accord est un champ obligatoire.
error.field.agreementref.duplicate=RÃ©fÃ©rence d'accord en double {0}. SpÃ©cifiez une rÃ©fÃ©rence unique.
error.field.email1.max=L'adresse Ã©lectronique doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.email1=Adresse email invalide.
error.field.email2.max=L'adresse Ã©lectronique doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.email2=Adresse email invalide.
error.field.phone1.max=Le numÃ©ro de tÃ©lÃ©phone doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.phone2.max=Le numÃ©ro de tÃ©lÃ©phone doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.phone=NumÃ©ro de tÃ©lÃ©phone invalide, seulement 0-9, '+', espace, trait d'union, 'ext' et 'x' autorisÃ©s. 'Ext' ou 'x' doit Ãªtre suivi d'un numÃ©ro d'extension numÃ©rique.
error.field.phone2=Second numÃ©ro de tÃ©lÃ©phone invalide, seulement 0-9, '+', espace, trait d'union, 'ext' et 'x' autorisÃ©s. 'Ext' ou 'x' doit Ãªtre suivi d'un numÃ©ro d'extension numÃ©rique.
error.field.startdate.null=Date de dÃ©but est un champ obligatoire.
error.field.startdate.future=Date de dÃ©but ne peut pas Ãªtre Ã  l'avenir.
error.field.installdate.future=La date d'installation ne peut pas Ãªtre Ã  l'avenir.
error.powerlimit.invalid=SÃ©lectionnez une valeur valide pour limite de puissance (nÃ©cessaire)
error.priority.invalid=La prioritÃ© doit Ãªtre un nombre (1 est la plus haute prioritÃ©)
error.search.meter=Entrez un numÃ©ro de compteur valide.
error.search.customer=Entrez un terme de recherche valide.
error.field.touseasonname.null=Le nom de la saison est obligatoire.
error.field.touperiodname.null=Nom PÃ©riode est un champ obligatoire..
error.field.touperiodcode.null=Code PÃ©riode est un champ obligatoire.
error.field.title.max=Le titre doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.initials.max=Les initiales doivent Ãªtre infÃ©rieures aux {max} caractÃ¨res.
error.field.firstnames.max=Les prÃ©noms doivent Ãªtre infÃ©rieurs Ã  {max} caractÃ¨res.
error.field.surname.max=Le nom de famille doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.companyname.max=La sociÃ©tÃ© doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.taxnum.max=NumÃ©ro de taxe doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.agreementref.max=L'accord Ref doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.usagepoint.name.null=Le nom du point d'utilisation est requis
error.field.usagepoint.name.duplicate=nom en double {0} pour un point d'utilisation. SpÃ©cifiez un nom unique.

error.field.comment.max=Les commentaires doivent Ãªtre infÃ©rieurs aux {max} caractÃ¨res.
error.field.accountref.max=La rÃ©fÃ©rence du compte doit Ãªtre infÃ©rieure aux {max} caractÃ¨res.
error.field.ourref.null=Notre rÃ©fÃ©rence est requise.
error.field.ourref.range=Notre rÃ©fÃ©rence doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.amtincltax.null=Le montant doit Ãªtre entrÃ©.
error.field.amttax.null=La taxe peut ne pas Ãªtre nulle. Entrez 0 si aucune taxe.
error.field.customvarchar.max=Le champ de texte personnalisÃ© doit Ãªtre infÃ©rieur Ã  {0} caractÃ¨res.

error.field.accountname.range=Le nom du compte doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.

# Error headers, etc
error.validation.header=Veuillez corriger ces erreurs de validation:
error.validation.fields.header=Veuillez corriger ces erreurs d'entrÃ©e:
error.field.code.null=Le code est requis
error.field.code.range=Le code doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.server.connection=Impossible de se connecter au serveur Web.
error.server=RÃ©ponse d'erreur reÃ§ue du serveur Web.
error.field.manufacturerid.null=Le fabricant est requis.
error.field.serviceresourceid.null=Une ressource de service est requise.
error.field.metertypeid.null=Le type du compteur est requis.
error.field.paymentmodeid.null=Le mode de paiement est requis.
error.field.accountname.null=Un nom de compte est requis.
error.field.accountname.duplicate=Nom du compte en double {0}. SpÃ©cifiez un nom unique.
error.field.taskschedulename.null=Le nom est requis.
error.field.taskschedulename.max=Le nom doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.cronexpression.null=Un horaire est requis.
error.field.cronexpression.range=L'horaire doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.scheduledtaskid.null=Le type de tÃ¢che est requis.
error.field.taskschedulename.range=Le nom de l'horaire des tÃ¢ches est requis.
error.field.taskclassid.null=Le type de tÃ¢che est requis.
error.field.scheduledtaskname.range=Le nom doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.taskscheduleid.null=Le calendrier des tÃ¢ches est requis.
error.messages.header=Les erreurs:

# Customer Account Threshold errors
error.field.lowbalance.null=Le bas solde doit Ãªtre entrÃ©
error.field.emergencycredit.null=Le crÃ©dit d'urgence doit Ãªtre entrÃ©
error.field.disconnect.null=DÃ©connecter doit Ãªtre entrÃ©
error.field.reconnect.null=La connexion doit Ãªtre entrÃ©e

# Questions
question.discard.changes=Voulez-vous supprimer les modifications actuelles?
question.discard.potential.changes=Si vous avez effectuÃ© des modifications et que vous n'avez pas encore enregistrÃ©, ils seront rejetÃ©s. Continuer?
option.yes=Annuler les modifications
option.no=Annuler
option.positive=Oui
option.negative=Non
question.delete=Voulez-vous supprimer l'Ã©lÃ©ment sÃ©lectionnÃ©?
dayprofile.question.delete=Voulez-vous supprimer l'Ã©lÃ©ment sÃ©lectionnÃ©? Cela Ã©liminera Ã©galement toutes les pÃ©riodes associÃ©es Ã  ce profil de jour.
question.confirm.installation.date.1=Confirmez que le compteur {0} a Ã©tÃ© installÃ© sur {1} Ã  {2}.
question.confirm.installation.date.2=Il est important que cette date et heure soient exactes.
question.confirm.link.to.customer=Client {0} est dÃ©jÃ  sur la page. Obtenir cet PointUsage ici vous assignera automatiquement ce client. Continuer?
question.close.tabs.1=Ne peut pas Ãªtre sauvegardÃ© alors que d'autres onglets, potentiellement susceptibles de faire rÃ©fÃ©rence Ã  cet Ã©lÃ©ment, sont ouverts.
question.close.tabs.2=Vous pouvez les fermer vous-mÃªme, puis essayer d'enregistrer Ã  nouveau - choisissez NON ou
question.close.tabs.3=Faut-il fermer automatiquement d'autres onglets pour vous (perdre des donnÃ©es non enregistrÃ©es) - choisir OUI?
option.confirm=Confirmer
option.continue=Continuer?

## Messages
message.saved={0} a Ã©tÃ© enregistrÃ©.

## Links
link.logout=DÃ©connexion
link.loggedin=Utilisateur:
link.group.change=Groupe de changement
link.group=Groupe:  
link.meters=Compteurs
link.customers=Les clients
link.groups=Groupes
link.menu=Menu
link.pricingstructure=Structure de prix
link.calendars=Calendriers
link.calendarsettings=ParamÃ¨tres du calendrier
link.auxchargeschedule=Horaire de charge auxiliaire
link.auxilliarytype=Type auxilliaire
link.supplygroup=Groupe d'approvisionnement
link.displaytokens=Jetons d'affichage
link.devicestores=Dispositifs
link.usergroup=Groupe d'accÃ¨s de l'utilisateur
link.accessgroups=Groupes d'accÃ¨s
link.search=Recherche
link.search.advanced=Recherche AvancÃ©e
link.search.meters.viewed=Derniers compteurs visionnÃ©s
link.search.meters.modified=Derniers compteurs modifiÃ©s
link.meter.readings=RelevÃ©s des compteurs
link.energybalancing=Ãquilibrage Ã©nergÃ©tique
link.energybalancing.meters=Compteurs d'Ã©quilibrage Ã©nergÃ©tique
link.analytics=Analytique
link.configuration=Configuration
link.tools=Outils
link.taskschedules=Horaires des tÃ¢ches
link.locationgroups=Groupes d'emplacement
link.about=Ã propos
link.appsettings=ParamÃ¨tres de l'application
link.global.ndp=NDP Globale
link.billingdet= Facteurs de Facturation

## Buttons ##
button.save=Sauvegarder
button.new=Nouveau
button.edit=Ãditer
button.back=Retour
button.cancel=Annuler
button.close=Fermer
button.select=SÃ©lectionner
button.delete=Effacer
button.viewentity=Voir l'entitÃ©
button.yes=Oui
button.no=Non
button.create=CrÃ©er
button.update=Mettre Ã  jour
button.viewtariffs=Afficher les tarifs
button.replacemeter=Remplacer le compteur
button.removemeter=Enlever le Compteur
button.displayunits=UnitÃ©s d'affichage
button.gettoken=Obtenir un jeton
button.saveaccount=Enregistrer le compte
button.addnew=Ajouter nouveau
button.editchargeschedule=Modifier l'horaire des frais
button.clear.group=Effacer le group
button.search=Recherche
button.clear=Effacer
button.view=Voir
button.add=Ajouter
button.remove=Retirer
button.export=Exporter
button.view.scheduledtasks=Afficher les tÃ¢ches planifiÃ©es
button.login=Connexion
button.logout=Quitter le site
button.set=Ensemble
button.show.inherited=Afficher les valeurs hÃ©ritÃ©es
button.send=Envoyer
button.viewtrans=Afficher les transactions

## Menus ##
menu.add=Ajouter
menu.update=Mettre Ã  jour
menu.delete=Effacer
menu.search=Recherche

## Record Statuses ##
status.active=actif
status.inactive=Inactive
status.deleted=Deleted

### Supply Group ###
supplygroups.header=Groupes d'approvisionnement
supplygroups.title=Groupes actuels d'approvisionnement
supplygroup.title=Groupe d'approvisionnement
supplygroup.name=Groupe d'approvisionnement
supplygroup.field.name=Nom
supplygroup.field.code=Code
supplygroup.field.keyrevisionnumber=NumÃ©ro de rÃ©vision clÃ©
supplygroup.field.keyexpirynumber=NumÃ©ro d'expiration clÃ©
supplygroup.field.status=Ãtat
supplygroup.field.active=actif
supplygroup.title.add=Ajouter un groupe d'approvisionnement
supplygroup.title.update=Mettre Ã  jour le groupe d'approvisionnement
supplygroup.error.save=Impossible de sauvegarder le groupe d'approvisionnement.

### Group Type ###
grouptypeshierarchies.title=Types de groupes et hiÃ©rarchies
grouptypes.header=Types de groupe
grouptypes.hierarchies.header=HiÃ©rarchies de groupe
grouptypes.title=Types de groupes actuels
grouptype.title=Type de groupe
grouptype.field.id=ID de type de groupe
grouptype.field.name=Nom
grouptype.field.name.help=Entrez le nom de ce type de groupe. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
grouptype.field.description=La description
grouptype.field.description.help=Entrez une description de ce type de groupe.
grouptype.field.parent=Parent
grouptype.field.parent.help=Si ce type de groupe appartient Ã  une hiÃ©rarchie sous un autre type de groupe, sÃ©lectionnez ici le type de groupe parent.
grouptype.field.active=Actif
grouptype.field.active.help=Ce groupe est-il actif? Cochez la case pour activer ce type de groupe.
grouptype.field.status=Ãtat
grouptype.field.required=Champs obligatoires
grouptype.field.required.help=Pour les groupes de points d'utilisation, cela dÃ©termine si une sÃ©lection pour ce type de groupe est requise
grouptype.field.accessgroup=Groupe d'accÃ¨s
grouptype.field.locationgroup=Groupe de localisation
grouptype.field.access.location.group=AccÃ¨s / Emplacement
grouptype.field.feature=CaractÃ©ristiques
grouptype.field.available.feature=CaractÃ©ristiques disponibles
grouptype.field.assigned.feature=FonctionnalitÃ©s assignÃ©es
grouptype.field.available.feature.help=Si une caractÃ©ristique est marquÃ©e comme une fonction de sÃ©lection unique, elle ne peut Ãªtre sÃ©lectionnÃ©e que pour un seul type de groupe, une fois que le type de groupe a Ã©tÃ© utilisÃ© dans les donnÃ©es, cette affectation ne peut pas changer. 
grouptype.field.assigned.feature.help=FonctionnalitÃ©s attribuÃ©es Ã  ce type de groupe
grouptype.button.viewhierarchies=Afficher les hiÃ©rarchies
grouptype.error.noneselected=Aucun type de groupe sÃ©lectionnÃ© n'est sÃ©lectionnÃ©.
grouptype.error.parentcanthavehierarchy=Les types de groupe parent ne peuvent pas avoir de hiÃ©rarchies
grouptype.none=Group Type: Aucune sÃ©lection
grouptype.title.add=Ajouter un type de groupe
grouptype.title.update=Mettre Ã  jour le type de groupe
grouptype.error.save=Impossible de sauvegarder le type de groupe.
grouptype.error.accessgroup.users=Impossible de modifier le groupe d'accÃ¨s. Les utilisateurs existants sont affectÃ©s aux groupes d'accÃ¨s.
grouptype.error.accessgroup.customers=Impossible de modifier le groupe d'accÃ¨s. Il existe des clients existants utilisant les groupes.
grouptype.error.accessgroup.up=Impossible de modifier le groupe d'accÃ¨s. Il existe des points d'utilisation existants Ã  l'aide des groupes.
grouptype.error.accessgroup.pricing=Impossible de modifier le groupe d'accÃ¨s. Il existe des structures de prix existantes utilisant les groupes.
grouptype.error.accessgroup.aux=Impossible de modifier le groupe d'accÃ¨s. Il existe des horaires aux charge aux groupes.
grouptype.error.accessgroup.stores=Impossible de modifier le groupe d'accÃ¨s. Il existe des magasins d'appareils existants utilisant les groupes.
grouptype.error.name.duplicate=Le nom du type de groupe est dÃ©jÃ  utilisÃ©. Choisissez un autre nom.
grouptype.error.cannot.change.required=Impossible de modifier le paramÃ¨tre Â«requisÂ», groupe dÃ©jÃ  utilisÃ©.
grouptype.error.select.add.feature=SÃ©lectionnez une fonctionnalitÃ© Ã  ajouter.
grouptype.error.select.remove.feature=SÃ©lectionnez une fonction Ã  supprimer.
grouptype.error.cannot.set.feature.for.group=Cette fonctionnalitÃ© peut ne pas Ãªtre dÃ©finie pour ce type de groupe.
grouptype.error.cannot.remove.feature=Impossible de supprimer le paramÃ¨tre de fonctionnalitÃ©, le groupe dÃ©jÃ  utilisÃ©.
grouptype.error.save.feature=Impossible de sauvegarder le paramÃ¨tre de fonctionnalitÃ©.
grouptype.error.feature.not.multi.instance=Cette fonctionnalitÃ© ne peut Ãªtre attribuÃ©e qu'une seule fois, dÃ©jÃ  attribuÃ©e Ã  un autre groupe. 
grouptype.accessgroup.help=Le type de groupe d'accÃ¨s dÃ©termine quel type de groupe contrÃ´le l'accÃ¨s de l'utilisateur au site Web. Il est dÃ©fini une fois et ne peut pas Ãªtre modifiÃ©.
grouptype.locationgroup.help=Le type de groupe de localisation dÃ©termine le type de groupe qui contrÃ´le les donnÃ©es de localisation. Il est dÃ©fini une fois et ne peut pas Ãªtre modi

### Group Hierarchy ###
grouphierarchies.header=HiÃ©rarchies de groupe
grouphierarchies.title=HiÃ©rarchies de groupe actuelles
grouphierarchy.title=HiÃ©rarchie de groupe
grouphierarchy.field.id=ID de la hiÃ©rarchie de groupe
grouphierarchy.field.name=Nom
grouphierarchy.field.name.help=Entrez le nom de ce niveau dans la hiÃ©rarchie des groupes.
grouphierarchy.field.description=La description
grouphierarchy.field.active=Actif
grouphierarchy.field.parent=HiÃ©rarchie parentale
grouphierarchy.field.parent.none=Aucun
grouphierarchy.field.level=Niveau
grouphierarchy.delete.confirm=Ãtes-vous sÃ»r de vouloir supprimer la hiÃ©rarchie des groupes?
grouphierarchy.deleted=La hiÃ©rarchie des groupes a Ã©tÃ© supprimÃ©e avec succÃ¨s.
grouphierarchy.error.delete=Impossible de supprimer la hiÃ©rarchie des groupes.
grouphierarchy.error.delete.linked=Impossible de supprimer la hiÃ©rarchie de groupe telle qu'elle est utilisÃ©e.
grouphierarchy.error.save=Impossible de sauvegarder la hiÃ©rarchie du groupe.
grouphierarchy.error.update=Impossible de mettre Ã  jour l'Ã©lÃ©ment hiÃ©rarchique de groupe tel qu'il est utilisÃ©.
grouptype.current=Type de groupe actuel.
grouphierarchy.title.add=Ajouter une hiÃ©rarchie de groupe
grouphierarchy.title.update=Mettre Ã  jour le jury de groupe
grouphierarchy.error.unknown=HiÃ©rarchie de groupe inconnue.
grouphierarchy.error.access.root=Les donnÃ©es de hiÃ©rarchie du premier niveau du groupe d'accÃ¨s ne peuvent pas Ãªtre modifiÃ©es.

### Usage Point Groups ###
usagepointgroups.header=Groupes de points d'utilisation
usagepointgroups.title=Groupes actuels de points d'utilisation
usagepointgroups.instructions=SÃ©lectionnez un type de groupe:
usagepointgroup.title=Groupe de points d'utilisation
usagepointgroup.noselection.grouptype=Aucun type de groupe sÃ©lectionnÃ© n'est sÃ©lectionnÃ©.
usagepointgroup.field.id=ID de groupe de point d'utilisation
usagepointgroup.field.name=Nom
usagepointgroup.field.hierarchy=HiÃ©rarchie
usagepointgroup.help.grouptype=SÃ©lectionnez un type de groupe pour afficher ses groupes de points d'utilisation correspondants. Seuls les types de groupes avec les hiÃ©rarchies de groupe sont visibles ici.
usagepointgroup.delete.ask=Ãtes-vous sÃ»r de vouloir supprimer le groupe de points d'utilisation {0}?
usagepointgroups.help=Cliquez sur les icÃ´nes des donnÃ©es de l'arbre pour ajouter, modifier ou supprimer les donnÃ©es.
usagepointgroup.title.add=Ajouter un groupe de points d'utilisation
usagepointgroup.title.update=Mettre Ã  jour le groupe de points d'utilisation
usagepointgroup.error.entityid=Impossible de sauvegarder l'ID de l'entitÃ© GenGroup.

###Old Usage Point Group Page
usagepointgroup.field.description=Description
usagepointgroup.field.parent=Nom du parent
usagepointgroup.field.active=Actif
usagepointgroup.field.status=Ãtat
usagepointgroup.field.name.help=Entrez un nom pour le groupe - ce sera utilisÃ© pour l'identifier lors de l'ajout de points d'utilisation au groupe.
usagepointgroup.field.description.help=Entrez une description du groupe (facultatif).
usagepointgroup.field.parent.help=Si le groupe fait partie d'un groupe plus important, identifiez le groupe parent ici
usagepointgroup.field.status.help=Activer ou dÃ©sactiver ce groupe

## Groups
group.error.save=Impossible de sauvegarder le groupe.
group.error.delete=Impossible de supprimer le groupe.
group.error.delete.ap=Impossible de supprimer le groupe car il existe encore des points d'utilisation actifs.
group.error.entity.save=Impossible de lier les informations de contact au groupe.
group.error.threshold.save=Impossible de lier les informations de seuil au groupe.
group.error.ndp.schedule.save=Impossible de lier les informations NDP au groupe.
group.new.instructions=Entrez un nouveau
group.new.for=pour
group.current.none=Aucun groupe actuel n'a Ã©tÃ© dÃ©fini pour votre utilisateur.
group.delete.ask=Ãtes-vous sÃ»r de vouloir supprimer le {0} groupe?
groups.error.select.at.minimum=Au minimum, {0} doit Ãªtre sÃ©lectionnÃ©.

## Usage Point Workspace
usagepointworkspace.meter.saved.usagepoint.deactivation.failed=MÃ¨tre {0} EnregistrÃ©! (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.meter.saved.usagepoint.deactivated=MÃ¨tre {0} enregistrÃ©! (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.meter.saved=MÃ¨tre {0} enregistrÃ©!
usagepointworkspace.meter.saved.attach.usagepoint=Compteur {0} sauvegardÃ©! Pour fixer ce compteur au point d'utilisation ci-dessous, sÃ©lectionnez la date d'installation et la structure de tarification, ainsi que l'activation.
usagepointworkspace.customer.saved.usagepoint.deactivation.failed=Client {0} EnregistrÃ©! (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.customer.saved.usagepoint.deactivated=Client {0} enregistrÃ©! (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.customer.saved.usagepoint.failed=Client {0} sauvegardÃ© mais impossible de mettre Ã  jour le point d'utilisation! Contactez le support.
usagepointworkspace.customer.saved.usagepoint.updated=Client {0} sauvegardÃ© et le point d'utilisation mis Ã  jour.
usagepointworkspace.customer.saved=Client {0} enregistrÃ©!
usagepointworkspace.customer.saved.no.usage.point=Client {0} sauvegardÃ©, aucun point d'utilisation pour mettre Ã  jour.
usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed=Le client {0} n'est plus affectÃ© au point d'utilisation {1} (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.customer.unassigned.usagepoint.deactivated=Le client {0} n'est plus affectÃ© au point d'utilisation {1} (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.customer.assign.error.already.assigned=Le client est dÃ©jÃ  affectÃ© au point d'utilisation {0}
usagepointworkspace.customer.assign.error=Le client {0} n'est PAS affectÃ© au point d'utilisation {1} (la dÃ©sactivation du point d'utilisation a Ã©chouÃ©)
usagepointworkspace.customer.assigned.usagepoint.deactivated=Le client {0} est maintenant affectÃ© au point d'utilisation {1} (point d'utilisation dÃ©sactivÃ©)
usagepointworkspace.customer.assigned=Le client {0} est maintenant affectÃ© au point d'utilisation {1}
usagepointworkspace.error.meter.not.found=Compteurs introuvable
usagepointworkspace.error.meter.already.assigned=Le compteur {0} est dÃ©jÃ  affectÃ© au point d'utilisation {1}
usagepointworkspace.error.meter.unable.to.unassign=Impossible de ne pas assigner de courant au compteur
usagepointworkspace.error.meter.installdate.before.previous=La nouvelle date d'installation {0} ne peut pas Ãªtre AVANT la date d'installation du compteur prÃ©cÃ©dent {1}.
usagepointworkspace.error.meter.installdate.before.last.remove=Nouvelle date d'installation {0} ne peut pas Ãªtre AVANT la date de retrait du compteur prÃ©cÃ©dent {1}.
usagepointworkspace.error.meter.installdate.before.last.reading=Impossible de rÃ©affecter le point d'utilisation - la nouvelle date d'installation est avant la derniÃ¨re date de lecture du compteur actuel.
usagepointworkspace.error.meter.installdate.before.last.register.reading= Impossible de rÃ©attribuer le point d'utilisation - la nouvelle date d'installation est avant la derniÃ¨re date de lecture du registre du compteur actuel.
usagepointworkspace.error.meter.removedate.before.last.reading= Impossible d'enlever le compteur du point d'utilisation maintenant - la derniÃ¨re date de lecture du compteur actuel est supÃ©rieure.
usagepointworkspace.error.meter.removedate.before.last.register.reading= Impossible d'enlever le compteur du point d'utilisation maintenant - la derniÃ¨re date de lecture du registre du compteur actuel est supÃ©rieure.
usagepointworkspace.meter.assigned=Le compteur {0} est maintenant affectÃ© au point d'utilisation {1}
usagepointworkspace.meter.assigned.usagepoint.deactivated=Compteur {0} est maintenant assignÃ© au point d'utilisation {1} (Le point d'utilisation n'est pas actif)
usagepointworkspace.meter.removed=Compteur {0} a Ã©tÃ© supprimÃ© du point d'utilisation {1}
usagepointworkspace.meter.add.usagepoint.to.join.customer=Ajouter le point d'utilisation (ci-dessous) pour lier le compteur {0} et le client {1} l'un Ã  l'autre
usagepointworkspace.meter.add.usagepoint.to.join =Ajouter le point d'utilisation (ci-dessous) pour lier ce compteur Ã  un point d'utilisation
usagepointworkspace.assign.activate.usage.point.question=Point d'utilisation n'est pas actif pour le moment. Voulez-vous l'activer maintenant?
usagepointworkspace.assign.usage.point.activated=Point d'utilisation activÃ©.
usagepointworkspace.save.usage.point.inactive=Le point d'utilisation n'est pas actif.
usagepointworkspace.meter.customer.assigned=Le Compteur {0}, Le Client {2} sont maintenant assignÃ©s au point d'utilisation {1}

### Usage Point ###
usagepoint.groups.title=Groupes de points d'utilisation
usagepoint.info.title=Informations sur le point d'utilisation
usagepoint.title=Point d'utilisation
usagepoint.add.new=Ajouter le point d'utilisation
usagepoint.show.info=Afficher l'information sur le point d'utilisation
usagepoint.showing.info=Affichage de l'information sur le point d'utilisation
usagepoint.field.active.help=Ce point d'utilisation est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce qu'un compteur actif et un client soient liÃ©s Ã  ce point d'utilisation.
usagepoint.field.activated_date.help=DÃ©finissez la date Ã  laquelle ce point d'utilisation a Ã©tÃ© activÃ©. Cela ne peut Ãªtre rÃ©glÃ© qu'une seule fois. Ce champ est requis pour l'activation. Bien que l'enregistrement puisse Ãªtre enregistrÃ© sans lui, le point d'utilisation ne peut Ãªtre activÃ© que s'il est rempli correctement.
usagepoint.field.active=Actif
usagepoint.field.activated_date=Date activÃ©e
usagepoint.field.meter.installation_date=Date / heure de l'installation du compteur
usagepoint.field.meter.installation_date.meter.required=Un compteur doit Ãªtre installÃ© avant de dÃ©finir la date d'installation.
usagepoint.field.meter.installation_date.help=La date et l'heure oÃ¹ le compteur a Ã©tÃ© installÃ© Ã  ce point d'utilisation. Cela ne peut Ãªtre rÃ©glÃ© qu'une fois par mÃ¨tre. Ce champ est requis et un compteur doit Ãªtre affectÃ© avant de dÃ©finir la date d'installation.
usagepoint.field.name.help=Entrez le nom de ce point d'utilisation. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
usagepoint.field.name=Nom du point d'utilisation
usagepoint.field.pricingstructure.help=SÃ©lectionnez la structure de prix. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
usagepoint.field.lastmdcconnectcontrol=Dernier contrÃ´le MDC Connect
usagepoint.field.group.help=Ajouter ce point d'utilisation Ã  un groupe
usagepoint.field.group=Groupes de points d'utilisation
usagepoint.required.text=*== Obligatoire - Un compteur associÃ© est Ã©galement requis
usagepoint.required.activation.text=** == Obligatoire pour l'activation - Un compteur actif et un client sont Ã©galement requis pour l'activation
usagepoint.name.required=Le nom du point d'utilisation est requis
usagepoint.pricingstructure.required=Structure de prix requise
usagepoint.pricingstructure.change.illegal=La structure des prix ne peut pas Ãªtre modifiÃ©e maintenant. Aucun compteur n'est attachÃ©. RÃ©initialiser le choix.
usagepoint.save.error=L'Ã©chec du point d'utilisation a Ã©chouÃ©.
usagepoint.save.errors=Point d'utilisation non enregistrÃ©. Corrigez les erreurs affichÃ©es.
usagepoint.save.errors.meter.required=L'Ã©chec du point d'utilisation a Ã©chouÃ©. Un compteur associÃ© est requis
usagepoint.saved=UsagePoint {0} enregistrÃ©.
usagepoint.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
usagepoint.no.meter=Un mÃ¨tre doit Ãªtre sauvegardÃ© avant d'ajouter un point d'utilisation.
usagepoint.txn.history=Historique des transactions
usagepoint.txn.history.description=Transactions clients antÃ©rieures pour ce point d'utilisation
usagepoint.txn.meterreadings=RelevÃ©s des compteurs
usagepoint.history=Historique des points d'utilisation
usagepoint.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce point d'utilisation (les modifications sont mises en surbrillance)
usagepoint.reports=Rapports pour le point d'utilisation
usagepoint.recharge.history=MÃ©thodes d'utilisation Historique de recharge
usagepoint.retailers=Commerces Ã  proximitÃ©
usagepoint.reports.general=Rapports gÃ©nÃ©raux pour les points d'utilisation
usagepoint.meter.reports=Rapports pour le point d'utilisation
usagepoint.coords=Ajoutez les coordonnÃ©es Latitude et Longitude au point d'utilisation, afin de voir les dÃ©taillants voisins.
usagepoint.txn.reference=RÃ©fÃ©rence
usagepoint.txn.receipt=NumÃ©ro de rÃ©ception
usagepoint.txn.date=Date
usagepoint.txn.meter=Compteur
usagepoint.txn.customer=Client
usagepoint.txn.type=Type
usagepoint.txn.client=Client
usagepoint.txn.term=Terme
usagepoint.txn.ref=RÃ©fÃ©rence
usagepoint.txn.revref=Reflet d'inversion
usagepoint.txn.isreversed=RenversÃ©
usagepoint.txn.amt=Montant
usagepoint.hist.serial=En sÃ©rie
usagepoint.hist.date=Date
usagepoint.hist.customer=Client
usagepoint.hist.datemod=Date Modifiee
usagepoint.hist.byuser=Par l'utilisateur
usagepoint.hist.user=Utilisateur
usagepoint.hist.action=Action
usagepoint.hist.status=Ãtat
usagepoint.hist.name=Nom
usagepoint.hist.meter=Compteur
usagepoint.hist.custagree=Contrat client
usagepoint.hist.service=Emplacement du service
usagepoint.recharge.title=Rechargement de point d'utilisation
usagepoint.recharge.date=Date
usagepoint.recharge.currency=R
usagepoint.recharge.kwh=kWh
usagepoint.history.filter=Filtre
usagepoint.txn.filter=Filtre
usagepoint.group.required=Groupe requis
usagepoint.onegroup.required=Au moins un groupe doit Ãªtre sÃ©lectionnÃ©
usagepoint.group.error.delete=Impossible de supprimer le groupe de points d'utilisation.
usagepoint.group.error.save=Impossible de sauvegarder le groupe du point d'utilisation.
usagepoint.group.no.value=Aucune valeur dÃ©finie
usagepoint.calculate.tariff=Calculer le tarif
usagepoint.calculate.tariff.error=Une erreur s'est produite lors du calcul du tarif. 
usagepoint.calculate.tariff.connection.error=Aucune rÃ©ponse reÃ§ue du service.
usagepoint.calculate.tariff.ok=Tarification calculÃ©e avec succÃ¨s
usagepoint.recharge.chart.title=Recharges de point d'utilisation
usagepoint.recharge.chart.subtitle=Recharge des montants
usagepoint.recharge.chart.xtitle=Date Heure
usagepoint.recharge.chart.ytitle=CoÃ»t
usagepoint.recharge.chart.price=Prix
usagepoint.recharge.chart.purchaseprice=Prix d'achat
usagepoint.error.meterandcustomer.required=Un compteur actif et un client sont requis pour l'activation
usagepoint.meter.installed.at=Le compteur {0} a Ã©tÃ© installÃ© sur {1} Ã  {2}
usagepoint.installation.date.required=La date et l'heure d'installation du compteur sont nÃ©cessaires.
usagepoint.name.instructions=Recherche par Nom du point utilisation
usagepoint.partial.search=Pas de correspondance exacte avec le point d'utilisation pour {0}. Faire une recherche avancÃ©e...
usagepoint.fetch=Chercher point dâutilisation
usagepoint.fetch.help=Chercher un point d'utilisation existant.
usagepoint.assigned=Ce point d'utilisation dispose dÃ©jÃ  d'un compteur ou d'un client qui lui est assignÃ©. Ne sont pas admissibles Ã  un Cherche.
usagepoint.name.instr=Entrez le nom du point d'utilisation
usagepoint.find=Trouver le point d'utilisation
usagepoint.fetch.duplicate=Point dâutilisation {0} dÃ©jÃ  sur la page.
usagepoint.install.date.required=La date et l'heure auxquelles le compteur ci-dessus a Ã©tÃ© installÃ© Ã  ce point d'utilisation {0}.
usagepoint.new.pricingstructure.required=Structure d'Ã©valuation du point d'utilisation sÃ©lectionnÃ© {1} incompatible avec Compteur {0}.
usagepoint.error.new.installdate.before.removal=La date d'installation ne peut pas Ãªtre AVANT la date de la derniÃ¨re suppression sur le point d'utilisation: {0}
usagepoint.error.new.installdate.before.removaldate.meter=La date d'installation ne peut pas Ãªtre AVANT la derniÃ¨re date de retrait du compteur: {0}
usagepoint.saved.linked.meter=LiÃ© au Compteur {0}.
usagepoint.saved.linked.customer= LiÃ© au Client {0}.
moxiechart.abbrev.month.categories="Jan", "FÃ©v", "Mar", "Avr", "Mai", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "DÃ©c"

### Group Entity ###
groupentity.header=Informations sur le groupe
groupentity.title=Informations de contact
groupentity.contact.title=Contact
groupentity.field.contact.name=Nom du Contact
groupentity.field.contact.number=Numero du Contact
groupentity.field.contact.email=Email du Contact 
groupentity.field.contact.address=Adresse de contact
groupentity.field.contact.taxref=Contactez la rÃ©fÃ©rence ImpÃ´t
groupentity.error.save=Impossible de sauvegarder les informations du groupe.

### Group Thresholds ###
groupthreshold.title=Informations sur les seuils du compte client
groupthreshold.meter.disconnect.text=Seuil de dÃ©connexion
groupthreshold.meter.disconnect.help=Seuil au-dessous duquel le client sera dÃ©connectÃ©.
groupthreshold.emergency.credit.text=Seuil de crÃ©dit d'urgence
groupthreshold.emergency.credit.help=Seuil au-dessous duquel le solde du client entraÃ®nera une demande de dÃ©connexion et d'activation au mdc s'il est pris en charge.
groupthreshold.meter.reconnect.text=Reconnecter le seuil
groupthreshold.meter.reconnect.help=Seuil au-dessus duquel le client sera reconnectÃ©.
groupthreshold.low.balance.text=Seuil d'avertissement de faible Ã©quilibre
groupthreshold.low.balance.help=Seuil pour alerter le client d'un faible solde dans son compte.
groupthreshold.global.source.label=ParamÃ¨tres globaux
groupthreshold.parent.source.label=ParamÃ¨tres parentaux
groupthreshold.children.change.alert=Des hiÃ©rarchies infÃ©rieures sans mÃªme seuils seront mises Ã  jour avec ces modifications. Continuer?
groupthreshold.null.thresholds.alert=Les seuils laissÃ©s en blanc seront dÃ©sactivÃ©s. Continuer?
groupthreshold.error.save.thresholds=Impossible de sauvegarder les informations de seuil.
groupthreshold.revert.parent.global=Ãtes-vous sÃ»r de supprimer? Ce noeud va maintenant hÃ©riter de {0} seuil ParamÃ¨tres.
groupthreshold.error.disconnect.greater.emergency.credit=Le dÃ©connexion doit Ãªtre infÃ©rieur ou Ã©gal au crÃ©dit d'urgence
groupthreshold.error.disconnect.greater.reconnect=DÃ©connecter doit Ãªtre plus petit ou Ã©gal Ã  Reconnecter
groupthreshold.error.emergency.credit.greater.low.balance=Le crÃ©dit d'urgence doit Ãªtre infÃ©rieur ou Ã©gal au faible solde

### Global Non-Disconnect Periods ###
global.ndp.tab.heading=NDP Globale
global.ndp.heading=PÃ©riodes globales de non-dÃ©connexion
global.ndp.none=Il n'y a pas de calendrier NDP Globale, appuyez sur le bouton CrÃ©er.
global.ndp.schedule.new.added=Le calendrier global NDP a Ã©tÃ© crÃ©Ã©.
global.ndp.schedule.activation.saved=Calendrier NDP mondial enregistrÃ©.

### Group Non-Disconnect Periods ###
ndp.active.instruction=Un calendrier NDP peut Ãªtre crÃ©Ã© et fonctionnÃ©, mais ne sera appliquÃ© que s'il est actif. L'horaire ne peut Ãªtre activÃ© que si au moins un jour NDP avec des heures a Ã©tÃ© entrÃ©. Soit une saison avec un jour ou un jour spÃ©cial.
ndp.schedule.title=Calendrier du NPD
ndp.schedule.active=Actif
ndp.schedule.active.help= Ce programme est-il actif? Cochez la case pour activer cette programmation - ne peut Ãªtre effectuÃ©e que lorsque au moins un jour NDP a Ã©tÃ© saisi.
ndp.schedule.delete.button=Supprimer L'horaire
ndp.disclosurePanel.title=PÃ©riodes de non-dÃ©connexion
ndp.seasons.title=Saisons
ndp.season.day.title=Jours de saison
ndp.season.day.description=Entrez une date de dÃ©but et de fin pour la saison, suivie des heures NDP par jour de la semaine.
ndp.assign.season.start=Date de dÃ©but
ndp.assign.season.start.help=Entrez la date de dÃ©but de la saison
ndp.assign.season.end=Date de fin
ndp.assign.season.end.help=Entrez la date de fin de saison
ndp.per.day.title=NDP par jour
ndp.days.of.week=Jour de la semaine
ndp.assign.dayperiod.start=Heure de dÃ©but
ndp.assign.dayperiod.start.help=Le moment oÃ¹ le NPD commence
ndp.assign.dayperiod.start.hour=Heure
ndp.assign.dayperiod.start.minute=Minute
ndp.assign.dayperiod.end=Heure de fin
ndp.assign.dayperiod.end.help=Le moment oÃ¹ le NPD finit
ndp.assign.dayperiod.end.hour=Heure
ndp.assign.dayperiod.end.minute=Minute
ndp.assign.dayperiod.saved=Le temps NDP a Ã©tÃ© sauvegardÃ©.
ndp.assign.dayperiod.deleted=Le temps NDP a Ã©tÃ© supprimÃ©.
ndp.assign.dayperiod.error.end.before.start=L'heure de fin ne peut pas Ãªtre avant l'heure de dÃ©but
ndp.assign.dayperiod.error.time.already.assigned=Le temps sÃ©lectionnÃ© a dÃ©jÃ  Ã©tÃ© assignÃ©.
ndp.assign.dayperiod.nulls=Toutes les valeurs de dÃ©but et de fin doivent Ãªtre saisies. Ne peut Ãªtre vide.
ndp.days.title=Jours NDP
ndp.add.season.button=Ajouter Saison
ndp.assign.season.error.end.before.start=Fin ne peut pas Ãªtre avant DÃ©but
ndp.assign.season.error.date.already.assigned=Ces dates se chevauchent avec une autre saison.
ndp.assign.season.error.time.already.assigned=Le temps sÃ©lectionnÃ© a dÃ©jÃ  Ã©tÃ© assignÃ©.
ndp.weekdays=Lundi,Mardi,Mercredi,Jeudi,Vendredi,Samedi,Dimanche
ndp.children.change.alert=Les hiÃ©rarchies infÃ©rieures sans NDP seront Ã©galement mises Ã  jour avec ces modifications. Continuer?
ndp.error.save.schedule=Impossible de sauvegarder les informations NDP Schedule.
ndp.abort.save=Vous avez choisi de ne pas sauvegarder ce calendrier NPD.
ndp.error.save.season=Impossible de sauvegarder la saison NDP.
ndp.error.save.day=Impossible de sauvegarder les temps de jours NDP.
ndp.revert.parent.global=Ãtes-vous sÃ»r de supprimer? Ce noeud va maintenant hÃ©riter {0} ParamÃ¨tres NDP.
ndp.children.delete.alert=Les hiÃ©rarchies infÃ©rieures avec le mÃªme programme NDP seront Ã©galement renvoyÃ©es vers {0} ParamÃ¨tres NDP. Continuer?
ndp.error.delete.day.profiles=Impossible de supprimer les profils de jour NDP.
ndp.error.delete.season=Impossible de supprimer une saison NDP.
ndp.error.delete.special.days=Impossible de supprimer les journÃ©es spÃ©ciales du NPD.
ndp.error.delete.schedule=Impossible de supprimer l'annexe NDP.
ndp.new.season.button=Ajouter une nouvelle Saison
ndp.new.special.day.button=Ajouter un jour spÃ©cial
ndp.special.day.column.heading=Jour
ndp.no.change.continue=Rien n'a changÃ©. Continuer?
ndp.day.panel.changed=Des changements ont Ã©tÃ© apportÃ©s Ã  cette saison. Abandon ces changements et continuez avec l'annulation?
ndp.schedule.activation.no.change=Pas de changement d'Ã©tat actif.
ndp.schedule.new.added=Un nouveau programme NDP a Ã©tÃ© ajoutÃ© Ã  ce groupe et aux sous-groupes pertinents.
ndp.schedule.activation.saved=Calendrier NDP sauvegarÃ©.
ndp.schedule.deleted=Le programme NDP a Ã©tÃ© supprimÃ© de ce groupe et des sous-groupes pertinents.
ndp.season.saved=Saison NDP enregistrÃ©e.
ndp.day.profile.confirm.delete=Confirmer la suppression du profil du jour pour {0}?
ndp.day.profile.confirm.delete.and.deactivate=Suppression du jour Profil pour {0} EntraÃ®nera une dÃ©sactivation du programme NPD lors de la mise Ã  jour de la saison, car c'est le seul temps NPD pour ce programme. Continuer?
ndp.season.confirm.delete=Confirmer la suppression de la saison {0}
ndp.season.deleted=Saison NDP supprimÃ©e.
ndp.season.confirm.delete.and.deactivate=Suppression de la saison {0} EntraÃ®nera la dÃ©sactivation du programme NDP, car il contient le (s) seul (s) temps (s) NDP pour ce programme. Continuer?
ndp.special.day.confirm.delete=Confirmer la suppression du jour spÃ©cial {0}
ndp.special.day.confirm.delete.and.deactivate=Suppression d'une journÃ©e spÃ©ciale {0} EntraÃ®nera la dÃ©sactivation du programme NDP, car il contient le (s) seul (s) temps (s) NDP pour ce programme. Continuer?
ndp.special.day.deleted=Jour spÃ©cial supprimÃ©.
ndp.special.day.title=Jours spÃ©cials
ndp.special.day.time.title= Temps de Jour spÃ©cial NDP
ndp.special.day.description=Entrez le jour et le mois du Jour spÃ©cial, suivis des heures NDP pour ce jour.
ndp.assign.special.day=Date spÃ©ciale du jour
ndp.assign.special.day.help=Entrez le jour et le mois du jour spÃ©cial.
ndp.assign.special.day.duplicate=Date du jour spÃ©ciale en copie.
ndp.special.day.panel.changed=Des changements ont Ã©tÃ© apportÃ©s Ã  ce jour spÃ©cial. Abandon les changements et continuez avec l'annulation?
ndp.special.day.times.confirm.delete.and.deactivate=Suppression des temps NDP pour {0} entraÃ®nera la dÃ©sactivation du programme NDP lors de la mise Ã  jour du jour spÃ©cial, car c'est le seul temps NPD pour cet horaire. Continuer?
ndp.special.day.saved=Jour spÃ©cial NDP sauvegardÃ©.
ndp.error.save.special.day=Impossible de sauver le Jour SpÃ©cial NDP.
ndp.global.none.found=Il n'y a pas de calendrier global de NPD. Allez crÃ©er un dans la section de configuration.
ndp.inherited.global.none=Il n'y a pas d'agenda Global NDP ou il n'est pas encore actif. Aucune pÃ©riode NDP globale ne peut Ãªtre hÃ©ritÃ©e!

### Customer Agreement ###
customeragreement.title=Contrat client

### Pricing Structures ###
pricingstructures.header=Structures de tarification
pricingstructures.title=Structures de tarification actuelles
pricingstructure.title=Structure de prix
pricingstructure.title.new=Nouvelle structure de tarification
pricingstructure.title.edit=Modifier la structure des prix
pricingstructure.field.name=Nom
pricingstructure.field.description=La description
pricingstructure.field.status=Ãtat
pricingstructure.field.active=Actif
pricingstructure.field.name.help=Le nom de cette structure de prix doit Ãªtre unique
pricingstructure.field.description.help=Une description de cette structure tarifaire
pricingstructure.field.active.help=Que la structure de prix soit active ou non.
pricingstructure.field.type=Type
pricingstructure.field.startdate=Date de dÃ©but
pricingstructure.field.tariffs=# Tarifs
pricingstructure.error.save=Impossible d'enregistrer la structure de prix.
pricingstructure.field.serviceresource.help=S'il n'y a pas de tarifs, vous pouvez sÃ©lectionner la ressource de service pour la structure de prix.
pricingstructure.field.metertype.help=S'il n'y a pas de tarifs, vous pouvez sÃ©lectionner le type de compteur pour la structure de prix.
pricingstructure.field.paymentmode.help=S'il n'y a pas de tarifs, vous pouvez modifier le mode de paiement pour la structure de tarification.
pricingstructure.tariffs.prt.none=Il n'y a pas de types de tarifs disponibles pour la ressource de service de la structure de prix actuelle, le type de compteur et la combinaison de mode de paiement.
pricingstructure.tariffs.ui.none=Aucun formulaire de tarif disponible entrÃ© pour Ãªtre enregistrÃ©.
pricingstructure.error.tariff.load=Erreur lors du chargement des donnÃ©es du tarif. Impossible d'afficher les champs du tarif et les valeurs correspondantes.
pricingstructure.name.duplicate=Duplique le nom {0} pour une structure de tarification. SpÃ©cifiez un nom unique.
pricingstructure.error.deactivate=La structure des prix est utilisÃ©e. Impossible de dÃ©sactiver.

### Tariffs ###
tariffs.header=Tarifs
tariffs.title=Tarifs actuels
tariff.title=Tarif
tariff.title.add=Ajouter un nouveau tarif
tariff.title.edit=Modifier le tarif
tariff.title.view=Afficher le tarif
tariff.field.name=Nom
tariff.field.name.help=Nom de ce tarif, doit Ãªtre unique
tariff.field.description=La description
tariff.field.description.help=Une description de ce tarif
tariff.field.startdate=Date de dÃ©but
tariff.field.startdate.help=La date Ã  laquelle ce tarif sera activÃ©. Aucun autre tarif ne peut commencer Ã  cette date et il doit s'agir d'une date ultÃ©rieure.
tariff.title.type=Type de tarif DÃ©tails
tariff.field.type=Type
tariff.field.unitprice=Prix unitaire
tariff.field.tax=Pourcentage de la taxe
tariff.field.tax.help=Pourcentage de 14%
tariff.field.free.units.descrip=Description des unitÃ©s gratuites mensuelles
tariff.field.free.units=UnitÃ©s gratuites mensuelles
tariff.field.free.units.help=UnitÃ©s qui sont dÃ©livrÃ©es gratuitement.
tariff.field.groupthreshold=Seuil de groupe
tariff.field.groupthreshold.help=Le seuil en kWh d'utilisation du groupe pour le mois aprÃ¨s lequel le prix de seuil prend effet
tariff.field.thresholdprice=Prix de seuil
tariff.field.thresholdprice.help=Prix par kWh (aprÃ¨s avoir franchi le seuil)
tariff.field.price=Prix
tariff.field.baseprice=Prix de Base
tariff.field.threshold=Seuil
tariff.field.threshold.help=Prix par kWh
tariff.field.step1=Ãtape 1
tariff.field.step2=Ãtape 2
tariff.field.step3=Ãtape 3
tariff.error.numeric.value=La valeur doit Ãªtre numÃ©rique
tariff.error.save=Impossible de sauvegarder le tarif.
tariff.error.save.duplicate=Impossible d'enregistrer le tarif, un autre tarif avec le mÃªme nom existe dÃ©jÃ .
tariff.error.load=Impossible de charger des donnÃ©es tarifaires.
tariff.field.block=Blocs
tariff.field.block.single=Bloc
tariff.error.freeunits.positive=Doit Ãªtre une valeur positive.
tariff.error.freeunits.decimal.limit=Les unitÃ©s ne peuvent avoir qu'une dÃ©cimale
tariff.blocks.error=Les blocs ont besoin d'un prix unitaire et des valeurs seuils valides.
tariff.blocks.error.incomplete=Les blocs ont besoin d'un prix unitaire pour une valeur seuil.
tariff.blocks.error.last=Seul le bloc final ne devrait pas avoir de seuil.
tariff.blocks.error.last.none=Le bloc final ne devrait pas avoir de seuil.
tariff.blocks.error.increasing.thresholds=Les seuils pour un bloc doivent Ãªtre supÃ©rieurs Ã  zÃ©ro et supÃ©rieurs au seuil du bloc prÃ©cÃ©dent.
tariff.blocks.error.none=Des blocs sont nÃ©cessaires.
tariff.tou.thinsmart.season=Saison
tariff.tou.thinsmart.period=PÃ©riode
tariff.tou.thinsmart.readingtype=Type de lecture du compteur
tariff.tou.thinsmart.rate=Taux
tariff.tou.thinsmart.chargeunits=UnitÃ©s
tariff.tou.thinsmart.specialday=Jour spÃ©cial
tarif.adv.settings.header=RÃ©glages avancÃ©s
tariff.field.pricesymbol=Symbole de la monnaie
tariff.field.pricesymbol.help=Le symbole monÃ©taire Ã  utiliser pour le prix.
tariff.field.unitsymbol=Unit Symbol
tariff.field.unitsymbol.help=Le symbole des unitÃ©s monÃ©taires Ã  utiliser pour le prix.
tariff.field.amountrounding=Mode arrondissement
tariff.field.amountrounding.help=Le mode arrondissement pour les montants.
tariff.field.amountprecision=QuantitÃ© de prÃ©cision
tariff.field.amountprecision.help=La prÃ©cision pour les montants.
tariff.field.unitsrounding=Arrondi de l'unitÃ©
tariff.field.unitsrounding.help=Le mode arrondissement Ã  utiliser pour les unitÃ©s.
tariff.field.unitsprecision=PrÃ©cision de l'unitÃ©
tariff.field.unitsprecision.help=La prÃ©cision pour les unitÃ©s.
tariff.field.taxrounding=Arrondissement des impÃ´ts
tariff.field.taxrounding.help=Le mode arrondissement de l'impÃ´t.
tariff.field.taxprecision=PrÃ©cision impÃ´t
tariff.field.taxprecision.help=La prÃ©cision Ã  utiliser pour l'impÃ´t.
tariff.error.field.pricesymbol=Un symbole de devise est requis.
tariff.error.field.unitsymbol=Le symbole de l'unitÃ© est requis.
tariff.error.field.amountrounding=Le mode arrondissement est requis.
tariff.error.field.unitsrounding=Le mode arrondissement est requis.
tariff.error.field.taxrounding=Le mode arrondissement est requis.
tariff.error.field.amountprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.error.field.unitsprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.error.field.taxprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.readonly=Le tarif a commencÃ© et est en lecture seule.

### Tou Calendar ###
calendar.settings.header=ParamÃ¨tres du calendrier
calendar.settings.title=ParamÃ¨tres du calendrier
calendar.season.title=Configuration des saisons
calendar.season.current.title=Les saisons actuelles
calendar.season.description=Ajouter ou mettre Ã  jour les saisons.
calendar.season.field.name=Nom
calendar.season.field.active=Actif
calendar.season.add=Ajouter saison
calendar.season.update=Mise Ã  jour de la saison
calendar.season.field.name.help=Entrez un nom pour cette saison. Ceci est un champ obligatoire.
calendar.season.field.active.help=L'Ã©tat d'activitÃ© actuel de cet Ã©lÃ©ment
season.error.update=La saison n'a pas pu Ãªtre mise Ã  jour.
season.error.save=La saison n'a pas pu Ãªtre enregistrÃ©e.
season.error.delete=La saison est en cours d'utilisation et ne peut pas Ãªtre supprimÃ©e.
calendar.season.deleted={0} la saison a Ã©tÃ© supprimÃ©e avec succÃ¨s.
calendar.period.deleted={0} la pÃ©riode a Ã©tÃ© supprimÃ©e avec succÃ¨s.

calendar.period.title=PÃ©riodes de configuration
calendar.period.current.title=PÃ©riodes actuelles
calendar.period.description=Ajouter et mettre Ã  jour les pÃ©riodes (p. Ex. Pointe, Creuse, Standard).
calendar.period.field.name=Nom
calendar.period.field.code=Code
calendar.period.field.active=Actif
calendar.period.add=Ajouter une pÃ©riode
calendar.period.update=PÃ©riode de mise Ã  jour
calendar.period.field.name.help=Entrez un nom pour cette pÃ©riode. Ceci est un champ obligatoire.
calendar.period.field.code.help=Entrez un code pour cette pÃ©riode. Il s'agit d'un code abrÃ©gÃ© unique pour la pÃ©riode. Ceci est un champ obligatoire.
calendar.period.field.active.help=L'Ã©tat d'activitÃ© actuel de cet Ã©lÃ©ment
period.error.save=La pÃ©riode n'a pas pu Ãªtre enregistrÃ©e.
period.error.delete=La pÃ©riode est utilisÃ©e et ne peut pas Ãªtre supprimÃ©e.

calendars.title=Calendriers
calendars.heading=Calendriers de configuration
calendars.description=Les calendriers actuels sont rÃ©pertoriÃ©s, les ajouter ou les modifier en utilisant les formulaires ci-dessous
calendar.field.name=Nom du calendrier
calendar.field.name.help=Entrez un nom pour ce calendrier.
calendar.field.description=La description
calendar.field.description.help=Entrez une description de ce calendrier.
calendar.field.active=Actif
calendar.field.active.help=L'Ã©tat d'activitÃ© actuel de ce calendrier
calendar.title=Calendrier
calendar.add=Ajouter un nouveau calendrier
calendar.update=Calendrier de mise Ã  jour
calendar.changes.cleared=Changements de calendrier effacÃ©s
calendar.save.errors=Impossible d'enregistrer le calendrier
calendar.complete=AchevÃ©e
calendar.incomplete=Incomplet
calendar.optional=Optionnel
calendar.duplicate=Nom du calendrier en double. Choisissez un nom unique.

calendar.assign.season.heading=Attribuer des dates Ã  des saisons pour le calendrier
calendar.assign.season.title=Dates de la saison
calendar.assign.season.description=Entrez une date de dÃ©but et de fin pour la saison sÃ©lectionnÃ©e.
calendar.assign.season.form.heading=Affecter des dates
calendar.assign.season=Saison
calendar.assign.season.help=SÃ©lectionner une Saison
calendar.assign.season.start=Date de dÃ©but
calendar.assign.season.start.help=Entrez la date de dÃ©but de la saison
calendar.assign.season.end=Date de fin
calendar.assign.season.end.help=Entrez la date de fin de saison
calendar.assign.season.deleted=Les dates de la saison ont Ã©tÃ© supprimÃ©es.
calendar.assign.season.error.end.before.start=Fin ne peut pas Ãªtre avant DÃ©marrer
calendar.assign.season.error.date.already.assigned=La date est dÃ©jÃ  attribuÃ©e Ã  une saison
calendar.assign.season.error.select.season=SÃ©lectionnez une saison dans la liste

calendar.assign.period.title=Profil du jour
calendar.assign.period.description=Affectez les heures du jour aux PÃ©riodes
calendar.assign.period=PÃ©riode 
calendar.assign.period.help=SÃ©lectionnez une pÃ©riode
calendar.assign.period.start=Heure de dÃ©but  
calendar.assign.period.start.help=Le moment oÃ¹ la pÃ©riode commence   
calendar.assign.period.start.hour=Heure  
calendar.assign.period.start.minute=Minute    
calendar.assign.period.end=Heure de fin  
calendar.assign.period.end.hour=Heure   
calendar.assign.period.end.minute=Minute    
calendar.assign.period.saved=La pÃ©riode a Ã©tÃ© enregistrÃ©e.
calendar.assign.period.cleared=Les changements de pÃ©riode ont Ã©tÃ© effacÃ©s.
calendar.assign.period.deleted=La pÃ©riode a Ã©tÃ© supprimÃ©e.
calendar.assign.period.error.end.before.start=L'heure de fin ne peut pas Ãªtre avant l'heure de dÃ©but
calendar.assign.period.error.time.already.assigned=L'heure sÃ©lectionnÃ©e a dÃ©jÃ  Ã©tÃ© attribuÃ©e.
calendar.assign.period.nulls=Toutes les valeurs de dÃ©but et de fin doivent Ãªtre entrÃ©es. Ne peut Ãªtre vide.  

calendar.dayprofiles.heading=Configurer les profils de jour pour le calendrier
calendar.dayprofiles.title=Profils de jour
calendar.dayprofiles.description=CrÃ©ez des jours qui sont divisÃ©s en pÃ©riodes spÃ©cifiques.

calendar.dayprofile.field.name=Nom de profil
calendar.dayprofile.field.code=Code
calendar.dayprofile.field.active=Actif
calendar.dayprofile.field.name.help=Entrez un nom pour ce jour de profil
calendar.dayprofile.field.code.help=Entrez un code abrÃ©gÃ© pour ce jour de profil
calendar.dayprofile.field.active.help=L'Ãtat de l'activitÃ© actuelle de ce jour
calendar.dayprofile.error.save=Impossible d'enregistrer un profil de jour
calendar.dayprofile.error.first.unassign=Impossible de supprimer un profil de jour qui est actuellement affectÃ© Ã  une saison de calendrier. DÃ©signez d'abord le profil du jour, puis supprimez.
calendar.dayprofile.error.special.day.first.unassign=Impossible de supprimer un jour de profil qui est actuellement affectÃ© Ã  une journÃ©e spÃ©ciale. Supprimez d'abord le jour spÃ©cial, puis supprimez le profil du jour. 
calendar.dayprofile.deleted=Profil du jour supprimÃ©
calendar.dayprofile.saved=Changements de profil jour enregistrÃ©s
calendar.dayprofile.cleared=Changements de profil jour effacÃ©s

calendar.assign.dayprofile.heading=Affecter des profils de jour au calendrier
calendar.assign.dayprofile.description=Pour chaque saison qui a Ã©tÃ© assignÃ©e au calendrier, spÃ©cifiez le profil du jour pour chaque jour de la semaine.
calendar.assign.dayprofile.cleared=Affectez les changements de profil journalisÃ©s effacÃ©s.
calendar.assign.dayprofile.saved=Affectez les changements de profil jour enregistrÃ©s.
calendar.assign.dayprofile.error.save=Erreur lors de l'attribution des profils de jour

calendar.specialday.heading=Jours spÃ©ciaux de configuration pour le calendrier
calendar.specialday.title=Configuration des jours spÃ©ciaux
calendar.specialday.description=Affecter des profils de jour Ã  des jours spÃ©cifiques
calendar.specialday.field.name=Nom du jour spÃ©cial
calendar.specialday.field.name.help=Un nom unique pour le jour.
calendar.specialday.field.active=Actif
calendar.specialday.field.active.help=L'Ã©tat d'activitÃ© actuel de ce jour spÃ©cial
calendar.specialday.field.day=journÃ©e
calendar.specialday.field.month=Mois
calendar.specialday.field.dayprofile=Profil du jour
calendar.specialday.field.dayprofile.help=SÃ©lectionnez le profil du jour
calendar.specialday.field.dayprofile.error=Le profil du jour doit Ãªtre saisi pour le jour spÃ©cial.
calendar.specialday.add=Ajouter un jour spÃ©cial
calendar.specialday.update=Mise Ã  jour du jour spÃ©cial
calendar.special.day.deleted=JournÃ©e spÃ©ciale supprimÃ©e.   
calendar.specialday.error.date.already.assigned.to=Cette date a dÃ©jÃ  Ã©tÃ© attribuÃ©e Ã  un jour spÃ©cial

calendar.readOnly=Remarque: Ce calendrier ne peut pas Ãªtre mis Ã  jour car il est dÃ©jÃ  utilisÃ© par les structures de prix suivantes: {0}

### Aux Charge Schedule ###
auxchargeschedules.header=Horaires de charge auxiliaire
auxchargeschedules.title=Horaires de charge auxiliaire actuels
auxchargeschedule.title=Horaire de charge auxiliaire
auxchargeschedule.title.add=Ajouter un programme de frais auxiliaire
auxchargeschedule.title.update=Mettre Ã  jour le programme de frais auxiliare
auxchargeschedule.field.name=Nom
auxchargeschedule.field.name.help=Le nom de ce calendrier doit Ãªtre unique et nÃ©cessaire
auxchargeschedule.field.status=Ãtat
auxchargeschedule.field.minamount=Montant min.
auxchargeschedule.field.minamount.help=Saisissez le montant minimum requis pour ce tarif
auxchargeschedule.field.maxamount=Montant maximal
auxchargeschedule.field.maxamount.help=Entrez le montant maximal autorisÃ© Ã  Ãªtre chargÃ© par ce calendrier
auxchargeschedule.field.vendportion=Portion de vente
auxchargeschedule.field.vendportion.help=Entrez le pourcentage d'une vente qui sera utilisÃ© pour calculer la charge.
auxchargeschedule.field.currportion=Portion actuelle
auxchargeschedule.field.currportion.help=Entrez le pourcentage du solde restant qui sera utilisÃ© pour calculer la charge.
auxchargeschedule.field.active=Actif
auxchargeschedule.field.active.help=L'Ãtat d'activitÃ© actuel de cet Ã©lÃ©ment
auxchargeschedule.error.save=Impossible d'enregistrer le programme de charge auxiliaire.
auxchargeschedule.error.duplicate=Duplique le nom {0} pour un programme de frais auxiliaire. SpÃ©cifiez un nom unique.
auxchargeschedule.nocharge.error=Une portion de vente, une partie courante ou un montant public doit Ãªtre dÃ©finie

### Auxilliary Type ###
auxilliarytypes.header=Types auxiliaires
auxilliarytypes.title=Types auxiliaires actuels
auxillarytype.title.add=Ajouter un type auxiliaire
auxillarytype.title.update=Mise Ã  jour du type auxiliaire
auxillarytype.title=Type auxiliaire
auxtype.field.name=Nom
auxtype.field.description=La description
auxtype.field.status=Ãtat
auxtype.field.active=Actif
auxtype.field.name.help=Le nom de ce type auxiliaire doit Ãªtre unique
auxtype.field.description.help=Description de ce type auxiliaire
auxtype.field.active.help=L'Ãtat d'activitÃ© actuel de cet Ã©lÃ©ment
auxtype.error.save=Impossible de sauvegarder le type auxiliaire.
auxtype.error.save.duplicate=Impossible d'enregistrer le type auxiliaire, un autre type auxiliaire avec le mÃªme nom existe dÃ©jÃ .
auxtype.error.update=Impossible de mettre Ã  jour le type auxiliaire.
auxtype.error.update.duplicate=Impossible de mettre Ã  jour le type auxiliaire, un autre type auxiliaire avec le mÃªme nom existe dÃ©jÃ .

### Device Store ###
devicestores.header=Dispositifs
devicestores.title=Magasins de dispositifs actuels
devicestore.title.add=Ajouter un magasin d'appareils
devicestore.title.update=Mettre Ã  jour le magasin d'appareils
devicestore.title=Dispositifs
devicestore.field.name=Nom
devicestore.field.description=La description
devicestore.field.name.help=Nom de ce magasin d'appareils (doit Ãªtre unique)
deviceStore.name.duplicate=Duplique le nom {0} pour un magasin d'appareils. SpÃ©cifiez un nom unique.
devicestore.field.description.help=Description de ce magasin d'appareils
devicestore.field.active=Actif
devicestore.field.active.help=L'Ãtat d'activitÃ© actuel de ce magasin d'appareils
devicestore.location.title=Emplacement de la boutique dispositif
devicestore.error.save=Impossible d'enregistrer le magasin de dispositifs.
devicestore.error.update=Impossible de mettre Ã  jour le magasin de dispositifs.
devicestore.button.addmeters=Ajouter des compteurs Ã  la banque de dispositifs sÃ©lectionnÃ©e
devicestore.meters=Compteurs actuels
devicestore.meters.in=Compteurs actuels dans
devicestore.meters.description=MÃ¨tres actuellement dans ce magasin d'appareils.
devicestore.meters.header=Compteur Dispositifs
devicestore.meters.title=Appareils de stockage de dispositifs actuels
devicestore.history=Historique du magasin dispositif
devicestore.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce magasin d'appareils (les modifications sont mises en surbrillance)
devicestore.user=Utilisateur
devicestore.date=Date
devicestore.date.mod.column=Date modifiÃ©e
devicestore.user.by.column=Par utilisateur
devicestore.action.column=Action
devicestore.status.column=Ãtat
devicestore.name.column=Nom
devicestore.description.column=La Description
devicestore.button.importmeters=Compteurs d'importation dans le magasin de dispositifs sÃ©lectionnÃ©
devicestore.import.meters.header=Importer des compteurs dans le magasin de dispositifs {0}

### Meter ###
meter.title=Compteur
meter.number.instructions=Recherche par numÃ©ro de compteur
meter.add=Ajouter un nouveau compteur
meter.add.new=Ajouter un nouveau compteur
meter.or=ou
meter.then=puis
meter.fetch.number=Chercher Compteur
meter.specify.install.date=SpÃ©cifiez la date d'installation
meter.open=Compteur ouvert
meter.open.newtab=Ouvrez le mÃ¨tre remplacÃ© dans un nouvel onglet.
meter.assign=Compteur de rÃ©cupÃ©ration
meter.attach=Fixez le compteur au point d'utilisation
meter.info.title=Information du compteur
meter.required.text=* \= Obligatoire
meter.required.activation.text=** \= Obligatoire pour l'activation
meter.show.info=Afficher les informations du compteur
meter.showing.info=Affichage de l'information du compteur
meter.active.help=Ce compteur est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations requises soient enregistrÃ©es.
meter.active=Actif
meter.replace.help=Remplacez le compteur actuel sur ce point d'utilisation par un autre compteur.
meter.replace=Remplacer le compteur
meter.remove.help=Retirez le compteur actuel de ce point d'utilisation. DÃ©sactivera le point d'utilisation. Peut l'accÃ©der Ã  nouveau via Recherche avancÃ©e.
meter.remove=Supprimer le compteur du Point D'utilisation
meter.assign.from.store.help=Obtenez un compteur Ã  partir du magasin de l'appareil.
meter.assign.from.store=DÃ©tecter le compteur depuis le magasin de dispositifs
meter.date.install.missing=Entrez la nouvelle date d'installation.
meter.select.meter.model=SÃ©lectionnez le modÃ¨le du compteur
meter.select.meter.model.help=SÃ©lectionnez le modÃ¨le du compteur. Cela dÃ©terminera les dÃ©tails nÃ©cessaires au compteur.
meter.select.metertype=Type de compteur
meter.select.metertype.help=SÃ©lectionnez le modÃ¨le du compteur. Cela dÃ©terminera les dÃ©tails nÃ©cessaires au compteur.
meter.number.help=Entrez le numÃ©ro du compteur. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
meter.number=NumÃ©ro du compteur
meter.number.optional.help=Entrez un numÃ©ro de compteur spÃ©cifique, si vous en avez un.
meter.number.optional= NumÃ©ro de compteur (optionnel)
meter.iso.help=Entrez le compteur ISO.
meter.iso=ISO
meter.checksum.help=Entrez la somme de contrÃ´le du compteur.
meter.checksum=Somme de contrÃ´le 
meter.serialnumber.help=Entrez le numÃ©ro de sÃ©rie du compteur.
meter.serialnumber=NumÃ©ro de sÃ©rie
meter.breakerid=Id du Disjoncteur
meter.breakerid.help=Le modÃ¨le du compteur nÃ©cessite un identifiant du disjonteur, entrez ici.
meter.breakerid.error=Le modÃ¨le du compteur nÃ©cessite un identifiant du disjoncteur.
meter.stsinfo=Informations STS
meter.algorithmcode=Code d'algorithme
meter.tokentechcode=Code jeton tech 
meter.supplygroupcode=Code actuel du groupe d'approvisionnement
meter.tariffindex=Indice tarifaire actuel
meter.save.errors=Le compteur n'est pas enregistrÃ©. Corrigez les erreurs.
meter.saved=MÃ¨tre enregistrÃ©.
meter.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
meter.enter.number=Entrez le numÃ©ro du compteur
meter.powerlimit=Limite de puissance
meter.powerlimit.help=SÃ©lectionnez la limite de puissance requise
meter.description=La description
meter.description.help=Entrez une description
meter.token.active=Le point d'utilisation doit Ãªtre actif pour obtenir ce jeton
meter.token.code=Code jeton
meter.token.code1=Code jeton 1
meter.token.code2=Code jeton 2
meter.title.enginerringtokens=Jetons d'ingÃ©nierie
meter.issue.engineeringtoken=Ãmettre un nouveau jeton d'ingÃ©nierie
meter.engineeringtoken.history=Historique des jetons d'ingÃ©nierie
meter.engineeringtoken.description=Les jetons d'ingÃ©nierie prÃ©cÃ©dents gÃ©nÃ©rÃ©s pour ce compteur
meter.select.tokentype=SÃ©lectionnez le type de jeton
meter.select.tokentype.help=SÃ©lectionnez un type de jeton dans la liste ci-dessous.
meter.txn.history=Historique des transactions
meter.txn.history.description=Les transactions client antÃ©rieures pour ce compteur
meter.history=Histoire du compteur
meter.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  ce compteur (les changements sont mis en Ã©vidence)
meter.reports=Rapports pour compteur
meter.recharge.history=Historique de la recharge du compteur
meter.retailers=Commerces Ã  proximitÃ©
meter.reports.general=Rapports gÃ©nÃ©raux pour les compteurs
meter.reports.meter=Rapports pour compteur
meter.freeissue.units=Emission gratuite - UnitÃ©s
meter.cleartamper=Effacer Tamper
meter.clearcredit=Effacer credit
meter.clearcredit.all=Effacer tout crÃ©dit
meter.clearcredit.elec=CrÃ©dit d'Ã©lectricitÃ© clair
meter.clearcredit.type.description=Type de crÃ©dit
meter.clearcredit.type.help=SÃ©lectionnez le type de crÃ©dit qui doit Ãªtre effacÃ©
meter.coords=Ajoutez les coordonnÃ©es Latitude et Longitude au point d'utilisation pour ce compteur, afin de voir les dÃ©taillants proches.
meter.user=L'utilisateur
meter.serial=En sÃ©rie
meter.date=Date
meter.date.mod.column=Date modifiÃ©e
meter.user.by.column=Par utilisateur
meter.action.column=Action
meter.status.column=Ãtat
meter.number.column=Numero Compteur
meter.serial.column=En sÃ©rie 
meter.uniqueid.column=Identifiant unique
meter.unique.external.column=ID unique externe
meter.techtoken.column=TT
meter.alg.column=Alg
meter.supplygroup.column=SG
meter.keyrevision.column=KR
meter.tariffindex.column=TI
meter.enddevicestore.column=le magasin
meter.units.kwh=UnitÃ©s (kWh)
meter.units.kwh.help=Entrez le nombre d'unitÃ©s kWh requises.
meter.units.watts=UnitÃ©s (Watts)
meter.units.watts.help=Entrez la limite de phase maximale en watts.
meter.currency=Devise
meter.currency.help=Entrez la valeur de la devise requise.
meter.free.description=La Description
meter.free.description.help=Entrez une description pour ce numÃ©ro gratuit.
meter.setphase=DÃ©finir la phase maximale
meter.setphase.description=La Description
meter.setphase.description.help=Entrez une description pour ce jeton de phase dÃ©finie.
meter.token.error=Impossible de rÃ©cupÃ©rer le jeton. VÃ©rifiez les erreurs.
meter.error.units=Entrez une valeur valide pour le nombre d'unitÃ©s requis
meter.error.amount=Entrez une valeur valide pour le montant requis
meter.txn.type=Type
meter.txn.token.type=Type de jeton
meter.txn.receipt=Le reÃ§u
meter.txn.token=Jeton
meter.txn.amount=Montant Incl impÃ´t
meter.txn.tax=ImpÃ´t
meter.txn.units=UnitÃ©s
meter.txn.tariff=Tarif
meter.txn.date=Date
meter.txn.ref=RÃ©fÃ©rence
meter.txn.receiptnum=NumÃ©ro de reÃ§u
meter.txn.customer=Client
meter.txn.client=Client
meter.txn.term=Terme
meter.txn.revref=Reflet d'inversion
meter.txn.isreversed=RenversÃ©
meter.txn.amount.column=Montant
meter.txn.usagepoint=Point d'utilisation
meter.txn.user=Utilisateur
meter.txn.description=La description
meter.clear.description=La description
meter.clear.description.help=Entrez une description pour cette sabotage.
meter.changekey=Changement clÃ©
meter.changekey.instructions=Pour les jetons de changement de clÃ©:\n1. Ouvrez le panneau du compteur.\n2. Dans le bloc d'informations STS, modifiez le code du groupe d'approvisionnement actuel.\n3. Cela ajoutera une nouvelle liste avec les options de changement de clÃ© - sÃ©lectionnez ce dont vous avez besoin.\n4. Lors de la sauvegarde du compteur, une boÃ®te contextuelle affichera d'autres dÃ©tails.
meter.new.supplygroupcode.help=Entrez le nouveau code de groupe d'approvisionnement.
meter.new.supplygroupcode=Code du nouveau groupe d'approvisionnement
meter.old.supplygroupcode=Ancien code du groupe d'approvisionnement
meter.new.tariffindex=Nouvel indice tarifaire
meter.old.tariffindex=Ancien indice tarifaire
meter.new.keyrevisionnum=Nouveau numÃ©ro de rÃ©vision clÃ©
meter.old.keyrevisionnum=Ancien numÃ©ro de rÃ©vision clÃ©
meter.new.tariffindex.help=Entrez le nouvel indice tarifaire.
meter.txn.filter=Filtre
mdc.txn.show.connect.disconnect.only=Afficher le dÃ©connexion de connexion uniquement
mdc.txn.show.balance.messages.only=Afficher uniquement les messages d'Ã©quilibre
meter.engtoken.filter=Filtre
meter.history.filter=Filtre
meter.generate.keychange=GÃ©rer des jetons de changement de clÃ©?
meter.generate.keychange.help=Si le compteur doit Ãªtre mis Ã  jour pour correspondre aux nouveaux dÃ©tails du STS, il faut gÃ©nÃ©rer des tokens de changement de clÃ©. Si l'enregistrement est mis Ã  jour pour correspondre aux dÃ©tails du compteur, il n'est pas nÃ©cessaire de gÃ©nÃ©rer des jetons.
meter.luhncheck.failed=NumÃ©ro de compteur incorrect (vÃ©rification Luhn Ã©chouÃ©e)
meter.error.alreadyexists=Un compteur avec ce numÃ©ro existe dÃ©jÃ .
meter.keychange.none=Ne gÃ©nÃ¨re pas de jetons de changement de clÃ©
meter.keychange.now=GÃ©nÃ©rer des jetons de changement de clÃ© maintenant
meter.keychange.flag=DÃ©finir pour gÃ©nÃ©rer des jetons de changement de clÃ© avec la prochaine vente
meter.pending.keychange=* Changement de clÃ© en attente:
meter.warning.sg.transactions=REMARQUE: Il y a dÃ©jÃ  plus de 3 transactions sur ce compteur.
meter.error.save=Impossible de sauvegarder le compteur.
stsmeter.error.save=Impossible de sauvegarder le compteur STS.
meter.select.store.move=DÃ©placez le compteur actuel vers le magasin suivant:
meter.select.store.help=Le compteur actuel doit Ãªtre ajoutÃ© Ã  un magasin. Lorsqu'il est affectÃ© Ã  un point d'utilisation, il sera automatiquement retirÃ© du magasin. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
meter.message.type=Type de message
meter.message.type.help=Envoyer un message pour le compteur 
meter.assigned.to.usagepoint=Le compteur est actuellement affectÃ© au point d'utilisation:
meter.error.invalid=Compteur invalide spÃ©cifiÃ©.
meter.error.invalid.datemanu=La date de fabrication n'est pas valide.
meter.error.cannot.activate=Impossible d'activer
meter.error.required.for.activation=NÃ©cessaire pour l'activation
meter.partial.search=Pas de compteur correspondant. Faire une recherche avancÃ©e ...
meter.install.date.required=La date et l'heure oÃ¹ le nouveau compteur a Ã©tÃ© installÃ© au point d'utilisation {0}. 
meter.installed.at=Meter a Ã©tÃ© installÃ© sur {0} Ã  {1}.
meter.connect.disconnect.error=Une erreur s'est produite lors de l'envoi des instructions du compteur: {0}
meter.connect.disconnect.connection.error=Aucune rÃ©ponse reÃ§ue. Service non disponible. Veuillez informer votre administrateur systÃ¨me.
meter.connect.disconnect.ok.mdc000=La commande Meter {0} a Ã©tÃ© complÃ©tÃ©e avec succÃ¨s. RÃ©fÃ©rence = {1}.
meter.connect.disconnect.ok.mdc010=La commande Meter {0} est acceptÃ©e. RÃ©fÃ©rence = {1}.
meter.connect.disconnect.ok.mdc011=Le message du compteur {0} a Ã©tÃ© mis en file d'attente. RÃ©fÃ©rence = {1}.
meter.manufacturer.code.length=Code Fabricant
meter.manufacturer.code.length.help=Longueur du code du fabricant, 2 chiffres ou 4 chiffres
meter.2digit.manufacturer.code=Code Ã  2 chiffres
meter.4digit.manufacturer.code=Code Ã  4 chiffres
meter.found.incorrect.group=Compteur trouvÃ© mais en groupe diffÃ©rent pour utilisateur
usagepoint.found.incorrect.group=Point d'utilisation trouvÃ© mais en groupe diffÃ©rent pour l'utilisateur
meter.attach.cancelled=Processus d'attachement annulÃ©. Le Compteur {0} a Ã©tÃ© crÃ©Ã© dans le magasin de pÃ©riphÃ©riques, mais pas assignÃ© Ã  ce point d'utilisation.
meter.attach.instructions=Fixez le compteur au point d'utilisation
meter.attach.cancel.button=Annuler le processus d'attachement

### Customer ###
customer.title=Client
customer.search.instructions=Recherche du client par:
customer.add=Ajouter un nouveau client
customer.add.new=Ajouter un nouveau client
customer.info.title=Informations client
customer.show.info=Afficher l'information du client
customer.showing.info=Affichage de l'information du client
customer.active.help=Est-ce que ce client est actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations client requises soient enregistrÃ©es.
customer.active=Actif
customer.unassign=DÃ©sassignez le client du point d'utilisation
customer.assign=Affecter le point de l'utilisateur au point d'utilisation
customer.assign.short=Affecter un client
customer.assign.help=Affectez un client existant Ã  ce point d'utilisation. 
customer.unassign.help=Supprimez le client actuel de ce point d'utilisation.
customer.fetch=Chercher un Client
customer.fetch.help=Rechercher un client existant
customer.fetch.duplicate=Client {0} dÃ©jÃ  sur la page.
customer.open=Client ouvert
customer.field.title.help=Entrez le titre du client (p. Ex., M., Mme, Dr).
customer.field.title=Titre
customer.initials.help=Entrez les initiales du client.
customer.initials=Initiales
customer.firstnames.help=Entrez les prÃ©noms du client.
customer.firstnames=PrÃ©noms
customer.surname.help=Entrez le nom de famille du client. Ceci est un champ obligatoire - le client ne peut pas Ãªtre enregistrÃ© sans lui.
customer.surname=Nom de famille
customer.surname.instr=Entrez le nom de famille du client
customer.company.help=Entrez le nom de la sociÃ©tÃ©.
customer.company=Nom de la compagnie
customer.tax.help=Entrez le numÃ©ro de l'impÃ´t du client.
customer.tax=NumÃ©ro d'identification fiscale
customer.emails.help=Entrez les adresses e-mail du client.
customer.phones.help=Entrez les numÃ©ros de tÃ©lÃ©phone du client.
customer.address.physical=Adresse physique
customer.agreement=Contrat client
customer.agreementref.help=Entrez la rÃ©fÃ©rence de l'accord avec le client. Ceci est un champ obligatoire - le client ne peut pas Ãªtre enregistrÃ© sans lui.
customer.agreementref=Ref. D'accord
customer.startdate.help=Entrez la date d'entrÃ©e en vigueur de l'accord avec le client. Bien que l'enregistrement puisse Ãªtre enregistrÃ© sans lui, le client ne peut pas Ãªtre activÃ© Ã  moins qu'il ne soit rempli correctement.
customer.startdate=Date de dÃ©but
customer.freeissue.help=SÃ©lectionnez le compte auxiliaire Ã  utiliser avec les jetons d'Ã©mission gratuits, oÃ¹ la valeur du jeton sera rÃ©cupÃ©rÃ©e en ajoutant le montant au compte sÃ©lectionnÃ©.
customer.freeissue=Compte auxiliaire gratuit
customer.required=* \= Obligatoire
customer.required.activation=** \= Obligatoire pour l'activation
customer.save.errors=Le client n'est pas enregistrÃ©. Corrigez les erreurs affichÃ©es.
customer.sync.accbal.error=Une erreur s'est produite lors de la synchronisation du solde du compte par rapport au compteur. RÃ©fÃ©rence = {0}
customer.sync.accbal.connection.error=Aucune rÃ©ponse reÃ§ue du service.
customer.sync.accbal.ok.mdc000=Synchroniser le solde du compte avec succÃ¨s. RÃ©fÃ©rence = {0}
customer.sync.accbal.ok.mdc010=Synchroniser la commande de balance de compte acceptÃ©e. RÃ©fÃ©rence = {0}
customer.sync.accbal.ok.mdc011=Le message de synchronisation de compte de synchronisation a Ã©tÃ© mis en file d'attente. RÃ©fÃ©rence = {0}
customer.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
customer.auxaccount.adjust=Ajuster le compte auxiliaire
customer.title.find=Trouver un client
customer.assigned=Impossible d'attribuer le client, dÃ©jÃ  affectÃ©.
customer.auxaccount=Compte Auxiliaire: {0}
customer.auxaccount.addedit=Comptes Auxiliaires
customer.auxaccount.active=Actif
customer.auxaccount.active.help=Ce compte est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations de compte requises soient enregistrÃ©es.
customer.auxaccount.type.help=SÃ©lectionnez le type de compte auxiliaire que vous ajoutez.
customer.auxaccount.balance=Balance
customer.auxaccount.balance.help=Entrez le solde actuel de ce compte.
customer.auxaccount.balance.pos=Un solde positif indique un REMBOURSEMENT
customer.auxaccount.balance.neg=Un solde nÃ©gatif indique une DETTE
customer.auxaccount.priority.help=Entrez la prioritÃ© pour ce compte (1 est la prioritÃ© absolue).
customer.auxaccount.chargeschedule.help=SÃ©lectionnez le calendrier des charges.
customer.auxaccount.txn.history=Historique des transactions des comptes auxiliaires pour : {0}.
customer.auxaccount.txn.description=Transactions antÃ©rieures pour ce compte auxiliaire
customer.title.auxaccounts=Comptes Auxiliaires
customer.title.auxaccounts.current=Comptes Auxiliaires Actuels
customer.title.auxaccounts.description=Affichez et Ã©ditez les comptes auxiliaires actuels et crÃ©ez de nouveaux comptes auxiliaires.
customer.title.txnhistory=Historique des transactions de compte
customer.title.history=Historique des clients
customer.title.reports=Rapports pour le client
customer.title.generalreports=Rapports gÃ©nÃ©raux pour les clients
customer.title.chargescheduledetails=DÃ©tails pour l'horaire des charges auxiliaires
customer.title.chargeschedule=Horaire de charge
customer.chargeschedule.startdate=Date de dÃ©but
customer.chargeschedule.vendpor=Vente Portion
customer.chargeschedule.currpor=Portion actuelle
customer.chargeschedule.minamt=Montant min.
customer.chargeschedule.maxamt=Montant maximal
customer.chargeschedule.status=Ãtat
customer.auxaccount.filter=Filtre
customer.auxaccount.date=Date
customer.auxaccount.edit=modifier
customer.auxaccount.name=Nom du compte
customer.auxaccount.name.help=Entrez un nom pour ce compte.
customer.auxaccount.type=Type
customer.auxaccount.type.column=Type
customer.auxaccount.priority=PrioritÃ©
customer.auxaccount.chargeschedule=Horaire de charge
customer.auxaccount.status=Ãtat
customer.auxaccount.freeissue=Exempt
customer.auxaccount.add=Ajouter un compte auxiliaire
customer.auxaccount.update=Mettre Ã  jour le compte auxiliaire
customer.auxaccount.error.save=Impossible de sauvegarder le compte auxiliaire.
customer.auxaccount.error.id=Impossible d'appliquer l'ajustement du compte auxiliaire par rapport au compte client. Contactez le support!
customer.auxaccount.error.unique.priority=La prioritÃ© est requise et doit Ãªtre unique. Ne peut Ãªtre infÃ©rieur ou Ã©gal Ã  zÃ©ro.
customer.freeissue.error.save=Impossible de mettre Ã  jour l'accord du client. 
customer.partial.search=Pas de correspondance exacte avec le client. Faire une recherche avancÃ©e ...
customer.agreement.partial.search=Pas d'accord de correspondance exacte. Faire une recherche avancÃ©e ...
customer.account.partial.search=Pas de correspondance exacte au nom du compte. Faire une recherche avancÃ©e ...

customer.date.mod.column=Date modifiÃ©e
customer.user.by.column=Par utilisateur
customer.action.column=Action
customer.status.column=Ãtat
customer.title.column=Titre
customer.initials.column=Initiales
customer.firstnames.column=PrÃ©noms
customer.surname.column=Nom de famille
customer.company.column=Compagnie
customer.email1.column=Email
customer.email2.column=Email 2
customer.phone1.column=TÃ©lÃ©phone 
customer.phone2.column=TÃ©lÃ©phone 2

customer.user=Utilisateur
customer.date=Date
customer.history=Historique des clients
customer.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce client (les changements sont mis en Ã©vidence)
customer.history.filter=Filtre

customer.agreement.user=Utilisateur
customer.agreement.date=Date
customer.agreement.history=Historique de l'accord client
customer.agreement.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  cet accord client (les changements sont mis en Ã©vidence)
customer.agreement.history.filter=Filtre
customer.agreement.date.mod.column=Date modifiÃ©e
customer.agreement.user.by.column=Date modifiÃ©e
customer.agreement.action.column=Action
customer.agreement.status.column=Ãtat
customer.agreement.customer.column=Client
customer.agreement.ref.column=Ref. D'accord
customer.agreement.start.column=Date de dÃ©but
customer.agreement.freeaux.column=Compte Aux gratuit
customer.error.save=Impossible de sauvegarder le client.
customeraggreement.error.save=Impossible de sauvegarder l'accord client.

customer.account=Compte client
customer.account.name=Nom du compte
customer.account.name.help=Entrez un nom pour ce compte
customer.account.balance=Solde du compte
customer.account.balance.help=Le solde du compte courant
customer.account.sync=Synchroniser l'Ã©quilibre
customer.account.sync.help=Synchroniser le solde du compte avec le solde du compteur
customer.account.low.balance.threshold=Seuil de faible Ã©quilibre
customer.account.low.balance.threshold.help=Lorsque le solde du compte atteint ce seuil, un message sera envoyÃ© au client.
customer.account.credit.limit=Limite de crÃ©dit
customer.account.credit.limit.help=La limite de crÃ©dit autorisÃ©e sur ce compte
customer.account.note=Un compte client n'est nÃ©cessaire que si la structure de prix choisie pour le point d'utilisation (ci-dessous) l'exige.
customer.account.notification.email=Adresse Ã©lectronique de notification
customer.account.notification.email.help=Une adresse e-mail Ã  laquelle les notifications liÃ©es au compte peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de balance bas a Ã©tÃ© atteint)
customer.account.notification.phone=NumÃ©ro de tÃ©lÃ©phone de la notification
customer.account.notification.phone.help=Un numÃ©ro de tÃ©lÃ©phone auquel les notifications liÃ©es au compte peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de balance bas a Ã©tÃ© atteint)
customeraccount.error.save=Impossible de sauvegarder le compte client.

customer.txn.filter=Filtre
customer.txn.history=Historique des transactions de compte
customer.txn.description=OpÃ©rations de compte antÃ©rieures pour cette convention client
customer.txn.ent.date=Date entrÃ©e
customer.txn.user=Utilisateur entrÃ©
customer.txn.name=Client
customer.txn.agreement.ref=Ref. D'accord
customer.txn.trans.type=Type de transaction
customer.txn.trans.date=Date de la transaction
customer.txn.comment=Commentaire
customer.txn.amt=Montant incluant l'impÃ´t
customer.txn.tax=L'impÃ´t
customer.txn.bal=Balance
customer.txn.input=Ajuster le compte
customer.txn.acc.ref=RÃ©fÃ©rence du compte
customer.txn.our.ref=Notre rÃ©fÃ©rence
customer.txn.amt.incl.tax=Montant incl. ImpÃ´t
customer.txn.successful.adjustment=Compte rÃ©ussi et nouveau solde de compte
customer.txn.no.agreement=Il n'y a PAS d'accord client pour ajuster
customer.txn.error.amt.and.tax.zero=Le montant et l'impÃ´t ne peuvent pas Ãªtre zÃ©ro
customer.txn.error.update=Erreur lors de la mise Ã  jour du solde du compte client
customer.txn.error.insert=Erreur lors de l'insertion de la nouvelle transaction d'ajustement
customer.txn.error.no.usagepoint=Point d'utilisation introuvable
customer.txn.notification.failure=Impossible d'envoyer une notification d'ajustement du compte. Veuillez informer votre administrateur systÃ¨me.
customer.txn.send.email.failure=Impossible d'envoyer un compte d'ajustement de compte au client. Veuillez informer votre administrateur systÃ¨me.

# Customer transaction upload
customer.trans.upload= TÃ©lÃ©charger la Transaction
customer.trans.upload.heading=TÃ©lÃ©chargement des transactions d'ajustement du solde du compte client
customer.trans.upload.data.title=Importation des transactions d'ajustement du solde du compte client
customer.trans.upload.data.description=SÃ©lectionnez le fichier CSV contenant les transactions du client pour l'importation dans le systÃ¨me de gestion des compteurs.
customer.trans.upload.file.help=SÃ©lectionnez un fichier contenant des informations de transaction client dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me
customer.trans.upload.file=SÃ©lectionnez le fichier de transaction
customer.trans.upload.csv.button=TÃ©lÃ©charger des donnÃ©es CSV
customer.process.trans.button=Transactions de processus
customer.trans.upload.identifierType=Type de Identificateur
customer.trans.upload.identifier=Identificateur
customer.trans.upload.amt.incl.tax= Montant incl impÃ´t
customer.trans.upload.amt.tax=Montant ImpÃ´t
customer.trans.upload.trans.date=Date de Transaction
customer.trans.upload.account.ref=RÃ©fÃ©rence du compte
customer.trans.upload.comment=Commentaire
customer.trans.upload.errors=Erreurs
customer.trans.upload.table.heading.errors=Transactions : Erreurs
customer.trans.upload.Bizswitch.down=Aucune rÃ©ponse reÃ§ue. Le service BizSwitch n'est pas disponible. Veuillez informer votre administrateur systÃ¨me.
customer.trans.upload.table.heading.valid=Transactions valides: Ã©chantillon des 15 premiÃ¨res lignes dans le fichier
customer.trans.upload.invalid.cannot.create.dir=ERREUR! Impossible de crÃ©er le rÃ©pertoire. Contactez Support.
customer.trans.upload.filename= Nom de fichier sÃ©lectionnÃ© ={0}
customer.trans.upload.invalid.filename=Nom de fichier incorrect - Un trait d'union ou une pÃ©riode manquant. Nom de fichier attendu comme TransCompte -rÃ©fÃ©rence.csv oÃ¹ <rÃ©fÃ©rence> est sauvegardÃ© comme Â«notre RefÂ» sur les transactions
customer.trans.upload.invalid.filename.changed=Le nom de fichier a changÃ© entre les Ã©tapes! Ãtait {0}; maintenant {1}
customer.trans.upload.invalid.unexpected.commas=Commas Ã  l'intÃ©rieur des champs - ne peut pas identifier prÃ©cisÃ©ment les champs distincts
customer.trans.upload.invalid.identifiertype=typeIdentificateur doit Ãªtre NomPointUtilisation / NomCompte / NumÃ©roCompteur
customer.trans.upload.invalid.identifier=Identifiant non valide - pas dans la base de donnÃ©es
customer.trans.upload.invalid.agreement=Usage Point n'a pas de contrat client en place
customer.trans.upload.invalid.usagepoint.or.agreement=Le Compteur n'a pas de point d'utilisation ou le point d'utilisation n'a pas d'accord
customer.trans.upload.invalid.usagepoint=Le compte client n'appartient pas Ã  un point d'utilisation
customer.trans.upload.invalid.amt.incl.tax=Montant incl. lâImpÃ´t n'est pas numÃ©rique
customer.trans.upload.invalid.amt.tax=Le montant de lâ impÃ´t n'est pas numÃ©rique
customer.trans.upload.invalid.trans.date=La date de transaction doit Ãªtre soit vide (par dÃ©faut Ã  la date de processus) ou formatÃ© comme aaaa-mm-dd hh:mm:ss, example: 2015-09-23 22:14:55
customer.trans.upload.invalid.account.ref=RÃ©fÃ©rence du compte maximum de 100 caractÃ¨res
customer.trans.upload.invalid.comment=Commentaire maximum 255 caractÃ¨res
customer.trans.upload.invalid.duplicate=La transaction de typeIdentificateur en copie: {0}, identificateur: {1}. Les deux ciblent le mÃªme compte client.
customer.trans.upload.file.action.unknown=Action de tÃ©lÃ©chargement de fichier inconnu, contacter Support
customer.trans.upload.file.none=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour Ãªtre tÃ©lÃ©chargÃ©
customer.trans.upload.file.error=Erreur lors du tÃ©lÃ©chargement du fichier
customer.trans.upload.file.process.error=Erreur lors du processus du fichier
customer.trans.upload.successful.counts=Total de {0} transactions en lot - {1} ont Ã©tÃ© traitÃ©es avec succÃ¨s, {2} Ã©taient des doublons et ont Ã©tÃ© ignorÃ©s dans cette course.
customer.trans.upload.process.failed=Erreur systÃ¨me lors de la transaction:typeIdentificateur= {0}, identificateur= {1}, compteRef= {2}. Essayez de renvoyer le fichier.
customer.trans.upload.trans.validation.errors= Erreurs de validation trouvÃ©es. RÃ©parez-vous et re-soumettre. Maximum 15 erreurs sont traitÃ©es Ã  tout moment

### Location ###
location.field.erfnumber=NumÃ©ro Erf
location.field.erfnumber.help=Entrez le numÃ©ro ERF de cet endroit
location.field.address=Adresse
location.field.address.help=Entrez l'adresse de cet endroit
location.field.city=Ville
location.field.city.help=Entrez la ville de cet endroit
location.field.province=Province
location.field.province.help=Entrez la province de cet endroit
location.field.country=Pays
location.field.country.help=SÃ©lectionnez le pays de cet endroit
location.field.postalcode=Code postal
location.field.postalcode.help=Entrez le code postal de cet endroit
location.field.lat=Latitude
location.field.lat.help=Entrez latitude de ce lieu
location.field.long=Longitude
location.field.long.help=Entrez longitude de ce lieu
location.latitude.invalid=Latitude est invalide.
location.longitude.invalid=Longitude est invalide.
location.field.streetnumber=NumÃ©ro de rue
location.field.streetnumber.help=Entrez le numÃ©ro de rue
location.field.buildingname=Nom du bÃ¢timent
location.field.buildingname.help=Entrez le nom du bÃ¢timent
location.field.suitenumber=NumÃ©ro de Suite
location.field.suitenumber.help=Entrez le numÃ©ro d'appartement
location.error.save=Impossible de sauvegarder le lieu.
location.user=Utilisateur
location.date=Date
location.history=Localisation Histoire
location.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  cette adresse.
location.history.date.mod.column=Date de modification
location.history.user.by.column=Par utilisateur
location.history.action.column=Action
location.history.status.column=Ãtat
location.history.address1.column=Adresse
location.history.address2.column=
location.history.address3.column=
location.history.erfnumber.column=NumÃ©ro Erf   
location.history.latitude.column=Latitude    
location.history.longitude.column=Longitude   
location.history.group.column=Groupe   
location.history.streetnum.column=NumÃ©ro de rue   
location.history.buildingname.column=BÃ¢timent  
location.history.suitenum.column=NumÃ©ro de Suite 
location.history.physical.address=Historique d'adresse Physique 

## Display Tokens
displaytokens.title=Afficher Jetons
display.initiate=Initier compteur d'essai
display.testload=1. Tester le commutateur de charge
display.testdisplay=2. Tester les dispositifs d'affichage paiement d'information du compteur
display.totals=3. Afficher kWh totaux cumulÃ©s de registre d'Ã©nergie
display.krn=4. Affichez le KRN
display.ti=5. Affichez TI
display.testreader=6. Testez le dispositif de lecteur de jetons
display.powerlimit=7. Afficher la limite de puissance maximale
display.tamper=8. Afficher l'Ãtat de sabotage
display.consumption=9. Affichage de la consommation d'Ã©nergie
display.version=10. Afficher la version du logiciel
display.phase=11. Affichage limite de puissance de dÃ©sÃ©quilibre de phase

## Change Group
changegroup.change=Changer de Groupe
changegroup.username=Utilisateur
changegroup.current=Groupe actuel
usergroup.field.grouphierarchy=Niveau de Groupe
changegroup.set=Mettre en place Groupe 
changegroup.select=Parcourez les groupes de contrÃ´le d'accÃ¨s disponibles disponibles ci-dessous et sÃ©lectionnez un groupe.
changegroup.available=Groupes disponibles
changegroup.error.group.none=SÃ©lectionnez un groupe valide.
changegroup.error.same=Le groupe sÃ©lectionnÃ© est le mÃªme groupe que votre groupe actuel.
changegroup.assigned=AccÃ¨s AssignÃ© Group Utilisateur
changegroup.group.changed=Votre groupe actuel a Ã©tÃ© changÃ©.
changegroup.group.cleared=Votre groupe actuel a Ã©tÃ© effacÃ©.
changegroup.error.clear=Vous ne pouvez pas effacer votre groupe que vous avez un groupe assignÃ© actuellement.
changegroup.current.details=DÃ©tails actuels
changegroup.username.help=Commencez Ã  taper le nom d'utilisateur de l'utilisateur et sÃ©lectionnez l'utilisateur appropriÃ© dans la liste des utilisateurs.
changegroup.warning=Note: Modification de votre groupe aura besoin de actualiser et / ou la fermeture de tous les onglets d'espace de travail qui sont liÃ©s Ã  un groupe.

## User Group
usergroup.title=Groupe AccÃ¨s d'utilisateur
usergroup.header=Groupe AccÃ¨s d'utilisateur
usergroup.title.current=Groupe AccÃ¨s d'utilisateur actuel
usergroup.label.help=SÃ©lectionnez le groupe d'accÃ¨s que l'utilisateur fera partie pour la visualisation et la modification des donnÃ©es.
usergroup.error.user=Utilisateur non valide spÃ©cifiÃ©.
usergroup.error.group=Groupe non valide spÃ©cifiÃ©.
usergroup.error.save=Impossible de sauvegarder le groupe de l'utilisateur.
usergroup.error.update=Impossible de mettre Ã  jour le groupe de l'utilisateur.
usergroup.field.user=Utilisateur
usergroup.field.usergroup=Groupe d'accÃ¨s assignÃ©
usergroup.title.add=Ajouter un Groupe d'accÃ¨s d'utilisateur
usergroup.title.update=Mise Ã  jour Groupe AccÃ¨s d'utilisateur/s
usergroup.instructions=Chaque utilisateur peuvent Ãªtre assignÃ© Ã  un groupe d'accÃ¨s. Le groupe d'accÃ¨s de l'utilisateur dÃ©termine quelles donnÃ©es sont disponibles Ã  l'utilisateur de visualiser et de modifier.
usergroup.confirm.clear=Etes-vous sÃ»r de vouloir effacer le groupe d'accÃ¨s de l'utilisateur?
usergroup.clear.yourself=Impossible de limiter le groupe d'accÃ¨s de votre propre utilisateur pendant que vous Ãªtes connectÃ© Ã  cette session.
usergroup.cleared=Le groupe d'accÃ¨s de l'utilisateur a Ã©tÃ© effacÃ©.
usergroup.error.usergroup.none=Il n'y a pas de groupe d'accÃ¨s actuel pour l'utilisateur.
usergroup.error.delete=Impossible d'effacer le groupe d'accÃ¨s de l'utilisateur.
usergroup.no.accessgroup=Aucun type de groupe d'accÃ¨s n'a encore Ã©tÃ© fixÃ©e pour l'accÃ¨s aux donnÃ©es. Utilisez la page types de groupe pour dÃ©finir un type de groupe d'accÃ¨s.
usergroup.accessgroup.none=Impossible d'assigner le groupe d'accÃ¨s d'un utilisateur car il n'y a pas de groupes disponibles pour le contrÃ´le d'accÃ¨s de l'utilisateur.

## Access Groups
accessgroups.title=Groupe d'accÃ¨s d'utilisateurs
accessgroup.access.label=Type de Groupe:
accessgroups.title.current=Groupes d'accÃ¨s actuels
accessgroup.access.instructions=Votre groupe d'accÃ¨s assignÃ© actuellement ne peuvent Ãªtre mis Ã  jour, ainsi que l'un de ses sous-groupes. Nouveaux sous-groupes de votre groupe d'accÃ¨s peuvent Ã©galement Ãªtre ajoutÃ©s. S'il n'y a pas de donnÃ©es liÃ©es, vous pouvez Ã©galement supprimer des sous-groupes de niveau infÃ©rieur.

locationgroups.title=Localisation des Groupes
locationgroup.instructions=Localisation des groupes sont utilisÃ©s pour les donnÃ©es de localisation de groupe dans une hiÃ©rarchie.
locationgroups.title.current=Localisation actuel des Groupes

## Advanced Search
search.advanced.title=Recherche avancÃ©e
search.advanced.header=Recherche avancÃ©e
search.advanced.instructions=Entrez une partie ou les donnÃ©es de recherche complets dans les diffÃ©rents champs ci-dessous et cliquez sur le bouton Rechercher pour afficher vos rÃ©sultats. 
search.advanced.results.header=RÃ©sultats avancÃ©e de la recherche
search.meter.number=NumÃ©ro du compteur  
search.meter.model=ModÃ¨le du Compteur
search.meter.no.usage.point=Compteurs sans point d'utilisation 
search.type=Type de recherche
search.type.agreement=Accord du Type de recherche
search.startswith=Commence par
search.contains=Contient
search.customer.name=PrÃ©nom
search.customer.surname=Nom
search.customer.title=Titre
search.customer.agreement=Accord
search.customer.agreement.ref=RÃ©fÃ©rence
search.customer.no.usage.point=Les clients sans point d'utilisation
search.account=Compte
search.account.name=PrÃ©nom
search.usagepoint.name=PrÃ©nom
search.meter=Recherche Compteur 
search.customer=Recherche Client 
search.usagepoint=Recherche Point Usage 
search.usage.point.no.customer=Point Usage non clients
search.usage.point.no.meter=Points d'utilisation sans compteur
search.error.no.criteria=Aucun critÃ¨re de recherche valide a Ã©tÃ© spÃ©cifiÃ©.
search.no.results=Aucun rÃ©sultat de recherche correspondant n'a Ã©tÃ© trouvÃ©.
search.meter.result=Compteur
search.meter.model.result=ModÃ¨le Compteur
search.customer.result=Client
search.usagepoint.result=Point Usage
search.pricingStructure.result=Structure de prix
search.paymentMode.result=Mode de paiement
search.name=PrÃ©nom
search.no.meter=Pas de compteur correspondant exactement a Ã©tÃ© trouvÃ©. Effectuer la recherche en utilisant le numÃ©ro de compteur partiel: {0}.

meterreadings.title=Lectures de compteurs
meterreadings.chart.title.single=Lectures de compteurs
meterreadings.chart.title.balancing=Ãnergie Ãquilibrage
meterreadings.header.graph=Graphe
meterreadings.title.graph=Graphe de Compteur Lectures 
meterreadings.error.none=Commencez Ã  taper un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant des compteurs.
meterreadings.error.none.selected=SÃ©lectionnez un compteur valide Ã  partir des compteurs disponibles.
meterreadings.start=Date de dÃ©but
meterreadings.end=Date de fin
meterreadings.error.dates=DÃ©but doit Ãªtre avant la date de fin.
meterreadings.error.type=SÃ©lectionnez un type de lecture.
meterreadings.type=Type de lecture
meterreadings.error.start=Date de dÃ©but est nÃ©cessaire.
meterreadings.error.start.format=Date de dÃ©but doit correspondre {0}.
meterreadings.error.end=Date de fin est nÃ©cessaire.
meterreadings.error.end.format=Date de fin doit correspondre Ã  {0}.
meterreadings.noreadings=Pas de relevÃ©s de compteurs correspondants ont Ã©tÃ© trouvÃ©s.
meterreadings.chart.title=Lectures de compteurs
meterreadings.chart.subtitle=Utilisation kWh 
meterreadings.chart.ytitle=kWh
meterreadings.chart.xtitle=Date et heure
meterreadings.series.name=kWh
meterreadings.graph.type=Type de graphe
meterreadings.type.graph.single=Compteur Simple
meterreadings.type.graph.balancing=Ãnergie Ãquilibrage
meterreadings.error.type.reading.unknown=Type de lecture est inconnue.
meterreadings.balancing=Compteur Super
meterreadings.error.balancing=Compteur Super  est requise.
meterreadings.error.type.graph=Type de graphe est nÃ©cessaire.
meterreadings.meter.super=Lectures du compteur Super
meterreadings.meter.totals=Sous-compteur total
meterreadings.meter.field.super=Compteur Super
meterreadings.meter.field.subs=Sous compteurs
meterreadings.header.table=Rapport
meterreadings.title.table=Rapport de Lectures des Compteurs
meterreadings.table.meter=Compteur
meterreadings.table.reading=Lecture
meterreadings.table.start=Heure de dÃ©but
meterreadings.table.end=Heure de fin
meterreadings.report.type=Type de rapport
meterreadings.label.supermeter.total=Compteur Super
meterreadings.label.singlemeters.total=Sous compteurs
meterreadings.report.results=Lectures de compteurs

energybalancing.header=Ãnergie Ãquilibrage
energybalancing.title=Ãnergie Ãquilibrage
energybalancing.start=Date de dÃ©but
energybalancing.end=Date de fin
energybalancing.variation=Variation
energybalancing.error.start=Date de dÃ©but est nÃ©cessaire.
energybalancing.error.end=Date de fin est nÃ©cessaire.
energybalancing.error.dates=Date de dÃ©but doit Ãªtre antÃ©rieure Ã  la date de fin.
energybalancing.error.percent=DiffÃ©rence doit Ãªtre une valeur pourcentage positive.
energybalancing.error.readingtype=RelevÃ© de compteur de type est nÃ©cessaire.
energybalancing.supermeter=Compteur Super
energybalancing.supermeter.reading=Compteur Super total
energybalancing.submeters.total=Sous Compteurs total
energybalancing.none=Aucune mesure d'Ã©quilibrage Ã©nergÃ©tique correspondante n'a Ã©tÃ© trouvÃ©e.
energybalancing.view.graph=Voire Graphe

energybalancing.meter.header=Compteurs d'Ãnergie Ãquilibrage
energybalancing.meter.title=Compteur d'Ãnergie Ãquilibrage
energybalancing.meter.super=Ãquilibrage du numÃ©ro compteur
energybalancing.meter.sub=Sous numÃ©ro compteur
energybalancing.meter.subs=Sous-compteurs sÃ©lectionnÃ©s
energybalancing.meter.super.help=Entrez un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant.
energybalancing.meter.sub.help=Entrez un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant. Puis cliquez sur le bouton> pour le sÃ©lectionner comme un sous compteur.  
energybalancing.meter.subs.help=Tous les compteurs sÃ©lectionnÃ©s seront sauvegardÃ©s en tant que sous-compteurs du compteur d'Ã©quilibrage actuel.
energybalancing.meter.instructions=SÃ©lectionnez un compteur pour le compteur d'Ã©quilibrage. SÃ©lectionner et ajouter d'autres sous compteurs ou supprimer des sous compteurs existants.
energybalancing.error.super=Un compteur d'Ã©quilibrage sÃ©lectionnÃ© valide est nÃ©cessaire.
energybalancing.error.sub.selected=Sous compteurs valides sÃ©lectionnÃ©s sont nÃ©cessaires.
energybalancing.error.super.id=Compteur Ã©quilibrant id invalid.
energybalancing.error.sub.ids=Sous compteur id(s) invalid.
energybalancing.error.save=Impossible de sauvegarder l'Ã©quilibrage / sous compteur.
energybalancing.meter.error.no.sub=SÃ©lectionnez un sous compteur en premier.
energybalancing.meter.error.same.meter=Le sous compteur ne peut pas Ãªtre le compteur d'Ã©quilibrage..
energybalancing.meter.save=Ãquilibrage et sous compteurs sauvegardÃ©s.
energybalancing.confirm.delete=Etes-vous sÃ»r de vouloir supprimer ce compteur d'Ã©quilibrage et tous ses sous-compteurs?
energybalancing.meter.deleted=Ãquilibrage et sous compteurs ont Ã©tÃ© supprimÃ©s.
energybalancing.error.delete.none=Pas de compteurs d'Ã©quilibrage qui existent pour le compteur spÃ©cifiÃ©.

meterrecharge.chart.title=Recharges de Compteur 
meterrecharge.chart.subtitle=Montants de Recharge
meterrecharge.chart.xtitle=Date Heure
meterrecharge.chart.ytitle=CoÃ»t
meterrecharge.chart.price=Prix
meterrecharge.chart.purchaseprice=Prix d'achat

# Manufacturer
meter.manufacturers=Fabricant des compteurs
meter.manufacturers.title=Actuel Fabricants des compteurs
meter.manufacturers.title.add=Ajouter Fabricant de compteur
meter.manufacturers.title.update=Mise Ã  jour Fabricant de compteur
meter.manufacturer.name=Fabricant
meter.manufacturers.field.name=Nom
meter.manufacturers.field.description=Description
meter.manufacturers.field.active=Actif
meter.manufacturers.field.status=Ãtat
meter.manufacturers.field.name.help=Nom unique du fabricant.
meter.manufacturers.field.description.help=La description du fabricant.
meter.manufacturers.field.active.help=Seuls les fabricants actifs peuvent Ãªtre utilisÃ©s.
meter.manufacturer.name.duplicate=Nom en copie pour un fabricant: {0}.

# Mdc (Meter Data Collector)
meter.mdc=Lecteur de donnÃ©es du compteur
meter.mdc.title=Lecteur de donnÃ©es du compteur actuel
meter.mdc.title.add=Ajouter Lecteur de donnÃ©es du compteur
meter.mdc.title.update=Mise Ã  jour Lecteur de donnÃ©es du compteur
meter.mdc.name=Lecteur de donnÃ©es du compteur
meter.mdc.field.name=Nom
meter.mdc.field.description=Description
meter.mdc.field.active=Actif
meter.mdc.field.status=Ãtat
meter.mdc.field.value=Valeur
meter.mdc.field.name.help=Le nom unique de mdc.
meter.mdc.field.description.help=La description de mdc.
meter.mdc.field.value.help=UtilisÃ© dans les messages communiquant avec mdc.
meter.mdc.field.active.help=Seul mdc actif peut Ãªtre utilisÃ©.
meter.mdc.name.duplicate=Nom en copie pour mdc: {0}.
meter.mdc.value.duplicate=Valeur en copie pour mdc: {0}.
mdc.txn.messages=Messages MDC 
mdc.txn.message=Message dÃ©tails MDC -  Tirer Ici
mdc.txn.messages.description=Messages Mdc pour ce compteur
mdc.txn.ref=RÃ©fÃ©rence
mdc.txn.meter=Compteur
mdc.txn.usagepoint=Point Usage
mdc.txn.customer=Client
mdc.txn.reqreceived=Demande reÃ§ue
mdc.txn.reqsent=Demande EnvoyÃ©s
mdc.txn.reqtype=Type de demande
mdc.txn.controltype=Type de contrÃ´le
mdc.txn.cmdaccrec=Commande AcceptÃ©e
mdc.txn.params=Params
mdc.txn.recdate=RÃ©ponse reÃ§ue
mdc.txn.repcount=RÃ©pÃ©titions
mdc.txn.status=Ãtat
mdc.txn.timecompld=Temps TerminÃ©
mdc.txn.client=Client
mdc.txn.term=Term
mdc.txn.refreceived=RÃ©f ReÃ§u
mdc.txn.identifier=Identifiant
mdc.txn.identifiertype=Type d'identificateur
mdc.txn.rescodereceived=Code de rÃ©ponse
mdc.txn.scheduledate=Date plannifier
mdc.txn.success=SuccÃ¨s
mdc.txn.pending=En attente
mdc.txn.discarded=SupprimÃ©s
mdc.txn.successful=SuccÃ¨s
mdc.txn.failed=ÃchouÃ©
mdc.txn.popup.label=DonnÃ©es
mdc.txn.popup.value=Valeur
mdc.txn.send.message.title=Envoyer un message Ã  mdc
mdc.button.viewchannels=Afficher les canaux
mdc.error.noneselected=Aucun Mdc actuel sÃ©lectionnÃ©
mdc.txn.connect=Connecter
mdc.txn.disconnect= DÃ©connecter
mdc.txn.disconnect.enable=DÃ©connecter_Activer

# Mdc Channel
channel.header=Cannaux
channel.title=Canaux MDC actuels
channel.value.duplicate=Valeur en double pour un canal mdc: {0}
channel.field.titlename=Canal Mdc
channel.field.value=Valeur
channel.field.name=Nom
channel.field.billingdet=Facteur de Facturation
channel.field.status=Ãtat
channel.title.add=Ajouter Canal Mdc
channel.title.update=Mettre Ã  jour canal Mdc
channel.field.value.help=Entrez l'identificateur de canal.
channel.field.descrip=Description
channel.field.billingdetname=Facteur de Facturation
channel.field.billingdetname.channel.help=SÃ©lectionnez Facteur de Facturation. Peut Ãªtre vide SI les lectures de chaÃ®nes ne doivent pas Ãªtre utilisÃ©es pour la facturation.
channel.field.meter.reading.type=Type de lecture du compteur
channel.field.maxsize=Taille maximale de lecture
channel.field.active.help=L'Ã©tat d'activitÃ© actuel de cette chaÃ®ne
channel.field.active=Actif
channel.billingdet.confirm=Vous n'avez pas sÃ©lectionnÃ© un dÃ©terminant de facturation qui implique que cette chaÃ®ne n'est pas utilisÃ©e pour la facturation. Continuer?

#Mdc Channel Initial Readings
channel.readings.header=Assigner les lectures de chaÃ®nes initiales pour le compteur:
channel.readings.meter.model=ModÃ¨le du Compteur:
channel.readings.mdc=MDC:
channel.readings.pricing.structure=Structure des prix:
channel.readings.installdate=Date dâinstallation:
channel.readings.table.heading=Lecture Initiale
channel.readings.reading.error=Canal d'Erreur {0} : La valeur de lecture doit Ãªtre une valeur numÃ©rique positive infÃ©rieure ou Ã©gale Ã  la taille maximale {1}

# Billing Determinant
billingdet.tab.label=Fact de Facturation
billingdet.header=Facteurs de Facturation
billingdet.title=DÃ©terminants de facturation actuels
billingdet.name=Facteur de Facturation
billingdet.title.add=Ajouter Facteur de Facturation
billingdet.title.update=Mettre Ã  jour Facteur de Facturation
billingdet.field.name=Nom
billingdet.field.name.help=Nom de Facteur de Facturation
billingdet.field.description=Description
billingdet.field.description.help=Description de Facteur de Facturation
billingdet.active=Actif
billingdet.active.help=VÃ©rifiez pour activer, doit Ãªtre actif pour Ãªtre utilisÃ©.
billingdet.error.save=Impossible de sauvegarder le Facteur de Facturation.
billingdet.error.update=Impossible de mettre le Facteur de Facturation.

# Meter Model
meter.models=ModÃ¨les de compteurs
meter.models.title=ModÃ¨les de compteurs actuels
meter.models.name=ModÃ¨le de Compteur
meter.models.field.manufacturer=Fabricant
meter.models.field.manufacturer.help=SÃ©lectionnez le fabricant correct pour le modÃ¨le de compteur.
meter.models.field.name=Nom
meter.models.field.description=Description
meter.models.field.active=Actif
meter.models.field.status=Ãtat
meter.models.field.name.help=Nom unique du modÃ¨le de compteur.
meter.models.field.description.help=La description du modÃ¨le de compteur.
meter.models.field.active.help=Seulement modÃ¨les de compteurs  actifs peut Ãªtre utilisÃ©.
meter.models.field.resource=Service Ressource
meter.models.field.resource.help=SÃ©lectionnez la ressource de service correct pour le modÃ¨le de compteur.
meter.models.field.metertype=Type de compteur
meter.models.field.metertype.help=SÃ©lectionnez le type de compteur correct pour le modÃ¨le de compteur.
meter.models.field.toa=Support jeton transmission direct
meter.models.field.toa.help=Indique si ce modÃ¨le prend en charge l'envoi de jetons tels que jetons sts directement sur le compteur par l'intermÃ©diaire d'un rÃ©seau.
meter.models.field.mdc=Lecteur de donnÃ©es du compteur
meter.models.field.mdc.help=Associer ce modÃ¨le de compteur avec meter data collector.
meter.models.field.paymentmodes=Modes de paiement
meter.models.field.paymentmodes.help=Utilisez la touche Ctrl pour sÃ©lectionner plusieurs modes de paiement pour le modÃ¨le de compteur.
meter.models.title.add=Ajouter ModÃ¨le compteur
meter.models.title.update=Mise Ã  jour du ModÃ¨le compteur 
meter.models.paymentmodes.required=Au moins un mode de paiement est nÃ©cessaire.
meter.models.name.duplicate=Nom en copie pour un modÃ¨le de compteur: {0}.
meter.models.field.balance.sync=Prise en charge de la balance de synchronisation
meter.models.field.balance.sync.help=Indique si ce modÃ¨le de compteur prend en charge la synchronisation de l'Ã©quilibre.
meter.models.field.needs.breaker.id=Besoin Id du disjoncteur
meter.models.field.needs.breaker.id.help=Indique si le compteur nÃ©cessite un identifiant de disjoncteur.
meter.model.unset.breaker.id.error=Impossible de modifier l'exigence de l'identifiant du disjoncteur - il existe dÃ©jÃ  des compteurs avec DisjoncteurId utilisant ce modÃ¨le de compteur.

ptr.serviceresource=Service Ressource
ptr.metertype=Type de compteur
ptr.paymentmode=Mode de paiement

tou.thin.field.calendar=Calendrier
tou.thin.field.calendar.help=SÃ©lectionnez le calendrier pertinent pour le tarif.
tou.thin.field.monthlydemand=Demande mensuelle de frais
tou.thin.field.monthlydemandtype=Demande mensuelle du Type de lecture
tou.thin.field.monthlydemandtype.help=SpÃ©cifiez la demande mensuelle du Type de lecture de charge Ã  utiliser pour la charge.
tou.thin.field.servicecharge=Frais de service quotidien
tou.thin.field.servicecharge.descrip=Description
tou.thin.field.servicecharge.descrip.help=Entrez une description d'Ã©lÃ©ment de ligne pour la charge de service.
tou.thin.field.servicechargecycle=Cycle de Frais de service
tou.thin.field.servicechargecycle.help=Lorsque le frais de service doit Ãªtre appliquÃ©.
tou.thin.field.enablereadingtypes=Activer les lectures de types de compteurs
tou.thin.field.enablereadingtypes.help=SÃ©lectionnez les types de lecture Ã  appliquer pour ce tarif.
tou.thin.charges.button=Saisie les frais
tou.thin.field.charges=Frais
tou.thin.field.charges.specialday=Frais des Jours spÃ©cials
tou.thin.change.calendar=Etes-vous sÃ»r de vouloir annuler toutes vos charges et dÃ©finir un nouveau calendrier?
tou.thin.error.no.calendar=SÃ©lectionnez un calendrier.
tou.thin.error.no.types=SÃ©lectionnez au moins un type.
tou.thin.error.no.tax=ImpÃ´t est nÃ©cessaire.
tou.thin.monthlydemandtype.required=Type de lecture est nÃ©cessaire.
tou.thin.monthlydemand.required=Frais est nÃ©cessaire.
tou.thin.monthlydemand.positive=Frais doit Ãªtre positif.
tou.thin.monthlydemand.invalid=Frais doit Ãªtre une valeur numÃ©rique valide.
tou.thin.cycle.required=Cycle est nÃ©cessaire.
tou.thin.servicecharge.invalid=Frais de service doit Ãªtre une valeur numÃ©rique valide.
tou.thin.tax.invalid=ImpÃ´t doit Ãªtre une valeur numÃ©rique valide.
tou.thin.servicecharge.required=Frais de service est nÃ©cessaire.
tou.thin.servicecharge.positive=Frais de service doit Ãªtre positif.
tou.thin.no.tariff.data.available=Pas de donnÃ©es tarifaires disponibles pour Ãªtre mis Ã  jour et sauvegardÃ©es.
tou.thin.charges.none=Frais doivent Ãªtre capturÃ©s pour le tarif.
tou.thin.charges.invalid=Taux de frais doivent Ãªtre des valeurs numÃ©riques positives, valides.
tou.thin.specialdayscharges.none=Jour spÃ©cial doivent Ãªtre capturÃ©s pour le tarif.
tou.thin.specialdayscharges.invalid=Taux de jour spÃ©ciaux doivent Ãªtre des valeurs numÃ©riques positives, valides.
tou.thin.rates.none=Aucun taux entrÃ©
tou.thin.rates.invalid=Les taux doivent Ãªtre positifs et ne pas Ãªtre zÃ©ro

# Register Reading Tariffs
register.reading.billing.determinant.title=Facteurs de Facturation
register.reading.billing.determinant.help=SÃ©lectionnez les dÃ©terminants de facturation pour lesquels les taux de capture de cette structure de prix
register.reading.rates.button=Capturez les Taux
register.reading.rates.title=Taux
register.reading.billing.determinant.column.title=Facteur de Facturation
register.reading.error.no.tax=ImpÃ´t est requis.
register.reading.rates.none= Aucun taux entrÃ©
register.reading.rates.invalid=Les taux doivent Ãªtre positifs et ne pas Ãªtre zÃ©ro
register.reading.error.no.billingdet=SÃ©lectionnez au moins un Facteur de Facturation.
register.reading.no.tariff.data.available=Aucune donnÃ©e tarifaire disponible pour Ãªtre mise Ã  jour et sauvegardÃ©e.
register.reading.billing.det.change=Les dÃ©terminants de facturation dans la liste sÃ©lectionnÃ©e ne sont pas les mÃªmes que les frais courants pour ce tarif. Les frais prÃ©cÃ©dents seront supprimÃ©s, tous les frais doivent Ãªtre rÃ©inscrits. Continuer?
register.reading.servicecharge.descrip.required=La description des Frais de Service est requise.
register.reading.cannot.change.charge=Le tarif dÃ©jÃ  actif, ne peut pas changer les taux.

# Register Reading Information tab
register.reading.txn.label=Lectures de registre
register.reading.txn.description=Enregistrement Lectures pour ce compteur pour la pÃ©riode sÃ©lectionnÃ©e
register.reading.txn.timestamp=Horodatage
register.reading.txn.channel=Canal
register.reading.txn.determinant=Facteur
register.reading.txn.readingtype=Type de lecture du compteur
register.reading.txn.readingvalue=Valeur
register.reading.txn.error.enddate=La date de fin doit Ãªtre Ã©gale ou supÃ©rieure Ã  la date de dÃ©but.
register.reading.txn.filter=Filtrer
register.reading.txn.noreadings=Aucune indication de registre correspondante n'a Ã©tÃ© trouvÃ©e.

# AppSetting
appsetting.header=ParamÃ¨tres d'application
appsetting.title=ParamÃ¨tres actuels d'application 
appsetting.title.update=Mise Ã  jour paramÃ¨tre d'application
appsetting.name=ParamÃ¨tre  d'application
appsetting.field.name=Nom
appsetting.field.value=Valeur
appsetting.field.description=Description
appsetting.field.name.help=Nom du paramÃ¨tre d'application.
appsetting.field.value.help=La valeur Ã  appliquer pour ce paramÃ¨tre.
appsetting.field.description.help=Description du paramÃ¨tre d'application.
appsetting.name.duplicate=Nom en copie pour paramÃ¨tre d'application: {0}.
appsetting.error.new=ParamÃ¨tre de lâapplication {0} pas trouvÃ©. Veuillez contacter votre Administrateur SystÃ¨me.
appsetting.error.disconnect.greater.emergency.credit=Le crÃ©dit d'urgence doit Ãªtre supÃ©rieur ou Ã©gal Ã  DÃ©connectÃ©.
appsetting.error.disconnect.greater.reconnect=Le reconnexion doit Ãªtre supÃ©rieur ou Ã©gal Ã  DÃ©connectÃ©.
appsetting.error.disconnect.greater.both=La dÃ©connexion doit Ãªtre infÃ©rieure ou Ã©gale Ã  ReconnectÃ© ET infÃ©rieure ou Ã©gale Ã  un CrÃ©dit d'urgence
appsetting.error.maxvend.smaller.minvend=Le montant maximum de la vente doit Ãªtre supÃ©rieur ou Ã©gal au montant minimum de la vente
appsetting.error.emergency.credit.greater.low.balance=Le crÃ©dit d'urgence doit Ãªtre infÃ©rieur ou Ã©gal au faible solde.
appsetting.error.emergency.credit.errors=Le crÃ©dit d'urgence doit Ãªtre supÃ©rieur ou Ã©gal Ã  Disconnect ET infÃ©rieur ou Ã©gal Ã  Faible Solde.
appsetting.error.invalid.custom.status=L'Ã©tat du domaine personnalisÃ© n'est pas valide! Doit Ãªtre l'un OPTIONNEL, REQUIS OU NON DISPONIBLE.

demo.addmeterreadings.title.criteria=CritÃ¨res de lectures de compteurs
demo.addmeterreadings.interval=Intervalle de Lecture du compteur
demo.addmeterreadings.readingtypes=Types de lectures du compteur
demo.addmeterreadings.tariffcalc=Envoyez tariffCalcRequÃªte aprÃ¨s lectures
demo.addmeterreadings.error.paymentmode=Mode de paiement est nÃ©cessaire.
demo.addmeterreadings.error.meter=Entrez un numÃ©ro de compteur et sÃ©lectionnez l'une de la sÃ©lection disponible.
demo.addmeterreadings.error.meter.select=S'il vous plaÃ®t sÃ©lectionner un compteur de la sÃ©lection disponible.
demo.addmeterreadings.no.usagepoint1=Compteur n'a pas Point d'utilisation
demo.addmeterreadings.no.usagepoint2=Tariff Calc RequÃªte pas possible.
demo.addmeterreadings.no.usagepoint3=Impossible de lire le point d'utilisation pour le compteur. Veuillez informer le Support.
demo.addmeterreadings.error.start=Commencement est nÃ©cessaire.
demo.addmeterreadings.error.end=La fin est nÃ©cessaire.
demo.addmeterreadings.error.dates=DÃ©but doit Ãªtre avant la fin.
demo.addmeterreadings.error.end.future=La fin doit Ãªtre avant aujourd'hui.
demo.addmeterreadings.error.interval=Lecture Intervalle est nÃ©cessaire.
demo.addmeterreadings.error.types=Au moins un type de lecture est nÃ©cessaire.
demo.addmeterreadings.invalid.input=EntrÃ©e non valide pour ajouter des lectures de compteur.
demo.addmeterreadings.error.insert=Erreur insertion de la lecture du compteur.
demo.addmeterreadings.error.insert.fact=Erreur insertion du fait de lecture de compteur.
demo.addmeterreadings.error.insert.timeDim=Erreur insertion deTimeDim pour fait nouveau compteur de lecture.
demo.addmeterreadings.error.duplicates=Compteur a des lectures compteur existants pour la pÃ©riode en cours. Modifier date pÃ©riode ou choisir de supprimer les letures de compteurs existants.
demo.addmeterreadings.error.duplicate.Facts=Compteur a de lectures Faits compteur  pour la pÃ©riode en cours existant. Modifier date de pÃ©riode pou choisir de supprimer les lectures de compteurs existants. 
demo.addmeterreadings.minutes.15=15 minutes
demo.addmeterreadings.minutes.30=30 minutes
demo.addmeterreadings.minutes.60=60 minutes

demo.addsupermeterreadings.link=[DEMO] Ajouter les rÃ©sultats de lectures de compteur d'Ã©quilibrage
demo.addsupermeterreadings.header=Ajouter les rÃ©sultats de lectures de compteur d'Ã©quilibrage
demo.addsupermeterreadings.title=Ãquilibrage de lectures compteur
demo.addsupermeterreadings.title.criteria=CritÃ¨res de l'Ãquilibrage de lectures compteur
demo.addsupermeterreadings.readingtype=Type de lecture de compteur 
demo.addsupermeterreadings.variation=Variation de lecture de compteur 
demo.addsupermeterreadings.variations=Variations de compteur d'Ã©quilibrage
demo.addsupermeterreadings.variations.help=Ãquilibrer les lectures de compteurs de compteurs correspondra au total de ses sous compteurs. Vous pouvez spÃ©cifier les heures de la journÃ©e et un pourcentage qui sera utilisÃ© pour rÃ©duire mÃ¨tre la lecture du compteur d'Ã©quilibrage de sorte qu'il se distingue de ses sous compteurs.
demo.addsupermeterreadings.hour=Heure
demo.addsupermeterreadings.supermeter=Compteur Ãquilibrage 
demo.addsupermeterreadings.super.delete.readings=Supprimer les lectures existant du compteur d' Ã©quilibrage 
demo.addsupermeterreadings.subs.regen.readings=Re-gÃ©nÃ©rer Lectures de sous compteurs
demo.addsupermeterreadings.error.supermeter=Compteur Ãquilibrage est nÃ©cessaire.
demo.addsupermeterreadings.error.type=Type de Lecture de compteur est nÃ©cessaire.
demo.addsupermeterreadings.hour.required=Heure du jour est nÃ©cessaire.
demo.addsupermeterreadings.percentage.required=Pourcentage est nÃ©cessaire.
demo.addsupermeterreadings.variation.duplicate=Heure est dÃ©jÃ  utilisÃ© dans une variante existante.
demo.addsupermeterreadings.invalid.input=EntrÃ©e non valide pour gÃ©nÃ©rer des lectures de compteur du compteur d'Ã©quilibrage.
demo.addsupermeterreadings.invalid.input.submeters=Pas de sous compteurs disponibles pour compteur d'Ã©quilibrage.
demo.addsupermeterreadings.success=Lectures de compteurs d'Ãquilibrage ont Ã©tÃ© ajoutÃ©es avec succÃ¨s.
demo.addsupermeterreadings.error.super.duplicates=Compteur d'Ã©quilibrage a de lecture des compteurs existants pour la pÃ©riode en cours. Modifier la date pÃ©riode ou choisir de supprimer les lectures de compteurs d'Ã©quilibrage existant.
demo.addsupermeterreadings.error.sub.duplicates=Il y a des lectures de compteurs existants pour la pÃ©riode en cours. Modifier la date de pÃ©riode ou sÃ©lectionnez pour rÃ©gÃ©nÃ©rer les lectures de compteurs.

export.error=Erreur: Impossible d'exporter des donnÃ©es avec succÃ¨s.
export.error.nodata=Erreur: Aucune donnÃ©e Ã©tait disponible pour l'exportation.
export.error.nofile=Erreur: Le nom de fichier n'a pas pu Ãªtre dÃ©terminÃ©e.
export.field.meter=Compteur
export.field.metertype=Type de compteur
export.field.date=Date de lecture
export.field.start=Heure de dÃ©but
export.field.end=Heure de fin
export.field.reading=Lecture
export.denied=AccÃ¨s refusÃ©. Vous devez Ãªtre connectÃ© pour accÃ©der Ã  cette fonctionnalitÃ©.
export.denied.group=AccÃ¨s refusÃ©. Vous ne faites pas partie du mÃªme groupe que le compteur.
export.singlemeter.filename.prefix=LecturesCompteurs-Compteur
export.energybalancing.filename.prefix=CompteurÃquilibrag-CompteurSuper

taskschedule.title.add=Ajouter un planification des tÃ¢ches
taskschedule.title.update=Mettre Ã  jour un planning de tÃ¢ches
taskschedule.header=Listes de tÃ¢ches actuelles
taskschedule.title=Horaires des tÃ¢ches
taskschedule.title.single=Horaire des tÃ¢ches
taskschedule.type=Horaire des tÃ¢ches
taskschedule.name=Nom de l'horaire des tÃ¢ches
taskschedule.name.help=Indiquez un nom pour identifier votre horaire de tÃ¢che.
taskschedule.active=Actif
taskschedule.active.help=Que le calendrier des tÃ¢ches est actif et se dÃ©roulera comme prÃ©vu.
taskschedule.schedule=Date/Heure de tÃ¢che
taskschedule.schedule.help=SpÃ©cifiez lorsque le calendrier de tÃ¢che sera exÃ©cutÃ©e en sÃ©lectionnant une option appropriÃ©e et remplir les champs.
taskschedule.status=Ãtat
taskschedule.scheduledtask=Type de tÃ¢che
taskschedule.schedule.daily=Une fois par jour
taskschedule.schedule.weekly=Une fois par semaine
taskschedule.schedule.monthly=Une fois par mois
taskschedule.schedule.repeatedly=RÃ©pÃ©tition par Minutes / Heures
taskschedule.hours=Heures
taskschedule.minutes=Minutes
taskschedule.time=Temps
taskschedule.day=Jour de la semaine
taskschedule.daymonth=Jour du mois
taskschedule.days=Dimanche, lundi, mardi, mercredi, jeudi, vendredi, samedi
taskschedule.error.time=Le temps est un champ obligatoire.
taskschedule.error.day=Jour est nÃ©cessaire.
taskschedule.error.daymonth=Jour est nÃ©cessaire.
taskschedule.users=Utilisateurs de TÃ¢ches
taskschedule.error.no.user=Rechercher un utilisateur d'abord, puis les ajouter en tant qu'utilisateur de la tÃ¢che.
taskschedule.error.taskusers=Au moins un utilisateur de tÃ¢che ou un client est nÃ©cessaire.
taskschedule.error.customer.notselected=Le nom du client ne correspond pas au nom du client sÃ©lectionnÃ©. Tapez un nom de famille Ã  sÃ©lectionnÃ© un client valide. 
taskschedule.user.noemail=L'utilisateur ne possÃ¨de pas d'adresse e-mail valide. Utilisateurs de tÃ¢ches ont besoin d'avoir une adresse e-mail valide pour recevoir des tÃ¢ches planifiÃ©es emails.
taskschedule.meter.single=Compteur Simple
taskschedule.meter.super=Compteur Super
taskschedule.meter.readingtype=Type de lecture compteur
taskschedule.timeperiods=PÃ©riode de temps
taskschedule.class.error.singlemeter.none=Compteur Simple est nÃ©cessaire.
taskschedule.class.error.singlemeter.select=Commencer en tapant un numÃ©ro de compteur et sÃ©lectionnez un compteur de la liste.
taskschedule.class.error.type=Type de Lecture de compteur est nÃ©cessaire.
taskschedule.class.error.time=PÃ©riode de temps est nÃ©cessaire.
taskschedule.class.error.supermeter=Compteur Super est nÃ©cessaire.
taskschedule.class.error.hours=Heures doit Ãªtre un numÃ©ro positif.
taskschedule.taskclass.previous.day=Jour prÃ©cÃ©dent
taskschedule.taskclass.previous.week=Semaine prÃ©cÃ©dente
taskschedule.taskclass.previous.month=Mois prÃ©cÃ©dent
taskschedule.error.taskschedule.save=Erreur lors de l'enregistrement de la Planification de tÃ¢che.
taskschedule.error.taskschedule.none=Pas de Planification de tÃ¢che spÃ©cifiÃ©.
taskschedule.error.scheduledtask.save=Erreur lors de l'enregistrement de la tÃ¢che planifiÃ©e.
taskschedule.error.scheduledtaskuser.delete=Erreur lors de la suppression de l'utilisateur pour la tÃ¢che planifiÃ©e.
taskschedule.error.scheduledtaskuser.save=Erreur lors de l'enregistrement de l'utilisateur pour la tÃ¢che planifiÃ©e.
taskschedule.error.scheduling=Erreur lors de la planification de la planification des tÃ¢ches.
taskschedule.error.descheduling=Erreur lors du dÃ©sÃ©lection de plannification des tÃ¢ches existante
taskschedule.every=Chaque
taskschedule.of=NumÃ©ro de
taskschedule.repeatedly.error.number=Un chiffre positif est nÃ©cessaire.
taskschedule.repeatedly.error.units=SÃ©lectionnez un type d'intervalle.
taskschedule.all.supermeters=Tous les compteurs Super

scheduledtask.title=TÃ¢ches planifiÃ©es
scheduledtask.header=Actuel TÃ¢ches planifiÃ©es
scheduledtask.type=TÃ¢che planifiÃ©e
scheduledtask.field.name=Nom de la tÃ¢che planifiÃ©e
scheduledtask.field.name.help=Indiquez un nom pour identifier votre horaire de travail.
scheduledtask.field.class=Type de tÃ¢che
scheduledtask.field.class.help=SÃ©lectionnez le type de tÃ¢che Ã  exÃ©cuter sur la date et l'heure prÃ©vue.
scheduledtask.title.add=Ajouter une tÃ¢che planifiÃ©e
scheduledtask.title.update=Mettre Ã  jour une tÃ¢che planifiÃ©e
scheduledtask.previous.hours=Heures prÃ©cÃ©dentes
scheduledtask.customer.name=Client
scheduledtask.customer.name.help=Tapez le nom d'un client et sÃ©lectionnez le client correspondant qui devrait Ãªtre liÃ©e Ã  cette tÃ¢che planifiÃ©e.
scheduledtask.error.delete.none.selected=TÃ¢che Non programmÃ© est sÃ©lectionnÃ©.
scheduledtask.delete.confirm=Etes-vous sÃ»r de vouloir supprimer la tÃ¢che planifiÃ©e?
scheduledtask.error.delete.none=Aucune tÃ¢che planifiÃ©e est spÃ©cifiÃ©e.
scheduledtask.error.delete=Impossible de supprimer la tÃ¢che planifiÃ©e.
scheduledtask.deleted=La tÃ¢che planifiÃ©e a Ã©tÃ© supprimÃ©e avec succÃ¨s.

scheduledtask.email.subject.taskschedule=Gestion Des Compteurs - planification des tÃ¢ches:
scheduledtask.email.subject.scheduledtask= TÃ¢che: 
scheduledtask.email.message.taskschedule=Planification de tÃ¢che: 
scheduledtask.email.message.scheduledtask=TÃ¢che: 
scheduledtask.email.message.meter=Compteur: 
scheduledtask.email.message.start=Date DÃ©but: 
scheduledtask.email.message.end=Date de fin: 
scheduledtask.email.message=Veuillez trouver ci-joint la sortie de votre tÃ¢che.
scheduledtask.email.message.supermeter=Compteur Super: 

password.change.header=Changer mot de passe
password.old=Mot de passe actuel
password.new=Nouveau mot de passe
password.confirm=Confirmer le nouveau mot de passe
password.old.required=Mot de passe actuel est nÃ©cessaire.
password.new.required=Nouveau mot de passe est nÃ©cessaire.
password.confirm.required=Confirmer un nouveau mot de passe est nÃ©cessaire.
password.new.nonmatching=Les mots de passe nouveaux et confirmÃ©s ne correspondent pas.
password.changed=Mot de passe a Ã©tÃ© changÃ© avec succÃ¨s.
password.expiry=Votre mot de passe expire dans in {0} jour(s).
password.ldap=Votre utilisateur est authentifiÃ© via LDAP donc incapable de changer le mot de passe ici.
password.login.expired=Vous devez changer votre mot de passe, car il a expirÃ©.
password.login.reset=Vous devez changer votre mot de passe, car il a Ã©tÃ© rÃ©initialisÃ©.
password.user.change=Vous devez changer votre mot de passe afin de pouvoir utiliser l'application.
password.locked=Votre compte a Ã©tÃ© bloquÃ©.
password.validate.current=Mot de passe actuel incorrect.
password.validation.minUppercase=Mot de passe doit avoir au moins {0} CaractÃ¨re majuscule(s).
password.validation.minDigits=Mot de passe doit avoir au moins {0} chiffres.
password.validation.minLength=Mot de passe doit avoir une longueur minimum de {0} chiffres.
password.validation.maxLength=Mot de passe doit avoir une longueur maximum de {0} chiffres.
password.validation.numHistory=Vous ne pouvez pas utiliser un mot de passe qui correspond Ã  l'un des derniers {0} ceux.
password.required=Vous ne pouvez pas utiliser un mot de passe qui correspond Ã  l'un des derniers
username.required=Nom d'utilisateur est nÃ©cessaire.
logged.in=Vous avez Ã©tÃ© connectÃ© avec succÃ¨s.
login.session.timedout=Votre session a expirÃ©. S'il vous plaÃ®t vous connecter Ã  nouveau pour continuer Ã  utiliser le site.

# Dashboard
dashboard.title=Tableau de bord 
dashboard.meter.count.title=Nombre de compteurs
dashboard.meter.count.description=Le nombre actuel de compteurs dans le systÃ¨me, selon le modÃ¨le de compteur.
dashboard.meter.model.name=ModÃ¨le de compteur
dashboard.meter.count=#

## Custom fields
user.custom.fields.title=Les champs personnalisÃ©s
user.custom.fields.error.get=Erreur de base de donnÃ©es pour obtenir des paramÃ¨tres d'application de champ personnalisÃ©s pour {0}! Contactez le Support.
user.custom.fields.error.unknown.setting=ParamÃ¨tre d'application de champ personnalisÃ© {0} pas pris en charge. Informer le Support.
user.custom.field.status.optional=OPTIONNEL
user.custom.field.status.required=REQUIS
user.custom.field.status.unavailable=NON DISPONIBLE

#Import Data
import.data.header=Importer les donnÃ©es
import.meters.title=Importer les compteurs
import.data.metermodel.help=Si un modÃ¨le de compteur n'est pas spÃ©cifiÃ© dans le fichier en cours d'importation, le modÃ¨le sÃ©lectionnÃ© dans la liste dÃ©roulante sera utilisÃ© Ã  la place
import.data.metermodel=ModÃ¨le de compteur par dÃ©faut
import.data.file.help=SÃ©lectionnez un fichier contenant des informations de compteur dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me  
import.data.file=Fichier des compteurs sÃ©lectionnÃ©s
import.data.button=Importer les compteurs 
import.meters.heading=Importer compteurs au magasin dispositif
import.meters.description=SÃ©lectionnez le fichier CSV contenant des informations de compteur pour l'importation dans le systÃ¨me de gestion de compteur. <br/> SÃ©lectionnez un modÃ¨le de compteur par dÃ©faut dans la liste dÃ©roulante. Il sera utilisÃ© quand un modÃ¨le de compteur est pas spÃ©cifiÃ© dans le fichier importÃ©

timezone.change=Changer le fuseau horaire
timezone.header=Mettre en place l'actuel fuseau horaire
timezone.label=Fuseau horaire

############################################################
#### IpayXml Messages for account balance notifications ####
############################################################
# The defaultAccountAdjustmentProcessor.notification messages have the following arguments:
# arg0=customer.title
# arg1=customer.initials
# arg2=customer.firstnames
# arg3=customer.surname
# arg4=meter number
# arg5=customer agreement ref
# arg6=usage point name
# arg7=account balance
# arg8=low balance threshold
# arg9=emergency credit threshold
# arg10=disconnect threshold
# arg11=reconnect threshold
# currency format example for account balance: {7,number,currency}

defaultAccountAdjustmentProcessor.notification.disconnect.email.subject=Le solde du compte s''est Ã©coulÃ© pour {6}
defaultAccountAdjustmentProcessor.notification.disconnect.email.message=ChÃ¨re Client,\n\nVotre compteur sera dÃ©connectÃ©.\n\nL''Ã©tat de votre compte est: \n Solde du Compte: {7,number,currency}\n Seuil de notification du bas niveau de bilan: {8,number,currency}\n\nCordialement,\nÃquipe de soutien
defaultAccountAdjustmentProcessor.notification.disconnect.sms.message=Solde pour {6} s''est Ã©puisÃ© et sera dÃ©connectÃ©. Le solde est {7,number,currency} ââCordialement, Ã©quipe de soutien

defaultAccountAdjustmentProcessor.notification.lowBalance.email.subject=Solde du compte faible pour {6}
defaultAccountAdjustmentProcessor.notification.lowBalance.email.message=ChÃ¨re Client,\n\nL''Ã©tat de votre compte est: \n Solde du Compte: {7,number,currency}\n Seuil de notification du bas niveau de bilan: {8,number,currency}\n\nCordialement,\nÃquipe de soutien
defaultAccountAdjustmentProcessor.notification.lowBalance.sms.message=Solde pour {6} est faible. Solde est {7,number,currency} Cordialement, Ãquipe de soutien

#database actions
db.action.update=Mettre Ã  jour
db.action.insert=InsÃ©rer
db.action.delete=Effacer

#MDC controltypes
controltype.connect=CONNECTÃ
controltype.disconnect=DÃCONNECTÃ
controltype.disconnect.enable=DÃCONNECTÃ_ACTIVER
controltype.pan.display=PAN_AFFICHAGE
controltype.sync.balance=SYNC_SOLDE
controltype.adjust.balance=AJUSTER_SOLDE
controltype.power.limit=LIMITE DE PUISSANCE
