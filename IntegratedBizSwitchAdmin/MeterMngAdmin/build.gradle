buildscript {
    repositories {
        maven {
            url 'https://plugins.gradle.org/m2/'
        }
        maven {
            allowInsecureProtocol true
            name 'local'
            url "${System.env.HOME}/.ipayM2/repository"
        }
        maven {
            allowInsecureProtocol true
            name 'repo'
            url "${repoBaseUrl}/repo"
            credentials {
                username repoUsername
                password repoPassword
            }
        }
    }
    dependencies {
        classpath 'org.wisepersist:gwt-gradle-plugin:1.1.16'
    }
}

plugins {
  id "com.bmuschko.cargo" version "2.2.3"
}
// GWT compile workaround, because jars include sources, see: https://discuss.gradle.org/t/compilation-fails-when-i-use-guavas-optional-class-duplicate-class-com-google-common-collect-abstractiterator/7370/8
tasks.withType(JavaCompile) {
    options.compilerArgs.sourcePath << '-sourcepath nonExistingDir'
}

apply plugin: 'java'
apply plugin: 'war'
apply plugin: 'gwt'
apply plugin: 'distribution'
apply plugin: 'checkstyle'

// Add developer specific gradle config. This should only be things that do not affect the main build like IDE settings and should not be in the git repo.
if(file('dev.gradle').exists()) {
    apply from: 'dev.gradle'
}

sourceCompatibility = 8
targetCompatibility = 8
compileJava.options.fork = true
compileJava.options.forkOptions.executable = '/opt/jdk1.8/bin/javac'

webAppDirName = 'war'

war {
    archiveFileName = 'MeterMngAdmin.war'
    manifest {
        attributes("Specification-Title": project.name,
                   "Specification-Vendor": "iPay",
                   "Specification-Version": project.version,
                   "Implementation-Title": project.name,
                   "Implementation-Vendor": "iPay",
                   "Implementation-Version": project.version)
    }
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}

configurations {
    all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

    // don't need these from external dependencies
    all*.exclude module: "aspectjrt" // only require weaver currently
    all*.exclude group: "com.sun.jdmk"
    all*.exclude group: "com.sun.jmx"
    all*.exclude module: "jms"

    // don't need these from ipay libs like AccessControlCommon, iPayUtils, JDBCConnectionPool
    all*.exclude group: "org.acegisecurity"
    all*.exclude module: "mybatis-generator-core"
}

sourceSets {
    main {
        java {
            srcDir 'src'
        }
        resources {
            srcDir 'src'
        }
    }
    test {
        java {
            srcDir 'test'
        }
        resources {
            srcDir 'test'
        }
    }
}

dependencies {
    providedCompile group: "javax.servlet", name: "servlet-api", version: '2.5'
    providedCompile group: "javax.mail", name: "mail", version: "1.4.1"

    def springVersion = "5.3.5"
    implementation group: "org.springframework", name: "spring-aop", version: springVersion
    implementation group: "org.springframework", name: "spring-beans", version: springVersion
    implementation group: "org.springframework", name: "spring-context", version: springVersion
    implementation group: "org.springframework", name: "spring-context-support", version: springVersion
    implementation group: "org.springframework", name: "spring-core", version: springVersion
    implementation group: "org.springframework", name: "spring-expression", version: springVersion
    implementation group: "org.springframework", name: "spring-jdbc", version: springVersion
    implementation group: "org.springframework", name: "spring-tx", version: springVersion
    implementation group: "org.springframework", name: "spring-web", version: springVersion
    implementation group: "org.springframework", name: "spring-webmvc", version: springVersion

    def springSecurityVersion = "5.3.8.RELEASE"
    implementation group: "org.springframework.security", name: "spring-security-config", version: springSecurityVersion
    implementation group: "org.springframework.security", name: "spring-security-core", version: springSecurityVersion
    implementation group: "org.springframework.security", name: "spring-security-ldap", version: springSecurityVersion
    implementation group: "org.springframework.security", name: "spring-security-taglibs", version: springSecurityVersion
    implementation group: "org.springframework.security", name: "spring-security-web", version: springSecurityVersion

    implementation group: "org.aspectj", name: "aspectjweaver", version: "1.7.2"
    implementation group: "org.mybatis", name: "mybatis", version: "3.5.6"
    implementation group: "org.mybatis", name: "mybatis-spring", version: "2.0.6"

    implementation group: "javax.validation", name: "validation-api", version: "1.0.0.GA"
    implementation group: "org.hibernate", name: "hibernate-validator", version: "4.1.0.Final"
    providedCompile group: "org.hibernate", name: "hibernate-validator", version: "4.1.0.Final", classifier: "sources", transitive: false

    implementation group: "org.slf4j", name: "slf4j-log4j12", version: "1.7.7"
    implementation group: "log4j", name: "log4j", version: "1.2.9"

    implementation group: "javax.servlet", name: "jstl", version: "1.1.2"
    implementation group: "taglibs", name: "standard", version: "1.1.2"

    implementation group: "org.quartz-scheduler", name: "quartz", version: "2.2.1", transitive: false
    implementation group: "org.apache.commons", name: "commons-email", version: "1.2", transitive: false
    implementation group: "za.co.ipay", name: "iPayUtils", version: "2.13"
    implementation group: "za.co.ipay", name: "JDBCConnectionPool", version: "2.1.6"
    implementation group: "za.co.ipay", name: "AccessControlCommon", version: "2.19"
    implementation group: "za.co.ipay", name: "iPayXml", version: "1.274"
    implementation group: "za.co.ipay", name: "GWTCommon", version: "1.102"
    implementation group: "za.co.ipay", name: "MeterMngCommon", version: "2.183"
    implementation (group: "com.github.branflake2267", name: "gwt-maps-api", version: "3.10.0-alpha-7") {
        exclude module: "gwt-user"
        exclude module: "gwt-dev"
    }
    implementation group: "org.moxieapps.gwt", name: "highcharts", version: "1.5.0"
    implementation group: "commons-fileupload", name: "commons-fileupload", version: "1.3.1"
    implementation group: "com.googlecode.gwtupload", name: "gwtupload-project", version: "1.0.3"
    implementation group: 'org.apache.pdfbox', name: 'pdfbox', version: '2.0.12'
    implementation group: 'com.github.dhorions',name:'boxable',version:'1.5'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-core', version: '2.8.2'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.8.2'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-annotations', version: '2.8.2'

	// Including not-really-gwt dependency on jackson 1 because of annotations like @JsonRawValue being used in GWT
    // classes in the shared package like: TariffPsImportRecord. Jackson purely used server side.
    gwt group: "org.codehaus.jackson", name: "jackson-core-asl", version: "1.9.13"
    gwt group: "org.codehaus.jackson", name: "jackson-mapper-asl", version: "1.9.13"


    gwt group: "org.hibernate", name: "hibernate-validator", version: "4.1.0.Final", classifier: "sources"
    gwt group: "javax.validation", name: "validation-api", version: "1.0.0.GA"
    gwt group: "org.slf4j", name: "slf4j-log4j12", version: "1.7.7"
}

gwt {
    gwtVersion = "2.7.0"
    minHeapSize = '512M'
    maxHeapSize = '1024M'
    // overriding the default which is war, since otherwise it overwrites current war directory which is our base dir
    // if warTemplate is enabled
    devWar = file('warTemplate')
    modules 'za.co.ipay.metermng.MeterMngAdmin'
    compiler.localWorkers = 8
}

// This task from gwt plugin creates a dev mode template we don't want
warTemplate.onlyIf {
 false
}

distributions {
    main {
        contents {
            from(war)
            into('tomcatLib') {
                from(configurations.providedCompile)
                exclude '**/*servlet*'
            }
            into('templates') {
                from { 'src/templates' }
            }
        }
    }
}

task copyMessagesEn(type: Copy) {
    from sourceSets.main.java.srcDirs
    include '**/messages.properties*'
    rename { String fileName ->
        println fileName
        fileName.replace('.properties', '_en.properties')
    }
    into processResources.destinationDir
}

copyMessagesEn.dependsOn processResources
classes.dependsOn copyMessagesEn

compileGwt.dependsOn copyMessagesEn

task dist(dependsOn: [cleanInstallDist, installDist, distZip]) {
    // depending on cleanInstallDist because of restriction in distribution plugin which has been fixed and needs future update
    // https://github.com/gradle/gradle/commit/67e6c2214713cbe62683e83241c0c34f63b4be40
}

checkstyle {
    toolVersion = '6.7'
}

checkstyleMain {
    configFile = "${project.projectDir}/config/checkstyle/checkstyle.xml" as File
    source = fileTree('src') {
        includes = ['**/messages*.properties']
    }
}

checkstyleTest {
    configFile = "${project.projectDir}/config/checkstyle/checkstyle.xml" as File
    source = fileTree('src') {
        includes = ['**/messages*.properties']
    }
}

repositories {
    maven {
        allowInsecureProtocol true
        name 'local'
        url "${System.env.HOME}/.ipayM2/repository"
    }
    maven {
        allowInsecureProtocol true
        name 'repo'
        url "${repoBaseUrl}/repo"
        credentials {
            username repoUsername
            password repoPassword
        }
    }
}
