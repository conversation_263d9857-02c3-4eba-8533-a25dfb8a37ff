<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView" />
        <property name="prefix" value="/WEB-INF/jsp/" />
        <property name="suffix" value=".jsp" />
    </bean>

    <!-- *** Auto wiring controllers ******* -->
    <context:component-scan base-package="za.co.ipay.metermng.server.importfile, za.co.ipay.metermng.server.bulkupload" />

    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" />

    <bean class="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping">
        <property name="mappings">
            <value>
                /**/login.do=loginController
                /**/forgottenPassword.do=forgottenPasswordController
                /**/sessionAuth.do=sessionAuthController
                /**/secure/accessControlService.do=accessControlServiceRpc
                /**/secure/app.do=appServiceRpc
                /**/secure/search.do=searchServiceRpc
                /**/secure/lookups.do=lookupServiceRpc
                /**/secure/usagepoint.do=usagePointServiceRpc
                /**/secure/customer.do=customerServiceRpc
                /**/secure/meter.do=meterServiceRpc

                /**/secure/keyindicator.do=keyIndicatorServiceRpc
                /**/secure/usagepointgroups.do=usagePointGroupsServiceRpc
                /**/secure/salesperresource.do=salesPerResourceServiceRpc
                /**/secure/dashboard.do=dashBoardServiceRpc

                /**/secure/pricingStructure.do=pricingStructureServiceRpc
                /**/secure/tokengen.do=tokenGenerationServiceRpc
                /**/secure/auxChargeSchedule.do=auxChargeScheduleServiceRpc
                /**/secure/auxTypes.do=auxTypeServiceRpc
                /**/secure/auxaccnt.do=auxAccountServiceRpc
                /**/secure/supplyGroup.do=supplyGroupServiceRpc
                /**/secure/group.do=groupServiceRpc
                /**/secure/deviceStores.do=deviceStoreServiceRpc
                /**/secure/user.do=userServiceRpc
                /**/secure/calendar.do=calendarServiceRpc
                /**/secure/manufacturer.do=manufacturerServiceRpc
                /**/secure/mdc.do=mdcServiceRpc
                /**/secure/metermodel.do=meterModelServiceRpc
                /**/secure/schedule.do=scheduleServiceRpc
                /**/secure/location.do=locationServiceRpc
                /**/secure/appSetting.do=appSettingServiceRpc
                /**/secure/customertransbulkupload.do=customerTransBulkUploadController
                /**/secure/auxtransbulkupload.do=auxTransBulkUploadController
                /**/secure/ndp.do=ndpServiceRpc
                /**/secure/billingDet.do=billingDetServiceRpc
                /**/secure/mdcChannel.do=mdcChannelServiceRpc
                /**/secure/meterbulkupload.do=meterBulkUploadController
                /**/secure/auxaccountbulkupload.do=auxAccountBulkUploadController
                /**/secure/specialActions.do=specialActionsServiceRpc
                /**/secure/notify.do=notificationServiceRpc
                /**/secure/blockingtype.do=blockingTypeServiceRpc
                /**/secure/importfiledata.do=importFileServiceRpc
                /**/secure/importfileservlet.do=importFileController
                /**/secure/userInterface.do=userInterfaceServiceRpc
                /**/secure/ping.do=pingServiceRpc
            </value>
        </property>
    </bean>

    <bean name="loginController" class="org.springframework.web.servlet.mvc.ParameterizableViewController">
        <!--    <property name="supportedMethods" value="GET,POST,PUT,DELETE" />-->
        <property name="viewName" value="login" />
    </bean>

    <bean id="forgottenPasswordController" class="za.co.ipay.accesscontrol.web.controller.ForgottenPasswordController">
        <property name="adminUserService" ref="acCommonAdminUserService" />
        <property name="appSettingsService" ref="acCommonAppSettingsService" />
        <property name="emailService" ref="acCommonEmailService" />
        <property name="localeResolver" ref="localeResolver" />
        <property name="messageSource" ref="messageSource" />
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="1048576" />
        <property name="maxInMemorySize" value="1048576" />
    </bean>

    <bean name="searchServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="searchRpc" />
    </bean>

    <bean name="lookupServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="lookupRpc" />
    </bean>

    <bean name="usagePointServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="usagePointRpc" />
    </bean>

    <bean name="customerServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="customerRpc" />
    </bean>

    <bean name="meterServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="meterRpc" />
    </bean>

    <bean name="keyIndicatorServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="keyIndicatorRpc" />
    </bean>

    <bean name="usagePointGroupsServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="usagePointGroupsRpc" />
    </bean>

    <bean name="salesPerResourceServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="salesPerResourceRpc" />
    </bean>

    <bean name="dashBoardServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="dashBoardRpc" />
    </bean>

    <bean name="pricingStructureServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="pricingStructureRpc" />
    </bean>

    <bean name="tokenGenerationServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="tokenGenerationRpc" />
    </bean>

    <bean name="auxChargeScheduleServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="auxChargeScheduleRpc" />
    </bean>

    <bean name="auxTypeServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="auxTypeRpc" />
    </bean>

    <bean name="calendarServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="calendarRpc" />
    </bean>

    <bean name="supplyGroupServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="supplyGroupRpc" />
    </bean>

    <bean id="appRpc" class="za.co.ipay.metermng.server.rpc.MeterMngAppRpcImpl">
        <property name="defaultLocaleName" ref="defaultLocaleName" />
        <property name="useDefaultLocaleOnly" ref="useDefaultLocaleOnly" />
        <property name="messageSource" ref="messageSource" />
        <property name="formatSource" ref="formatSource" />
        <property name="meterMngConfig" ref="meterMngConfig" />
        <property name="timezoneSource" ref="timezoneSource" />
        <property name="appSettingService" ref="appSettingService" />
    </bean>

    <bean name="appServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="appRpc" />
    </bean>

    <bean name="auxAccountServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="auxAccountRpc" />
    </bean>

    <bean name="groupServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="groupRpc" />
    </bean>

    <bean name="userServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="userRpc" />
    </bean>

    <bean name="accessControlServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService">
            <bean class="za.co.ipay.accesscontrol.gwt.server.GWTAccessControlServiceImpl" />
        </property>
    </bean>

    <bean name="deviceStoreServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="deviceStoreRpc" />
    </bean>

    <bean name="manufacturerServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="manufacturerRpc" />
    </bean>

    <bean name="mdcServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="mdcRpc" />
    </bean>

    <bean name="mdcChannelServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="mdcChannelRpc" />
    </bean>

    <bean name="meterModelServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="meterModelRpc" />
    </bean>

    <bean name="scheduleServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="scheduleRpc" />
    </bean>

    <bean name="locationServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="locationRpc" />
    </bean>

    <bean name="appSettingServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="appSettingRpc" />
    </bean>

    <bean name="ndpServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="ndpRpc" />
    </bean>

    <bean name="billingDetServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="billingDetRpc" />
    </bean>

    <bean name="specialActionsServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="specialActionsRpc" />
    </bean>

    <bean name="notificationServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="notificationRpc" />
    </bean>

    <bean name="blockingTypeServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="blockingTypeRpc" />
    </bean>

    <bean name="importFileServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="importFileDataRpc" />
    </bean>
    
    <bean id="pingRpc" class="za.co.ipay.gwt.common.server.rpc.PingRpcImpl">
    </bean>

    <bean name="pingServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="pingRpc" />
    </bean>
    

    <bean id="defaultLocaleName" class="org.springframework.web.context.support.ServletContextParameterFactoryBean">
        <property name="initParamName" value="defaultLocale" />
    </bean>

    <bean id="useDefaultLocaleOnly" class="za.co.ipay.utils.web.ServletContextParameterFactoryBeanWithDefault">
        <property name="initParamName" value="useDefaultLocaleOnly" />
        <property name="defaultValue" value="true" />
    </bean>

    <bean id="localeResolver" class="za.co.ipay.utils.web.DefaultAcceptHeaderLocaleResolver">
        <constructor-arg ref="defaultLocaleName" />
        <constructor-arg ref="useDefaultLocaleOnly" />
    </bean>

    <bean id="SpringBeanFactory" class="za.co.ipay.metermng.spring.SpringBeanFactory" />

    <bean name="userInterfaceServiceRpc" class="za.co.ipay.gwt.common.server.GwtController">
        <property name="remoteService" ref="userInterfaceRpc" />
    </bean>

    <!-- Configured like this because ${} placeholders don't resolve in subcontexts -->
    <bean id="enableAccessGroups" class="za.co.ipay.utils.web.ServletContextParameterFactoryBeanWithDefault">
        <property name="initParamName" value="enableAccessGroups"/>
        <property name="defaultValue" value="false"/>
    </bean>

    <bean id="sessionAuthController" class="za.co.ipay.accesscontrol.web.controller.sessionauth.SessionAuthorizationController">
        <property name="accessControlService" ref="accessControlService"/>
        <property name="successUrl" value="/MeterMngAdmin.jsp"/>
        <property name="enableAccessGroups" ref="enableAccessGroups"/>
        <property name="applicationPerm" value="mm_meter_mng_user"/>      
    </bean>

</beans>
