/** ---------------------------------------------------------------------------------------------------------------- **/
/** ------------------    GWT Clean theme CSS   -------------------------------------------------------------------- **/

body, table td, select, button {
  font-family: Arial Unicode MS, Arial, sans-serif;
  font-size: small;
} 

a, a:visited {
  color: #0066cc;
  text-decoration:none;
}

a:hover {
  color: #0066cc;
  text-decoration:underline;
}

select {
  background: white;
}

.gwt-SuggestBoxPopup {
}

.gwt-SuggestBoxPopup .item {
  padding: 2px 6px;
  color: #000;
  cursor: default;
  font-size: 110%;
}
.gwt-SuggestBoxPopup .item-selected {
  background: #D5E2FF;
}
.gwt-SuggestBoxPopup .suggestPopupContent {
  background: white;
  max-height: 400px;
  overflow-y: scroll;
}
.gwt-SuggestBoxPopup .suggestPopupTopCenter {
  border-top: 1px solid #bbb;
}
.gwt-SuggestBoxPopup .suggestPopupBottomCenter {
  border-bottom: 1px solid #bbb;
}
.gwt-SuggestBoxPopup .suggestPopupTopCenterInner,
.gwt-SuggestBoxPopup .suggestPopupBottomCenterInner {
  height: 1px;
  line-height: 1px;
}
.gwt-SuggestBoxPopup .suggestPopupMiddleLeft {
  border-left: 1px solid #bbb;
}
.gwt-SuggestBoxPopup .suggestPopupMiddleRight {
  border-right: 1px solid #bbb;
}
.gwt-SuggestBoxPopup .suggestPopupMiddleLeftInner,
.gwt-SuggestBoxPopup .suggestPopupMiddleRightInner {
  width: 1px;
  line-height: 1px;
}
.gwt-SuggestBoxPopup .suggestPopupTopLeftInner {
  width: 0px;
  height: 0px;
  zoom: 1;  
}
.gwt-SuggestBoxPopup .suggestPopupTopRightInner {
  width: 0px;
  height: 0px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupBottomLeftInner {
  width: 0px;
  height: 0px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupBottomRightInner {
  width: 0px;
  height: 0px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupTopLeft {
  background: url(images/circles.png) no-repeat 0px -6px;
  -background: url(images/circles_ie6.png) no-repeat 0px -6px;
  width:5px;
  height:5px;
}
.gwt-SuggestBoxPopup .suggestPopupTopRight {
  background: url(images/circles.png) no-repeat -5px -6px;
  -background: url(images/circles_ie6.png) no-repeat -5px -6px;
  width:5px;
  height:5px;
}
.gwt-SuggestBoxPopup .suggestPopupBottomLeft {
  background: url(images/circles.png) no-repeat 0px -11px;
  -background: url(images/circles_ie6.png) no-repeat 0px -11px;
  width:5px;
  height:5px;
}
.gwt-SuggestBoxPopup .suggestPopupBottomRight {
  background: url(images/circles.png) no-repeat -5px -11px;
  -background: url(images/circles_ie6.png) no-repeat -5px -11px;
  width:5px;
  height:5px;
}
* html .gwt-SuggestBoxPopup .suggestPopupTopLeftInner {
  width: 0px;
  height: 0px;
  overflow: hidden;
}
* html .gwt-SuggestBoxPopup .suggestPopupTopRightInner {
  width: 0px;
  height: 0px;
  overflow: hidden;
}
* html .gwt-SuggestBoxPopup .suggestPopupBottomLeftInner {
  width: 0px;
  height: 0px;
  overflow: hidden;
}
* html .gwt-SuggestBoxPopup .suggestPopupBottomRightInner {
  width: 0px;
  height: 0px;
  overflow: hidden;
}

.gwt-TabBar {
  background: #ccc;
  padding-top: 6px;
}

.gwt-TabBar .gwt-TabBarFirst {
  width: 5px;  /* first tab distance from the left */
}

.gwt-TabBar .gwt-TabBarItem {
	color: #FFF;
    font-weight: normal;
    text-align: center;
    background: none repeat scroll 0% 0% #8E8E8E;
    border-radius: 3px 3px 0px 0px;
}

.gwt-TabBar .gwt-TabBarItem-selected {
    cursor: default;
    background: none repeat scroll 0% 0% #FFF;
    color: #333;
    font-weight: bold;
}

.gwt-TabBar .gwt-TabBarItem-wrapper {
	padding-left: 5px;
}

.gwt-TabPanelBottom {
    border-color: #CCC;
    border-style: solid;
    border-width: 0px 1px 1px;
    overflow: hidden;
    padding: 6px;
}

.gwt-PopupPanel {
    border: 3px solid #E7E7E7;
    padding: 3px;
    background: none repeat scroll 0% 0% #FFF;
}

.gwt-DecoratorPanel {
}

.gwt-DecoratorPanel .topCenter {
  border-top: 1px solid #bbb;
  line-height: 0px;
}
.gwt-DecoratorPanel .bottomCenter {
  border-bottom: 1px solid #bbb;
  line-height: 0px;
}
.gwt-DecoratorPanel .topCenterInner,
.gwt-DecoratorPanel .bottomCenterInner {
  height: 1px;
  line-height: 0px;
  font-size: 1px;
}
.gwt-DecoratorPanel .middleLeft {
  border-left: 1px solid #bbb;
}
.gwt-DecoratorPanel .middleRight {
  border-right: 1px solid #bbb;
}
.gwt-DecoratorPanel .middleLeftInner,
.gwt-DecoratorPanel .middleRightInner {
  width: 1px;
  line-height: 1px;
}
.gwt-DecoratorPanel .topLeftInner,
.gwt-DecoratorPanel .topRightInner,
.gwt-DecoratorPanel .bottomLeftInner,
.gwt-DecoratorPanel .bottomRightInner {
  width: 5px;
  height: 5px;
  zoom: 1;
  font-size: 1px;
  overflow: hidden;
}
.gwt-DecoratorPanel .topLeft {
  line-height: 0px;
  background: url(images/circles.png) no-repeat 0px -6px;
  -background: url(images/circles_ie6.png) no-repeat 0px -6px;
}
.gwt-DecoratorPanel .topRight {
  line-height: 0px;
  background: url(images/circles.png) no-repeat -5px -6px;
  -background: url(images/circles_ie6.png) no-repeat -5px -6px;
}
.gwt-DecoratorPanel .bottomLeft {
  line-height: 0px;
  background: url(images/circles.png) no-repeat 0px -11px;
  -background: url(images/circles_ie6.png) no-repeat 0px -11px;
}
.gwt-DecoratorPanel .bottomRight {
  line-height: 0px;
  background: url(images/circles.png) no-repeat -5px -11px;
  -background: url(images/circles_ie6.png) no-repeat -5px -11px;
}
* html .gwt-DecoratorPanel .topLeftInner,
* html .gwt-DecoratorPanel .topRightInner,
* html .gwt-DecoratorPanel .bottomLeftInner,
* html .gwt-DecoratorPanel .bottomRightInner {
  width: 5px;
  height: 5px;
  overflow: hidden;
}

.gwt-DecoratorPanel .middleCenter {
   height: 100%;
   width: 100%;
 }

.gwt-DateBox {
  padding: 5px 4px;
  border: 1px solid #ccc;
  border-top: 1px solid #999;
  font-size: 100%;
}
.gwt-DateBox input {
  width: 8em;
}
.dateBoxFormatError {
  background: #ffcccc;
}
.dateBoxPopup {
}

.gwt-DatePicker {
  border: 1px solid #ccc;
  border-top:1px solid #999;
  cursor: default;
}
.gwt-DatePicker td,
.datePickerMonthSelector td:focus {
  outline: none;
}
.datePickerDays {
  width: 100%;
  background: white;
}
.datePickerDay,
.datePickerWeekdayLabel,
.datePickerWeekendLabel {
  font-size: 85%;
  text-align: center;
  padding: 4px;
  outline: none;
  font-weight:bold;
  color:#333;
  border-right: 1px solid #EDEDED;
  border-bottom: 1px solid #EDEDED;
}
.datePickerWeekdayLabel,
.datePickerWeekendLabel {
  background: #fff;
  padding: 0px 4px 2px;
  cursor: default;
  color:#666;
  font-size:70%;
  font-weight:normal;
}
.datePickerDay {
  padding: 4px 7px;
  cursor: hand;
  cursor: pointer;
}
.datePickerDayIsWeekend {
  background: #f7f7f7;
}
.datePickerDayIsFiller {
  color: #999;
  font-weight:normal;
}
.datePickerDayIsValue {
  background: #d7dfe8;
}
.datePickerDayIsDisabled {
  color: #AAAAAA;
  font-style: italic;
}
.datePickerDayIsHighlighted {
  background: #F0E68C;
}
.datePickerDayIsValueAndHighlighted {
  background: #d7dfe8;
}
.datePickerDayIsToday {
  padding: 3px;
  color: #fff;
  background: url(images/hborder.png) repeat-x 0px -2607px;
}

.datePickerMonthSelector {
  width: 100%;
  padding: 1px 0 5px 0;
  background: #fff;
}
td.datePickerMonth {
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  font-size: 100%;
  font-weight: bold;
  color: #333;
}
.datePickerPreviousButton,
.datePickerNextButton {
  font-size: 120%;
  line-height: 1em;
  color: #3a6aad;
  cursor: hand;
  cursor: pointer;
  font-weight: bold;
  padding: 0px 4px;
  outline: none;
}

.gwt-MenuBar .gwt-MenuItem-selected {
  background: #E3E8F3;
}

.gwt-DialogBox .Caption {
  background: #F1F1F1;
  padding: 4px 8px 4px 4px;
  cursor: default;
  font-family: Arial Unicode MS, Arial, sans-serif;
  font-weight: bold;
  border-bottom: 1px solid #bbbbbb;
  border-top: 1px solid #D2D2D2;
}
.gwt-DialogBox .dialogContent {
}
.gwt-DialogBox .dialogMiddleCenter {
  padding: 3px;
  background: white;
}
.gwt-DialogBox .dialogBottomCenter {
  background: url(images/hborder.png) repeat-x 0px -2945px;
  -background: url(images/hborder_ie6.png) repeat-x 0px -2144px;
}
.gwt-DialogBox .dialogMiddleLeft {
  background: url(images/vborder.png) repeat-y -31px 0px;
}
.gwt-DialogBox .dialogMiddleRight {
  background: url(images/vborder.png) repeat-y -32px 0px;
  -background: url(images/vborder_ie6.png) repeat-y -32px 0px;
}
.gwt-DialogBox .dialogTopLeftInner {
  width: 10px;
  height: 8px;
  zoom: 1;
}
.gwt-DialogBox .dialogTopRightInner {
  width: 12px;
  zoom: 1;
}
.gwt-DialogBox .dialogBottomLeftInner {
  width: 10px;
  height: 12px;
  zoom: 1;
}
.gwt-DialogBox .dialogBottomRightInner {
  width: 12px;
  height: 12px;
  zoom: 1;
}
.gwt-DialogBox .dialogTopLeft {
  background: url(images/circles.png) no-repeat -20px 0px;
  -background: url(images/circles_ie6.png) no-repeat -20px 0px;
}
.gwt-DialogBox .dialogTopRight {
  background: url(images/circles.png) no-repeat -28px 0px;
  -background: url(images/circles_ie6.png) no-repeat -28px 0px;
}
.gwt-DialogBox .dialogBottomLeft {
  background: url(images/circles.png) no-repeat 0px -36px;
  -background: url(images/circles_ie6.png) no-repeat 0px -36px;
}
.gwt-DialogBox .dialogBottomRight {
  background: url(images/circles.png) no-repeat -8px -36px;
  -background: url(images/circles_ie6.png) no-repeat -8px -36px;
}
* html .gwt-DialogBox .dialogTopLeftInner {
  width: 10px;
  overflow: hidden;
}
* html .gwt-DialogBox .dialogTopRightInner {
  width: 12px;
  overflow: hidden;
}
* html .gwt-DialogBox .dialogBottomLeftInner {
  width: 10px;
  height: 12px;
  overflow: hidden;
}
* html .gwt-DialogBox .dialogBottomRightInner {
  width: 12px;
  height: 12px;
  overflow: hidden;
}


/** ------------------    END GWT Clean theme CSS   ---------------------------------------------------------------- **/
/** ---------------------------------------------------------------------------------------------------------------- **/

body {
    background: #D1CCC5;
    color: black;
}

button {
    font-family: Arial Unicode MS, Arial, sans-serif;
    font-size: small;
    margin-right: 2px;
}

.border {
    border: 1px red solid;
}

.siteHeading {
    color: #333333;
    font-size: 1.5em;
    font-weight: bold;
    text-align: center;
    margin-top: 10px;
}

.headerLinks {
    margin-top: 0px;
    margin-right: 5px;
}

.sideMenu {
	margin-left: 5px;
}

.panel {
    display: block;
    margin-bottom: 5px;
}

.close-border {
    cursor: pointer;
    font-size: small;
    margin: 0;
    text-decoration: none;
    background-color: #D1CCC5;
}

.close-border-closed {
    cursor: pointer;
}

.close-border-closed:hover {
    margin-top: 2.1em;
}

.close-border-closed:focus,.close-border:focus {
    outline: none;
    background: none;
}

.aboutLink {
    position: absolute;
    bottom: 10px;
    left: 10px;
    cursor: pointer;
    color: black;
}

.aboutLink:hover {
    text-decoration: underline;
    color: black;
}

.gwt-TabLayoutPanel{
     float:left;
}
.gwt-TabLayoutPanel-sidepanel {
    
}

.gwt-TabLayoutPanel-sidepanel .gwt-TabLayoutPanelTabs {
    background: none repeat scroll 0 0 white;
    padding-left: 5px;
    padding-top: 6px;
}

.gwt-TabLayoutPanel-sidepanel .gwt-TabLayoutPanelContentContainer {
    border: thick solid lightgray;
}

.gwt-TabLayoutPanel-sidepanel .gwt-TabLayoutPanelContent {
    overflow: hidden;
    padding: 6px;
}

.gwt-TabLayoutPanel-sidepanel .gwt-TabLayoutPanelTab {
    border-radius: 3px 3px 0 0;
    color: white;
    cursor: pointer;
    font-weight: normal;
    margin-left: 4px;
    padding: 4px 8px;
    text-align: center;
    width: 6em;
    border: thin solid white;
}

.gwt-TabLayoutPanel-sidepanel .gwt-TabLayoutPanelTab-selected {
    background: none repeat scroll 0 0 white;
    color: white;
    cursor: default;
    font-weight: bold;
}

.gwt-TabLayoutPanel-mainpanel {
     float:left;
}

.gwt-TabLayoutPanel-mainpanel .gwt-TabLayoutPanelTabs {
    -moz-border-bottom-colors: none;
    -moz-border-image: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    padding-left: 5px;
    padding-top: 6px;
}

.gwt-TabLayoutPanel-mainpanel .gwt-TabLayoutPanelContentContainer {
    border: 2px solid darkgray;
    margin-right: 2px;
    margin-bottom: 2px;
    background: #F7F7F7;
}

.gwt-TabLayoutPanel-mainpanel .gwt-TabLayoutPanelContent {
    overflow: auto;  
}

.gwt-TabLayoutPanel-mainpanel .gwt-TabLayoutPanelTab {
    background: none repeat scroll 0 0 #7E756E;
    border-bottom: none;
    border-radius: 3px 3px 0 0;
    cursor: pointer;
    font-weight: bold;
    margin-left: 4px;
    padding: 4px 8px;
    text-align: center;
    color: #eeeeee;
}

.gwt-TabLayoutPanel-mainpanel .gwt-TabLayoutPanelTab-selected {
    cursor: default;
    font-weight: bold;
    padding: 4px 25px 4px 8px;  
    background: #40525E; /* Old browsers */
    background: -moz-linear-gradient(top, #547C95 0%, #38291e 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#547c95), color-stop(100%,#38291e)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #547c95 0%,#38291e 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #547c95 0%,#38291e 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #547c95 0%,#38291e 100%); /* IE10+ */
    background: linear-gradient(to bottom, #547c95 0%,#38291e 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#547c95', endColorstr='#38291e',GradientType=0 ); /* IE6-8 */ 
    color: #f7f7f7;
    border-top: 1px solid #E60000;
}

.gwt-Image {
    margin-left: 2px;
    margin-right: 1px;
    border: none;
}

.gwt-Image-logo {
    margin-top: 1px;
    margin-bottom: 1px;
}

.gwt-Anchor-logo {
    float: right;
    cursor: pointer;
    color: #5f5f5f; 
    text-decoration: underline;
    margin-right: 2px;
}

.gwt-Anchor-logo:active {
    color: gray; 
}
.gwt-Anchor-logo:visited {
    color: gray; 
}
.gwt-Anchor-logo:link {
    color: #5f5f5f; 
}
.gwt-Anchor-logo:hover {
    color: gray; 
}

a.gwt-Anchor:active {
	color: #000000;
}
a.gwt-Anchor:visited {
    color: #000000;
}
a.gwt-Anchor:link {
    color: #000000;
}
a.gwt-Anchor:hover {
    color: #000000;
}

.subheading {
    padding-top: 2px;
    padding-bottom: 1px;
    border-top: medium double;
    border-bottom: medium double;
    font-size: 1.4em;
    font-style: normal;
    font-weight: bold;
    text-align: center;
}

.error {
    color: Red;
    font-weight: normal;
    text-align: center;
    display: block;
}

.errorStatus {
    color: Red;
    font-weight: normal;
    text-align: center;
}

.successStatus {
    color: Green;
    font-weight: normal;
    text-align: center;
}

.errorInline {
    color: Red;
    font-weight: bold;
    text-align: center;
    display: inline;
    margin-left: 5px;
}

.errorInlineNotBold {
    color: Red;
    font-weight: normal;
    display: inline;
}

.success {
    color: Green;
    font-weight: bold;
    text-align: center;
    display: block;
}

.warn {
    color: Orange;
    font-weight: bold;
    text-align: center;
    display: block;
}

.warnInline {
    color: Orange;
    font-weight: bold;
    text-align: center;
    display: inline;
    margin-left: 5px;
}

.requiredLabel {
    color: Red;
    text-align: center;
    display: inline;
    margin-left: 5px;
}

.requiredForActivationLabel {
    color: Orange;
    text-align: center;
    display: inline;
    margin-left: 5px;
}

.changed {
}

.cellChanged {
    background-color: lightgrey;
}

.disabled {
    color: gray;
}

.gwt-Label-header {
    padding: 2px;
    font-size: 1em;
    font-weight: bold;
}

.gwt-StackLayoutPanel-searchpanels {
    border-color: darkgray;
    border-style: solid;
    border-width: 1px;
    position: absolute !important;
    left: 0px;
    top: 25px !important;
    right: 0px;
    bottom: 0px;
}

.gwt-StackLayoutPanel-searchpanels {
    
} 
.gwt-StackLayoutPanelHeader {
    background: #40525E; /* Old browsers */
    background: -moz-linear-gradient(top, #547C95 0%, #38291e 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#547c95), color-stop(100%,#38291e)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #547c95 0%,#38291e 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #547c95 0%,#38291e 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #547c95 0%,#38291e 100%); /* IE10+ */
    background: linear-gradient(to bottom, #547c95 0%,#38291e 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#547c95', endColorstr='#38291e',GradientType=0 ); /* IE6-8 */ 
    color: #f7f7f7;
}
.gwt-StackLayoutPanel-searchpanels {
    background: #F7F7F7;
} 
.gwt-StackLayoutPanelHeader-hovering {
    background: #D7D7D7; /* Old browsers */
    background: -moz-linear-gradient(top, #f7f7f7 0%, #D3D3D3 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#D3D3D3)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f7f7f7 0%,#D3D3D3 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#D3D3D3',GradientType=0 ); /* IE6-8 */ 
    color: black;
}
.sidePanelStackHeader{
    width: 100%;
    vertical-align: middle;
}

.gwt-Image-closetab {
    position: absolute;
    right: 2px;
    top: 2px;
}

.gwt-Label-closetab {
    float: left;
}

.ipay-tab-header {
    
}

.gwt-StackLayoutPanelContent {
    background: #F7F7F7;
    border-style: double none;
    border-width: 2px 0 0;
    padding: 10px 5px 10px 5px;
    border-top-color: #E60000;
}

.gwt-Label-bold {
    font-weight: bold;
}

.labelBold {
	font-weight:   bold;
	margin-left:   2px;
	margin-right:  5px;
}

.gwt-Button-newmeter {
    bottom: 10px;
    font-weight: bold;
    position: absolute;
    right: 50px;
}

.gwt-Image-search {
    padding-left: 2px;
    cursor: pointer;
    vertical-align: middle;
}

.gwt-Image-search:active {
    border: 2px inset lightgray;
}

.gwt-DisclosurePanel-component {
    padding: 2px;
    margin: 0px;
}

.disclosureContent {
    padding: 0.5em;
}

.gwt-DisclosurePanel-component-open {
    padding: 2px;
    margin: 0px;
}

.gwt-DisclosurePanel-component-closed {
    padding: 2px;
    margin: 0px;
}

.gwt-DisclosurePanel-component .header {
    color: black;
    padding: 0px;
    border: 1px solid black;
    font-weight: bold;
    text-decoration: none;
    background: #D7D7D7; /* Old browsers */
    background: -moz-linear-gradient(top, #f7f7f7 0%, #D3D3D3 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#D3D3D3)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f7f7f7 0%,#D3D3D3 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#D3D3D3',GradientType=0 ); /* IE6-8 */ 
}

.gwt-DisclosurePanel-component .header a {
    color: black;
    font-weight: bold;
    cursor: hand;
}

.gwt-DisclosurePanel-component .header td {
    color: black;
    font-weight: bold;
    cursor: hand;
    padding: 0px;
    margin: 0px;
}

.gwt-DisclosurePanel-component .content {
    border-left: 2px solid #e7e7e7;
    padding: 0px;
    margin-left: 3px;
}

.gwt-DisclosurePanel-information {
    margin: 0px;
    padding: 0px;
}

.gwt-DisclosurePanel-information-open {
    margin: 0px;
    padding: 0px;
}

.gwt-DisclosurePanel-information-closed {
    margin: 0px;
    padding: 0px;
}

.gwt-DisclosurePanel-information .header {
    color: #bfbfbf;
    padding: 0px;
    border: 1px solid #bfbfbf;
    font-weight: bold;
    text-decoration: none;
}

.gwt-DisclosurePanel-information .header a {
    color: black;
    font-weight: bold;
    cursor: hand;
}

.gwt-DisclosurePanel-information .header td {
    color: black;
    font-weight: normal;
    cursor: hand;
    padding: 0px;
    margin: 0px;
}

.gwt-DisclosurePanel-information .content {
    border-left: 3px solid #e7e7e7;
    padding: 0px;
    margin-left: 3px;
}

.gwt-DisclosurePanel-insidecomponent {
    
}

.gwt-DisclosurePanel-insidecomponent-open {
    
}

.gwt-DisclosurePanel-insidecomponent-closed {
    
}

.gwt-DisclosurePanel-insidecomponent .header {
    border: none;
    border-bottom: 0px solid #000;
}

.gwt-DisclosurePanel-insidecomponent .content {
    border-left: 3px solid #E7E7E7;
    padding: 4px 0px 4px 8px;
    margin-left: 6px;
}


.gwt-DisclosurePanel-minimalist {
    
}

.gwt-DisclosurePanel-minimalist-open {
    
}

.gwt-DisclosurePanel-minimalist-closed {
    
} 

.gwt-TextBox {
  padding: 2px 2px 2px 2px;
  border: 1px solid #ccc;
  border-top: 1px solid #999;
  font-size: 1em;
  font-family: Arial Unicode MS, Arial, sans-serif;
  color: #38291E;
  margin-left: 0px;
}

.gwt-TextBox-readonly {
  color: #999;
}

/* When a field is selected, set the red border on it */
INPUT[type="text"]:focus,
INPUT[type="number"]:focus,
INPUT[type="email"]:focus,
INPUT[type="search"]:focus,
INPUT[type="password"]:focus,
INPUT[type="range"]:focus
{
    border: 1px solid #FF3C00;
}

.gwt-TextBox-gray {
    padding: 1px 1px 1px 1px;
    border: 1px solid #ccc;
    border-top: 1px solid #999;
    font-size: small;
    font-family: Arial Unicode MS, Arial, sans-serif;
    color: darkgray;
    font-weight: bold;
}

.viewonlytextbox {
    margin-left: 5px;
    margin-bottom: 5px;
    padding: 1px 1px 1px 1px;   
    border: none;
    font-size: small;
    font-family: Arial Unicode MS, Arial, sans-serif;
    background: white;
    color: black;
    font-weight: bold;
    text-align: center;
}

/* decimalInput is used by the DecimalInputCell class to set the style on its input field */
.decimalInput {
    width: 75px;
    text-align: right;
}

.largeNumericInput {
    width: 75px;
    text-align: right;
}

.largeInput {
    width: 100px;   
}

.largeRightInput {
    width: 100px;   
    text-align: right;
}

.largerInput {
    width: 200px;   
}

.percentInput {
    width: 100px;
}

.horizontalFlow {
    float: left;
    margin: auto 5px;   
    border: none;
}

.gwt-ListBox-ipay {
    margin-left: 0px;
    font-size: small;
    font-family: Arial Unicode MS, Arial, sans-serif;
}

.verticalFlow {
    float: none;
}

.gwt-Label-bold-left {
    float: left;
    font-weight: bold;
    vertical-align: middle;
    text-align: left;
}

.gwt-Label-iPayLink {
    color: Blue;
    cursor: pointer;
}

.gwt-Label-iPayLink:hover {
    text-decoration: underline;
}

.gwt-Button-ipay {
    margin-left: 5px;
    margin-right: 5px;
}

.gwt-Button {
	margin: 0px;
	cursor: pointer;
    cursor: hand;
    color: #eeeeee;
    background: #ededed;
    font-size:13px;
    font-family:Arial;
    font-weight:bold;
    border-radius:5px;
    border:1px solid #9f9f9f;
    padding:3px 5px;
    text-decoration:none;
    background: #40525E; /* Old browsers */
    background: -moz-linear-gradient(top, #547C95 0%, #38291e 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#547c95), color-stop(100%,#38291e)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #547c95 0%,#38291e 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #547c95 0%,#38291e 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #547c95 0%,#38291e 100%); /* IE10+ */
    background: linear-gradient(to bottom, #547c95 0%,#38291e 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#547c95', endColorstr='#38291e',GradientType=0 ); /* IE6-8 */ 
}

.gwt-Button[disabled] {
    cursor: default;
    color: #aaa;
    background: #42474A;
}

.gwt-Button[disabled]:hover {
    cursor: default;
    color: #aaa;
    background: #42474A;
}

.gwt-Button:hover {
    background: #D7D7D7; /* Old browsers */
    background: -moz-linear-gradient(top, #f7f7f7 0%, #D3D3D3 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#D3D3D3)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f7f7f7 0%,#D3D3D3 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#D3D3D3',GradientType=0 ); /* IE6-8 */ 
    color: #222;
}
.gwt-Button:active {
    position:relative;
    top:1px;
}

.gwt-DisclosurePanel-engineeringtokens {
    
}

.gwt-DisclosurePanel-insidecomponent-open {
    
}

.gwt-DisclosurePanel-engineeringtokens-closed {
    
}

.gwt-DisclosurePanel-engineeringtokens .header {
    
}

.margin {
    margin-bottom: 20px;
}

.wide {
    width: 140px;
}

.ipaypopup {
    border: 3px outset #000;
    background: white;
    padding: 10px;
}

.gwt-PopupPanel.popupMessage {
    background: white;
    border: 2px solid;
    padding: 5px;
}

.gwt-PopupPanel.popupMessageError {
    background: #f7f7f7;
    border: 2px solid red;
    padding: 5px;
}

.gwt-PopupPanel.popupMessageWarning {
    background: #f7f7f7;
    border: 2px solid #f5b00d;
    padding: 5px;
}

.gwt-PopupPanel.popupWait {
    border: 0px;
    padding: 0px;
    background: #F7F7F7;
}

.gwt-PopupPanel.mainPopup {
    background: #f7f7f7;
    border: 2px solid gray;
    padding: 5px;
}

.gwt-PopupPanelGlass {
    opacity:0.7;
    filter:alpha(opacity=30);
    background-color: grey;
}

.header {
    font-size: medium;
    font-weight: bold;
}

.container {
    border-top: 2px outset #C0C0C0;
}

.spaced {
    padding: 0.25em;
    width: 100%;
}

.gap {
    margin-right: 2px;
}

.label {
    font-weight: bold;
    padding-bottom: 10px;
}

.tree {
    border: 2px solid #C0C0C0;
    margin-top: 10px;
    margin-bottom: 10px;
}

.component {
    background: #f7f7f7;
}

.hintActive {
    font-size: x-small;
}

.hintInactive {
    font-size: x-small;
    color: #8B8989;
}

.level { 
    color: #000000;
}

.lastLevel {
    color: #000000;
}

.active {
    color: black;
}

.inactive {
    color: #8B8989;
}

.mainPanel {
    width: 99%;
    margin-left: 0.5em;
    margin-top: 0.5em;
    margin-right: 1.5em;
    margin-bottom: 1.5em;
}

.breadCrumbFonts {
    border: thin;
    border-color: Gray;
    font-size: medium;
    font-weight: bold;
    padding: 0.25em;
    background: #D7D7D7; /* Old browsers */
    background: -moz-linear-gradient(top, #f7f7f7 0%, #D3D3D3 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#D3D3D3)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f7f7f7 0%,#D3D3D3 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#D3D3D3',GradientType=0 ); /* IE6-8 */ 
    margin-bottom: 2px;
}

.formBorder {
    border: thin;
    border-style: solid;
    border-color: LightGray;
    background-color: LightGray;
}

.dataHeader {
    border-bottom-width: 2px;
    border-bottom-style: solid;
    border-color: LightGray;
    font-weight: bold;
    font-size: medium;
}

.headerNameFont {
    margin-top: 0.5em;
    font-size: medium;
    font-weight: bold;
}

.secondaryHeader {
    font-size: x-small;
    font-weight: bold;
}

.secondaryHeaderCentre {
    text-align: center;
}

.pager {
    margin: 1px;
}

.mainButtons {
    padding-top: 2px;
    padding-bottom: 5px;
}

.pageSectionTitle {
    font-size: 15px;
    font-weight: bold;
    padding-top: 0.5em;
    margin-bottom: 0.5em;
}

.sectionTitle {
    font-size: 15px;
    font-weight: bold;
    padding-top: 2px;
    margin-top: 1.5em;
    margin-bottom: 8px;
}

.dataTitle {
    font-size: 15px;
    font-weight: bold;
    padding-top: 0.75EM;
    margin-bottom: 0.5EM;
}

.dataDescription {
    font-size: 13px;
    font-weight: normal;
    padding-top: 0.25EM;
    margin-bottom: 0.25EM;
}

.dialogButtons {
    padding-top: 0.25EM;
    padding-bottom: 0.25EM;
}

a:hover {
  color: #2973BF;
  text-decoration:underline;
}

a:visited {
  color: #2973BF;
  text-decoration: none;
}

.floating {
    float: left;
}

.floatingImage {
    float: left;
    padding-right: 5px;
}

.menuLink {
    padding: 7px 7px 4px 7px;
    font-size: 1em;
    color: #000000; 
}

.menuLink a {
    color: #000000; 
}

.menuLink a:visited {
    color: #000000;
}

.menuLink a:hover {
    color: #000000;
}

.menuLinkDemo {
    padding:   7px;
    font-size: 1em;
}

.menuLinkDemo a {
    color: #4d4d4d;
}

.menuLinkDemo a:hover {
    color: #4d4d4d;
}

.highlight {
    color: #5f5f5f;
    font-size: small;
    margin-top: 5px;
    margin-bottom: 5px;
}

.warning {
    color: red;
    font-size: small;
    margin-top: 5px;
    margin-bottom: 5px;
    font-weight: bold;
}

.instructions {
	background: #ffffff;
    color: #7f7f7f;
	margin-top: 5px;
	margin-bottom: 5px;
	font-weight: bold;	
}

.gwt-TabPanel-informationPanel {
}

.gwt-TabPanel-informationPanel .gwt-TabPanelBottom {
    border-style: none;
    overflow: hidden;
    padding: 6px;
}

.gwt-TabPanel-informationPanel .gwt-TabBar {
    background: none repeat scroll 0 0 #f7f7f7;
    padding-top: 6px;
}
.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarFirst {
    width: 20px;
}
.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarRest {
}

.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarItem {
    background: none repeat scroll 0 0 #f7f7f7;
    border-radius: 3px 3px 0 0;
    border: solid thin;
    cursor: pointer;
    font-weight: bold;
    margin-left: 4px;
    padding: 0px 0px;
    text-align: center;
    font-size: 1em;
    color: #EDEDED;
}

.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarItem-selected {
    background: none repeat scroll 0 0 white;
    color: #000000;
    cursor: default;
    font-weight: bold;
    font-size: 1.5em;
    border-bottom: double 
}

.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarItem-disabled {
    color: #999999;
    cursor: default;
}

.gwt-TabPanel-informationPanel .gwt-TabBar .gwt-TabBarItem-selected .gwt-Label {
    font-size: 1.2em;
    color: #000000;
}

.menu {
    position: absolute;
    left: 10px;
    top: 50px;
    right: 0px;
    bottom: 0px;
    border: 1
}    

.horizontal {
    display: inline;
}

.gwt-SuggestBoxPopup {
    background: none repeat scroll 0 0 #f7f7f7;
    font-weight: bold;
   
}

.gwt-SuggestBoxPopup .item {
    color: #000000;
    cursor: default;
    font-size: 110%;
    padding: 2px 6px;
}
.gwt-SuggestBoxPopup .item-selected {
    background: none repeat scroll 0 0 #D5E2FF;
}

.shadow {
   -moz-box-shadow:    inset 0 0 10px #000000;
   -webkit-box-shadow: inset 0 0 10px #000000;
   box-shadow:         inset 0 0 10px #000000;
}

.compact {
    padding: 0px;
    margin: 0px;
    vertical-align: top;
}

.searchForm {
    margin:         1px;
    margin-right:   2px;
}

.searchTitle {
    width:          95%;
    border:         thin;
    border-style:   solid;
    border-color:   Gray;
    border-radius:  1px;
    font-size:      small;
    font-weight:    bold;
    padding:        0.25em;
    background:     LightGray;  
    background: #D7D7D7; /* Old browsers */
    background: -moz-linear-gradient(top, #f7f7f7 0%, #D3D3D3 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#D3D3D3)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f7f7 0%,#D3D3D3 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f7f7f7 0%,#D3D3D3 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#D3D3D3',GradientType=0 ); /* IE6-8 */ 
    margin-bottom:  10px;
}

.searchResults {
    width:          99%;
    margin-left:    0.5em;
    margin-top:     0em;
    margin-right:   1.5em;
    margin-bottom:  0em;
    vertical-align: top;
}

.results {
    width:          99%;
    margin-left:    0.5em;
    margin-top:     0.5em;
    margin-right:   1.5em;
    margin-bottom:  0.5em;
    vertical-align: top;
}

.searchResultSummary {
    font-size:     medium;
    font-weight:   bold;
    color:         gray;
}

.searchResultSmall {
    font-size:      small;
    font-weight:    normal;
    color:          gray;
    text-align:     left;
    padding-right:  5px;
}

.searchResultSmallText {
    font-size:      small;
    font-weight:    normal;
    text-align:     left;
    padding-right:  5px;
}

.searchResultLink {
    text-decoration: underline;
}

.searchResultLink:HOVER {
    text-decoration: underline;
    cursor: pointer;
}

.gwt-DateBox {
   padding: 5px 4px;
   border: 1px solid #ccc;
   border-top: 1px solid #999;
   font-size: 100%;
}

input.gwt-DateBox {
    width: 125px;
}

.fatSelect {
    width: 130px;
    height: 20px;
}

.bordered {
    border: outset thin gray;
    border-radius: 5px;
    margin-bottom: 10px;
    padding-left: 5px;
    padding-right: 3px;
    padding-top: 5px;
    padding-bottom: 5px;
}

.accountBalance {
    border: outset thin gray;
    border-radius: 5px;
    margin-bottom: 10px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    width: 100%;
}

.rightSpaced {
    margin-right: 5px;
}

.topSpaced {
	margin-top: 5px;
}

.searchBox {
	width: 115px;
}

.centreAlign {
    vertical-align: middle;
}

.topAlign {
    vertical-align: top;
}

.imagelink {
    cursor: pointer;
    margin-right: 10px;   
}

.gwt-TabBar .gwt-TabBarItem {
    padding: 0px 4px 0px 0px;
}

.gwt-TabBar .gwt-TabBarItem-disabled {
    color: #f7f7f7;
}

.editCell {
    width: 25px;
}

/* FormElement */
.formElement_panel {
    display: inline-block;
    margin: 0.1em 1em 0.1em 0.1em;
    #margin-left: 1em;
    vertical-align: top;
}
.formElement_innerPanel {
    display: inline-block;
}
.formElement_label {
   display: inline;
   color: #5f5f5f;
   font-weight: bold;
   margin-right: 0.2em;
}
.formElement_errorLabel {
   color: red;
}
.formElement_requiredLabel {
   color: red;
   display: inline;
   margin-left: 0.2em;
}
.formElement_requiredForActivationLabel {
   color: orange;
   display: inline;
   margin-left: 0.2em;
}

.formElement_requiredForActivationLabel {
   color: orange;
   display: inline;
   margin-left: 0.2em;
}

.formElement_helpImage {
    margin-left: 0.2em
}

/* FormGroupPanel */
.formGroupPanel_captionPanel {
    display: inline-block;
    #border-color: lightgray;
    border-radius: 0.5em;
    border-width: 0.2em;
    margin-bottom: 1em;
    padding-bottom: 0.2em;
    #background-color: #D9D9D9;
}

.formGroupPanel_captionPanel legend {
   font-weight: bold;
   color: #333333;
}

.formGroupPanel_internalPanel {
    display: block;
    margin-bottom: 0.3em;
}
.pseudoCaptionPanel {
    padding: 0.2em 0em 0em 0.8em;
    margin-right: 0.5em;
    border: 0.1em solid #949494;
    border-radius: 0.5em;
}
.pseudoCaptionLabel{
    font-weight: bold;
    margin-bottom: 0.2em;
}

/* FormRowPanel */
.formRowPanel_panel {
    display: inline-block;
    margin-bottom: 1em;
}

.infoPanelHeading {
    display: inline;
    vertical-align: super;
}

/* Container for input elements of a form. From SimpleForm.ui.xml (may be reused across non SimpleForm items) */
.formElementsPanel {
    background-color: #e0e0e0;
    border: 1px solid #949494;
    margin-bottom: 0.5em;
    display: inline-block;
    padding: 0.5em;
}

.veryWideSelect {
	   min-width: 150px;
}

/** Custom override style for the time picker */
.gwt-InlineLabel {
	background: #FFFFFF;
}

/** Custom override style for the time picker */
.timepickr-main {
	background: #FFFFFF;
	display: inline-block;	
}

/** Custom override style for the time picker */
.timepickr-display {
    padding: 2px;
	border-right: 1px solid #CCC;
	border-width: 1px;
	border-style: solid;
	border-color: #999 #CCC #CCC;
	-moz-border-top-colors: none;
	-moz-border-right-colors: none;
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	border-image: none;
	font-size: 1em;
	font-family: Arial Unicode MS,Arial,sans-serif;
	color: #38291E;
	margin-left: 0px;
	width: 40px;
	text-align: left;
}

.errorMessage {
    color: #ff0000;
    font-weight: normal;
    text-align: left;
    display: block;
}

.menuButton {
    margin-right: 10px;
}

.menuButtonIE {
    margin-right: 9em;
}

.menuImage {
	vertical-align: middle;
	margin-right: 5px;
}

.usernameMenuItem {
	font-size: medium;
	color: #547C95;
	border-top: 1px solid #547C95;
	border-bottom: 1px solid #547C95;
	background: #f0eeee;
}

.gwt-MenuBar-vertical .gwt-MenuItem {
    padding: 2px 5px 2px 1px;
    cursor: pointer;
}

.multipleView {
    background: white;
}

.verticalSpace {
    margin-top: 5px;
}

.leftSpace {
    margin-left: 5px;
}

/** Display currency symbol on tariffs */
.btCurrency-left {
	margin: 0.1em 0.2em 0px 0px;
	display: inline;
	text-align: left;
}

.btCurrency-right {
	margin: 0.3em 0px 0px 0.2em;
	display: inline;
	text-align: right;
	float: right;
}